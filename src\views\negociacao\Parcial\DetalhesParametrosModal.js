import { CButton, CCol, CModal, CModalBody, CModalHeader, CRow } from '@coreui/react';
import React from 'react';

const DetalhesParametrosModal = ({ isOpen, onClose, data }) => {
  const handleClose = () => {
    onClose();
  };

  return (
    <CModal size="lg" show={isOpen} onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Detalhes dos parâmetros de cálculo por Faixa de Atraso</CModalHeader>
      <CModalBody>
      <div className="table-responsive">
          <table className='table'>
            <thead>
              <tr>
                <th style={{ whiteSpace: "nowrap" }}>Descrição</th>
                <th style={{ whiteSpace: "nowrap" }}>Usa Juros Contrato</th>
                <th style={{ whiteSpace: "nowrap" }}>Dias de Cálculo</th>
                <th style={{ whiteSpace: "nowrap" }}>Dias Até Cálculo</th>
                <th style={{ whiteSpace: "nowrap" }}>% Juros</th>
                <th style={{ whiteSpace: "nowrap" }}>% Multa</th>
                <th style={{ whiteSpace: "nowrap" }} >% Honorários</th>
                <th style={{ whiteSpace: "nowrap" }}>% Desc. Juros</th>
                <th style={{ whiteSpace: "nowrap" }}>% Desc. Multa</th>
                <th style={{ whiteSpace: "nowrap" }}>Desc. Original</th>
              </tr>
            </thead>
            <tbody>
              {data.parametro.faixasDeCalculo.map((item, index) => (
                <tr key={index}>
                  <td style={{ whiteSpace: "nowrap" }}>{item.descricao}</td>
                  <td>{item.juros_Usar_Tx_Contrato ? "Sim":"Não"}</td>
                  <td>{item.dias_De_Calc}</td>
                  <td>{item.dias_Ate_Calc}</td>
                  <td>{item.perc_Juros}%</td>
                  <td>{item.perc_Multa}%</td>
                  <td>{item.perc_Honor}%</td>
                  <td>{item.perc_Desc_Juros}%</td>
                  <td>{item.perc_Desc_Multa}%</td>
                  <td>{item.desc_Original}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <CRow>
          <CCol className="d-flex justify-content-end">
            <CButton
              color="primary"
              className="mr-2"
              onClick={handleClose}
            >
              Cancelar
            </CButton>
          </CCol>
        </CRow>
      </CModalBody>
    </CModal>
  );
};

export default DetalhesParametrosModal;
