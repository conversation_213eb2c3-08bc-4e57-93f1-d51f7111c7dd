import React, { useState, useEffect } from "react";
import {
  CButton,
  CForm,
  CFormGroup,
  CInput,
  CDataTable,
  CCard,
  CCardBody,
  CLabel,
  CCol,
} from "@coreui/react";
import Select from "react-select";
import { GET_DATA, POST_DATA, PUT_DATA, DELETE_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import ConfirmModal from "src/reusable/ConfirmModal";
import LoadingComponent from "src/reusable/Loading";
import TableSelectItens from "src/reusable/TableSelectItens";
import { formatCurrency, formatThousands } from "src/reusable/helpers";
import { useAuth } from "src/auth/AuthContext";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  deleteApiInline,
  getApi,
  postApi,
  putApi,
} from "src/reusable/functions";

const ParamDelay = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Simulações",
    submodulo: "Atraso Aditamento",
  };

  const [data, setData] = useState([]);
  const [fromValue, setFromValue] = useState("");
  const [toValue, setToValue] = useState("");
  const [percValue, setPercValue] = useState("");
  const [selectedItem, setSelectedItem] = useState(null);

  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  async function getGroupCalculation() {
    const grupoCalculo = await getApi(null, "restSimulacoesParamDelay");
    if (grupoCalculo) {
      setData(grupoCalculo);
    }
    return;
  }

  function validate() {
    const isValidFrom = /^[0-9]+$/.test(fromValue);
    const isValidTo = /^[0-9]+$/.test(toValue);

    if (!isValidFrom || !isValidTo) {
      alert("Por favor, insira valores válidos para os dias de atraso.");
      return false;
    }

    const daysFrom = parseInt(fromValue);
    const daysTo = parseInt(toValue);

    if (daysFrom >= daysTo) {
      alert(
        '"Dias de atraso inicial" deve ser menor que "Dias de atraso final".'
      );
      return false;
    }
    if (percValue > 100) {
      alert("Valores percentuais não podem exceder 100.");
      return false;
    }
    return true;
  }

  const handleAdd = async () => {
    const validFields = validate();
    if (validFields) {
      const data = {
        from: parseInt(fromValue),
        to: parseInt(toValue),
        perc: percValue ? parseFloat(percValue) : 0,
      };
      const postSuccess = await postApi(data, "restSimulacoesParamDelay");
      if (postSuccess.success) {
        await getGroupCalculation();
        clearInputs();
      } else {
        alert(postSuccess.message);
      }
    }
  };

  const handleEdit = (item) => {
    const element = data.find((el) => el.id === item.id);

    setSelectedItem(element);
    setFromValue(element.from);
    setToValue(element.to);
    setPercValue(element.perc);
  };

  const handleUpdate = async () => {
    const validFields = validate();
    if (validFields) {
      const data = {
        id: selectedItem.id,
        from: parseInt(fromValue),
        to: parseInt(toValue),
        perc: percValue ? parseFloat(percValue) : 0,
      };
      const updateSuccess = await putApi(data, "restSimulacoesParamDelay");
      if (updateSuccess.success) {
        await getGroupCalculation();
        clearInputs();
      } else {
        alert(updateSuccess.message);
      }
    }
  };

  const groupCalcDelete = async (item) => {
    setSelectedItem(item);
    setShowConfirmModal(true);
  };

  const handleModalClose = (confirmation) => {
    setShowConfirmModal(false);
    handleDelete(confirmation);
  };

  const handleDelete = async (confirmation) => {
    if (confirmation) {
      const deleteSuccess = await deleteApiInline(
        selectedItem.id,
        `restSimulacoesParamDelay`
      );
      if (deleteSuccess.success) {
        await getGroupCalculation();
        clearInputs();
      } else {
        alert(deleteSuccess.message);
      }
    }
  };

  const clearInputs = () => {
    setFromValue("");
    setToValue("");
    setPercValue("");

    setSelectedItem(null);
  };

  const columns = [
    {
      key: "from",
      label: "Dias de atraso",
      formatter: (item) => item,
    },
    {
      key: "to",
      label: "até",
    },
    {
      key: "perc",
      label: "Percentual",
      formatter: (item) => renderValue(item),
    },
    {
      key: "actions",
      label: "Ações",
      formatterByObject: (item) => renderActionButton(item),
    },
  ];

  const renderValue = (value) =>
    value === 0 ? "0,00" : formatThousands(value);

  const renderActionButton = (item) => (
    <div style={{ whiteSpace: "nowrap" }}>
      <CButton
        title="Editar"
        color="info"
        onClick={() => handleEdit(item)}
        className="mr-2"
        disabled={!checkPermission("Simulações de Cálculos", "Edit")}
      >
        <i className="cil-pencil" />
      </CButton>
      <CButton
        title="Deletar"
        color="danger"
        onClick={() => groupCalcDelete(item)}
        disabled={!checkPermission("Simulações de Cálculos", "Delete")}
      >
        <i className="cil-trash" />
      </CButton>
    </div>
  );

  useEffect(() => {
    getGroupCalculation();
  }, []);

  const handlePercChange = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    value = Number(input) / 100;
    if (value > 100) value = 100;
    if (isNaN(value)) value = 0;

    setPercValue(value);
  };

  return (
    <div>
      <h3>Atraso de Aditamento</h3>
      <p style={{ color: "gray" }}>
        Definir o percentual utilizado na simulação de adiatamento baseado no
        atraso.
      </p>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard style={{ width: "60%" }}>
          <CCardBody>
            <CForm>
              <CFormGroup row>
                <CCol className="pr-1">
                  <CInput
                    type="text"
                    placeholder="Dias de Atraso Inicial"
                    value={fromValue}
                    onChange={(e) =>
                      setFromValue(e.target.value.replace(/[^0-9]/g, ""))
                    }
                  />
                </CCol>
                <CCol className="pl-1">
                  <CInput
                    type="text"
                    placeholder="Dias de Atraso Final"
                    value={toValue}
                    onChange={(e) =>
                      setToValue(e.target.value.replace(/[^0-9]/g, ""))
                    }
                  />
                </CCol>
              </CFormGroup>
              <CInput
                type="text"
                placeholder="Percentual %"
                value={formatCurrency(percValue, false)}
                onChange={handlePercChange}
              />
              <CFormGroup></CFormGroup>
              {selectedItem !== null ? (
                <CButton color="info" onClick={handleUpdate} className="mr-2">
                  Salvar
                </CButton>
              ) : (
                <CButton
                  color="info"
                  onClick={handleAdd}
                  className="mr-2"
                  title={inforPermissions(permissao).create}
                  disabled={
                    !checkPermission(
                      permissao.modulo,
                      "Create",
                      permissao.submodulo
                    )
                  }
                >
                  Adicionar
                </CButton>
              )}
              <CButton color="secondary" onClick={clearInputs}>
                Limpar campos
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </div>{" "}
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard
          style={{
            textAlign: "center",
          }}
        >
          <TableSelectItens
            data={data}
            columns={columns}
            onSelectionChange={(_) => {}}
            defaultSelectedKeys={[]}
            selectable={false}
            heightParam="600px"
          />
        </CCard>
        <ConfirmModal
          isOpen={showConfirmModal}
          onClose={handleModalClose}
          texto={"Tem certeza que deseja deletar esse grupo de cálculo?"}
        />
      </div>
    </div>
  );
};

export default ParamDelay;
