const sharedEndpoints = {
  login: "/Auth/Login",
  authLogUser: "/Auth/LogUser",
  getAuthLogUser: "/Auth/LogUser",
  postListLogUserExcel: "/Auth/ListLogUser/ExportToExcel",
  authRefresh: "/Auth/refresh",
  getcustastelaunica: "/Custas",
  postcustastelaunicainserir: "/Custas/Inserir",
  getmktzap: "/Mktzap/Messages",
  getConfigUnicaByKey: "/Config",
  postUserConnection: "/User/UpdateActiveConnection",

  /* Barramento */
  getCEPapi: "/Barramento/ConsultarCep/", //cep
  postAberturaChamado: "/Barramento/Ocomon/AbrirChamado",

  /* Datacob */
  postDatacobLogin: "/Datacob/Login",
  getDadosFinanciados: "/Datacob/BuscaDadosFinanciados",
  getContratosAtivos: "/Datacob/ContratosAtivos",
  getcustasdatacob: "/Datacob/Custas",
  getbensdatacob: "/Datacob/DadosBens",
  getNegociacoesDatacob: "/Datacob/Negociacoes",
  getdetcontratosdatacob: "/Datacob/DetalhesContratos",
  getDatacobs: "/Datacob/Datacobs",
  getDatacobUsers: "/Datacob/Users",
  getDatacobGroups: "/Datacob/Groups",
  getGroupPhases: "/Datacob/Fases",
  postCalcularNegociacao: "/Datacob/Negociacoes/CalcularNegociacao",
  getParametroCalculo: "/Datacob/Parametro/Calculo",
  getAcordoCalcData: "/Datacob/Acordo/Calcular/Data",
  getBoletoEmail: "/Datacob/Boletos/Email",
  getDatacobIndicadoresCampanha: "/Datacob/IndicadoresCampanha",
  getCrm: "/Datacob/FindCrm",
  getIndicadoresHistoricos: "/Datacob/Ocorrencias/Indicadores",
  getNumeroNegociacoes: "/Datacob/Negociacoes/Count",
  getOutrosContratos: "/Datacob/Financiado/OutrosContratos",
  getBoletosContrato: "/Datacob/Contrato/Boletos",
  postCalcularAcordo: "/Datacob/Acordos/Calcular",
  postConfirmarAcordo: "/Datacob/Acordos/Confirmar",
  getCheckStatusContrato: "/Datacob/Contrato/CheckStatus",
  postCustasCadastrar: "/Datacob/Custas/Cadastrar",
  postCustasAtualizar: "/Datacob/Custas/Atualizar",
  postCustasDevolver: "/Datacob/Custas/Devolver",
  postCustasReativar: "/Datacob/Custas/Reativar",
  getCustasMotivoDevolucao: "/Datacob/Custas/MotivoDevolucao",
  postNegociacaoLivre: "/Datacob/NegociacaoLivre",
  postSalvarNegociacaoCalculoLivre: "/Negociacao/SaveTUNegociacaoLivre",
  getTUNegociacaoLivreByContract: "/Negociacao/TUNegociacaoLivreByContract/", //idContract
  getListNegociacaoCalculoLivrePendente:
    "/Negociacao/TUNegociacaoLivrePendentes",
  updateStatusTuNegociacaoLivre: "/Negociacao/UpdateStatusTUNegociacaoLivre",
  postGerarBoletoNegociacaoSalva:
    "/Datacob/Negociacoes/GerarBoleto/NegociacaoSalva",
  postHistoricoAdicionar: "/Datacob/historicoAdicionar",
  getOcorrencias: "/Datacob/Ocorrencias",
  getBoletosContratoListar: "/Datacob/Contrato/Boletos/Listar",
  getSafraCampaignFilter: "/Datacob/Safra/Campaign/Filter",
  getSafraCampaignFilterExcel: "/Datacob/Safra/Campaign/Filter/Excel",
  getControleUsuario: "/Datacob/ControleUsuario",
  getControleUsuarioAll: "/Datacob/ControleUsuario",
  postControleUsuarioExcel: "/Datacob/ControleUsuario/ExportToExcel",
  postControleUsuario: "/UserControlTicket",
  postControleUsuarioSendEmail: "/UserControlTicket/SendEmail",

  getSafraCampaignFilterExportToExcel: "/Datacob/Safra/Campaign/ExportToExcel",
  getSafraCampaignFilterExcelExportToExcel:
    "/Datacob/Safra/Campaign/Filter/Excel/ExportToExcel",
  getTicketsUser: "/Datacob/VisaoBoletos",
  postTicketsUserPdf: "/Datacob/VisaoBoletos/ExportToPdf",
  postTicketsUserCsv: "/Datacob/VisaoBoletos/ExportToCsv",
  postTicketsUserExcel: "/Datacob/VisaoBoletos/ExportToExcel",
  getListContratoPadrao: "/Datacob/DadosAuxiliares",
  getBuscaDadosMailings: "/Datacob/BuscaDadosMailings",
  getBuscaDadosMailingsFromFile: "/Datacob/BuscaDadosMailings/FromFile",
  /*Newcon*/
  postnewconcnsccotas: "/Newcon/CnsCotas",
  postNewconDadosPrincipais: "/Newcon/DadosPrincipaisAtendimento",
  postnewconsaldoacumulado: "/Newcon/SaldoAcumulado",
  getNewconPlanoCobrança: "/Newcon/PlanoCobranca",
  postNewconValoresPagos: "/Newcon/SaldoAcumulado/ValoresPagos",
  getNewconSaldoDevedor: "/Newcon/SaldoAcumulado/SaldoDevedor",
  getNewconValorDevolver: "/Newcon/ValoresDevolver",
  postNewconValorDevolverDet: "/Newcon/ValoresDevolver/Detalhes",
  getNewconDebitoConta: "/Newcon/DebitoConta",
  getNewconSocios: "/Newcon/Socios",
  postnewconatrasos: "/Newcon/Atrasos",
  postnewconAgenda: "/Newcon/Agenda",
  postNewconAgendaProtocol: "/Newcon/Agenda",
  postNewconEstruturaVendas: "/Newcon/EstruturaDeVendas",
  getNewConEstruturaComercial: "/Newcon/EstruturaComercial",
  getNewConEstruturaDetalhes: "/Newcon/EstruturaComercial/Detalhes",
  getNewConEstruturaPonto: "/Newcon/PontoVenda",
  getNewConEstruturaEquipe: "/Newcon/EquipeVenda",
  postnewconevopreco: "/Newcon/EvolucaoPrecoBem",
  getNewconBensContrato: "/Newcon/Bens/Contrato",
  getNewconBensGarantia: "/Newcon/Bens/Garantia",

  /* Projuris */
  geteventosprojuris: "/Projuris/Eventos",
  geteventosprojurisAWS: "/Projuris/EventosAwsS3",
  getcustasprojuris: "/Projuris/Custas",
  getcustasprojurisAWS: "/Projuris/CustasAwsS3",
  getdesdobramentosprojuris: "/Projuris/Desdobramento",
  getdesdobramentosprojurisAWS: "/Projuris/DesdobramentoAwsS3",
  getpastasprojuris: "/Projuris/Processos",
  getprocessosprojuris: "/Projuris/Processo",
  getprocessosprojurisAWS: "/Projuris/ProcessosAwsS3",
  getdocumentosprojuris: "/Projuris/Documentos",
  getdocumentosprojurisAWS: "/Projuris/DocumentosAwsS3",
  postdownloaddocrpa: "/Projuris/Documentos/Download/Rpa",
  getdownloaddocrpa: "/Projuris/Documentos/Download/",

  /* Safra */
  cyberSafraConsultaSaldo: "/CyberSafra/ConsultaSaldo",
  cyberSafraconsultarElegibilidade: "/CyberSafra/ConsultarElegibilidade",
  cyberSafraSimularSaldo: "/CyberSafra/SimularAcordo",
  cyberSafraCadastrarAcordo: "/CyberSafra/CadastrarAcordo",
  cyberSafraCancelarAcordo: "/CyberSafra/CancelarAcordo",
  cyberSafraGerarBoleto: "/CyberSafra/GerarBoleto",
  cyberSafraObterBase64Boleto: "/CyberSafra/ObterBase64Boleto",
  cyberSafraEnviarEmail: "/CyberSafra/EnviarEmail",

  /* Negociacao Safra TelaUnica*/
  negociacaoSafraFinanciadoBuscaPorID: "/Negociacao/Financiado/BuscarPorId/", //{id}
  negociacaoSafraFinanciadoBuscarPorCpf: "/Negociacao/Financiado/BuscarPorCpf/", //{cpf}
  negociacaoSafraFinanciadoCriar: "/Negociacao/Financiado/Criar",
  negociacaoSafraProcessoCriar: "/Negociacao/Acordo/Processo/Criar",
  negociacaoSafraIntegracao: "/Negociacao/Acordo/Integracao",
  negociacaoSafraDeleteBoleto: "/Negociacao/Acordo/Boleto", //{guid ticket}

  negociacaoSafraContratoBuscarPorId: "/Negociacao/Contrato/BuscarPorId/", //{id}
  negociacaoSafraContratoBuscarPorContratoId:
    "/Negociacao/Contrato/BuscarPorContratoId/", //{contratoId}
  negociacaoSafraContratoCriar: "/Negociacao/Contrato/Criar",

  negociacaoSafraAcordoCriar: "/Negociacao/Acordo/Criar",
  negociacaoSafraAcordoListarAcordoPorCpfCnpj:
    "/Negociacao/Acordo/ListarAcordoPorCpfCnpj/", //{cpfCnpj}
  negociacaoSafraAcordoAtualizarMotivo: "/Negociacao/Acordo/AtualizarMotivo/", //{id}
  negociacaoSafraAcordoAtualizarStatus: "/Negociacao/Acordo/AtualizarStatus/", //{id}

  negociacaoSafraAtualizarIdAcordoDataCob:
    "/Negociacao/Acordo/AtualizarIdAcordoDataCob/", //{id}
  negociacaoSafraAtualizarAtualizarStatusDataCob:
    "/Negociacao/Acordo/AtualizarStatusDataCob/", //{id}
  negociacaoSafraAcordoAtualizarParcelaStatusDataCob:
    "/Negociacao/Acordo/AtualizarParcelaStatusDataCob",

  negociacaoParametriResticaoListar: "/Negociacao/Restricao/Listar",
  negociacaoParametriResticaoInserir: "/Negociacao/Restricao/Inserir",
  negociacaoParametriResticaoDelete: "/Negociacao/Restricao/Delete", //{id}
  negociacaoAnalisesRestricaoListar: "/Negociacao/AnalisesRestricao/Listar",
  negociacaoAnalisesRestricaoUpdateStatus:
    "/Negociacao/AnalisesRestricao/Aprovacao",
  negociacaoAnalisesRestricaoInserir: "/Negociacao/AnalisesRestricao/Inserir",
  negociacaoAnalisesRestricaoPorIdFinanciado: "/Negociacao/AnalisesRestricao", //{id_financiado}  //GET
  negociacaoAnalisesRestricaoPendentesPorIdFinanciado:
    "/Negociacao/AnalisesRestricaoPendentes", //{id_financiado}  //GET

  /* Safra */
  funilSafraCreate: "/Safra/CreateFunnelSafra",

  /* GVC Manager */
  gvcmanagerCyberSafraAcoesContrato: "/GVCManager/CyberSafra/AgreenetsActions",

  /* Telefonia */
  getDadosContratoLigacao: "/Datacob/BuscaDadosFinanciadoContrato",
  getServicosTelefonicos: "/Telephony/Servicos",
  postCredentials: "/Telephony/Credentials",
  postAutenticarTel: "/Telephony/Autenticar",
  postDeslogarTel: "/Telephony/Deslogar",
  postAcionarTabulacao: "/Telephony/Tabulacao/Acionar",
  postAcionarLigacao: "/Telephony/Ligacao/Acionar",
  postAcionarPausaLigacao: "/Telephony/Pausa/Ligar",
  putConfirmarLigacao: "/Telephony/Ligacao/Confirmar",
  postPausarLigacao: "/Telephony/Pausa/Acionar",
  postDespausarLigacao: "/Telephony/Pausa/Sair",
  postDesligarLigacao: "/Telephony/Ligacao/Desligar",
  postSairTabulacao: "/Telephony/Manual/Sair",
  postCallCampaign: "/CallCampaign",
  putCallCampaign: "/CallCampaign",
  deleteCallCampaign: "/CallCampaign",
  tactiumPause: "/PauseTactium",
  postTransferirLigacao: "/Telephony/Ligacao/Transferir",
  getAgentCall: "/AgentCall/ListByUser",

  /* Support */
  postSupportEnviarEmail: "/Support/SendEmail",
  postSupportCriarDeparamento: "/Support/Deparamentos/Create",
  deleteSupportDeleteDeparamento: "/Support/Deparamentos/Delete/" /* {id} */,
  putSupportAtualizarDeparamento: "/Support/Deparamentos/Update",
  getSupportDeparamentoPorDescricao:
    "/Support/Deparamentos/GetByDescription/" /* {description} */,
  getSupportDeparamentoPorId: "/Support/Deparamentos/GetById/{id}" /* {id} */,
  getSupportDeparamentoListaPorDescricao:
    "/Support/Deparamentos/List/" /* {description} */,
  getSupportDeparamentoLista: "/Support/Deparamentos/List",

  /* Modulos */
  getModulosLista: "/Module",

  /* WebSoket */
  wsProjuris: "/Hub/Projuris",
  wsTelefonia: "/Hub/Telephony",

  /* Grupos */
  getGrupoLista: "/Group",
  postGrupoCriar: "/Group",
  putGrupoAtualizar: "/Group",
  deleteGrupoDeletar: "/Group",

  /* Grupos DataCob */
  getGrupoDataCobLista: "/Datacob/Groups",

  /* CRMS */
  getCrms: "/Datacob/Datacobs",

  /* Users */
  getUser: "/User",
  getUserById: "/User/",
  putUserRoles: "/User/UpdateRoles",
  getTelaUnicaUsers: "/User/AllUsers",
  putUser: "/User/UpdateUser",
  postUserUpdateGropu: "/User/UpdateGroups",
  postUserUpdateConection: "/User/UpdateActiveConnection",
  postUserCrm: "/User/Crm",
  putUserCrm: "/User/Crm",
  deleteUserCrm: "/User/Crm",
  getAllUsersSimplified: "/User/AllUsers/Simplified",

  /* Configs */
  getConfigByKey: "/Config/",
  /* TransferCampaign */
  getTransferCampignByClient: "/TransferCampaign/ByClient",
  /* DatacobSaveNegotiation */
  getSaveNegListByGrouping: "/DatacobSaveNegotiation/ByGrouping/",
  getSaveNegSearchStatusByGrouping: "/DatacobSaveNegotiation/Search/Status/",
  postSaveNeg: "/DatacobSaveNegotiation",
  getSaveNegExecute: "/DatacobSaveNegotiation/Execute",
  postSaveNegUpdateStatus: "/DatacobSaveNegotiation/Update/Status",
  /* DatacobTicketJoker */
  getJokerTicketListByGrouping: "/DatacobJokerTicket/ByGrouping/",
  postJokerTicket: "/DatacobJokerTicket",

  /* LogErrorApp */
  postLogErrorFromApp: "/LogErrorApp",
  postLogErrorFromAppLocalStorage: "/LogErrorApp/LocalStorage",
  getLogIntegracoes: "/LogIntegracao/Filter",

  /* MissedOccurrence */
  restMissedOccurrence: "/MissedOccurrence",

  /* CorJuridicoParam */
  restCorJuridicoParam: "/CorJuridicoParam",

  /* Simulações Newcon */
  postEmailSimulation: "/Simulacoes/EnviarEmail",

  /* Apis Newcon */
  getNewconParcelas: "/Newcon/ConsultaParcelasCob",

  /* Safra Campanhas */
  restSafraCampaign: "/SafraCampaign",
  /* Safra Campanhas Permissoes */
  restSafraCampaignPermissions: "/SafraCampaignPermissions",

  /* BBC */
  getBBCProducts: "/BBC/Products",
  BBCcheckEligibility: "/BBC/Simulation/Eligible",
  BBCcheckBalance: "/BBC/Balance/Check",
  BBCSimulation: "/BBC/Simulation/Simulate",
  BBCDealRegister: "/BBC/Deal/Register",
  BBCDealList: "/BBC/Deal/List/",
  BBCDealCheck: "/BBC/Deal/Check",
  BBCTicket: "/BBC/Ticket/",

  restBBCInstallmentParam: "/BbcInstallmentParam",
  restBBCDiscountParam: "/BbcDiscountParam",
  /* Email Occurrence */
  emailOccurrence43: "/EmailOccurrence/Send/43",
  /* Simulações Param Delay */
  restSimulacoesParamDelay: "/AddtionEntrySimulation/Param/Delay",

  /* Simulações */
  postAddtionEntrySimulation: "/AddtionEntrySimulation",
  postDilutionEntrySimulation: "/DilutionEntrySimulation",

  /* Historico Cresol */
  getHistoricoCresol: "/HistoricoCresol",
  postSendEmailOccRules: "/OccurrenceRules/SendEmail",

  postManager: "/Manager/encrypt",
  /* Acordos Manuais */
  postGerarAcordoManual: "/AcordoManual",
  getAcordosManuais: "/AcordoManual/GetAcordosManuais", // idAContrato , NrContrato

  /* Cartas Etermos */
  filaAprovacaoCartasETermos: "/CartasETermos/FilaAprovacao",
  filaAprovacaoCartasETermosUpdateStatus:
    "/CartasETermos/FilaAprovacao/updateStatus", //PUT

  prestadoresServicos: "/PrestadoresServicos",

  /* Termo Jurídico */
  getTermoJuridico: "/CartasETermos/Termos/tipo",
  getTermosInfos: "/CartasETermos/Termos/Infos/GetByPedido",
  postPedidoCartasEtermos: "/CartasETermos/FilaAprovacao/Create",
  postTermosInfos: "/CartasETermos/Termos/Infos",
  putTermosInfos: "/CartasETermos/Termos/Infos", // PUT for updates
  postPedidoCartasEtermosGenerateByPedido:
    "/CartasETermos/FilaAprovacao/Generate/ByPedido",
};

const apiConfig = {
  PROD: {
    baseURL: process.env.REACT_APP_API_URI_PROD,
    endpoints: sharedEndpoints,
    customEndpoints: {},
  },

  PILOTO: {
    baseURL: process.env.REACT_APP_API_URI_PILOTO,
    endpoints: sharedEndpoints,
    customEndpoints: {},
  },

  HML: {
    baseURL: process.env.REACT_APP_API_URI_HML,
    endpoints: sharedEndpoints,
    customEndpoints: {},
  },

  DEV: {
    baseURL: process.env.REACT_APP_API_URI_DEV,
    endpoints: sharedEndpoints,
    customEndpoints: {},
  },

  LOCAL: {
    baseURL: process.env.REACT_APP_API_URI_LOCAL,
    endpoints: sharedEndpoints,
    customEndpoints: {},
  },
};

const webSocketConfig = {
  PROD: {
    baseURL: process.env.REACT_APP_WEBSOCKET_URI_PROD,
    endpoints: sharedEndpoints,
    customEndpoints: {},
  },

  PILOTO: {
    baseURL: process.env.REACT_APP_WEBSOCKET_URI_PILOTO,
    endpoints: sharedEndpoints,
    customEndpoints: {},
  },

  HML: {
    baseURL: process.env.REACT_APP_WEBSOCKET_URI_HML,
    endpoints: sharedEndpoints,
    customEndpoints: {},
  },

  DEV: {
    baseURL: process.env.REACT_APP_WEBSOCKET_URI_DEV,
    endpoints: sharedEndpoints,
    customEndpoints: {},
  },

  LOCAL: {
    baseURL: process.env.REACT_APP_WEBSOCKET_URI_LOCAL,
    endpoints: sharedEndpoints,
    customEndpoints: {},
  },
};

const getURI = (endpoint = null) => {
  const env = process.env.REACT_APP_ENV || "DEV";
  let baseURL = apiConfig[env].baseURL;
  if (window.location.protocol === "https:") {
    baseURL = baseURL.replace("http://", "https://");
  }

  if (endpoint === null || endpoint === undefined || endpoint === "")
    return baseURL;

  const sharedEndpoints = apiConfig[env].endpoints;
  const customEndpoints = apiConfig[env].customEndpoints;

  const selectedEndpoint =
    customEndpoints[endpoint] || sharedEndpoints[endpoint];

  if (!selectedEndpoint) {
    throw new Error(
      `Endpoint "${endpoint}" not found for environment "${env}"`
    );
  }

  return `${baseURL}${selectedEndpoint}`;
};

const getUriWebSocket = (endpoint = null) => {
  const env = process.env.REACT_APP_ENV || "DEV";
  let baseURL = webSocketConfig[env].baseURL;

  if (endpoint === null || endpoint === undefined || endpoint === "")
    return baseURL;

  const sharedEndpoints = webSocketConfig[env].endpoints;
  const customEndpoints = webSocketConfig[env].customEndpoints;

  const selectedEndpoint =
    customEndpoints[endpoint] || sharedEndpoints[endpoint];

  if (!selectedEndpoint) {
    throw new Error(
      `Endpoint "${endpoint}" not found for environment "${env}"`
    );
  }

  return `${baseURL}${selectedEndpoint}`;
};

export { getURI, apiConfig, webSocketConfig, getUriWebSocket };
