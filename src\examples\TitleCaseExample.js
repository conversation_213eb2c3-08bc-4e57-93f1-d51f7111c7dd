import React from 'react';
import { toTitleCase } from '../reusable/helpers';

const TitleCaseExample = () => {
  // Exemplos de uso da função toTitleCase
  const examples = [
    'ACORDO VEÍCULO APREENDIDO - BA',
    'ACORDO QUITAÇÃO À VISTA - EXECUÇÃO',
    'ACORDO QUITAÇÃO PARCELADO - EXECUÇÃO',
    'ACORDO ATUALIZAÇÃO PARCELADO - BA',
    'ACORDO QUITAÇÃO À VISTA - BA',
    'ACORDO ATUALIZAÇÃO PARCELADO - EXECUÇÃO',
    'ACORDO QUITAÇÃO PARCELADO - BA',
    'ACORDO ATUALIZAÇÃO À VISTA - BA',
    'ACORDO ATUALIZAÇÃO À VISTA - EXECUÇÃO',
    'termo de acordo para quitação',
    'CONTRATO DE PRESTAÇÃO DE SERVIÇOS',
    'acordo de parcelamento da dívida'
  ];

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>Exemplo de uso da função toTitleCase</h2>
      <p>Esta função converte texto para o formato "Title Case", onde apenas as primeiras letras das palavras são maiúsculas, exceto para preposições, artigos e conjunções.</p>
      
      <div style={{ marginTop: '20px' }}>
        <h3>Exemplos:</h3>
        {examples.map((example, index) => (
          <div key={index} style={{ marginBottom: '10px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
            <div style={{ fontWeight: 'bold', color: '#dc3545' }}>
              Original: {example}
            </div>
            <div style={{ color: '#28a745' }}>
              Formatado: {toTitleCase(example)}
            </div>
          </div>
        ))}
      </div>

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#e9ecef', borderRadius: '5px' }}>
        <h4>Como usar:</h4>
        <pre style={{ backgroundColor: '#fff', padding: '10px', borderRadius: '3px' }}>
{`import { toTitleCase } from 'src/reusable/helpers';

// Exemplo de uso
const texto = 'ACORDO VEÍCULO APREENDIDO - BA';
const textoFormatado = toTitleCase(texto);
// Resultado: "Acordo Veículo Apreendido - Ba"

// Em um componente React
<label>{toTitleCase(termo.nome)}</label>`}
        </pre>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#d1ecf1', borderRadius: '5px' }}>
        <h4>Características da função:</h4>
        <ul>
          <li>Converte todo o texto para minúsculas primeiro</li>
          <li>Capitaliza a primeira letra de cada palavra</li>
          <li>Mantém preposições, artigos e conjunções em minúsculas (exceto se forem a primeira palavra)</li>
          <li>Palavras tratadas especialmente: de, da, do, das, dos, e, em, na, no, nas, nos, a, o, as, os, para, por, com, sem</li>
          <li>Preserva espaços e outros caracteres especiais</li>
        </ul>
      </div>
    </div>
  );
};

export default TitleCaseExample;
