import { CButton } from "@coreui/react";
import React, { useState, useEffect } from "react";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import { formatCurrency, formatDate } from 'src/reusable/helpers';

const PostData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), payload, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};


const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(getURI(endpoint), null, true, true, payload);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const CardParcelasAcordo = ({ dataParcelas }) => {
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");

  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");

  const [idContratoDataCob, setIdContratoDataCob] = useState(null);


  const DownloadBoletos = async (contratosSelecionados) => {
    if (contratosSelecionados != null) {
      setLoading(true);
      setLoadingAction("VarifyParam");
      await GetData(`/${contratosSelecionados.id}/${contratosSelecionados.idAcordo}`, "cyberSafraObterBase64Boleto")
        .then((data) => {
          if (data !== undefined && data !== null) {
            const url = URL.createObjectURL(data);
            window.open(url, '_blank');
            setTitleAvisoLoading("Dados do Boleto Aberto Safra Gerado");
            setMsgAvisoLoading("Caso não abra uma segunda janela com o Boleto, verifique os Bloqueios de Pop-ups do navegador");
          } else {
            setTitleAvisoLoading("Não há boletos em aberto");
            setMsgAvisoLoading("");
          }
        }).catch((err) => {
          setTitleAvisoLoading("Não há boletos em aberto");
          setMsgAvisoLoading("");
        })
        .finally(() => {

          setTimeout(() => {
            setLoading(false);
            setLoadingAction("empty");
            setMsgAvisoLoading("");
            setTitleAvisoLoading("");
          }, 3000);
        });
    }
  }

  const gerearBoleto = async (item) => {
    setLoading(true);
    setLoadingAction("VarifyParam");
    setTitleAvisoLoading(`Solicitando boleto ao Safra`);
    setMsgAvisoLoading(`...`);
    await PostData(
      {
        idAcordo: dataParcelas.idAcordo.toString(),
        numeroParcela: item.numeroParcela.toString()
      }
      , "cyberSafraGerarBoleto")
      .then((data) => {
        if (data.success) {
          setTitleAvisoLoading(`Boleto Gerado com Sucesso`);
          setMsgAvisoLoading(`Boleto gerado com sucesso, clique no botão de download.`);
          buscarContratoTelaUnica(dataParcelas,item.numeroParcela);
          DownloadBoletos(item);
        } else {
          setMsgAvisoLoading(`Boleto não Gerado`);
          setTitleAvisoLoading(data.message);
          setTimeout(() => {
            setLoading(false);
            setLoadingAction("empty");
            setMsgAvisoLoading("");
            setTitleAvisoLoading("");
          }, 3000);
        }
      })
      .catch((err) => {
        setTitleAvisoLoading(`Erro na chamada das APIS`);
        setMsgAvisoLoading(`Erro na chamada API de Gerar Boleto, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`);
      })
  };

  const limparAvisos = () => {
    setTimeout(() => {
      setLoading(false);
      setLoadingAction("empty");
      setMsgAvisoLoading("");
      setTitleAvisoLoading("");
    }, 3000);
  }

  const AtualizarStatusParcelaDataCob = async (contratosSelecionados, parcela, status) => {
    if (contratosSelecionados != null) {
      setTitleAvisoLoading("Atualizando Status Acordo")
      setMsgAvisoLoading(`Enviando...`)
      setLoading(true);
      setLoadingAction("VarifyParam");
      await PostData({
        agreement_id: contratosSelecionados.id,
        nrInstallment: parcela,
        descricption: status
      }, "negociacaoSafraAcordoAtualizarParcelaStatusDataCob",)
        .then((data) => {
          if (data.success) {
            setTitleAvisoLoading("Status Atualizando da parcela do Acordo")
            setMsgAvisoLoading("")
          } else {
            setTitleAvisoLoading("Status Não Atualizando da parcela  do Acordo")
            setMsgAvisoLoading(data.message)
          }
        })
        .catch((err) => {
          setTitleAvisoLoading("Erro ao Tentar atualizar Status da parcela do Acordo")
          setMsgAvisoLoading("")
        }).finally(() => {
          limparAvisos();
        });

    }
  }

  const EnviarGvcManager = async (contratosSelecionados, parcela) => {
    if (contratosSelecionados != null) {
      setLoading(true);
      setLoadingAction("VarifyParam");
      if (idContratoDataCob === null) {
        setTitleAvisoLoading("Falha na busca do Id Contrato Tela Única")
        setMsgAvisoLoading("")
        limparAvisos();
        return;
      }

      setTitleAvisoLoading("Enviando solicitação ao GVC Manager")
      setMsgAvisoLoading("Enviando Acordo...")

      const dateString = contratosSelecionados.dtParcelaEntrada;
      const dateObj = new Date(dateString);

      const formattedDate = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')}`;

      await PostData(
        {
          type_action: 2,
          id_agreements: idContratoDataCob,
          nr_installment: parcela + 1,
          id_agreements_tu: contratosSelecionados.id,
          prazoSimulado: contratosSelecionados.qtParcelasAtivas,
          idAcordoDataCob: contratosSelecionados.idAcordoDataCob,
          valorFinanciadoSimulado: contratosSelecionados.valorFinanciadoSimulado,
          valorHonorario: contratosSelecionados.valorHonorarios,
          dtParcelaEntrada: formattedDate,
          valorEntrada: contratosSelecionados.valorEntrada,
          cpfCnpj: contratosSelecionados.cpfCnpj
        }, "gvcmanagerCyberSafraAcoesContrato")
        .then((data) => {
          if (data.success) {
            setTitleAvisoLoading("Acordo Enviado ao GVC Manager")
            setMsgAvisoLoading("")
            AtualizarStatusParcelaDataCob(contratosSelecionados, parcela, `Enviado GVC Manager Comando de Gerar Boleto Da Parcela ${parcela}`);
          } else {
            AtualizarStatusParcelaDataCob(contratosSelecionados, parcela, "Falha")
            limparAvisos();
          }
        })
        .catch((err) => {
          setTitleAvisoLoading("Falha ao Enviar Comando de Gerar Boleto Da Parcela")
          setMsgAvisoLoading("")
          limparAvisos();
        })

    }
  }

  const buscarContratoTelaUnica = async (contratoSelecionado,parcela) => {
    if (contratoSelecionado != null) {
      setLoading(true);
      setLoadingAction("VarifyParam");
      await GetData(contratoSelecionado.contract_id, "negociacaoSafraContratoBuscarPorId")
        .then((data) => {
          if (data !== undefined && data !== null) {
            setIdContratoDataCob(data.id_Contrato);
            EnviarGvcManager(dataParcelas, parcela);
          }
        });
    }
  }

  return (<div>
    {loading && loadingAction === "VarifyParam" ?
      (<CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />) : <div className="table-responsive">
        <table className='table'>
          <thead>
            <tr>
              <th className="center-column">Parcela</th>
              <th>Data Vencimento</th>
              <th>Valor Parcela</th>
              <th>Valor Amortização</th>
              <th>Valor Imposto</th>
              <th>Valor Juros</th>
              <th>Valor Saldo</th>
              <th>Status DataCob</th>
              <th className="center-column">Ações</th>
            </tr>
          </thead>
          <tbody>
            {dataParcelas.parcelas.map((item, index) => (
              <tr key={index}>
                <td style={{ textAlign: 'center' }}>{item.numeroParcela}</td>
                <td>{formatDate(item.dtVencimento)}</td>
                <td>{formatCurrency(item.vlrParcela)}</td>
                <td>{formatCurrency(item.vlrAmortizacao)}</td>
                <td>{formatCurrency(item.vlrImposto)}</td>
                <td>{formatCurrency(item.vlrJuros)}</td>
                <td>{formatCurrency(item.vlrSaldo)}</td>
                <td> {item.statusDataCob} </td>
                <td style={{ textAlign: 'center' }}>
                  {dataParcelas.status !== "Cancelado" ? <CButton
                    color="success"
                    onClick={() => gerearBoleto(item)}
                  ><i className="cil-file" /></CButton> : ""}

                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    }

  </div>
  );
};

export default CardParcelasAcordo;
