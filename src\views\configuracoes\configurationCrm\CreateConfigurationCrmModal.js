import React, { useState, useEffect, useCallback } from "react";
import { CLabel, CCol, CInput, CSwitch, CForm } from "@coreui/react";
import { CButton, CModal, CModalBody, CModalFooter, CModalHeader, CModalTitle, CFormGroup } from "@coreui/react";
import { getURI } from "src/config/apiConfig";
import LoadingComponent from "src/reusable/Loading";
import { toast } from "react-toastify";
import Select from "react-select";
import { GET_DATA } from "src/api";

const CreateConfigurationCrmModal = ({ isOpen, editConfigurationCrm, onClose }) => {
  const token = localStorage.getItem("token");

  const [configurationcrm, setConfigurationCrm] = useState({ id: null, groupId: null, crmId: null, active: true, tag: 'Geral', smtpEmail: "", smtpFrom: "", smtpPassword: "", smtpUser: "", smtpPort: 0, smtpSsl: true, isBodyHtml: false });
  const [gruposOptions, setGruposOptions] = useState([]);
  const [crmsOptions, setCrmsOptions] = useState([]);
  const [crmSelected, setCrmSelectd] = useState(null);
  const [grupoSelected, setGrupoSelected] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleInputNameChange = (e) => {
    const { name, value } = e.target;
    setConfigurationCrm((prevConfigurationCrm) => ({ ...prevConfigurationCrm, [name]: value }));
  };

  const handleCheckedChange = (e) => {
    const { name, checked } = e.target;
    setConfigurationCrm((prevConfigurationCrm) => ({ ...prevConfigurationCrm, [name]: checked }));
  };

  const handleCrmsChange = (selectedOptions) => {
    setGrupoSelected(null);
    setCrmSelectd(selectedOptions);
    setGruposOptions(selectedOptions.grupos);
    setConfigurationCrm((confg) => ({ ...confg, crmId: selectedOptions.id }));
  }

  const handleGrupoChange = (selectedOptions) => {
    setGrupoSelected(selectedOptions);
    setConfigurationCrm((confg) => ({ ...confg, groupId: selectedOptions.id_Grupo }));
  }


  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };

  const requiredFields = [
    { name: "groupId", displayName: "Grupo Crm" },
    { name: "crmId", displayName: "Crm" },
    { name: "tag", displayName: "Tag" },
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = configurationcrm[field.name];
      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    const newConfigurationCrm = {
      groupId: configurationcrm.groupId,
      crmId: configurationcrm.crmId,
      active: configurationcrm.active,
      tag: configurationcrm.tag,
      smtpFrom: configurationcrm.smtpFrom,
      smtpEmail: configurationcrm.smtpEmail,
      smtpPassword: configurationcrm.smtpPassword,
      smtpUser: configurationcrm.smtpUser,
      smtpPort: configurationcrm.smtpPort,
      smtpSsl: configurationcrm.smtpSsl,
      isBodyHtml: configurationcrm.isBodyHtml,
    };

    handleCreateConfigurationCrm(newConfigurationCrm);
  };

  const handleCreateConfigurationCrm = async (newConfigurationCrm) => {
    try {
      setIsLoading(true);
      const url = `${getURI()}/configurationcrm`;

      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` },
        body: JSON.stringify(newConfigurationCrm),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          toast.success("Configuração criada com sucesso");
          editConfigurationCrm = status.data;
          setConfigurationCrm(status.data);
          setIsLoading(false);
          handleClose();
        } else {
          toast.warning("Configuração já cadastrada");
          setIsLoading(false);
        }
      } else {
        console.error("Erro:", response?.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro cadastrando da Configuração:", error);
      setIsLoading(false);
    }
  };

  const handleUpdateConfigurationCrm = async (configurationcrm) => {
    try {
      setIsLoading(true);
      const url = `${getURI()}/configurationcrm`;

      const response = await fetch(url, {
        method: "PUT",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}`, },
        body: JSON.stringify({
          ...configurationcrm,
          id: configurationcrm.id,
        }),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          setIsLoading(false);
          handleClose();
        }
      } else {
        console.error("Erro:", response?.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro editandar a Configuração:", error);
      setIsLoading(false);
    }
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleEdit = (e) => {
    e.preventDefault();
    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = configurationcrm[field.name];

      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    handleUpdateConfigurationCrm(configurationcrm);
  };

  const handleClose = () => {
    onClose();
  };

  function resetModal() {
    setConfigurationCrm({
      id: null,
      groupId: null,
      crmId: null,
      active: true,
      tag: 'Geral',
      smtpFrom: "",
      smtpEmail: "",
      smtpPassword: "",
      smtpUser: "",
      smtpPort: 0,
      smtpSsl: true,
      isBodyHtml: false,
    });
  }

  const getCrms = useCallback(() => {
    setIsLoading(true);
    return GetData({}, "getCrms").then((res) => {
      if (res && res !== null && res !== undefined) {
        setIsLoading(false);
        return res;
      } else setIsLoading(false);
    }).catch((err) => {
      setIsLoading(false);
      console.warn(err);
      return null;
    });
  }, []);

  const getGrupoCrm = useCallback((crm) => {
    setIsLoading(true);
    return GetData({ ActiveConnection: crm }, 'getGrupoDataCobLista').then((res) => {
      if (res && res !== null && res !== undefined) {
        setIsLoading(false);
        return res;
      } else setIsLoading(false);
    }).catch((err) => {
      console.warn(err);
      setIsLoading(false);
      return null;
    });
  }, []);

  const fetchGroupsForCrm = useCallback((crm) => {
    return getGrupoCrm(crm.datacobName).then((groupData) => {
      crm.grupos = groupData;
      return crm;
    });
  }, [getGrupoCrm]);

  useEffect(() => {
    if (isOpen) {
      getCrms().then((crms) => {
        if (crms) {
          return Promise.all(crms.map(fetchGroupsForCrm));
        }
        return [];
      })
        .then((crmGroupData) => {
          setCrmsOptions(crmGroupData);
          if (editConfigurationCrm) {
            if (crmGroupData) {
              const crm = crmGroupData?.find((a) => a.id === editConfigurationCrm.crmId);
              const crmName = crm?.datacobName || "N/A";
              setCrmSelectd({ id: editConfigurationCrm.crmId, datacobName: crmName });
              setGruposOptions(crm?.grupos || []);
              const groupName = crm?.grupos?.find((a) => a.id_Grupo === editConfigurationCrm.groupId)?.descricao || "N/A";
              setGrupoSelected({ id_Grupo: editConfigurationCrm.groupId, descricao: groupName });
            }

            setConfigurationCrm({
              id: editConfigurationCrm.id,
              groupId: editConfigurationCrm.groupId,
              crmId: editConfigurationCrm.crmId,
              active: editConfigurationCrm.active,
              tag: editConfigurationCrm.tag,
              smtpFrom: editConfigurationCrm.smtpFrom,
              smtpEmail: editConfigurationCrm.smtpEmail,
              smtpPassword: editConfigurationCrm.smtpPassword,
              smtpUser: editConfigurationCrm.smtpUser,
              smtpPort: editConfigurationCrm.smtpPort,
              smtpSsl: editConfigurationCrm.smtpSsl,
              isBodyHtml: editConfigurationCrm.isBodyHtml,
            });
          } else resetModal();
        });
    }
  }, [isOpen, getCrms, fetchGroupsForCrm, editConfigurationCrm]);

  return (
    <>
      <CModal
        show={isOpen}
        onClose={handleClose}
        closeOnBackdrop={false}
        size="lg"
      >
        <CModalHeader closeButton>
          <CModalTitle>{(configurationcrm?.id ?? false) ? "Editar Configuração" : "Adicionar Configuração"}</CModalTitle>
        </CModalHeader>
        <CModalBody>
          {isLoading ? (
            <div>
              <LoadingComponent />
            </div>
          ) : (
            <CForm>
              <CFormGroup>
                <CLabel>CRM</CLabel>
                <Select
                  options={crmsOptions}
                  value={crmSelected ?? null}
                  onChange={handleCrmsChange}
                  getOptionValue={(option) => option.id}
                  getOptionLabel={(option) => option.datacobName}
                />
              </CFormGroup>
              <CFormGroup>
                <CLabel>Grupos CRM</CLabel>
                <Select
                  options={gruposOptions}
                  value={grupoSelected ?? null}
                  onChange={handleGrupoChange}
                  getOptionValue={(option) => option.id_Grupo}
                  getOptionLabel={(option) => option.descricao}
                />
              </CFormGroup>
              <CFormGroup>
                <CLabel>Email </CLabel>
                <CInput
                  id="smtpFrom"
                  name="smtpFrom"
                  type="text"
                  value={configurationcrm.smtpFrom}
                  placeholder="Email de Remetente"
                  onChange={handleInputNameChange}
                />
              </CFormGroup>
              <CFormGroup>
                <CLabel>Smtp </CLabel>
                <CInput
                  id="smtpEmail"
                  name="smtpEmail"
                  type="text"
                  value={configurationcrm.smtpEmail}
                  placeholder="Smtp Configuração"
                  onChange={handleInputNameChange}
                />
              </CFormGroup>
              <CFormGroup>
                <CLabel>Usuário Smtp</CLabel>
                <CInput
                  id="smtpUser"
                  name="smtpUser"
                  type="text"
                  value={configurationcrm.smtpUser}
                  placeholder="Usuário Conta Smtp"
                  onChange={handleInputNameChange}
                />
              </CFormGroup>
              <CFormGroup>
                <CLabel>Password Smtp</CLabel>
                <CInput
                  id="smtpPassword"
                  name="smtpPassword"
                  type="text"
                  value={configurationcrm.smtpPassword}
                  placeholder="Senha Conta Smtp"
                  onChange={handleInputNameChange}
                />
              </CFormGroup>
              <CFormGroup>
                <CLabel>Porta Smtp</CLabel>
                <CInput
                  id="smtpPort"
                  name="smtpPort"
                  type="number"
                  precision="0"
                  value={configurationcrm.smtpPort}
                  placeholder="Porta Conta Smtp"
                  onChange={handleInputNameChange}
                />
              </CFormGroup>
              <CFormGroup>
                <CLabel htmlFor="smtpSsl" style={{ paddingRight: "20px" }}>Smtp SSL</CLabel>
                <CSwitch
                  id="smtpSsl"
                  name="smtpSsl"
                  color="success"
                  checked={configurationcrm.smtpSsl}
                  onChange={handleCheckedChange}
                  shape="pill"
                  size="sm"
                />
                <CLabel style={{ paddingLeft: "20px" }}> {configurationcrm.smtpSsl ? "Ativo" : "Inativo"} </CLabel>
              </CFormGroup>
              <CFormGroup>
                <CLabel htmlFor="isBodyHtml" style={{ paddingRight: "20px" }}>Usa HTML no Email</CLabel>
                <CSwitch
                  id="isBodyHtml"
                  name="isBodyHtml"
                  color="success"
                  checked={configurationcrm.isBodyHtml}
                  onChange={handleCheckedChange}
                  shape="pill"
                  size="sm"
                />
                <CLabel style={{ paddingLeft: "20px" }}> {configurationcrm.isBodyHtml ? "Ativo" : "Inativo"} </CLabel>
              </CFormGroup>

              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="active" style={{ paddingRight: "20px" }}>Status</CLabel>
                  <CSwitch
                    id="active"
                    name="active"
                    color="success"
                    checked={configurationcrm.active}
                    onChange={handleCheckedChange}
                    shape="pill"
                    size="sm"
                  />
                  <CLabel style={{ paddingLeft: "20px" }}> {configurationcrm.active ? "Ativo" : "Inativo"} </CLabel>
                </CCol>
              </CFormGroup>
            </CForm>
          )}
        </CModalBody>
        <CModalFooter>
          {!configurationcrm?.id && (
            <CButton color="info" onClick={handleSubmit} disabled={isLoading}>
              <i className="cil-plus"></i> Adicionar Config
            </CButton>
          )}
          {configurationcrm?.id && (
            <>
              <CButton color="info" onClick={handleEdit} disabled={isLoading}>
                Salvar
              </CButton>
            </>
          )}
        </CModalFooter>
      </CModal>
    </>
  );
};

export default CreateConfigurationCrmModal;
