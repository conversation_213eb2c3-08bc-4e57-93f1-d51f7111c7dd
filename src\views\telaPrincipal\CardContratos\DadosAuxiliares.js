import React, { useState, useEffect } from "react";
import { CRow, CCol, CCardBody } from "@coreui/react";

import { useMyContext } from "src/reusable/DataContext";

import Select from "react-select";
import { GET_DATA } from "src/api";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import LoadingComponent from "src/reusable/Loading";

const DadosAuxiliares = () => {
  const { data } = useMyContext();

  const [contractSelected, setContractSelected] = useState(null);

  const [dadosAuxiliaresList, setDadosAuxiliaresList] = useState([]);
  const [optionsSelect, setOptionsSelect] = useState(null);

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  useEffect(() => {
    setOptionsSelect(null); // Limpa as opções
    setContractSelected({ label: "Selecione um contrato", value: null }); // Limpa o contrato selecionado
    setDadosAuxiliaresList([]); // Limpa os dados auxiliares

    const interval = setInterval(() => {
      const currentContracts = JSON.parse(
        localStorage.getItem("contratosAtivos") || "[]"
      );

      setOptionsSelect((prevOptions) => {
        const newOptions = [
          { label: "Selecione um contrato", value: null },
          ...currentContracts.map((x) => ({
            label: "Contrato: " + x.numero_Contrato,
            value: x.id_Contrato,
          })),
        ];
        return JSON.stringify(prevOptions) !== JSON.stringify(newOptions)
          ? newOptions
          : prevOptions;
      });
    }, 1000); // Verifica alterações a cada segundo

    return () => {
      clearInterval(interval);
      setLoading(false);
      setOptionsSelect(null);
      setDadosAuxiliaresList([]);
      setContractSelected({ label: "Selecione um contrato", value: null });
    };
  }, [data]);

  const [loading, setLoading] = useState(false);

  const handleChange = async (option) => {
    if (option.value === null) {
      setDadosAuxiliaresList([]);
      setContractSelected(null);
      return;
    }
    setContractSelected(option);
    const dadosAuxiliares = await getContratos(option.value);
    setDadosAuxiliaresList(dadosAuxiliares);
  };

  async function getContratos(id_Contrato) {
    setLoading(true);
    const data = { crm: financiadoData.coddatacob };
    const contratosAbertos = await GET_DATA(
      `Datacob/DadosAuxiliares/${id_Contrato}`,
      data
    );
    setLoading(false);
    return contratosAbertos;
  }

  return (
    <CCardBody>
      <CRow>
        <>
          <CCol md="4">
            <Select
              className="mb-3"
              placeholder="Selecione um contrato"
              options={optionsSelect}
              value={contractSelected}
              onChange={handleChange}
            />
          </CCol>
        </>
      </CRow>
      {loading && <LoadingComponent />}
      {dadosAuxiliaresList.length === 0 && <NaoHaDadosTables />}
      {dadosAuxiliaresList.length > 0 && (
        <>
          <table className="table">
            <thead>
              <tr>
                <th>Nome</th>
                <th>Valor</th>
              </tr>
            </thead>
            <tbody>
              {dadosAuxiliaresList.map((item, index) => (
                <tr key={index}>
                  <td>{item.nome}</td>
                  <td>{item.valor}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </>
      )}
    </CCardBody>
  );
};

export default DadosAuxiliares;
