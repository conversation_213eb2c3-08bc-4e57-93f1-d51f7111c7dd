import React, { useState, useEffect } from "react";
import { CButton, CDataTable, CRow, CCol } from "@coreui/react";
import { getURI } from "src/config/apiConfig";
import LoadingComponent from "src/reusable/Loading";
import { formatDateTime } from "src/reusable/helpers";
import ValoresDevolverModal from "./ValoresDevolverModal/ValoresDevolverModal";
import DebitoEmContaModal from "./DebitoEmContaModal/DebitoEmContaModal";
import { useAuth } from "src/auth/AuthContext";

const ConsultasBuscas = ({ selected }) => {

  const { checkPermission,inforPermissions } = useAuth();

  const permissaoTelaPrincipalConsultaseBuscasCasultaValoresADevolver = {
    modulo: "Tela Principal",
    submodulo: "Consultas e Buscas - Consulta de Valores à Devolver",
  }

  const permissaoTelaPrincipalConsultaseBuscasCDebitoEmConta = {
    modulo: "Tela Principal",
    submodulo: "Consultas e Buscas - Debito em Conta",
  }


  const [showValoresDevolverModal, setShowValoresDevolverModal] =
    useState(false);
  const [showDebitoEmContaModal, setShowDebitoEmContaModal] = useState(false);

  const cnscCotas =
    localStorage.getItem("cnscCotas") &&
      localStorage.getItem("cnscCotas") !== "undefined"
      ? JSON.parse(localStorage.getItem("cnscCotas")) ?? []
      : [];

  return (
    <>
      <CRow>
        <CCol className='mt-2'>
          <CButton
          className='mr-2'
            color="info"
            onClick={() => setShowValoresDevolverModal(true)}
            title={inforPermissions(permissaoTelaPrincipalConsultaseBuscasCasultaValoresADevolver).view}
            disabled={!checkPermission(permissaoTelaPrincipalConsultaseBuscasCasultaValoresADevolver.modulo,"View",permissaoTelaPrincipalConsultaseBuscasCasultaValoresADevolver.submodulo)}
          >
            Consulta de Valores a Devolver
          </CButton>

          <CButton
            color="info"
            onClick={() => setShowDebitoEmContaModal(true)}
            title={inforPermissions(permissaoTelaPrincipalConsultaseBuscasCDebitoEmConta).view}
            disabled={!checkPermission(permissaoTelaPrincipalConsultaseBuscasCDebitoEmConta.modulo,"View",permissaoTelaPrincipalConsultaseBuscasCDebitoEmConta.submodulo)}
          >
            Débito Em Conta
          </CButton>
        </CCol>
      </CRow>

      <ValoresDevolverModal
        isOpen={showValoresDevolverModal}
        onClose={() => setShowValoresDevolverModal(false)}
        idCota={cnscCotas.idCota}
      />
      <DebitoEmContaModal
        isOpen={showDebitoEmContaModal}
        onClose={() => setShowDebitoEmContaModal(false)}
        idCota={cnscCotas.idCota}
      />
    </>
  );
};

export default ConsultasBuscas;
