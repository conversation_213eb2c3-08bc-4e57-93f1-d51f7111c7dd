import React, { useState, useEffect } from "react";
import { <PERSON>utton, CCardBody, CInput, CCol, CRow, <PERSON>ard<PERSON>ooter, <PERSON>ard<PERSON>eader, <PERSON><PERSON>, CTooltip } from "@coreui/react";

import Select from "react-select";
import CreateOcorrenciaModal from "../ocorrencias/CreateOcorrenciaModal";
import { formatDateTime } from "src/reusable/helpers";
import { GET_DATA } from "src/api";
import TableSelectItens from "src/reusable/TableSelectItens";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import { useMyContext } from "src/reusable/DataContext";
import CardLoading from "src/reusable/CardLoading";
import { useAuth } from "src/auth/AuthContext";
import { getURI } from "src/config/apiConfig";
import ObsMoreModal from "./Modal/ObsMoreModal.tsx";

const CardHistorico = ({ wrapMoment, onCloseModal }) => {
  const { data } = useMyContext();
  const [isLoading, setIsLoading] = useState(false);
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Histórico e registros",
    submodulo: "Adicionar ocorrências",
  };

  const [financiadoData, setDadosFinanciados] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  const [isRpa, setIsRpa] = useState(false);

  async function getRpaConfig() {
    try {
      const response = await GET_DATA(
        getURI("getConfigByKey"),
        null,
        true,
        true,
        "rpa_save_occurrences_active"
      );
      if (response === "1") {
        setIsRpa(true);
      }
    } catch (error) {
      setIsRpa(false);
    }
  }

  useEffect(() => {
    if (data) {
      setDadosFinanciados(data);
    }
  }, [data]);

  useEffect(() => {
    if (financiadoData) {
      fetchData();
    }
  }, [financiadoData]);

  const [historicoOcorrencia, setHistoricoOcorrencia] = useState(null);
  const [selectedOcorrencia, setSelectedOcorrencia] = useState("");
  const [selectedCodigo, setSelectedCodigo] = useState("");
  const [ocorrenciaOptions, setOcorrenciaOptions] = useState([]);
  const [codigoOptions, setCodigoOptions] = useState([]);
  const [operadorOptions, setOperadorOptions] = useState([]);
  const [tipoLigacaoOptions, setTipoLigacaoOptions] = useState([]);
  const [selectedOperador, setSelectedOperador] = useState("");
  const [selectedTipoLigacao, setSelectedTipoLigacao] = useState("");
  const [filteredData, setFilteredeData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");

  const [modalShow, setModalShow] = useState(false);
  const [obsModalShow, setObsModalShow] = useState(false);
  const [obsModal, setObsModal] = useState("");

  const handleOcorrenciaChange = (selectedOption) => {
    setSelectedOcorrencia(selectedOption.value);
  };

  const handleCodigoChange = (selectedOption) => {
    setSelectedCodigo(selectedOption.value);
  };

  const handleOperadorChange = (selectedOption) => {
    setSelectedOperador(selectedOption.value);
  };

  const handleTipoLigacaoChange = (selectedOption) => {
    setSelectedTipoLigacao(selectedOption.value);
  };

  const handleHistoricoChange = (event) => {
    const term = event.target.value;
    setSearchTerm(term);
  };

  async function getHistoricoResumo(Id_Agrupamento, numeroContrato) {
    const data = { Id_Agrupamento: Id_Agrupamento, numeroContrato: numeroContrato };
    const historicoResumo = await GET_DATA("Datacob/HistoricoResumo", data);
    if (historicoResumo) {
      const filteredResumoHist = historicoResumo.filter(
        (item) =>
          item.cod_Ocorr_Sistema !== "030" && //SMS Enviado
          item.cod_Ocorr_Sistema !== "104" && //SMS Com Erro
          item.cod_Ocorr_Sistema !== "105" && //SMS Com Sucesso
          item.cod_Ocorr_Sistema !== "182" //Digital | Negociação SMS
      );
      return filteredResumoHist;
    }
  }

  const handleSave = async () => {
    const novoHistoricoResumo = await getHistoricoResumo(
      financiadoData.id_Agrupamento,
      financiadoData.numero_Contrato
    );
    setFilteredeData(novoHistoricoResumo);
  };

  const handleClose = () => {
    setModalShow(false);
    onCloseModal();
  };

  async function fetchData() {
    setIsLoading(true);
    const data = await getHistoricoResumo(financiadoData.id_Agrupamento, financiadoData.numero_Contrato);
    setHistoricoOcorrencia(data);
    setFilteredeData(data);

    if (data) {
      const uniqueOcorrencias = [
        ...new Set(data.map((item) => item.descricao)),
      ];
      const uniqueCodigos = [
        ...new Set(data.map((item) => item.cod_Ocorr_Sistema)),
      ];
      const uniqueOperador = [...new Set(data.map((item) => item.nome))];
      const uniqueTipoLigacao = [...new Set(data.map((item) => item.callType))];

      const optionsOcorrencias = [
        { value: "", label: "Ocorrência" }, // "All" option
        ...uniqueOcorrencias.map((Ocorrencia) => ({
          value: Ocorrencia,
          label: Ocorrencia,
        })),
      ];

      const optionsCodigo = [
        { value: "", label: "Código" },
        ...uniqueCodigos.map((Cod) => ({
          value: Cod,
          label: Cod,
        })),
      ];

      const optionsOperador = [
        { value: "", label: "Operador" },
        ...uniqueOperador.map((Cod) => ({
          value: Cod,
          label: Cod,
        })),
      ];

      const optionsTipoLigacao = [
        { value: "", label: "Tipo de Ligação" },
        ...uniqueTipoLigacao
          .filter((item) => item !== "" && item !== null && item !== undefined)
          .map((Cod) => ({
            value: Cod,
            label: Cod,
          })),
      ];

      setOcorrenciaOptions(optionsOcorrencias);
      setCodigoOptions(optionsCodigo);
      setOperadorOptions(optionsOperador);
      setTipoLigacaoOptions(optionsTipoLigacao);
    }
    setIsLoading(false);
  }

  useEffect(() => {
    if (financiadoData) {
      fetchData();
    }
    getRpaConfig();
  }, []);

  useEffect(() => {
    if (historicoOcorrencia) {
      const filteredDataVar = historicoOcorrencia.filter((item) => {
        const matchesOcorrencia =
          !selectedOcorrencia || item.descricao === selectedOcorrencia;

        const matchesOperador =
          !selectedOperador || item.nome === selectedOperador;

        const matchesTipoLigacao =
          !selectedTipoLigacao || item.callType === selectedTipoLigacao;

        const matchesCodigo =
          !selectedCodigo ||
          item.cod_Ocorr_Sistema === selectedCodigo ||
          (selectedCodigo === "All" && item.cod_Ocorr_Sistema);

        const matchesSearchTerm = !searchTerm ||
          (item.observacao && item.observacao.toLowerCase().includes(searchTerm.toLowerCase()));        

        return (
          matchesOcorrencia &&
          matchesCodigo &&
          matchesOperador &&
          matchesTipoLigacao && 
          matchesSearchTerm
        );
      });
      setFilteredeData(filteredDataVar);
    }
  }, [
    selectedCodigo,
    selectedOcorrencia,
    selectedOperador,
    selectedTipoLigacao,
    searchTerm
  ]);

  const tableColumns = [
    {
      key: "dt_ocorr",
      label: "Datas",
      formatter: (value) => formatDateTime(value),
      defaultSort: "descending",
    },
    { key: "cod_Ocorr_Sistema", label: "Código" },
    {
      key: "descricao",
      label: "Desc. Ocorrência",
      style: { whiteSpace: "nowrap" },
    },
    {
      key: "observacao",
      label: "Observações",
      formatter: (item) => renderObsCell(item),
    },
    { key: "nome", label: "Operador" },
    {
      key: "dddFone",
      label: "Telefone",
      formatter: (item) => renderCell(item),
    },
    { key: "complemento", label: "Complemento" },
    {
      key: "callType",
      label: "Tipo de Ligação",
      formatter: (item) =>
        item === "" || item === null || item === undefined ? "---" : item,
    },
  ];

  const renderCell = (item) => {
    return <div style={{ whiteSpace: "nowrap" }}>{item}</div>;
  };

  const renderObsCell = (item) => {
    return (
      <>
        <div className="d-flex justify-content-between">
          <div
            style={{ textWrap: "nowrap" }}
            dangerouslySetInnerHTML={{
              __html: item.replaceAll(/\n/g, " ").slice(0, 40),
            }}
          />
          {item?.replaceAll(/\n/g, " ")?.length > 41 && (
            <CTooltip content={`Ver ocorrência fechada`} placement={"top"}>
              <CButton
                color="info"
                className={" ml-2"}
                style={{ width: "40px", height: "30px" }}
                onClick={() => {
                  setObsModal(item);
                  setObsModalShow(true);
                }}
              >
                <strong>
                  <i className="cil-plus" />
                </strong>
              </CButton>
            </CTooltip>
          )}
        </div>
      </>
    );
  };

  return (
    <CCard style={{ height: "auto" }}>
      <CCardHeader
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <div>
          <i className="cil-clock mr-2" />
          Histórico e registros - Resumo
        </div>
        <div>
          <span>Você usa MKTZAP, clique aqui para acessar: </span>
          <a
            href="https://app.mktzap.com.br/login"
            target="_blank"
            rel="noreferrer"
          >
            <CButton color="success">
              <img src="/images/mktzap_logo.png" alt="Tela Única Login" />{" "}
              Acessar MKTZAP
            </CButton>
          </a>
        </div>
      </CCardHeader>
      {isLoading ? (
        <CardLoading />
      ) : (
        <>
          <CCardBody className="mb-3 pt-2" style={{ maxHeight: "15px" }}>
            <CRow>
              <CCol md="2">
                <Select
                  size="sm"
                  options={ocorrenciaOptions}
                  value={ocorrenciaOptions.find(
                    (option) => option.value === selectedOcorrencia
                  )}
                  onChange={handleOcorrenciaChange}
                  placeholder={"Selecione"}
                />
              </CCol>
              <CCol md="2">
                <Select
                  options={codigoOptions}
                  value={codigoOptions.find(
                    (option) => option.value === selectedCodigo
                  )}
                  onChange={handleCodigoChange}
                  placeholder={"Selecione"}
                />
              </CCol>
              <CCol md="2">
                <Select
                  options={operadorOptions}
                  value={operadorOptions.find(
                    (option) => option.value === selectedOperador
                  )}
                  onChange={handleOperadorChange}
                  placeholder={"Selecione"}
                />
              </CCol>
              <CCol md="2">
                <Select
                  options={tipoLigacaoOptions}
                  value={tipoLigacaoOptions.find(
                    (option) => option.value === selectedTipoLigacao
                  )}
                  onChange={handleTipoLigacaoChange}
                  placeholder={"Selecione"}
                />
              </CCol>            
              <CCol md="2">
                <CInput
                  type="text"
                  value={searchTerm}
                  onChange={handleHistoricoChange}
                  placeholder={"Localize histórico"}
                />
              </CCol>
              <CCol className="d-flex justify-content-end" md="2">
                <CButton
                  color="info"
                  onClick={() => setModalShow(true)}
                  title={inforPermissions(permissao).create}
                  disabled={
                    !financiadoData ||
                    !checkPermission(
                      permissao.modulo,
                      "Create",
                      permissao.submodulo
                    )
                  }
                >
                  <i className="cil-plus" /> Adicionar ocorrência
                </CButton>
              </CCol>
            </CRow>
          </CCardBody>
          {filteredData && filteredData?.length > 0 ? (
            <CCardFooter className="mt-2">
              <TableSelectItens
                data={filteredData}
                columns={tableColumns}
                onSelectionChange={(_) => {}}
                defaultSelectedKeys={[]}
                selectable={false}
                heightParam="290px"
              />
            </CCardFooter>
          ) : (
            <CCardBody>
              <NaoHaDadosTables />
            </CCardBody>
          )}
        </>
      )}
      {modalShow && (
        <CreateOcorrenciaModal
          isOpen={modalShow}
          onClose={handleClose}
          onSave={handleSave}
          isRpa={isRpa}
        />
      )}
      {obsModalShow && (
        <ObsMoreModal
          isOpen={obsModalShow}
          onClose={() => setObsModalShow(false)}
          obs={obsModal}
        />
      )}
    </CCard>
  );
};

export default CardHistorico;
