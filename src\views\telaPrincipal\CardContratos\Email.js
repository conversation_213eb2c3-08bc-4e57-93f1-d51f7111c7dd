import React, { useState, useEffect } from "react";
import {
  <PERSON>ow,
  <PERSON>ol,
  CLabel,
  CCardBody,
  CButton,
  CInputCheckbox,
  CBadge,
} from "@coreui/react";
import SelecionarModal from "./SelecionarModal";
import { formatDate } from "src/reusable/helpers";
import AdicionarEmail from "./OutrosModals/AdicionarEmail";
import EditarEmail from "./OutrosModals/EditarEmail";
import { GET_DATA, POST_DATA } from "src/api";
import { getTiposEmails, getDadosFinanciado } from "src/reusable/functions";
import { useMyContext } from "src/reusable/DataContext";
import { useAuth } from "src/auth/AuthContext";
import { toast } from "react-toastify";

const Email = ({ selected, onHandleSave }) => {
  const { checkPermission, inforPermissions } = useAuth();

  const permissao = {
    modulo: "Tela Principal",
    submodulo: "Email",
  };

  const { data } = useMyContext();

  const [financiadoData, setFinanciadoData] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  const [financiadoEmails, setFinanciadoEmails] = useState(
    localStorage.getItem("clientData")
      ? JSON.parse(localStorage.getItem("clientData")).emails
      : null
  );

  useEffect(() => {
    if (financiadoData && selected) {
      updateView();
    }
  }, [financiadoData]);

  const handleTipoEmail = async () => {
    const res = await getTiposEmails();
    setTipoEmail(res);
  };

  const [showModalSelecionar, setShowModalSelecionar] = useState(false);
  const [showModalAdicionar, setShowModalAdicionar] = useState(false);
  const [showModalEditar, setShowModalEditar] = useState(false);

  const [email, setEmail] = useState(null);
  const [emailList, setEmailList] = useState([]);

  const [tipoEmail, setTipoEmail] = useState([]);

  const handleCloseAddModal = () => {
    setShowModalSelecionar(false);
    setShowModalAdicionar(false);
  };

  const handleCloseEditModal = () => {
    setShowModalEditar(false);
  };

  const handleCloseModal = () => {
    setShowModalSelecionar(false);
  };

  const handleChange = (selecao) => {
    setEmail(selecao);
  };

  const handleAdicionar = async (newData) => {
    const postSuccess = await dataPost(newData);
    if (postSuccess.success) {
      const updatedData = await getDadosFinanciado(
        financiadoData.id_Financiado, financiadoData.numero_Contrato
      );
      setEmailList(updatedData.emails);
      localStorage.setItem("clientData", JSON.stringify(updatedData));

      const lastIndex = updatedData.emails.length - 1;
      const lastEmail = updatedData.emails[lastIndex];
      setEmail(lastEmail);
      if (onHandleSave) onHandleSave();
      toast.success("Dados do financiado alterados com sucesso!");
    } else {
      toast.warning(postSuccess.message);
    }
  };

  const dataPost = async (newData) => {
    const data = {
      cpfCnpj: financiadoData.cpfCnpj,
      idGrupo: financiadoData.id_Grupo,
      nome: financiadoData.nome,
      emails: [{ ...newData }],
    };
    const result = await POST_DATA("Datacob/DadosCadastrais/Financiado", data);
    return result;
  };

  const updateView = async () => {
    const updatedData = await getDadosFinanciado(financiadoData.id_Financiado, financiadoData.numero_Contrato);
    if (updatedData) {
      setEmailList(updatedData.emails);
      localStorage.setItem("clientData", JSON.stringify(updatedData));
      setFinanciadoEmails(updatedData.emails);
      setEmail(updatedData.emails[0]);
    }
  };

  const renderStatus = (value) => {
    switch (value) {
      case 0:
        return <CBadge color="danger">Inativo</CBadge>;
      case 1:
        return <CBadge color="success">Ativo</CBadge>;
      case 2:
        return <CBadge color="info">Efetivo</CBadge>;
      case 3:
        return <CBadge color="danger">Inativo</CBadge>;
      default:
        break;
    }
  };

  const [tiposEmails, setTiposEmails] = useState(null);
  const renderTipoEmail = (value) => {
    if (tipoEmail) {
      const findEmail = tipoEmail.find((eId) => eId.idTipo === value);
      if (findEmail) {
        return <div>{findEmail?.descricao}</div>;
      } else return <div>---</div>;
    } else return <div>---</div>;
  };

  const getReferenciasEmails = async () => {
    const result = await getTiposEmails();
    setTiposEmails(result);
  };

  useEffect(() => {
    if (selected && localStorage.getItem("clientData")) {
      const dadoAtualizado = JSON.parse(
        localStorage.getItem("clientData")
      ).emails;
      setFinanciadoEmails(dadoAtualizado);
      if (!email) {
        setEmail(dadoAtualizado[0]);
      } else {
        //Checar se o dado selecionado já existe na lista atual e manter
        const existeDado = dadoAtualizado.find(
          (item) => item.id_Email === email.id_Email
        );
        if (!existeDado) {
          setEmail(dadoAtualizado[0]);
        }
      }

      setEmailList(dadoAtualizado);
    } else {
      if (financiadoData && !financiadoEmails) {
        updateView();
      }
    }
    // if (selected) {
    //   if (
    //     localStorage.getItem("tiposOpcoes") &&
    //     localStorage.getItem("tiposOpcoes").tiposEmail
    //   ) {
    //     setTiposEmails(
    //       JSON.parse(localStorage.getItem("tiposOpcoes")).tiposEmail
    //     );
    //   } else {
    //     getReferenciasEmails();
    //   }
    // }
  }, [selected]);

  useEffect(() => {
    if (data) {
      setFinanciadoData(data);
    }
    const fetchData = async () => {
      try {
        await Promise.all([handleTipoEmail()]);
      } catch (error) {
        console.log(error);
      }
    };
    fetchData();
  }, [data]);

  return (
    <CCardBody>
      <CRow>
        <CCol>
          <CLabel>Email</CLabel>
          <div>
            <strong>
              {" "}
              {email?.endereco_Email
                ? email?.endereco_Email
                : "E-mail não cadastrado."}{" "}
            </strong>
          </div>
        </CCol>
      </CRow>
      <CRow>
        <CCol md="4">
          <CLabel>Status</CLabel>
          <div> {email ? renderStatus(email?.status_Email) : "---"}</div>
        </CCol>
        <CCol md="3">
          <CLabel>Tipo</CLabel>
          <div>
            <strong>
              {" "}
              {email?.id_Tipo_Email
                ? renderTipoEmail(email?.id_Tipo_Email)
                : "---"}
            </strong>
          </div>
        </CCol>
        <CCol md="5">
          <CCol className="d-flex">
            <div>
              <CInputCheckbox
                name="enviarCarta"
                checked={email?.enviarCarta === 1}
                readOnly
                disabled
              />
            </div>
            <div>
              <CLabel>Mala Direta</CLabel>
            </div>
          </CCol>
          <CCol className="d-flex">
            <div>
              <CInputCheckbox
                name="facebook"
                checked={email?.hasFaceBook ?? false}
                readOnly
                disabled
              />
            </div>
            <div>
              <CLabel>Facebook</CLabel>
            </div>
          </CCol>
        </CCol>
      </CRow>
      <CRow>
        <CCol md="4">
          <CLabel>Contato</CLabel>
          <div>
            <strong> {email?.contato ? email?.contato : "---"} </strong>
          </div>
        </CCol>
        <CCol md="4">
          <CLabel>Data de Inclusão</CLabel>
          <div>
            <strong>
              {" "}
              {email?.dtInclusao ? formatDate(email?.dtInclusao) : "---"}{" "}
            </strong>
          </div>
        </CCol>
      </CRow>
      <CRow>
        <CCol md="8" />
        <CCol
          md="4"
          style={{
            display: "flex",
            alignItems: "flex-end",
            justifyContent: "end",
          }}
        >
          <CButton
            color="secondary"
            onClick={() => setShowModalSelecionar(true)}
            size="sm"
            className="mr-2"
            disabled={!financiadoData}
          >
            Selecionar e-mail
          </CButton>
          <CButton
            color="secondary"
            onClick={() => setShowModalEditar(true)}
            size="sm"
            className="mr-2"
            title={
              !financiadoData
                ? "Nenhum financiado Selecionado"
                : inforPermissions(permissao).edit
            }
            disabled={
              !financiadoData ||
              !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
            }
          >
            Editar
          </CButton>
          <CButton
            color="secondary"
            size="sm"
            onClick={() => setShowModalAdicionar(true)}
            title={
              !financiadoData
                ? "Nenhum financiado Selecionado"
                : inforPermissions(permissao).create
            }
            disabled={
              !financiadoData ||
              !checkPermission(permissao.modulo, "Create", permissao.submodulo)
            }
          >
            Adicionar
          </CButton>
        </CCol>
        {showModalSelecionar && (
          <SelecionarModal
            onClose={handleCloseModal}
            options={emailList}
            isOpen={showModalSelecionar}
            onConfirm={handleChange}
            tab={5}
          />
        )}
        {showModalAdicionar && (
          <AdicionarEmail
            onClose={handleCloseAddModal}
            isOpen={showModalAdicionar}
            onSave={handleAdicionar}
            tipoEmail={tipoEmail}
          />
        )}
        {showModalEditar && (
          <EditarEmail
            onClose={handleCloseEditModal}
            isOpen={showModalEditar}
            editData={email}
            onEdit={handleAdicionar}
            tipoEmail={tipoEmail}
          />
        )}
      </CRow>
    </CCardBody>
  );
};

export default Email;
