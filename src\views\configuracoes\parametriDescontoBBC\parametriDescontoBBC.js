import {
  <PERSON>utton,
  <PERSON>ard,
  CCardBody,
  CCol,
  CForm,
  CFormGroup,
  CInput,
  CLabel,
  CRow,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { DELETE_DATA, GET_DATA, POST_DATA, PUT_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import ConfirmModal from "src/reusable/ConfirmModal";
import TableSelectItens from "src/reusable/TableSelectItens";
import { formatCurrency } from "src/reusable/helpers";

const ParametriDescontoBBC = () => {
  const [data, setData] = useState([]);
  const [inicio, setInicio] = useState(0);
  const [fim, setFim] = useState(0);
  const [vlAtt, setVlAtt] = useState(0);
  const [vlQtc, setVlQtc] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [insertMode, setInsertMode] = useState(true);
  const [guid, setGuid] = useState();

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const columns = [
    {
      key: "dayFrom",
      label: "De",
      defaultSort: "ascending",
    },
    {
      key: "dayTo",
      label: "Até",
    },
    {
      key: "updatePerc",
      label: "Atualização",
      formatter: (item) => item + "%",
    },
    {
      key: "acquittancePerc",
      label: "Quitação",
      formatter: (item) => item + "%",
    },
    {
      key: "actions",
      label: "Ações",
      formatterByObject: (item) => renderActionButton(item),
    },
  ];

  const renderActionButton = (item) => (
    <>
      <CButton
        color="info"
        onClick={() => handleConfirmUpdate(item)}
        className="mr-2"
        // title={inforPermissions(permissao).edit}
        // disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
      >
        <i className="cil-pencil" />
      </CButton>
      <CButton
        color="danger"
        onClick={() => handleConfirmDelete(item)}
        className="mr-2"
        // title={inforPermissions(permissao).edit}
        // disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
      >
        <i className="cil-trash" />
      </CButton>
    </>
  );

  const handleSave = async () => {
    if (inicio >= fim) {
      toast.info("Dia de inicio não pode ser maior ou igual que o dia de fim!");
      return;
    }
    setIsLoading(true);
    if (insertMode) {
      const data = {
        dayFrom: inicio,
        dayTo: fim,
        updatePerc: vlAtt,
        acquittancePerc: vlQtc,
        userId: user?.id,
      };
      const insertSuccess = await POST_DATA(
        getURI("restBBCDiscountParam"),
        data,
        true
      );
      if (insertSuccess.success) {
        toast.success("Cadastro realizado com sucesso!");
        await getLista();
        clearInputs();
      } else {
        alert(insertSuccess.message);
      }
    } else {
      const data = {
        dayFrom: inicio,
        dayTo: fim,
        updatePerc: vlAtt,
        acquittancePerc: vlQtc,
        id: guid,
      };
      const insertSuccess = await PUT_DATA(
        getURI("restBBCDiscountParam"),
        data,
        true
      );
      if (insertSuccess.success) {
        toast.success("Alteração realizada com sucesso!");
        await getLista();
        clearInputs();
      } else {
        alert(insertSuccess.message);
      }
    }
    setIsLoading(false);
  };

  const handleModalClose = (confirmation) => {
    setShowConfirmModal(false);
    handleDelete(confirmation);
  };

  const handleConfirmDelete = (item) => {
    setSelectedItem(item);
    setShowConfirmModal(true);
  };
  const handleConfirmUpdate = (item) => {
    setGuid(item.id);
    setInicio(item.dayFrom);
    setFim(item.dayTo);
    setVlAtt(item.updatePerc);
    setVlQtc(item.acquittancePerc);
    setInsertMode(false);
  };

  const handleDelete = async (confirmation) => {
    setIsLoading(true);
    if (confirmation) {
      const deleteSuccess = await DELETE_DATA(
        getURI("restBBCDiscountParam") + "/" + selectedItem.id,
        null,
        true
      );

      if (deleteSuccess.success) {
        toast.success("Exclusão realizada com sucesso!");
        await getLista();
        clearInputs();
      } else {
        alert(deleteSuccess.message);
      }
    }
    setIsLoading(false);
  };

  const clearInputs = () => {
    setInicio(0);
    setFim(0);
    setVlAtt(0);
    setVlQtc(0);
  };

  async function getLista() {
    setIsLoading(true);
    const lista = await GET_DATA(getURI("restBBCDiscountParam"), null, true);

    if (lista) {
      setData(lista);
    }
    setIsLoading(false);
    return;
  }

  useEffect(() => {
    const awaitFunc = async () => {
      await getLista();
    };
    awaitFunc();
  }, []);

  const handleAtualizacao = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");
    const value = Number(input) / 100;
    if (input.length > 11 || isNaN(value)) return;
    setVlAtt(value);
  };
  const handleQuitacao = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");
    const value = Number(input) / 100;
    if (input.length > 11 || isNaN(value)) return;
    setVlQtc(value);
  };

  return (
    <div>
      <h3>Cadastro de desconto BBC:</h3>
      <p style={{ color: "gray" }}></p>
      <div>
        <CCard>
          <CCardBody>
            <CForm>
              <CRow>
                <CCol xs>
                  <CRow>
                    <CCol>
                      <h5 style={{ color: "gray" }}>
                        {insertMode ? "Cadastro" : "Alteração"}:
                      </h5>
                    </CCol>
                  </CRow>
                  <CFormGroup className="row gx-3 gy-2 align-items-center">
                    <CCol xs={8}>
                      <CRow>
                        <CCol>
                          <CLabel>De</CLabel>
                          <CInput
                            type="number"
                            value={inicio}
                            min={0}
                            onChange={(event) => {
                              setInicio(event.target.value);
                            }}
                          />
                        </CCol>

                        <CCol>
                          <CLabel>Até</CLabel>
                          <CInput
                            type="number"
                            value={fim}
                            min={0}
                            onChange={(event) => {
                              setFim(event.target.value);
                            }}
                          />
                        </CCol>
                        <CCol>
                          <CLabel>Atualização</CLabel>
                          <CInput
                            type="number"
                            value={vlAtt}
                            min={0}
                            onChange={(event) => {
                              setVlAtt(event.target.value);
                            }}
                            // onChange={handleAtualizacao}
                          />
                        </CCol>
                        <CCol>
                          <CLabel>Quitação</CLabel>
                          <CInput
                            type="number"
                            min={0}
                            value={vlQtc}
                            onChange={(event) => {
                              setVlQtc(event.target.value);
                            }}
                          />
                        </CCol>
                      </CRow>
                    </CCol>
                    <CCol xs="auto" style={{ paddingTop: "28px" }}>
                      <CButton
                        color="success"
                        onClick={handleSave}
                        className="mr-2"
                      >
                        {insertMode ? "Incluir" : "Alterar"}
                      </CButton>
                      <CButton
                        color="light"
                        onClick={() => {
                          setInsertMode(true);
                          setGuid(null);
                          clearInputs();
                        }}
                        className="mr-2"
                      >
                        Cancelar
                      </CButton>
                    </CCol>
                  </CFormGroup>
                </CCol>
                <CCol xs>
                  <CRow>
                    <CCol>
                      <h5 style={{ color: "gray" }}>
                        Lista de faixas já inclusas:
                      </h5>
                    </CCol>
                  </CRow>
                  <CRow>
                    {isLoading ? (
                      <CardLoading />
                    ) : (
                      <CCol>
                        <TableSelectItens
                          data={data}
                          columns={columns}
                          onSelectionChange={(_) => {}}
                          defaultSelectedKeys={[]}
                          selectable={false}
                          heightParam="600px"
                        />
                        <ConfirmModal
                          isOpen={showConfirmModal}
                          onClose={handleModalClose}
                          texto={"Tem certeza que deseja excluir esse item?"}
                        />
                      </CCol>
                    )}
                  </CRow>
                </CCol>
              </CRow>
            </CForm>
          </CCardBody>
        </CCard>
      </div>
    </div>
  );
};

export default ParametriDescontoBBC;
