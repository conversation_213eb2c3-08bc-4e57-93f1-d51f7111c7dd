import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CButton,
  CModalTitle,
  CCardBody,
  CForm,
  CFormGroup,
  CInput,
  CLabel,
} from "@coreui/react";
import { POST_DATA, GET_DATA, PUT_DATA } from "src/api";
import Select from "react-select";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Crms } from "src/types/apiGroup/datacob";
import { User, CrmCred } from "src/types/localStorage";
import { ApiResponse } from "src/types/common";
import { reject, resolve } from "core-js/fn/promise";

type TypeSelect<T> = {
  value: T;
  label: string;
};

const EditUserCrmAuthModal = ({
  onClose,
  editUser,
}: {
  onClose: () => void;
  editUser: User;
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [login, setLogin] = useState("");
  const [password, setPassword] = useState("");
  const [crmOptions, setCrmOptions] = useState([]);
  const [crmSelected, setCrmSelected] = useState("");

  const user = JSON.parse(localStorage.getItem("user") || "{}") as User;

  useEffect(() => {
    if (error !== "") {
      toast.info(error);
      setError("");
    }
  }, [error, onClose]);

  const getDatacobs = async () => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI("getDatacobs"), {}, true);

        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        //await Promise.all([]);
        const datacobs = (await getDatacobs()) as Crms[];
        const options = datacobs.map((datacob) => {
          return {
            value: datacob.datacobNumber,
            label: datacob.datacobName,
          };
        });
        setCrmOptions(options);
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  async function handleConfirm(): Promise<void> {
    if (login === "" || password === "" || crmSelected === "") {
      setError("Por favor, preencha todos os campos");
      return;
    }
    setLoading(true);
    try {
      const response = await saveCrmCred();
      if (response === true) {
        toast.info("Credenciais salvas com sucesso");
        if (user.id === editUser.id) {
          user.crmCred = [
            ...user.crmCred,
            { login: login, crm: crmSelected } as CrmCred,
          ];
          localStorage.setItem("user", JSON.stringify(user));
        }
        //user.crmCred = [...user.crmCred, response.data];
        //localStorage.setItem("user", JSON.stringify(user));
        onClose();
      } else {
        toast.error("Erro ao salvar credenciais");
      }
    } catch (error) {
      toast.error("Erro ao salvar credenciais");
    }
    setLoading(false);
  }

  const saveCrmCred = async (): Promise<boolean> => {
    let update = false;
    let idCob = "";
    if (editUser?.crmCred.length > 0) {
      const cob = editUser.crmCred.find((crm) => crm.crm === crmSelected);
      if (cob === undefined) {
        update = false;
      } else {
        update = true;
        idCob = cob.id;
      }
    }
    try {
      if (update) {
        const res = await PUT_DATA(
          getURI("putUserCrm"),
          {
            login: login,
            password: password,
            id: idCob,
            crm: crmSelected,
          },
          true
        );
        if (res?.status === 400) {
          return false;
        }
        if (res?.success === false) {
          return false;
        }
      } else {
        const res = await POST_DATA(
          getURI("postUserCrm"),
          {
            login: login,
            password: password,
            userId: editUser?.id,
            crm: crmSelected,
          },
          true
        );
        if (res?.status === 400) {
          return false;
        }
        if (res?.success === false) {
          return false;
        }
      }
      return true;
    } catch (error) {}
    return false;
  };

  const handleCrmSelect = (e: TypeSelect<string>) => {
    if (editUser?.crmCred.length > 0) {
      const cob = editUser.crmCred.find((crm) => crm.crm === e.value);
      if (cob === undefined) setLogin("");
      else setLogin(cob.login);
    }

    setPassword("");

    setCrmSelected(e.value);
  };

  return (
    <CModal
      show={true}
      onClose={onClose}
      closeOnBackdrop={false}
      className="custom-modal"
    >
      <CModalHeader>
        <CModalTitle>Informe o usuário e senha do Datacob</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CRow>
          <CCol>
            <CCardBody className="py-1">
              {loading ? (
                <CardLoading Title={""} Msg={""} />
              ) : (
                <>
                  <CForm>
                    <CFormGroup>
                      <Select
                        placeholder={"Selecione"}
                        options={crmOptions}
                        onChange={handleCrmSelect}
                        // isDisabled={editUser && editUser.id === 1 && editUser.isAdmin}
                      />
                    </CFormGroup>
                    <CFormGroup>
                      <CLabel>login</CLabel>
                      <CInput
                        disabled={crmSelected === ""}
                        value={login}
                        onChange={(e) => setLogin(e.currentTarget.value)}
                      />
                      <CLabel>Senha</CLabel>
                      <CInput
                        disabled={crmSelected === ""}
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.currentTarget.value)}
                      />
                    </CFormGroup>
                  </CForm>
                </>
              )}
            </CCardBody>
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CButton color="danger" onClick={onClose}>
          Cancelar
        </CButton>
        <CButton color="info" onClick={handleConfirm}>
          Salvar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default EditUserCrmAuthModal;
