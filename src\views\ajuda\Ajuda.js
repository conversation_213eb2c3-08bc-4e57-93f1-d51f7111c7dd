/* Crie um formulario com os seguintes campos
  1- Input Text - Nome
  2 - Select 2 - Departamento {Digital, RH, Operação - Rodobens Consorcio, Operação - Safra,Operação - Banco,Operação- RNI / GVI}
  3 - Iunt Email - E-mail
  4 - Select 2 - <PERSON><PERSON><PERSON><PERSON> {Negociação,<PERSON><PERSON><PERSON>dico,Tela Principal,Cálculos,Boleto}
  5 - Checkbox - {Problema ou Duvida}
  6 - Textarea - Descrição
  7 - Anexo - Arrastas e Soltar - Arquivo
  8 - Botão - Enviar
  9 - Aviso de envio - {Seu chamado foi enviado com sucesso!}
*/
import { useEffect, useState } from "react";

import {
  CButton,
  CCard,
  CCardBody,
  CForm,
  CFormGroup,
  CInput,
} from "@coreui/react";

import Select from "react-select";
import FileDropzone from "src/reusable/FileDropzone";
import { GET_DATA, POST_FORMDATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import { isEmailValid } from "src/reusable/helpers";
import { useAuth } from "src/auth/AuthContext";

const PostFormData = async (formData, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_FORMDATA(
        getURI(endpoint),
        formData,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(getURI(endpoint), payload, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const Ajuda = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Ajuda",
    submodulo: null,
  };

  const [selectedDepartamentos, setSelectedDepartamentos] = useState(null);
  const [selectedModulo, setSelectedModulo] = useState(null);
  const [nomeValue, setNomeValue] = useState("");
  const [emailValue, setEmailValue] = useState("");
  const [tipoDuvida, setTipoDuvidaValue] = useState("Duvida");
  const [descricaoValue, setDescricaoValue] = useState("");
  const [arquivos, setArquivos] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [textoLoading, setTextoLoading] = useState("");
  const [departamentosOptions, setDepartamentosOptions] = useState([]);
  const [moduloOptions, setModuloOptions] = useState([]);
  const [erros, setErros] = useState({
    Nome: "",
    Departamento: "",
    Email: "",
    Modulo: "",
    TipoAjuda: "",
    Descricao: "",
  });

  const clearErros = () => {
    setErros({
      Nome: "",
      Departamento: "",
      Email: "",
      Modulo: "",
      TipoAjuda: "",
      Descricao: "",
    });
  };

  const clearForm = () => {
    setNomeValue("");
    setSelectedDepartamentos(null);
    setEmailValue("");
    setSelectedModulo(null);
    setTipoDuvidaValue("Duvida");
    setDescricaoValue("");
    setArquivos([]);
  };

  useEffect(() => {
    if (!isLoading) GetDepartamentos();
  }, []);

  useEffect(() => {
    if (departamentosOptions && departamentosOptions.length > 0) GetModulos();
  }, [departamentosOptions]);

  const GetDepartamentos = () => {
    setIsLoading(true);
    setTextoLoading("Carregando dados dos Departamentos...");
    GetData({}, "getSupportDeparamentoLista")
      .then((response) => {
        setDepartamentosOptions(response);
      })
      .catch((error) => {
        console.warn("error", error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const GetModulos = () => {
    setIsLoading(true);
    setTextoLoading("Carregando dados dos Modulos...");
    GetData({}, "getModulosLista")
      .then((response) => {
        setModuloOptions(response);
      })
      .catch((error) => {
        console.warn("error", error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleSelectModulo = (e) => {
    setSelectedModulo(e.name);
  };
  const handleSelectDepartamentos = (e) => {
    setSelectedDepartamentos(e.description);
  };

  const onFilesAdded = (newFiles) => {
    console.warn("newFiles", newFiles);
    // Atualize o estado ou faça o que precisar com os arquivos aqui
    setArquivos(newFiles);
  };

  const handleSubmit = async () => {
    clearErros();
    if (!validaFomrulario()) return;

    setIsLoading(true);
    setTextoLoading("Enviando chamado...");
    const payload = {
      Nome: nomeValue,
      Departamento: selectedDepartamentos,
      Email: emailValue,
      Modulo: selectedModulo,
      TipoAjuda: tipoDuvida,
      Descricao: descricaoValue,
      // Arquivos não são incluídos diretamente no JSON, pois requerem um tratamento especial
    };

    // Se estiver enviando arquivos, você precisa usar FormData
    const formData = new FormData();
    for (const key in payload) {
      formData.append(key, payload[key]);
    }

    arquivos.forEach((file) => formData.append("Anexos", file));

    // Enviar formData via sua API ou método de escolha aqui
    PostFormData(formData, "postSupportEnviarEmail")
      .then((response) => {
        setTextoLoading("Seu chamado foi enviado com sucesso!");
      })
      .catch((error) => {
        setTextoLoading("Seu chamado NÃO foi enviado, tente novamente!");
      })
      .finally(() => {
        setTimeout(() => {
          clearForm();
          clearErros();
          setIsLoading(false);
        }, 3000);
      });
  };

  const validaFomrulario = () => {
    let erros = {
      Nome: "",
      Departamento: "",
      Email: "",
      Modulo: "",
      TipoAjuda: "",
      Descricao: "",
    };

    let isValid = true;

    if (nomeValue === "") {
      erros.Nome = "Nome é obrigatório";
      isValid = false;
    }

    if (selectedDepartamentos === null) {
      erros.Departamento = "Departamento é obrigatório";
      isValid = false;
    }

    if (emailValue === "") {
      erros.Email = "E-mail é obrigatório";
      isValid = false;
    }

    if (!isEmailValid(emailValue)) {
      erros.Email = "E-mail é inválido";
      isValid = false;
    }

    if (selectedModulo === null) {
      erros.Modulo = "Módulo é obrigatório";
      isValid = false;
    }

    if (tipoDuvida === "") {
      erros.TipoAjuda = "Tipo de ajuda é obrigatório";
      isValid = false;
    }

    if (descricaoValue === "") {
      erros.Descricao = "Descrição é obrigatório";
      isValid = false;
    }

    setErros(erros);
    return isValid;
  };

  return (
    <div>
      <h3>Ajuda</h3>
      <p style={{ color: "gray" }}>
        Informe sua duvida ou problema para que possamos te ajudar
      </p>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard
          style={{
            width: "80%",
            minHeight: "500px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <CCardBody>
            {isLoading ? (
              <CardLoading Title={textoLoading} />
            ) : (
              <CForm>
                <CFormGroup>
                  <CInput
                    type="text"
                    placeholder="Nome"
                    value={nomeValue}
                    onChange={(e) => setNomeValue(e.target.value)}
                  />
                  {erros.Nome && (
                    <div className="text-danger">{erros.Nome}</div>
                  )}
                </CFormGroup>
                <CFormGroup>
                  <Select
                    className="mb-2"
                    placeholder="Departamento"
                    value={departamentosOptions.find(
                      (option) => option.description === selectedDepartamentos
                    )}
                    onChange={handleSelectDepartamentos}
                    options={departamentosOptions}
                    getOptionValue={(option) => option.id}
                    getOptionLabel={(option) => option.description}
                  />
                  {erros.Departamento && (
                    <div className="text-danger">{erros.Departamento}</div>
                  )}
                </CFormGroup>
                <CFormGroup>
                  <CInput
                    type="email"
                    placeholder="E-mail"
                    value={emailValue}
                    onChange={(e) => setEmailValue(e.target.value)}
                  />
                  {erros.Email && (
                    <div className="text-danger">{erros.Email}</div>
                  )}
                </CFormGroup>
                <CFormGroup>
                  <Select
                    className="mb-2"
                    placeholder="Módulo"
                    value={moduloOptions.find(
                      (option) => option.name === selectedModulo
                    )}
                    onChange={handleSelectModulo}
                    options={moduloOptions}
                    getOptionValue={(option) => option.id}
                    getOptionLabel={(option) => option.name}
                  />
                  {erros.Modulo && (
                    <div className="text-danger">{erros.Modulo}</div>
                  )}
                </CFormGroup>
                <CFormGroup>
                  <div className="form-check">
                    <input
                      onChange={(e) => setTipoDuvidaValue("Problema")}
                      className="form-check-input"
                      type="radio"
                      name="flexRadioDefault"
                      id="flexRadioDefault1"
                      checked={tipoDuvida === "Problema" ? true : false}
                    />
                    <label className="form-check-label" for="flexRadioDefault1">
                      É um problema
                    </label>
                  </div>
                  <div className="form-check">
                    <input
                      onChange={(e) => setTipoDuvidaValue("Duvida")}
                      className="form-check-input"
                      type="radio"
                      name="flexRadioDefault"
                      id="flexRadioDefault2"
                      checked={tipoDuvida === "Duvida" ? true : false}
                    />
                    <label className="form-check-label" for="flexRadioDefault2">
                      ou é uma dúvida
                    </label>
                  </div>
                  {erros.TipoAjuda && (
                    <div className="text-danger">{erros.TipoAjuda}</div>
                  )}
                </CFormGroup>
                <CFormGroup>
                  <textarea
                    placeholder="Informe sua duvida ou problema para que possamos te ajudar"
                    className="form-control"
                    id="exampleFormControlTextarea1"
                    rows="3"
                    value={descricaoValue}
                    onChange={(e) => setDescricaoValue(e.target.value)}
                  ></textarea>
                  {erros.Descricao && (
                    <div className="text-danger">{erros.Descricao}</div>
                  )}
                </CFormGroup>
                <CFormGroup>
                  <FileDropzone onFilesAdded={onFilesAdded} />
                </CFormGroup>
                <CButton
                  title={inforPermissions(permissao).create}
                  disabled={
                    !checkPermission(
                      permissao.modulo,
                      "Create",
                      permissao.submodulo
                    )
                  }
                  style={{ width: "150px" }}
                  color="primary"
                  onClick={handleSubmit}
                >
                  Enviar
                </CButton>
              </CForm>
            )}
          </CCardBody>
        </CCard>
      </div>
    </div>
  );
};

export default Ajuda;
