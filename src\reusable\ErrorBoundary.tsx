import React, { Component, ComponentType, ErrorInfo } from "react";
import { postAxios } from "./functions";

interface Props {
  children: React.ReactNode;
  errorElement: ComponentType;
}

interface State {
  hasError: boolean;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const user = localStorage.getItem("user")
      ? JSON.parse(localStorage.getItem("user"))
      : null;

    const local = JSON.stringify({
      ...localStorage,
    });

    postAxios(
      {
        error: error.name,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        userId: user?.id || 0,
        localStorage: local,
      },
      "postLogErrorFromApp"
    );
    this.setState({ hasError: true });
  }

  render() {
    if (this.state.hasError) {
      return <this.props.errorElement />;
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
