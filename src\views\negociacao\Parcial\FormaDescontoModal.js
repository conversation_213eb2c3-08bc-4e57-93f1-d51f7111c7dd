import {
  CButton,
  CCol,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CRow,
} from "@coreui/react";
import React from "react";

const FormaDescontoModal = ({ isOpen, onClose }) => {
  const handleClose = (honor) => {
    onClose(honor);
  };

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader>Atenção!</CModalHeader>
      <CModalBody>
        <div>
          Valor informado está abaixo do desconto máximo permitido em parâmetro!
          Deseja continuar com desconto no honorário?
        </div>
      </CModalBody>
      <CModalFooter>
        <CRow>
          <CCol className="d-flex justify-content-end">
            <CButton
              color="success"
              className="mr-2"
              onClick={() => handleClose(true)}
            >
              Sim
            </CButton>
            <CButton
              color="danger"
              className="mr-2"
              onClick={() => handleClose(false)}
            >
              Não
            </CButton>
          </CCol>
        </CRow>
      </CModalFooter>
    </CModal>
  );
};

export default FormaDescontoModal;
