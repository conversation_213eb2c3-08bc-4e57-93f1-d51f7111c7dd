import React, { useState } from "react";
import {
  CButton,
  CFormGroup,
  CInput,
  CCard,
  CCardBody,
  CLabel,
  CRow,
  CCol,
  CCardFooter,
  CInputCheckbox,
} from "@coreui/react";
import { GET_DATA, POST_DATA } from "src/api";
import { formatThousands } from "src/reusable/helpers";
import Select from "react-select";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import CardLoading from "src/reusable/CardLoading";

const QuitacaoPrevia = ({
  ocorrencia,
  onSave,
  cnscCotas,
  contratos,
  onClose,
}) => {
  const [selectedContrato, setSelectedContrato] = useState("");

  const [data, setData] = useState([]);
  const [multaValue, setMultaValue] = useState("");
  const [jurosValue, setJurosValue] = useState("");
  const [editJurosMultas, setEditJurosMulta] = useState(false);
  const [isLoading, setLoading] = useState(false);

  function validate() {
    // Replace commas with periods in the input value
    const sanitizedJurosValue = jurosValue.replace(/,/g, ".");
    const sanitizedMultaValue = multaValue.replace(/,/g, ".");

    const isValidJuros = /^[0-9]+(\.[0-9]+)?$/.test(sanitizedJurosValue);
    const isValidMulta = /^[0-9]+(\.[0-9]+)?$/.test(sanitizedMultaValue);
    if (!isValidJuros || !isValidMulta) {
      alert("Por favor, insira valores válidos para juros e multas.");
      return false;
    }
    const percentualJuros = parseFloat(jurosValue);
    const percentualMulta = parseFloat(multaValue);
    if (percentualJuros > 100 || percentualMulta > 100) {
      alert("Valor percentual de juros e multas não pode exceder 100.");
      return false;
    }
    return true;
  }

  async function getCalculo() {
    // const validFields = editJurosMultas ? validate() : true;
    // if (validFields) {
    setLoading(true);
    const sanitizedJurosValue = jurosValue.replace(/,/g, ".");
    const sanitizedMultaValue = multaValue.replace(/,/g, ".");
    const data = {
      IdContrato: selectedContrato.value,
      IdCota: cnscCotas.idCota ?? 0,
      juros: sanitizedJurosValue,
      multa: sanitizedMultaValue,
      // juros: jurosValue,
      // multa: multaValue,
    };
    const result = await GET_DATA("Simulacoes/QuitacaoPrevia", data);
    // if(result.success === false) {
    //   toast.warning(result.message)
    // }
    if (result) {
      setData(result);
      postCalculo(result);
    } else {
      toast.warning(
        "Não encontrado parcelas em aberto para o contrato selecionado."
      );
    }
    setLoading(false);
    return;
    // }
  }

  async function postCalculo(payload) {
    const result = await POST_DATA("Simulacoes/QuitacaoPrevia", payload);
    if (result?.success) {
      //Something
    }
    return;
  }

  const buildText = (label, value = 0) => {
    if (
      label === "Valor principal ou FC" ||
      label === "Valor principal ou FC com Prévia" ||
      label === "Juros e Multas" ||
      label === "Valor H.O." ||
      label === "Custas" ||
      label === "Valor prévia" ||
      label === "Valor da negociação"
    ) {
      return `${label}: R$ ${formatThousands(value)}`;
    }
    if (label === "H.O." || label === "Prévia") {
      return `${label}: ${value}%`;
    } else {
      return `${label}: ${value}`;
    }
  };

  const fieldMappings = [
    { label: "Valor principal ou FC", field: "vlPrincipal" },
    { label: "Prévia", field: "previa" },
    {
      label: "Valor principal ou FC com Prévia",
      field: "vlPrincipalPrevia",
    },
    { label: "Juros e Multas", field: "jurosMulta" },
    // { label: "H.O.", field: "honorPerc" },
    { label: "Valor H.O.", field: "vlHO" },
    { label: "Custas", field: "custas" },
    { label: "Valor prévia", field: "vlPrevia" },
    { label: "Valor da negociação", field: "vlTotal" },
  ];

  const renderTextLines = () => {
    return fieldMappings.map((mapping) =>
      buildText(mapping.label, data[mapping.field])
    );
  };

  const handleContratoChange = (selection) => {
    setSelectedContrato(selection);
  };

  const handleSaveClick = async () => {
    const arrayToSend = [
      "Quitação com Prévia",
      "Negociador:",
      "Ativo/",
      "E-mail:",
      "Vencimento:",
      "Parcelas",
      "Valor: R$",
      " ",
      ...renderTextLines(),
    ];
    onSave(arrayToSend);
    onClose();
  };

  const handleCloseClick = () => {
    onClose();
  };

  const handleCheckBox = (e) => {
    if (e.target.checked === true) {
      setJurosValue("0");
      setMultaValue("0");
    }
    if (e.target.checked === false) {
      setJurosValue("");
      setMultaValue("");
    }
    setEditJurosMulta(e.target.checked);
  };

  return (
    <div>
      {" "}
      <CRow className="my-2">
        <CCol>
          <CLabel>Contrato</CLabel>
          <Select
            value={selectedContrato}
            onChange={handleContratoChange}
            options={contratos}
            placeholder={"Selecione um contrato"}
          />
        </CCol>
      </CRow>
      <CRow className="mt-2">
        <CCol>
          <CLabel>Inserir juros e multas manualmente?</CLabel>
          <CInputCheckbox
            style={{ margin: "6px 0px 0px 4px" }}
            name="jurosMultaCheckbox"
            onChange={(e) => handleCheckBox(e)}
            checked={editJurosMultas}
          />
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CFormGroup style={{ display: "flex" }}>
            <CInput
              className="mr-2"
              type="text"
              placeholder="Juros"
              value={jurosValue}
              disabled={!editJurosMultas}
              onChange={(e) =>
                setJurosValue(e.target.value.replace(/[^0-9.,]/g, ""))
              }
            />
            <CInput
              type="text"
              placeholder="Multa"
              value={multaValue}
              disabled={!editJurosMultas}
              onChange={(e) =>
                setMultaValue(e.target.value.replace(/[^0-9.,]/g, ""))
              }
            />
          </CFormGroup>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CButton
            color="info"
            onClick={getCalculo}
            className="mb-2"
            block
            disabled={isLoading}
          >
            Calcular
          </CButton>
        </CCol>
      </CRow>
      <CCard>
        {" "}
        {isLoading ? (
          <CardLoading />
        ) : (
          <CCardBody>
            {data ? (
              <div>
                {renderTextLines().map((line, index) => (
                  <div key={index}>{line}</div>
                ))}
              </div>
            ) : (
              <NaoHaDadosTables />
            )}
          </CCardBody>
        )}
        <CCardFooter>
          <CButton
            color="secondary"
            className="mr-2"
            onClick={handleCloseClick}
            disabled={isLoading}
          >
            Fechar
          </CButton>
          {ocorrencia && (
            <CButton
              color="success"
              onClick={handleSaveClick}
              disabled={isLoading}
            >
              Adicionar à ocorrência
            </CButton>
          )}
        </CCardFooter>
      </CCard>
    </div>
  );
};

export default QuitacaoPrevia;
