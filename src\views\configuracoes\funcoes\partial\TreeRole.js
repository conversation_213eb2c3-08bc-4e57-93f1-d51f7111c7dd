import React, { useEffect, useState } from "react";

const TreeNode = ({ item, onCheckboxChange, marginLeftText = "", subModulesItem = false }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    if (item.subModules && item.subModules.length > 0)
      setIsExpanded(!isExpanded);
  };

  const permissions = ["view", "create", "edit", "delete"];

  return (<>
    <div style={{ display: 'flex', padding: "7px", marginBottom: '10px', borderBottom: (isExpanded ? '' : '1px solid #d8dbe0')
                  ,backgroundColor: subModulesItem ? "#efefef" : "",  }} >
      <div style={{ display: 'flex', flexDirection: 'row', width: '200px', flexGrow: "1", cursor: item.subModules && item.subModules.length > 0 ? "pointer" : "" }}
        onClick={toggleExpand}>
        <div>{item.subModules && item.subModules.length > 0 && (isExpanded ? <span style={{ paddingRight: "7px" }}>-</span> : <span style={{ paddingRight: "7px" }}>+</span>)}</div>
        <div style={{ marginLeft: marginLeftText }}>{item.module}</div>
      </div>
      {permissions.map((perm, index) => (
        <div style={{ textAlign: 'center', flexGrow: "4"}}>
          <input
            type="checkbox"
            style={{ width: '50px', textAlign: 'center' }}
            key={index}
            checked={item[perm]}
            onChange={(e) => onCheckboxChange(item.moduleId, perm, e.target.checked)}
          />
        </div>
      ))}
    </div>
    {isExpanded && item.subModules && item.subModules.map(child => (
      <TreeNode subModulesItem={true} key={child.moduleId} item={child} onCheckboxChange={onCheckboxChange} marginLeftText="15px" />
    ))}
  </>
  );
}

const TreeRole = ({ initialItems, onCheckboxChange }) => {
  const [items, setItems] = useState([]);

  useEffect(() => {
    setItems(initialItems);
  }, [initialItems]);

  const handleCheckboxChange = (moduleId, permission, isChecked) => {
    // Atualizar o estado dos itens
    const newItems = items.map(item => {
      if (item.moduleId === moduleId) {
        return { ...item, [permission]: isChecked };
      } else if (item.subModules) {
        return { ...item, subModules: item.subModules.map(subItem => subItem.moduleId === moduleId ? { ...subItem, [permission]: isChecked } : subItem) };
      } else {
        return item;
      }
    });
    setItems(newItems);
    onCheckboxChange(newItems);
  };

  const TreeHeader = () => {
    return (
      <div style={{
        display: 'flex', padding: "10px", fontWeight: 'bold',
        marginBottom: '10px', borderBottom: '2px solid #d8dbe0', borderTop: '1px solid #d8dbe0'
      }}>
        <div style={{ width: '200px', flexGrow: "1" }}>Módulo</div>
        <div style={{ textAlign: 'center', flexGrow: "4" }}>Ver</div>
        <div style={{ textAlign: 'center', flexGrow: "4" }}>Criar</div>
        <div style={{ textAlign: 'center', flexGrow: "4" }}>Editar</div>
        <div style={{ textAlign: 'center', flexGrow: "4" }}>Deletar</div>
      </div>
    );
  };

  return (
    <div>
      <TreeHeader />
      {items.map(item => (
        <TreeNode key={item.moduleId} item={item} onCheckboxChange={handleCheckboxChange} />
      ))}
    </div>
  );
}

export default TreeRole;
