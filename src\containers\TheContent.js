import React, { Suspense } from "react";
import { Redirect, Switch } from "react-router-dom";
import { CContainer, CFade } from "@coreui/react";
import VisualizarAcordos from "src/views/acordos/acordos";

// routes config
import routes from "../routes";
import PrivateRoute from "src/auth/PrivateRoute";

const loading = (
  <div className="pt-3 text-center">
    <div className="sk-spinner sk-spinner-pulse"></div>
  </div>
);

const TheContent = () => {
  return (
    <main className="c-main">
      <CContainer fluid>
        <Suspense fallback={loading}>
          <Switch>
            <PrivateRoute
              path="/acordos/visualizar/:id"
              // exact={true}
              component={(props) => (
                <CFade>
                  <VisualizarAcordos {...props} />
                </CFade>
              )}
            />
            {routes.map((route, idx) => {
              return (
                route.component && (
                  <PrivateRoute
                    key={idx}
                    path={route.path}
                    exact={route.exact}
                    name={route.name}
                    component={(props) => (
                      <CFade>
                        <route.component {...props} />
                      </CFade>
                    )}
                  />
                )
              );
            })}
            <Redirect from="/" to="/login" />
          </Switch>
        </Suspense>
      </CContainer>
    </main>
  );
};

export default React.memo(TheContent);
