import React, { useEffect, useState } from "react";
import { <PERSON>utton, CBadge } from "@coreui/react";
import "react-toastify/dist/ReactToastify.css";
import { CCard, CCardBody, CCol, CRow, CInput } from "@coreui/react";
import TableSelectItens from "src/reusable/TableSelectItens";
import CreateTopicModal from "./CreateTopicModal";
import { getURI } from "src/config/apiConfig";
import { useAuth } from "src/auth/AuthContext";

const FaqsTopics = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Configurações",
    submodulo: "FAQS",
  };

  const token = localStorage.getItem("token");

  const [selectedTopic, setSelectedTopic] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [topicsList, setTopicsList] = useState([]);

  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSearchChange = (event) => {
    const term = event.target.value;
    setSearchTerm(term);
  };

  const fields = [
    {
      key: "name",
      label: "Título",
      defaultSort: "desc",
      defaultSortColumn: true
    },
    {
      key: "description",
      label: "Descrição"
    },
    {
      key: "active",
      label: "Status",
      formatterByObject: (item) => handleViewActive(item),
    },
    {
      key: "actions",
      label: "Editar",
      formatterByObject: (item) => handleViewActions(item),
    },
  ];

  const getTopics = async (search) => {
    try {
      const response = await fetch(`${getURI()}/Topic/AllTopics?take=10&search=${search}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTopicsList(data.data);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando usuários:", error);
    }
  };

  const handleEdit = (editTopic) => {
    setSelectedTopic(editTopic);
    setIsModalOpen(true);
  };

  const handleCreate = () => {
    setSelectedTopic(null);
    setIsModalOpen(true);
  };


  const handleCloseModal = () => {
    getTopics("").then(() => {
      setSelectedTopic(null);
      setIsModalOpen(false);
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        await getTopics("");
      } catch (error) {
      }
    };
    fetchData();
  }, []);

  const search = async (searchTerm) => {
    setIsLoading(true);
    try {
      await getTopics(searchTerm);
    } catch (error) {
      console.error("Erro durante a pesquisa:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewActive = (item) => {
    return item.active ? (
      <CBadge color="success">Ativo</CBadge>
    ) : (
      <CBadge color="danger">Inativo</CBadge>
    );
  };

  const handleViewActions = (item) => {
    return (
      <>
        <CButton
          color="secondary"
          onClick={() => handleEdit(item)}
          title={inforPermissions(permissao).edit}
        /*disabled={
          !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
        }*/
        >
          <i className="cil-pencil"></i>
        </CButton>
      </>
    );
  };

  return (
    <div>
      <CRow>
        <CCol className="align-items-center" md="5">
          <h2>FAQS - Tópicos</h2>
          <p style={{ color: "gray", fontSize: "small" }}>
            Crie um manual para documentar o sistema.
            Anexando documentos, .pdf e videos.
          </p>
        </CCol>
        <CCol md="3" style={{ padding: 0 }}>
          <CInput
            type="text"
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder={"Informe Descricao"}
          />
        </CCol>
        <CCol md="1" style={{ padding: 0 }}>
          <CButton
            color="primary"
            onClick={() => search(searchTerm)}
            style={{ marginLeft: "0.5rem" }}
            disabled={isLoading}
          >
            {isLoading ? (
              <i className="cil-reload" />
            ) : (
              <i className="cil-search" />
            )}
          </CButton>
        </CCol>
        <CCol className="text-right" md="3">
          <CButton
            color="info"
            onClick={() => handleCreate()}
            title={inforPermissions(permissao).create}
          /*disabled={
            !checkPermission(permissao.modulo, "Create", permissao.submodulo)
          }
            */
          >
            <i className={"cil-plus"} /> Adicionar Tópico
          </CButton>
        </CCol>
      </CRow>
      <CCard style={{ height: "100hv" }}>
        <CCardBody>
          <TableSelectItens
            data={topicsList}
            columns={fields}
            onSelectionChange={(_) => { }}
            defaultSelectedKeys={[]}
            selectable={false}
            heightParam="100%"
          />
        </CCardBody>
      </CCard>
      {isModalOpen && (
        <CreateTopicModal
          isOpen={isModalOpen}
          editTopic={selectedTopic}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

export default FaqsTopics;
