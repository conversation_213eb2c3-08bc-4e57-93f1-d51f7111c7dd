import { useEffect, useRef, useState } from "react";
import JsSI<PERSON> from "jssip";
import { postAutenticar, deslogarAgente } from "src/config/telephonyFunctions";

const SoftphoneOlosConnection = ({ shouldConnect, onEnded }) => {
  const sessionRef = useRef(null);
  const remoteAudioRef = useRef(null);
  const audioContextRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const isReconnectingRef = useRef(false);
  const hasStartedCallRef = useRef(false);
  const userAgentRef = useRef(null);
  const [passCode, setPassCode] = useState(null);
  const [agentId, setAgentId] = useState(null);
  const [reconnectTrigger, setReconnectTrigger] = useState(0);
  const pingIntervalRef = useRef(null);

  const domain = 'rodobens-ecs01.oloschannel.com.br';
  const wsUri = `wss://${domain}:8443`;
  const dnis = "9999";

  const tryReconnect = async (requireNewPasscode = false) => {
    if (isReconnectingRef.current) return;
    isReconnectingRef.current = true;

    try {
      if (userAgentRef.current) userAgentRef.current.stop();

      if (requireNewPasscode) {
        postAutenticar().finally();
        await new Promise((resolve) => setTimeout(resolve, 6000));
      }

      hasStartedCallRef.current = false;
      setReconnectTrigger(prev => prev + 1);

    } catch (error) {
      console.error("Erro durante reconexão:", error);
    } finally {
      isReconnectingRef.current = false;
    }
  };

  const cleanup = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (sessionRef.current && !sessionRef.current.isEnded()) {
      sessionRef.current.terminate();
      sessionRef.current = null;
    }

    if (userAgentRef.current) {
      userAgentRef.current.removeAllListeners();
      userAgentRef.current.stop();
      userAgentRef.current = null;
    }

    if (remoteAudioRef.current) {
      try {
        remoteAudioRef.current.pause();
        remoteAudioRef.current.srcObject = null;
        if (document.body.contains(remoteAudioRef.current)) {
          document.body.removeChild(remoteAudioRef.current);
        }
      } catch (err) {
        console.warn("Erro ao remover remoteAudioRef:", err);
      } finally {
        remoteAudioRef.current = null;
      }
    }

    if (audioContextRef.current) {
      if (audioContextRef.current.state !== "closed") {
        audioContextRef.current.close().catch((err) => {
          console.warn("Erro ao fechar AudioContext:", err);
        });
      }
      audioContextRef.current = null;
    }

    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }
  };

  const startCall = (passcodeOlos) => {
    navigator.mediaDevices.getUserMedia({ audio: true, video: false })
      .then((stream) => {
        const audioContext = new AudioContext();
        audioContextRef.current = audioContext;

        const source = audioContext.createMediaStreamSource(stream);
        const gainNode = audioContext.createGain();
        gainNode.gain.value = 3.0;
        source.connect(gainNode);

        const destination = audioContext.createMediaStreamDestination();
        gainNode.connect(destination);

        const options = {
          mediaStream: destination.stream,
          mediaConstraints: { audio: true, video: false },
          extraHeaders: [`X-pin: ${passcodeOlos}`],
          sessionTimersExpires: 120,
        };

        const session = userAgentRef.current.call(`sip:${dnis}@${domain}`, options);
        sessionRef.current = session;

        session.on("confirmed", () => {
          console.log("Chamada conectada");
        });

        session.on("ended", (data) => {
          if (data.originator === "remote" && data.cause === "Terminated") {
            tryReconnect(true);
          } else {
            let payloadAgentId = {
              agentId: agentId,
            };
            deslogarAgente(payloadAgentId).finally(() => {
              console.info('connection deslogue');
              onEnded();
            });
          }
        });

        if (session.connection) {
          session.connection.addEventListener("track", (e) => {
            const remoteStream = e.streams[0];
            if (remoteAudioRef.current) {
              remoteAudioRef.current.srcObject = remoteStream;
              remoteAudioRef.current.volume = 1.0;
              remoteAudioRef.current.play().catch((err) => {
                console.warn("Erro ao reproduzir áudio remoto:", err);
              });
            }
          });
        }
      })
      .catch((err) => {
        console.error("Erro ao acessar microfone:", err);
      });
  };

  useEffect(() => {
    if (!shouldConnect || !agentId || !passCode) return;

    const ani = `olos.webrtc.agent.id.${agentId}`;
    const uri = `sip:${ani}@${domain};user=phone`;
    const socket = new JsSIP.WebSocketInterface(wsUri);
    const config = {
      uri,
      sockets: [socket],
      password: "whatever",
      authorization_user: agentId,
      register: true,
      contact_uri: uri,
      hack_via_ws: false,
    };
    const userAgent = new JsSIP.UA(config);
    userAgentRef.current = userAgent;

    const remoteAudio = new Audio();
    remoteAudioRef.current = remoteAudio;
    remoteAudio.style.display = 'none';
    document.body.appendChild(remoteAudio);

    userAgent.on('disconnected', () => {
      tryReconnect(false);
    });

    userAgent.on("connected", () => {
      if (!pingIntervalRef.current) {
        pingIntervalRef.current = setInterval(() => {
          try {
            if (
              userAgentRef.current &&
              userAgentRef.current.transport &&
              userAgentRef.current.transport.ws &&
              userAgentRef.current.transport.ws.readyState === WebSocket.OPEN
            ) {
              userAgentRef.current.transport.ws.send("ping");
            }
          } catch (error) {
            console.warn("Erro ao enviar ping:", error);
          }
        }, 30000);
      }

      if (!hasStartedCallRef.current) {
        hasStartedCallRef.current = true;
        startCall(passCode);
      }
    });
    userAgent.start();

    return () => {
      cleanup();
    };
  }, [shouldConnect, reconnectTrigger, passCode, agentId]);

  useEffect(() => {
    const channel = new BroadcastChannel("softphone_channel");

    const handleBroadcastMessage = (event) => {
      if (event.data?.type === "update-softphone-data") {
        const { passCode, agentId } = event.data.payload || {};
        if (passCode) setPassCode(passCode);
        if (agentId) setAgentId(agentId);
      }
    };
    channel.addEventListener("message", handleBroadcastMessage);
    return () => {
      channel.removeEventListener("message", handleBroadcastMessage);
      channel.close();
    };
  }, []);

  return null;
};

export default SoftphoneOlosConnection;
