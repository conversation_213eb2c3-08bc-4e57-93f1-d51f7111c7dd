import React, { useState, useEffect } from "react";
import {
  <PERSON>ard,
  CCardBody,
  CCol,
  CDataTable,
  CRow,
  CButton,
  CCardHeader,
  CInput,
  CLabel,
  CInputCheckbox,
  CTooltip,
  CBadge,
} from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";
import { GET_DATA } from "src/api";
import DetalhesCalculoModal from "./DetalhesCalculoModal";

const DetalhesCalculos = () => {
  const [calculosDados, setCalculosDados] = useState(null);
  const [detalhesCalculo, setDetalhesCalculo] = useState(null);

  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState("");

  const [modalOpen, setModalOpen] = useState(false);

  async function getDetalhesCalculo(id_Negociacao) {
    const data = { IdNegociacao: id_Negociacao };
    const parcelas = await GET_DATA("Datacob/Negociacoes/Calculo", data);
    return parcelas;
  }

  const handleClick = async (item) => {
    const parcelas = await getDetalhesCalculo(item.id_Negociacao);
    setDetalhesCalculo(parcelas);
    setSelectedRow(item.id_Negociacao);
    setModalOpen(true);
  };

  const handleClose = () => {
    setModalOpen(false);
    setSelectedRow(null);
    setDetalhesCalculo(null);
  };

  const renderBadge = (status) => {
    switch (status) {
      case "P":
        return (
          <td>
            <CBadge color="success">Pago</CBadge>
          </td>
        );
      case "A":
        return (
          <td>
            <CBadge color="info">Aberto</CBadge>
          </td>
        );
      case "C":
        return (
          <td>
            <CBadge color="danger">Cancelado</CBadge>
          </td>
        );
      default:
        break;
    }
  };

  const rowClassName = (item) => {
    return item.id_Negociacao === selectedRow ? "selected-row" : "";
  };


  //   useEffect(() => {
  //     if (negociacoes) {
  //       const filteredData = negociacoes.filter((item) => {
  //         const matchesStatus = !selectedStatus || item.status === selectedStatus;
  //         return matchesStatus;
  //       });
  //       setCalculosDados(filteredData);
  //     }
  //   }, [selectedStatus]);

  return (
    <div>
      <CRow className="mx-3 mb-2">
        <h1>Ver detalhes dos calculos</h1>
      </CRow>
      <div>
        <CCard>
          <CCardBody>
            <p>Tela em desenvolvimento.</p>
          </CCardBody>
        </CCard>
      </div>
      <DetalhesCalculoModal
        isOpen={modalOpen}
        dados={detalhesCalculo}
        onClose={handleClose}
      />
    </div>
  );
};

export default DetalhesCalculos;
