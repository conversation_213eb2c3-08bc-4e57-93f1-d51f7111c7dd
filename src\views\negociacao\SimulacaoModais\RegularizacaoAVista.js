import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON>utton,
  <PERSON>ard,
  CCardBody,
  CRow,
  CCol,
  CCardFooter,
  CLabel,
  CInput,
} from "@coreui/react";

import { GET_DATA, POST_DATA } from "src/api";
import {
  formatCurrency,
  formatDate,
  formatThousands,
} from "src/reusable/helpers";
import Select from "react-select";
import ReactDatePicker from "react-datepicker";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import CardLoading from "src/reusable/CardLoading";
import SendEmailModal from "./SendEmailModal";

const RegularizacaoAVista = ({
  ocorrencia,
  onSave,
  contratos,
  onClose,
  cleanCalculoPost,
}) => {
  const [selectedContrato, setSelectedContrato] = useState({
    label: "Todos",
    value: null,
  });
  const [data, setData] = useState([]);
  const [isLoading, setLoading] = useState(false);

  const [email, setEmail] = useState(null);
  const [selectedOcorrencia, setSelectedOcorrencia] = useState(null);
  const [optionsOcorrencia, setOptionsOcorrencia] = useState([]);
  const [selectedPhone, setSelectedPhone] = useState(null);
  const [optionsPhone, setOptionsPhone] = useState([]);
  const [optionsEmail, setOptionsEmail] = useState([]);
  const [negociador, setNegociador] = useState("");
  const [ativo, setAtivo] = useState("");
  const [nrParcelas, setNrParcelas] = useState("");
  const [valor, setValor] = useState(0);
  const [valorMinimo, setValorMinimo] = useState(0);
  const [vencimento, setVencimento] = useState(new Date());
  const textData = useRef(null);
  const [dataFields, setDataFields] = useState(null);
  const [numerosParcelasPorContrato, setNumerosParcelasPorContrato] =
    useState(null);

  const [showSendEmailModal, setShowSendEmailModal] = useState(false);

  async function getCalculo() {
    const dataFields = {
      negociador: negociador ?? "",
      ativo: ativo ?? "",
      email: email?.value,
      parcelas: nrParcelas ?? "",
      valor: formatCurrency(valor, false),
      valorMinimo: formatCurrency(valorMinimo, false),
      vencimento: formatDate(vencimento),
    };
    setDataFields(dataFields);
  }

  const buildText = (label, value = 0) => {
    if (
      label === "Valor principal ou FC" ||
      label === "Honorário" ||
      label === "Custas" ||
      label === "Valor da negociação"
    ) {
      return `${label}: R$ ${formatThousands(value)}`;
    }
    if (label === "Honorário %") {
      return `${label}: ${value}%`;
    } else {
      return `${label}: ${value}`;
    }
  };

  const fieldMappings = [
    { label: "Valor principal ou FC", field: "vlPrincipal" },
    {
      label: "Honorário",
      field: "honorario",
    },
    { label: "Honorário %", field: "percHonorario" },
    { label: "Custas", field: "custas" },
    { label: "Valor da negociação", field: "vlTotal" },
  ];

  const renderTextLines = () => {
    return fieldMappings.map((mapping) =>
      buildText(mapping.label, data[mapping.field])
    );
  };

  const handleContratoChange = async (selection) => {
    setSelectedContrato(selection);
  };

  const handleCloseClick = () => {
    onClose();
  };

  const handleOcorrenciaChange = (event) => {
    setSelectedOcorrencia(event);
  };

  async function getTiposOcorrencia() {
    setLoading(true);
    const tiposOcorrencia = await GET_DATA("Datacob/Ocorrencias");
    if (tiposOcorrencia !== null && tiposOcorrencia !== undefined) {
      const options = [
        ...tiposOcorrencia.map((item) => {
          return {
            label: item.cod_Ocorr_Sistema + " - " + item.descricao,
            value: item.id_Ocorrencia_Sistema,
            cod_Ocorr_Sistema: item.cod_Ocorr_Sistema,
          };
        }),
      ];
      setOptionsOcorrencia(options);
    }
    setLoading(false);
  }

  const handlePhoneChange = (event) => {
    setSelectedPhone(event);
  };

  const clientData = localStorage.getItem("clientData")
    ? JSON.parse(localStorage.getItem("clientData"))
    : null;

  const userData = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const parcelasAbertas =
    contratosAtivos === null || contratosAtivos === undefined
      ? []
      : contratosAtivos?.flatMap((item) =>
          item.parcelas.filter(
            (pItem) =>
              pItem.status === "A" &&
              !pItem.nr_Acordo /* && pItem.atraso > 0  ||
      (pItem.status === "A" && !pItem.nr_Acordo && pItem.atraso === 0 && pItem.nome_Tipo_Parcela === "DIF_PARCELAS")*/
          )
        );

  const handleCalculoSuccess = (response) => {
    const neg = response.negociacaoDto[0] ?? null;

    let contratosSelecionados = neg?.parcelas;
    if (
      selectedContrato.value !== null &&
      selectedContrato.value !== undefined
    ) {
      contratosSelecionados = neg?.parcelas.filter(
        (x) => x.nrContrato === selectedContrato.label
      );
    }

    const parcelas = contratosSelecionados;
    if (parcelas === undefined || parcelas === null) return [];
    const obj = parcelas
      .filter((item) => {
        const parcAb = parcelasAbertas.find(
          (x) => x.id_Parcela === item.idParcela
        );
        return item.atraso > 0 || parcAb?.nome_Tipo_Parcela === "DIF_PARCELAS";
      })
      .map((item) => {
        const parcAb = parcelasAbertas.find(
          (x) => x.id_Parcela === item.idParcela
        );
        item.parcelaSelecionada = !false;
        return {
          ...item,
          nome_Tipo_Parcela: parcAb?.nome_Tipo_Parcela,
          dt_Pgto: parcAb?.dt_Pgto,
        };
      });

    const agrupadoPorContrato = obj.reduce((acc, parcela) => {
      const { nrContrato } = parcela;
      if (!acc[nrContrato]) {
        acc[nrContrato] = [];
      }
      acc[nrContrato].push(parcela);
      return acc;
    }, {});
    const numerosParcelas = obj.map((item) => item.nrParcela).join(", ");
    const numerosParcelasPorContrato = Object.entries(agrupadoPorContrato).map(
      ([contrato, parcelas]) => ({
        contrato_numero: contrato,
        numerosParcelas: parcelas.map((item) => item.nrParcela).join(", "),
      })
    );
    setNrParcelas(numerosParcelas);
    setNumerosParcelasPorContrato(numerosParcelasPorContrato);

    const honor = obj
      .filter((item) => item.parcelaSelecionada)
      .reduce((a, b) => a + b.vlHoNegociado, 0);

    const honorariosNegociacao = obj
      ? obj.reduce(
          (total, item) => Math.round((total + item.vlHoNegociado) * 100) / 100,
          0
        )
      : 0;
    const custasNegociacao = obj
      ? obj.reduce(
          (total, item) =>
            Math.round((total + item.vlDespesasNegociado) * 100) / 100,
          0
        )
      : 0;
    const notificacaoNegociacao = obj
      ? obj.reduce(
          (total, item) =>
            Math.round((total + item.vlNotificacaoNegociado) * 100) / 100,
          0
        )
      : 0;
    const tarifaNegociacao = obj
      ? obj.reduce(
          (total, item) =>
            Math.round((total + item.vlTarifaNegociado) * 100) / 100,
          0
        )
      : 0;
    const iofNegociacao = 0;
    const totalNegociacao = obj
      ? obj.reduce(
          (total, item) => Math.round((total + item.vlAtualizado) * 100) / 100,
          0
        )
      : 0;

    const vlMimimo = obj.reduce(
      (acc, item) => acc + item.vlAtualizadoDescontoMax,
      0
    );

    setValorMinimo(vlMimimo);

    const subTotalNegociacao = obj
      ? totalNegociacao -
        honorariosNegociacao -
        custasNegociacao -
        notificacaoNegociacao -
        tarifaNegociacao -
        iofNegociacao
      : 0;
    let percHo = parseFloat(
      ((honorariosNegociacao * 100) / subTotalNegociacao).toFixed(2)
    );
    if (isNaN(percHo)) percHo = 0;
    if (percHo > 100) percHo = 100;
    if (percHo < 0) percHo = 0;
    setValor(totalNegociacao);
  };

  const asyncFunc = async () => {
    setLoading(true);
    // busca Calculo Datacob
    await cleanCalculoPost(
      [],
      0,
      0,
      new Date(vencimento),
      handleCalculoSuccess,
      emptyFunc,
      emptyFunc,
      () => {
        setLoading(false);
      }
    );
  };

  useEffect(() => {
    asyncFunc();
  }, [selectedContrato, vencimento]);

  const emptyFunc = () => {};

  const handleSaveOccurrence = async () => {
    if (selectedPhone === null || selectedOcorrencia === null) {
      toast.warning("Selecione o telefone e a ocorrência!");
      return;
    }
    toast.info("Enviando ocorrência...");
    const payload = {
      login: userData?.username,
      id_Contrato: selectedContrato.value,
      id_Ocorrencia_Sistema: selectedOcorrencia?.value,
      observacao: textData.current.innerText,
      telefones: [selectedPhone?.value],
      complemento: "",
      telefoneParaRetorno: selectedPhone?.value,
    };
    const ocorrencia = await POST_DATA("Datacob/historicoAdicionar", payload, false, true);
    if (ocorrencia.success) {
      toast.info("Ocorrência adicionada com sucesso");
    } else {
      toast.error("Erro ao enviar ocorrência!");
    }
  };

  useEffect(() => {
    if (userData) {
      setNegociador(userData.name);
    }

    getTiposOcorrencia();
    const optPhone = clientData?.telefones.map((item) => {
      return {
        label: item.ddd + item.fone,
        value: item.ddd + item.fone,
      };
    });
    setOptionsPhone(optPhone);
    const optEmail = clientData?.emails?.map((item) => {
      return {
        label: item.endereco_Email,
        value: item.endereco_Email,
      };
    });
    setOptionsEmail(optEmail);
  }, []);

  return (
    <div>
      <CRow className="my-2">
        <CCol>
          <CLabel>Contrato</CLabel>
          {/* <Select
            value={selectedContrato}
            onChange={handleContratoChange}
            options={contratos}
            placeholder={"Selecione um contrato"}
          /> */}
          <Select
            className="mt-3"
            placeholder="Contratos"
            options={[
              { label: "Todos", value: null },
              ...contratosAtivos.map((x) => ({
                label: x.numero_Contrato,
                value: x.numero_Contrato,
              })),
            ]}
            value={selectedContrato}
            onChange={handleContratoChange}
          />
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol>
          <CLabel className="text-nowrap">Negociador</CLabel>
          <CInput
            onChange={(e) => setNegociador(e.target.value)}
            value={negociador}
          />
        </CCol>
        <CCol>
          <CLabel className="text-nowrap">Ativo</CLabel>
          <CInput value={ativo} onChange={(e) => setAtivo(e.target.value)} />
        </CCol>
        <CCol>
          <CLabel className="text-nowrap">E-mail</CLabel>
          <Select
            value={email}
            options={optionsEmail}
            onChange={(e) => setEmail(e)}
            placeholder={"Selecione"}
          />
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol>
          <CLabel className="text-nowrap">Parcelas</CLabel>
          <CInput
            value={nrParcelas}
            onChange={(e) => setNrParcelas(e.target.value)}
          />
        </CCol>
        <CCol>
          <CLabel className="text-nowrap">Valor</CLabel>
          <CInput
            value={formatCurrency(valor ?? 0, false)}
            onChange={(e) => setValor(e.target.value)}
          />
        </CCol>
        <CCol>
          <CLabel className="text-nowrap">Valor Mínimo</CLabel>
          <CInput
            value={formatCurrency(valorMinimo ?? 0, false)}
            // onChange={(e) => setValorMinimo(e.target.value)}
          />
        </CCol>
        <CCol>
          <CLabel className="text-nowrap">Vencimento</CLabel>
          <br />

          <ReactDatePicker
            className="form-control"
            selected={new Date(vencimento)}
            onChange={(e) => setVencimento(e.toJSON())}
            dateFormat="dd/MM/yyyy"
            onKeyDown={(e) => e.preventDefault()}
          />
        </CCol>
      </CRow>
      <CRow>
        <CCol className="d-flex align-items-center">
          <CButton
            color="info"
            onClick={getCalculo}
            className="mb-2"
            block
            disabled={
              selectedContrato === null ||
              selectedContrato === undefined ||
              selectedContrato === "" ||
              isLoading
            }
          >
            Visualizar
          </CButton>
        </CCol>
      </CRow>

      <CCard>
        {" "}
        {isLoading ? (
          <CardLoading />
        ) : (
          <CCardBody>
            {selectedContrato !== null &&
            selectedContrato !== undefined &&
            selectedContrato !== "" &&
            dataFields ? (
              <div ref={textData}>
                <div>
                  <strong>Negociador:</strong> {dataFields.negociador}
                </div>
                <div>
                  <strong>Ativo:</strong> {dataFields.ativo}
                </div>
                <div>
                  <strong>E-mail:</strong> {dataFields.email}
                </div>
                <div>
                  {/* <strong>Parcelas: </strong> {dataFields.parcelas} */}
                  {numerosParcelasPorContrato && (
                    <div>
                      <strong>Parcelas por Contrato: </strong>
                      {numerosParcelasPorContrato.map((item) => (
                        <div key={item.contrato_numero}>
                          <strong className="ml-4">
                            {item.contrato_numero}:
                          </strong>{" "}
                          {item.numerosParcelas}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <div>
                  <strong>Valor: </strong> {dataFields.valor}
                </div>
                <div>
                  <strong>Valor Mínimo: </strong> {dataFields.valorMinimo}
                </div>
                <div>
                  <strong>Vencimento:</strong> {dataFields.vencimento}
                </div>
              </div>
            ) : (
              <></>
            )}
          </CCardBody>
        )}
        <CCardFooter>
          <CRow>
            <CCol md="6">
              <CLabel className={"mt-2"}>Selecione a ocorrência:</CLabel>
              <Select
                value={selectedOcorrencia}
                options={optionsOcorrencia}
                onChange={handleOcorrenciaChange}
                placeholder={"Selecione"}
              />
            </CCol>
            <CCol md="6">
              <CLabel className={"mt-2"}>Selecione o telefone:</CLabel>
              <Select
                value={selectedPhone}
                options={optionsPhone}
                onChange={handlePhoneChange}
                placeholder={"Selecione"}
              />
            </CCol>
          </CRow>

          <CRow className="mt-4 text-center">
            <CCol>
              <CButton
                className={"mr-2"}
                color="success"
                onClick={handleSaveOccurrence}
              >
                Adicionar à ocorrência
              </CButton>
              <CButton
                className={"mr-2"}
                color="info"
                onClick={() => setShowSendEmailModal(true)}
              >
                Enviar E-mail
              </CButton>

              <CButton
                color="secondary"
                className="mr-2"
                onClick={handleCloseClick}
                disabled={isLoading}
              >
                Fechar
              </CButton>
            </CCol>
          </CRow>
        </CCardFooter>
      </CCard>
      {showSendEmailModal && (
        <SendEmailModal
          show={showSendEmailModal}
          handleClose={() => setShowSendEmailModal(false)}
          msg={textData.current.innerText}
          em={email?.value}
        />
      )}
    </div>
  );
};

export default RegularizacaoAVista;
