/* eslint-disable react-hooks/exhaustive-deps */
import React, { createContext, useContext, useEffect, useState } from "react";
import { liberarNegociacao } from "../../../../containers/AprovacaoJuridica";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";

import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { useMyContext } from "src/reusable/DataContext";
import { BBCAcordo, BBCConsultaSaldo } from "src/types/commonBBC";

export interface BBCSimulacao {
  simulationId: string;
  dataBase: string;
  data1Vcto: string;
  dataUltVcto: string;
  qtdeParcelas: number;
  valorFinanciado: number;
  fluxoParcelas: {
    nrParcela: number;
    vencimento: string;
    valorParcela: number;
    valorDespesa: number;
    valorTarifa: number;
  }[];
  valorParcela: number;
  valorPrincipal: number;
  valorBruto: number;
  valorLiquido: number;
  taxaClienteMes: number;
  taxaClienteAno: number;
  taxaApMes: number;
  taxaApAno: number;
  taxaCetMes: number;
  taxaCetAno: number;
  taxaNominalMes: number;
  taxaNominalAno: number;
}
interface ContratoNegociar {
  parcelas: object;
  parcelasSelecionadas: BBCConsultaSaldo[];
  dadosBBC: {
    iof: string;
    mora: string;
    multa: string;
    pmt: string;
    saldoCurva: string;
    valor: string;
    simulacao: BBCSimulacao;
    acordo: BBCAcordo;
  };
  elegivel: {
    minAtraso: number;
    maxAtraso: number;
    parcelaInicial: number;
    parcelaFinal: number;
    percPrincipalComJuros: number;
    percMulta: number;
    percMora: number;
    percDesp: number;
    percTLA: number;
    descIOF: boolean;
  };
}
export interface FinanciadoData {
  id_Contrato: number;
  numero_Contrato: string;
  contrato_Aberto: boolean;
  id_Financiado: number;
  id_Agrupamento: number;
  nome: string;
  cpfCnpj: string;
  dt_Nascimento: null;
  data_Enriquecimento: null;
  sexo: "M" | "F";
  est_Civil: string;
  score_Serasa: number;
  tipo_Financiado: number;
  rg: string;
  dt_Emiss_Rg: string;
  orgao_Emiss_Rg: string;
  uf_Emiss_Rg: string;
  tipo_Pessoa: string;
  conjugue: string;
  mae: string;
  pai: string;
  id_Cliente: number;
  id_Grupo: number;
  cliente: string;
  grupo: string;
  id_Fase: number;
  cod_Fase: string;
  fase: string;
  cor: string;
  status: string;
  vl_contr: number;
  coddatacob: string;
}

interface BBCState {
  loading: boolean;
  setLoading: Function;
  loadingAction: string;
  setLoadingAction: Function;
  MsgAviso: string;
  setMsgAviso: Function;
  TitleAviso: string;
  setTitleAviso: Function;
  contratoNegociar: ContratoNegociar;
  setContratoNegociar: Function;
  financiadoData: FinanciadoData;
  comunicacaoState: string;
  setComunicacaoState: Function;

  produtos: {
    [index: number]: { product: number; label: string; taxa: number };
  }[];
  setProdutos: Function;

  produtoSelecionado: { product: number; label: string; taxa: number };
  setProdutoSelecionado: Function;

  alertModalShow: boolean;
  closeAlertModal: Function;
  alertModalMessage: string;
  setAlertModalMessage: Function;

  resetaSimulacao: Function;

  installmentParams: installmentParamsType[];
  discountParams: discountParamsType[];
}

export type optionsProducts = {
  [index: number]: { label: string; product: number };
};

export type simulateInstallmentPayload = {
  nrParcela: number;
  valorDespesa: number;
  valorTarifa: number;
};

export type simulatePayload = {
  idContrato: number;
  contract: string;
  product: number;
  taxa: number;
  parcelas: simulateInstallmentPayload[];
  parcelasContrato: number[];
  dtVenc: string;
  valor: number;
  valorDespesaReembolsavel: number;
  valorTarifaHonorario: number;
};

export type installmentParamsType = {
  id: string;
  maxInstallments: number;
  max: number;
  min: number;
};

export type discountParamsType = {
  id: string;
  dayFrom: number;
  dayTo: number;
  updatePerc: number;
  acquittancePerc: number;
};

const BBCContext = createContext<BBCState>(null);

export function ContextProviderBBC({ children }) {
  const history = useHistory();
  const context = useMyContext();

  const [alertModalShow, setAlertModalShow] = useState(false);

  const [alertModalMessage, setAlertModalMessage] = useState("");

  const [produtos, setProdutos] = useState(
    window.sessionStorage.getItem("BBCProducts")
      ? JSON.parse(window.sessionStorage.getItem("BBCProducts"))
      : []
  );

  const [produtoSelecionado, setProdutoSelecionado] = useState(
    window.sessionStorage.getItem("BBCProducts")
      ? JSON.parse(window.sessionStorage.getItem("BBCProducts"))[0]
      : null
  );

  const [installmentParams, setInstallmentParams] = useState<
    installmentParamsType[]
  >(
    window.sessionStorage.getItem("BBCInstallmentParams")
      ? JSON.parse(window.sessionStorage.getItem("BBCInstallmentParams"))
      : null
  );

  const [discountParams, setDiscountParams] = useState<discountParamsType[]>(
    window.sessionStorage.getItem("BBCDiscountParams")
      ? JSON.parse(window.sessionStorage.getItem("BBCDiscountParams"))
      : null
  );

  /* Vindos da antiga tela de negociação */
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");
  const [MsgAviso, setMsgAviso] = useState("");
  const [TitleAviso, setTitleAviso] = useState("");
  const simulacaoInicial = {
    simulationId: "",
    dataBase: "",
    data1Vcto: "",
    dataUltVcto: "",
    qtdeParcelas: 0,
    valorFinanciado: 0,
    fluxoParcelas: [],
    valorParcela: 0,
    valorPrincipal: 0,
    valorBruto: 0,
    valorLiquido: 0,
    taxaClienteMes: 0,
    taxaClienteAno: 0,
    taxaApMes: 0,
    taxaApAno: 0,
    taxaCetMes: 0,
    taxaCetAno: 0,
    taxaNominalMes: 0,
    taxaNominalAno: 0,
  };

  const dadosAcordoInicial = {
    situacaoProposta: "",
    atividade: 0,
    numeroAcordo: "",
    numeroOperacao: "",
    idAcordo: "",
  };

  const [contratoNegociar, setContratoNegociar] = useState({
    parcelas: {},
    parcelasSelecionadas: [],
    dadosBBC: {
      iof: "",
      mora: "",
      multa: "",
      pmt: "",
      saldoCurva: "",
      valor: "",
      simulacao: simulacaoInicial,
      acordo: dadosAcordoInicial,
    },
    elegivel: {
      minAtraso: 0,
      maxAtraso: 0,
      parcelaInicial: 0,
      parcelaFinal: 0,
      percPrincipalComJuros: 0,
      percMulta: 0,
      percMora: 0,
      percDesp: 0,
      percTLA: 0,
      descIOF: false,
    },
  });
  const financiadoData: FinanciadoData = context.data;
  const [comunicacaoState, setComunicacaoState] = useState("");

  const resetaSimulacao = () => {
    const temp = { ...contratoNegociar };
    temp.dadosBBC.simulacao = simulacaoInicial;
    setContratoNegociar(temp);
  };

  const closeAlertModal = () => {
    setAlertModalShow(false);
    setAlertModalMessage("");
  };

  useEffect(() => {
    if (alertModalMessage.length > 0) {
      setAlertModalShow(true);
    }
  }, [alertModalMessage]);

  /* Vindos da antiga tela de negociação */

  const getProducts = async () => {
    const payload = null;
    const endpoint = "getBBCProducts";

    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          `/${payload ?? ""}`
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const getInstallmentParams = async () => {
    const endpoint = "restBBCInstallmentParam";

    const response = await GET_DATA(getURI(endpoint), null, true, true);
    if (response.length > 0) {
      setInstallmentParams(response);
      window.sessionStorage.setItem(
        "BBCInstallmentParams",
        JSON.stringify(response)
      );
    }
    return response;
  };

  const getDiscountParams = async () => {
    const endpoint = "restBBCDiscountParam";

    const response = await GET_DATA(getURI(endpoint), null, true, true);
    if (response.length > 0) {
      setDiscountParams(response);
      window.sessionStorage.setItem(
        "BBCDiscountParams",
        JSON.stringify(response)
      );
    }
    return response;
  };

  useEffect(() => {
    const validaAsync = async () => {
      if (!(await liberarNegociacao(context.data))) {
        history.push("/telaprincipal");
      }
    };

    // Verificar a possibilidade depois que carregar tudo e executar a função abaixo
    validaAsync();

    const products = async () => {
      const products: optionsProducts[] =
        (await getProducts()) as optionsProducts[];
      if (products) {
        setProdutos(products);
        setProdutoSelecionado(products[0]);
        window.sessionStorage.setItem("BBCProducts", JSON.stringify(products));
      }
    };

    if (window.sessionStorage.getItem("BBCProducts") === null) {
      products();
    }
    if (window.sessionStorage.getItem("BBCInstallmentParams") === null) {
      getInstallmentParams();
    }
    if (window.sessionStorage.getItem("BBCDiscountParams") === null) {
      getDiscountParams();
    }
  }, []);

  return (
    <BBCContext.Provider
      value={{
        /* Vindos da antiga tela de negociação */
        loading,
        setLoading,
        loadingAction,
        setLoadingAction,
        MsgAviso,
        setMsgAviso,
        TitleAviso,
        setTitleAviso,
        contratoNegociar,
        setContratoNegociar,
        financiadoData,
        comunicacaoState,
        setComunicacaoState,
        /* Vindos da antiga tela de negociação */

        produtos,
        setProdutos,
        produtoSelecionado,
        setProdutoSelecionado,
        alertModalShow,
        closeAlertModal,
        alertModalMessage,
        setAlertModalMessage,
        resetaSimulacao,

        installmentParams,
        discountParams,
      }}
    >
      {children}
    </BBCContext.Provider>
  );
}

export const useBBCContext = () => {
  const context = useContext(BBCContext);
  if (!context) {
    throw new Error("useBBCContext must be used within a ContextProvider");
  }
  return context;
};
