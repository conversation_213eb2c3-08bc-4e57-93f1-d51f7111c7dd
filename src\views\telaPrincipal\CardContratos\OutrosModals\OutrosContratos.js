import {
  CButton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
} from "@coreui/react";

const OutrosContratos = ({ isOpen, onClose, otherContract = [] }) => {
  return (
    <CModal show={isOpen} size="lg" onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Contratos</CModalHeader>
      <CModalBody>
        <table class="table">
          <thead>
            <tr>
              <th scope="col">Nr. Contrato</th>
              <th scope="col">Carteira</th>
              <th scope="col">Status</th>
            </tr>
          </thead>
          <tbody>
            {otherContract.map((item, index) => (
              <tr>
                <td>{item?.nrContrato}</td>
                <td>{item?.grupo}</td>
                <td>{item?.ativo ? "Ativo" : "Inativo"}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default OutrosContratos;
