import React, { useState, useEffect } from "react";
import { <PERSON>ard<PERSON><PERSON>er, <PERSON>utton, CCardBody, CCol, CRow, CBadge } from "@coreui/react";
import Select from "react-select";
import { POST_DATA } from "src/api";
import { formatDate } from "src/reusable/helpers";
import { acionarLigacao, acionarPauseLigacao } from "src/config/telephonyFunctions";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { GET_ClientData } from "src/reusable/functions";
import TableSelectItens from "src/reusable/TableSelectItens";
import CardLoading from "src/reusable/CardLoading";
import { getDadosFinanciado, getTiposTelefone } from "src/reusable/functions";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";
import { useMyContext } from "src/reusable/DataContext";
import AdicionarTelefone from "./CardContratos/OutrosModals/AdicionarTelefone";
import EditarTelefone from "./CardContratos/OutrosModals/EditarTelefone";
import { useAuth } from "src/auth/AuthContext";

const CardTelefone = ({ atualizarCard = "", onHandleSave }) => {

  const { checkPermission, inforPermissions } = useAuth();
  let financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const [phoneData, setPhoneData] = useState([]);
  const [filteredData, setFilteredeData] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState("");
  const [selectedNumber, setSelectedNumber] = useState("");
  const [numberOptions, setNumberOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingConfirmar, setIsLoadingConfirmar] = useState(false);
  const [disableButtonCall, setDisableButtonCall] = useState(false);

  const { message } = useWebsocketTelefoniaContext();
  const { appSettings } = useMyContext();

  const [showModalAdicionar, setShowModalAdicionar] = useState(false);
  const [showModalEditar, setShowModalEditar] = useState(false);
  const [isBlacklist, setIsBlacklist] = useState(false);

  const [tel, setTel] = useState(null);
  const [tipoTelefone, setTipoTelefone] = useState([]);

  const handleCloseEditModal = () => {
    setShowModalEditar(false);
  };

  const handleChange = (selecao) => {
    setTel(selecao);
    setIsBlacklist(selecao.status === -1);
  };

  const handleAdicionar = async (newData) => {
    const postSuccess = await dataPost(newData);
    if (postSuccess.success) {
      const updatedData = await getDadosFinanciado(
        financiadoData.id_Financiado, financiadoData.numero_Contrato
      );
      localStorage.setItem("clientData", JSON.stringify(updatedData));
      setTel(newData);
      setIsBlacklist(newData.status === -1);
      if (onHandleSave) onHandleSave();
      toast.success("Dados do financiado alterados com sucesso!");
    } else {
      toast.warning(postSuccess.message);
    }
  };

  const dataPost = async (newData) => {
    const data = {
      cpfCnpj: financiadoData.cpfCnpj,
      idGrupo: financiadoData.id_Grupo,
      nome: financiadoData.nome,
      telefones: [{ ...newData }],
    };
    const result = await POST_DATA("Datacob/DadosCadastrais/Financiado", data);
    return result;
  };

  const permissao = {
    modulo: "Tela Principal",
    submodulo: "Telefone",
  };

  const tableColumns = [
    {
      key: "ordenacao",
      label: " ",
      defaultSortColumn: true,
      defaultSort: "ascending",
      className: "text-white",
      style: { display: "none" },
      cellStyleCondicional: (_) => ({ display: "none" }),
    },
    {
      key: "status",
      label: "Status",
      formatterByObject: (item) => renderStatusBadge(item),
    },
    {
      key: "id_Telefone",
      label: "Telefone",
      className: "nowrap-cell",
      formatterByObject: (item) => renderTelefone(item),
    },
    {
      key: "action",
      label: "Discar",
      formatterByObject: (item) => renderActionButton(item),
    },
    { key: "descricao", label: "Observação", className: "nowrap-cell" },
    {
      key: "dt_Ultima_Ligacao",
      label: "Ultima Ligação",
      className: "nowrap-cell",
      formatter: (item) => (item !== null ? formatDate(item) : null),
    },
    {
      key: "alt_status",
      label: "Alterar Status",
      formatterByObject: (item) => renderActionStatus(item),
    },
  ];

  const IconButton = ({ icon, color, titulo, onClick, disabled = false }) => {
    return (
      <CButton
        title={titulo}
        className="mr-1"
        style={{
          border: "solid 1px",
          borderColor: color,
          color: color,
          padding: "2px 4px",
        }}
        onClick={onClick}
        disabled={disabled}
      >
        <i className={icon} />
      </CButton>

    );
  };

  async function ligacaoManual(item) {
    let callData = message;

    //Checar se está conectado na Telefonia e Idle
    // if (callData && (callData.status === "Idle" || callData.status === "Pause")) { //É para poder ligar Pausado?
    setDisableButtonCall(true);
    if (callData && callData.status === "Idle") {
      setIsLoadingConfirmar(true);
      localStorage.removeItem("pausaId");
      const response = await acionarLigacao(item.ddd, item.fone, message);
      await handleCallResponse(callData, response);
    } else if (callData && callData.status === "Pause") {
      setIsLoadingConfirmar(true);
      const response = await acionarPauseLigacao(item.ddd, item.fone, message);
      await handleCallResponse(callData, response);
    } else {
      if (
        callData &&
        (callData.status === "Talking" ||
          callData.status === "TalkingWithPause" ||
          callData.status === "TalkingWithEnding" ||
          callData.status === "TalkingWithManualCall" ||
          callData.status === "PersonalCall" ||
          callData.status === "ManualCall")
      ) {
        toast.warning("Já existe uma chamada em andamento.");
      } else if (callData && callData.status === "Error") {
        toast.warning(callData.message);
      } else
        toast.warning(
          "Por favor, verifique se está conectado à telefonia e com o status Livre."
        );
    }
  }

  async function handleCallResponse(callData, response) {
    while (callData.status === "Idle") {
      await new Promise((resolve) => setTimeout(resolve, 500));
      // callData = JSON.parse(localStorage.getItem("callData"));
    }
    // await new Promise((resolve) => setTimeout(resolve, 1500));
    setIsLoadingConfirmar(false);
    switch (response) {
      case 11:
        toast.warning("Usuário não está conectado à telefonia.");
        break;
      case 12:
        toast.warning(
          "Por favor, faça conexão com a telefonia antes de realizar chamadas."
        );
        break;
      case 13:
        toast.warning("Problemas realizando a chamada.");
        break;
      default:
        break;
    }
  }

  const renderActionButton = (item) => (
    <div style={{ display: "flex" }}>
      <IconButton
        icon={"cil-phone"}
        color={"blue"}
        title={"Realizar Ligação"}
        onClick={() => ligacaoManual(item)}
        disabled={(item.status === -1) || (disableButtonCall && message?.status !== "Idle") || (message && message?.status !== "Idle" && message?.status !== "Pause")}
      />
    </div>
  );

  const handleChangeStatus = async (item, status_Tel) => {
    const trimmedFone = item.fone.trim();
    const data = {
      cpfCnpj: financiadoData.cpfCnpj,
      idGrupo: financiadoData.id_Grupo,
      nome: financiadoData.nome,
      telefones: [
        {
          ddd: item.ddd,
          fone: trimmedFone,
          tipoTelefone: item.id_Tipo_Telefone,
          ramal: item.ramal,
          descricao: item.descricao,
          contato: item.contato,
          status: status_Tel,
          isHotNumber: item.isHotNumber,
          isWhatsApp: item.isWhatsApp,
        },
      ],
    };

    const postSuccess = await telefonePost(data);
    if (postSuccess.success) {
      const updateTelefone = await getDadosFinanciado(
        financiadoData.id_Financiado, financiadoData.numero_Contrato
      );
      localStorage.setItem("clientData", JSON.stringify(updateTelefone));
      const clientPhones = updateTelefone.telefones;
      setPhoneData(clientPhones);
      const sortPhones = ordenacaoTelefones(updateTelefone.telefones);
      setFilteredeData(sortPhones);
      if (postSuccess.data !== null && postSuccess.data.success === true) {
        toast.info("Status do telefone alterado com sucesso");
      } else {
        postSuccess?.data?.resultados.forEach((element) => {
          toast.error(element);
        });
      }
    } else {
      console.log("Erro no POST");
    }
  };

  const telefonePost = async (data) => {
    const result = await POST_DATA("Datacob/DadosCadastrais/Financiado", data);
    return result;
  };

  const handleCloseAddModal = () => {
    setShowModalAdicionar(false);
  };

  // Handle status filter change
  const handleStatusFilterChange = (selectedOption) => {
    setSelectedStatus(selectedOption.value);
  };

  // Handle number filter change
  const handleNumberFilterChange = (selectedOption) => {
    setSelectedNumber(selectedOption.value);
  };

  // Options for the status filter select
  const statusOptions = [
    { value: "", label: "Status" },
    { value: 1, label: "Ativo" },
    { value: 0, label: "Inativo" },
    { value: 2, label: "Efetivo" },
    { value: 3, label: "Pesquisado" },
    { value: -1, label: "Blacklist" },
  ];

  const ordenacaoTelefones = (items) => {
    const clientPhonesSortData = items?.map((item) => {
      item.ordenacao = ordenacaoStatusBadge(item);
      return item;
    });
    const sortPhones = clientPhonesSortData?.sort(function (a, b) {
      // Primeiro, ordene pela propriedade ordenacao em ordem crescente
      if (a.ordenacao < b.ordenacao) return -1;
      if (a.ordenacao > b.ordenacao) return 1;

      // Se a ordenacao for igual, ordene pela propriedade dtInclusao em ordem decrescente
      var dataA = new Date(b.dtInclusao);
      var dataB = new Date(a.dtInclusao);

      if (dataA < dataB) return -1;
      if (dataA > dataB) return 1;

      return 0;
    });
    return sortPhones;
  };

  const fetchDataTipoTelefone = async () => {
    try {
      await Promise.all([handleTipoTelefone()]);
    } catch (error) {
      console.log(error);
    }
  };

  const handleTipoTelefone = async () => {
    const res = await getTiposTelefone();
    setTipoTelefone(res);
  };

  async function fetchData() {
    setIsLoading(true);
    financiadoData = localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null;
    const clientData = await GET_ClientData(financiadoData);
    const clientPhones = clientData?.telefones;
    setPhoneData(clientPhones);

    const sortPhones = ordenacaoTelefones(clientPhones);

    setFilteredeData(sortPhones);

    const uniqueNumbers = [
      ...new Set(clientPhones?.map((item) => item.ddd + item.fone)),
    ];
    const options = [
      { value: "", label: "Telefone" }, // "All" option
      ...uniqueNumbers.map((Telefone) => ({
        value: Telefone,
        label: Telefone,
      })),
    ];

    await fetchDataTipoTelefone();
    setNumberOptions(options);
    setIsLoading(false);
  }

  useEffect(async () => {
    if (financiadoData || !isLoading) {
      if ((phoneData?.length ?? 0) == 0 || !isLoading)
        await fetchData();

      atualizarCard = "";
    }
  }, [atualizarCard]);

  useEffect(() => {
    if (phoneData) {
      const filteredDataVar = phoneData.filter((item) => {
        // const matchesStatus = !selectedStatus || item.status === selectedStatus;
        let matchesStatus = item.status === selectedStatus;
        //Gambiarra pra resolver status inativado as vezes sendo 0 e as vezes sendo 3
        if (selectedStatus === "") {
          matchesStatus = true;
        }
        const matchesNumber =
          !selectedNumber ||
          item.ddd + item.fone === selectedNumber ||
          (selectedNumber === "All" && item.ddd + item.fone);
        return matchesStatus && matchesNumber;
      });
      setFilteredeData(filteredDataVar);
    }
  }, [phoneData, selectedStatus, selectedNumber]);

  const renderStatusBadge = (item) => {
    return item.status === 1 ? (
      <CBadge color={"success"}> Ativo </CBadge>
    ) : item.status === 2 ? (
      <CBadge color={"info"}> Efetivo </CBadge>
    ) : item.status === 3 ? (
      <CBadge color={"primary"}> Pesquisado </CBadge>
    ) : item.status === -1 ? (
      <CBadge color={"dark"}> Blacklist </CBadge>
    ) : (
      <CBadge color={"danger"}> Inativo </CBadge>
    );
  };

  const ordenacaoStatusBadge = (item) => {
    return item.status === 1
      ? 2
      : item.status === 2
        ? 1
        : item.status === 3
          ? 3
          : item.status === -1
            ? 5
            : 4;
  };

  const renderActionStatus = (item) => {
    return (
      <div style={{ display: "flex" }}>
        <IconButton
          icon={"cil-warning"}
          color={"blue"}
          titulo={"Efetivar"}
          onClick={() => handleChangeStatus(item, 2)}
          disabled={item.status === -1}
        />
        <IconButton
          icon={"cil-warning"}
          color={"red"}
          titulo={"Inativar"}
          onClick={() => handleChangeStatus(item, 0)}
          disabled={item.status === -1}
        />
        <IconButton
          icon={"cil-search"}
          color={"dark"}
          titulo={"Pesquisado"}
          onClick={() => handleChangeStatus(item, 3)}
          disabled={item.status === -1}
        />
        <IconButton
          icon={"cil-check"}
          color={"green"}
          titulo={"Ativar"}
          onClick={() => handleChangeStatus(item, 1)}
          disabled={item.status === -1}
        />
        <IconButton
          icon={"cil-pencil"}
          color="secondary"
          onClick={() => { handleChange(item); setShowModalEditar(true); }}
          size="sm"
          className="mr-2"
          title={!financiadoData ? "Nenhum financiado Selecionado" : inforPermissions(permissao).edit}
          disabled={!financiadoData || !checkPermission(permissao.modulo, "Edit", permissao.submodulo) || isBlacklist}
        />
      </div>
    );
  };

  const renderTelefone = (item) => {
    return item.ddd + item.fone;
  };

  return (
    <>
      <CCardHeader style={{ display: "flex", alignItems: "center" }}>
        <i className="cil-envelope-open mr-2" />
        Cadastro - Telefone
        <div className="mr-10" style={{ marginLeft: "auto" }}>
          <CButton
            className="btn-custom-outline-green"
            icon="cil-plus"
            color={"green"}
            titulo={"Cadastrar Novo Telefone"}
            onClick={() => setShowModalAdicionar(true)}
            size="sm"
            title={!financiadoData ? "Nenhum financiado Selecionado" : inforPermissions(permissao).create}
            disabled={!financiadoData || !checkPermission(permissao.modulo, "Create", permissao.submodulo)}
          ><i className="cil-plus"></i>Cadastrar Novo Telefone</CButton>
          {showModalAdicionar && (
            <AdicionarTelefone
              onClose={handleCloseAddModal}
              isOpen={showModalAdicionar}
              onSave={handleAdicionar}
              tipoTelefone={tipoTelefone}
              inserirTelefone={appSettings?.telefonia?.inserirTelefone}
            />
          )}
        </div>
      </CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <CRow style={{ padding: "5px" }}>
              <CCol md="4">
                <Select
                  options={statusOptions}
                  value={statusOptions.find(
                    (option) => option.value === selectedStatus
                  )}
                  onChange={handleStatusFilterChange}
                />
              </CCol>
              <CCol md="4">
                <Select
                  className="flex-grow-1"
                  options={numberOptions}
                  value={numberOptions.find(
                    (option) => option.value === selectedNumber
                  )}
                  onChange={handleNumberFilterChange}
                />
              </CCol>
              <CCol className="d-flex justify-content-end" md="4"></CCol>
            </CRow>
          </CCol>
        </CRow>
        {filteredData && filteredData?.length > 0 ? (
          <>
            <CRow>
              <CCol>
                {isLoading ? (
                  <CardLoading msg={"Atualizando..."} />
                ) : (
                  <TableSelectItens
                    data={filteredData}
                    columns={tableColumns}
                    onSelectionChange={(_) => { }}
                    defaultSelectedKeys={[]}
                    selectable={false}
                    heightParam="290px"
                  />
                )}
                {showModalEditar && (
                  <EditarTelefone
                    onClose={handleCloseEditModal}
                    isOpen={showModalEditar}
                    editData={tel}
                    onEdit={handleAdicionar}
                    tipoTelefone={tipoTelefone}
                  />
                )}
              </CCol>
            </CRow>
          </>
        ) : (
          <NaoHaDadosTables />
        )}
      </CCardBody>
    </>
  );
};

export default CardTelefone;
