/* NegociacaoBar.module.scss */
.sliderContainer {
  position: relative;
  width: 100%;
  padding: 54px 0; /* // Espaço para os balões de valor */
  border-radius: 10px;
  height: 130px;
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  height: 22px; /* // Aumenta a altura da barra do slider */
  background: #00800000;
  outline: none;
  border-radius: 10px;
  position: absolute;
  z-index: 4;
}

.filledBefore {
  position: absolute;
  background: linear-gradient(to right, #ff0000, #f9b5b5);
  border-radius: 10px 0px 20px 10px;
  z-index: 2;
  height: 22px;
}

.filledFull {
  position: absolute;
  background: linear-gradient(to right, #f3ffed, #65df1f); /* Degradê verde */
  border-radius: 10px;
  z-index: 1;
  height: 22px; /* // Aumenta a altura da barra do slider */
}


/* Estilização do Thumb (parte que desliza) */
.slider::-webkit-slider-thumb {
  -webkit-appearance: none; /* Remover a aparência padrão */
  appearance: none;
  width: 30px; /* Largura do Thumb */
  height: 30px; /* Altura do Thumb */
  background: #2b2b2ba9; /* Cor do Thumb para Firefox */
  cursor: pointer; /* Tipo de cursor ao passar o mouse */
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%); /* Forma de triângulo */
  margin-top: 25px; /* Ajustar para posicionar abaixo da barra do slider */
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.6); /* Sombra para efeito de profundidade */
}

.slider::-moz-range-thumb {
  width: 30px; /* Largura do Thumb para Firefox */
  height: 30px; /* Altura do Thumb para Firefox */
  background: #2b2b2ba9; /* Cor do Thumb para Firefox */
  cursor: pointer; /* Tipo de cursor para Firefox */
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%); /* Forma de triângulo */
  margin-top: 25px; /* Ajustar para posicionar abaixo da barra do slider */
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.6); /* Sombra para efeito de profundidade */
}

.valueBubble {
  position: absolute;
  top: 0px;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  background-color: white;
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  text-align: center;
  white-space: nowrap;
  font-size: 16px;
  z-index: 4;

  /* Adiciona um pseudo-elemento para criar o triângulo na parte de baixo */
  &:after {
    content: '';
    position: absolute;
    bottom: -10px; /* Posiciona abaixo do valueBubble */
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #969696; /* A cor do triângulo deve combinar com o fundo do valueBubble */
    z-index: 5;
  }
}

.maxValueBubble {
  position: absolute;
  bottom: 0px;
  background: linear-gradient(to left, #f3ffed, #65df1f);
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  text-align: center;
  white-space: nowrap;
  font-size: 16px;
  z-index: 4;
  width: 150px;
  color: black;
}
