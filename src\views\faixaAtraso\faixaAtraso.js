import React, { useState, useEffect } from "react";
import {
  CButton,
  CForm,
  CFormGroup,
  CInput,
  CDataTable,
  CCard,
  CCardBody,
} from "@coreui/react";
import { GET_DATA, POST_DATA, PUT_DATA, DELETE_DATA } from "src/api";
import ConfirmModal from "src/reusable/ConfirmModal";
import TableSelectItens from "src/reusable/TableSelectItens";
import { useAuth } from 'src/auth/AuthContext';

const FaixaAtraso = () => {
  const { checkPermission,inforPermissions } = useAuth();
  const permissao = {
    modulo: "Faixas de Atraso",
    submodulo: null,
  }
  
  const [data, setData] = useState([]);
  const [fromValue, setFromValue] = useState("");
  const [toValue, setToValue] = useState("");
  const [numberValue, setNumberValue] = useState("");
  const [selectedItem, setSelectedItem] = useState(null);

  const [showConfirmModal, setShowConfirmModal] = useState(false);

  async function getFaixaCalculo() {
    const faixaCalculo = await GET_DATA("Simulacoes/FaixaAtraso");
    if (faixaCalculo) {
      setData(faixaCalculo);
    }
    return;
  }

  function validate() {
    const isValidFrom = /^[0-9]+$/.test(fromValue);
    const isValidTo = /^[0-9]+$/.test(toValue);
    const isValidPercentual = /^[0-9]+(\.[0-9]+)?$/.test(numberValue);

    if (!isValidFrom || !isValidTo || !isValidPercentual) {
      alert("Por favor, insira números válidos para os dias e percentual.");
      return false;
    }
    const daysFrom = parseInt(fromValue);
    const daysTo = parseInt(toValue);
    const percentual = parseFloat(numberValue);

    if (daysFrom >= daysTo) {
      alert(
        'Para criar uma faixa de dias válida, "dias de" deve ser menor que "até".'
      );
      return false;
    }

    if (percentual > 100) {
      alert("Valor percentual não pode exceder 100.");
      return false;
    }
    return true;
  }

  const handleAdd = async () => {
    const validFields = validate();
    if (validFields) {
      const data = {
        daysFrom: fromValue,
        daysTo: toValue,
        percentual: numberValue,
      };
      const postSuccess = await POST_DATA("Simulacoes/FaixaAtraso", data);
      
      if (postSuccess.success) {
        await getFaixaCalculo();
        clearInputs();
      } else {
        alert(postSuccess.message);
      }
    }
  };

  const handleEdit = (item) => {
    const element = data.find((el) => el.id === item.id);
    setSelectedItem(element);
    setFromValue(element.daysFrom);
    setToValue(element.daysTo);
    setNumberValue(element.percentual);
  };

  const handleUpdate = async () => {
    const validFields = validate();
    if (validFields) {
      const data = {
        id: selectedItem.id,
        daysFrom: fromValue,
        daysTo: toValue,
        percentual: numberValue,
      };
      const updateSuccess = await PUT_DATA("Simulacoes/FaixaAtraso", data);
      if (updateSuccess.success) {
        await getFaixaCalculo();
        clearInputs();
      } else {
        alert(updateSuccess.message);
      }
    }
  };

  const faixaDelete = async (item) => {
    setSelectedItem(item);
    setShowConfirmModal(true);
  };

  const handleModalClose = (confirmation) => {
    setShowConfirmModal(false);
    handleDelete(confirmation);
  };

  const handleDelete = async (confirmation) => {
    if (confirmation) {
      const data = { id: selectedItem.id };
      const deleteSuccess = await DELETE_DATA(`Simulacoes/FaixaAtraso`, data);
      if (deleteSuccess.success) {
        await getFaixaCalculo();
        clearInputs();
      } else {
        alert(deleteSuccess.message);
      }
    }
  };

  const clearInputs = () => {
    setFromValue("");
    setToValue("");
    setNumberValue("");
    setSelectedItem(null);
  };

  const columns = [
    {
      key: "daysFrom",
      label: "Dias de",
    },
    {
      key: "daysTo",
      label: "até",
    },
    {
      key: "percentual",
      label: "Percentual",
    },
    {
      key: "actions",
      label: "Ações",
      formatterByObject: (item) => renderActionButton(item),
    },
  ];

  const renderActionButton = (item) => (
    <div style={{ whiteSpace: "nowrap" }}>
      <CButton
        color="info"
        onClick={() => handleEdit(item)}
        className="mr-2"
        title={inforPermissions(permissao).edit}
        disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
      >
        <i className="cil-pencil" />
      </CButton>
      <CButton
        color="danger"
        onClick={() => faixaDelete(item)}
        title={inforPermissions(permissao).delete}
        disabled={!checkPermission(permissao.modulo, "Delete", permissao.submodulo)}
      >
        <i className="cil-trash" />
      </CButton>
    </div>
  );

  useEffect(() => {
    getFaixaCalculo();
  }, []);

  return (
    <div>
      <h3>Faixa de Atraso</h3>
      <p style={{ color: "gray" }}>
        Definir as faixas de atrasos de negociações e simulações de cálculos.
      </p>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard style={{ width: "60%" }}>
          <CCardBody>
            <CForm>
              <CFormGroup>
                <CInput
                  type="text"
                  placeholder="De"
                  value={fromValue}
                  onChange={(e) =>
                    setFromValue(e.target.value.replace(/[^0-9]/g, ""))
                  }
                />
              </CFormGroup>
              <CFormGroup>
                <CInput
                  type="text"
                  placeholder="Até"
                  value={toValue}
                  onChange={(e) =>
                    setToValue(e.target.value.replace(/[^0-9]/g, ""))
                  }
                />
              </CFormGroup>
              <CFormGroup>
                <CInput
                  type="text"
                  placeholder="Percentual %"
                  value={numberValue}
                  onChange={(e) =>
                    setNumberValue(e.target.value.replace(/[^0-9.]/g, ""))
                  }
                />
              </CFormGroup>
              {selectedItem !== null ? (
                <CButton color="info" 
                title={inforPermissions(permissao).edit}
                disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
                onClick={handleUpdate} className="mr-2">
                  Salvar
                </CButton>
              ) : (
                <CButton
                  color="info"
                  onClick={handleAdd}
                  className="mr-2"
                  title={inforPermissions(permissao).create}
                  disabled={!checkPermission(permissao.modulo, "Create", permissao.submodulo)}
                >
                  Adicionar
                </CButton>
              )}
              <CButton color="secondary" onClick={clearInputs}>
                Limpar campos
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </div>{" "}
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard
          style={{
            width: "70%",
            textAlign: "center",
          }}
        >
          <TableSelectItens
            data={data}
            columns={columns}
            onSelectionChange={(_) => {}}
            defaultSelectedKeys={[]}
            selectable={false}
            heightParam="600px"
          />
        </CCard>
        <ConfirmModal
          isOpen={showConfirmModal}
          onClose={handleModalClose}
          texto={"Tem certeza que deseja deletar essa faixa de cálculo?"}
        />
      </div>
    </div>
  );
};

export default FaixaAtraso;
