import { <PERSON>utton, <PERSON>ard, CCardBody, CCol, CForm, CFormGroup, CInput, CRow } from "@coreui/react";
import React, { useEffect, useState } from "react";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import Select from "react-select";
import { toast } from "react-toastify";
import { DELETE_DATA, GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import ConfirmModal from "src/reusable/ConfirmModal";
import TableSelectItens from "src/reusable/TableSelectItens";

import { showMenuAprovacaoJuridica } from "./../../../containers/AprovacaoJuridica";

const ParametriRestricoesNegociacao = () => {

    const [data, setData] = useState([]);
    const [restrictionName, setRestrictionName] = useState([]);
    const [optionsSelect, setOptionsSelect] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);
    const [selectedOption, setSelectedOption] = useState(null);

    const columns = [
        {
            key: "name",
            label: "Fase",
        },
        {
            key: "actions",
            label: "Ações",
            formatterByObject: (item) => renderActionButton(item),
        }
    ];

    const getConfigFasesAprovacaoJuridica = async () => {

        try {
            const response = await GET_DATA(
                getURI("getConfigByKey"),
                null,
                true,
                true,
                "fases_aprovacao_juridica"
            );
            if (response != '') {
                let listconfig = JSON.parse(response);
                const options = [];
                if (listconfig.length > 0) {
                    listconfig.forEach(function (item) {
                        options.push({ value: item, label: item })
                    });
                }
                setOptionsSelect(options)
                return options;
            }
        } catch (error) {
            return [];
        }
    };

    const renderActionButton = (item) => (
        <>
            <CButton
                color="light"
                onClick={() => handleConfirmDelete(item)}
                className="mr-2"
            // title={inforPermissions(permissao).edit}
            // disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
            >
                <i className="cil-trash" />
            </CButton>
        </>
    );

    const handleSave = async () => {
        const data = {
            name: selectedOption.value,
        };
        const insertSuccess = await POST_DATA(getURI("negociacaoParametriResticaoInserir"), data, true);
        if (insertSuccess.success) {
            toast.success(insertSuccess.message);
            await getListaRestricoes();
            clearInputs();
        } else {
            alert(insertSuccess.message);
        }
    };

    const handleModalClose = (confirmation) => {
        setShowConfirmModal(false);
        handleDelete(confirmation);
    };

    const handleConfirmDelete = (item) => {
        setSelectedItem(item);
        setShowConfirmModal(true);
    };

    const handleDelete = async (confirmation) => {


        if (confirmation) {
            const data = { Id: selectedItem.id };
            const deleteSuccess = await DELETE_DATA(getURI("negociacaoParametriResticaoDelete") + "/" + selectedItem.id, null, true);

            if (deleteSuccess.success) {
                toast.success(deleteSuccess.message);
                await getListaRestricoes();
                clearInputs();
            } else {
                alert(deleteSuccess.message);
            }
        }
    };

    const clearInputs = () => {
        setRestrictionName("");
    };

    async function getListaRestricoes() {
        setIsLoading(true);
        const listaRestricoesNegociacao = await GET_DATA(getURI("negociacaoParametriResticaoListar"), null, true);

        if (listaRestricoesNegociacao) {
            setData(listaRestricoesNegociacao);
        }
        setIsLoading(false);
        return;
    }

    const handleChangeSelect = (selectedOption) => {
        setSelectedOption(selectedOption);
    };

    const history = useHistory();
    useEffect(async () => {
        if (!await showMenuAprovacaoJuridica()) {
            history.push('/telaprincipal');
        }
        await getConfigFasesAprovacaoJuridica();
        await getListaRestricoes();
    }, []);

    return (
        <div>
            <h3>Cadastro de Fases Juridicas com restrição para negociação:</h3>
            <p style={{ color: "gray" }}>

            </p>
            <div>
                <CCard >
                    <CCardBody>
                        <CForm>
                            <CRow>
                                <CCol xs>
                                    <CRow>
                                        <CCol>
                                            <h5 style={{ color: "gray" }}>
                                                Cadastro:
                                            </h5>
                                        </CCol>
                                    </CRow>
                                    <CFormGroup className="row gx-3 gy-2 align-items-center">

                                        <CCol xs={8}>
                                            <Select
                                                placeholder="Selecione a Fase desejada"
                                                onChange={handleChangeSelect}
                                                value={selectedOption}
                                                options={optionsSelect}
                                            />



                                        </CCol>
                                        <CCol xs="auto">
                                            <CButton
                                                color="success"
                                                onClick={handleSave}
                                                className="mr-2"
                                            >
                                                Incluir
                                            </CButton>
                                        </CCol>

                                    </CFormGroup>
                                </CCol>
                                <CCol xs>
                                    <CRow>
                                        <CCol>
                                            <h5 style={{ color: "gray" }}>
                                                Lista de restrições já inclusas:
                                            </h5>
                                        </CCol>
                                    </CRow>
                                    <CRow>
                                        {isLoading ? (
                                            <CardLoading />
                                        ) : (

                                            <CCol>
                                                <TableSelectItens
                                                    data={data}
                                                    columns={columns}
                                                    onSelectionChange={(_) => { }}
                                                    defaultSelectedKeys={[]}
                                                    selectable={false}
                                                    heightParam="600px"
                                                />
                                                <ConfirmModal
                                                    isOpen={showConfirmModal}
                                                    onClose={handleModalClose}
                                                    texto={"Tem certeza que deseja excluir esse item?"}
                                                />
                                            </CCol>


                                        )}

                                    </CRow>

                                </CCol>
                            </CRow>

                        </CForm>
                    </CCardBody>
                </CCard>
            </div>
        </div>

    );
};



export default ParametriRestricoesNegociacao;