import React, { useState, useEffect } from "react";
import {
  formatDate,
  formatCurrency,
  convertCurrencyToFloat,
} from "../../reusable/helpers";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";
import {
  CButton,
  CCard,
  CCol,
  CNav,
  CNavItem,
  CRow,
  CNavLink,
  CTabContent,
  CTabPane,
  CTabs,
} from "@coreui/react";
import CustasTable from "./partials/CustasTable";
import FormCustasModal from "./partials/FormCustasModal";
import CardContratos from "../telaPrincipal/TabContratos";
import TabProcessos from "../telaPrincipal/TabProcessos";
import TableSelectItens from "src/reusable/TableSelectItens";
import LoadingCustas from "./partials/LoadingCustas";
import { useAuth } from "src/auth/AuthContext";
import { useMyContext } from "src/reusable/DataContext";
import DeleteCustasModal from "./partials/Modals/DeleteCustasModal";
import ReactiveCustasModal from "./partials/Modals/ReactiveCustasModal";

const getCustas = async (payload, endpoint = "getcustasdatacob") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(getURI(endpoint), payload, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const Custas = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissaoCusta = {
    modulo: "Jurídico CRM",
    submodulo: "Custas",
  };
  const permissaoJuridico = {
    modulo: "Jurídico CRM",
    submodulo: "Jurídico",
  };
  const [dataCustas, setDataCusta] = useState([]);
  const [dataAutomation, setDataAutomation] = useState([]);
  const [total, setTotal] = useState(0);
  const [devolucao, setDevolucao] = useState(0);
  const [geral, setGeral] = useState(0);
  const [loading, setLoading] = useState(false);
  const [loadingAutomation, setLoadingAutomation] = useState(false);
  const [modalShow, setModalShow] = useState(false);
  const [pasta, setPasta] = useState(null);
  const [edit, setEdit] = useState(false);
  const [deleteCusta, setDeleteCusta] = useState(false);
  const [dataEdit, setDataEdit] = useState(null);
  const [deleteCustasModalShow, setDeleteCustasModalShow] = useState(false);
  const [reativeCustasModalShow, setReativeCustasModalShow] = useState(false);
  const [updateTable, setUpdateTable] = useState(true);
  const { updateCustas } = useMyContext();

  const handleClose = () => {
    setEdit(false);
    setDeleteCusta(false);
    setDataEdit(null);
    setModalShow(false);
    setDeleteCustasModalShow(false);
    setReativeCustasModalShow(false);
    setUpdateTable(true);
  };

  const fields = [
    { key: "numero_Contrato", label: "Contrato" },
    { key: "dt_Inc", label: "Data Despesa" },
    { key: "dt_Despesa", label: "Data Comprovante" },
    { key: "tipo_Comprov", label: "Tipo Comprovante" },
    { key: "nr_Comprov", label: "Documento" },
    { key: "vl_Despesa", label: "Valor Despesa" },
    { key: "motivo", label: "Motivo", className: "nowrap-cell" },
    {
      key: "dt_Dev",
      label: "Data Devolução",
      formatter: (value) => (!value ? "" : formatDate(value)),
      className: "nowrap-cell",
    },
    {
      key: "devolucao_Motivo",
      label: "Motivo Devolução",
      className: "nowrap-cell",
    },
    {
      key: "alt_status",
      label: "Ações",
      className: "nowrap-cell",
      formatterByObject: (item) => renderActions(item),
    },
  ];

  const handleDelete = (item) => {
    // setDeleteCusta(true);
    setDataEdit(item);
    // setModalShow(true);
    setDeleteCustasModalShow(true);
    setUpdateTable(false);
  };

  const handleReactive = (item) => {
    // setDeleteCusta(true);
    setDataEdit(item);
    // setModalShow(true);
    setReativeCustasModalShow(true);
    setUpdateTable(false);
  };

  const handleEdit = (item) => {
    setEdit(true);
    setDataEdit(item);
    setModalShow(true);
    setUpdateTable(false);
  };

  const renderActions = (item) => {
    return (
      <div>
        {checkPermission(
          permissaoCusta.modulo,
          "Delete",
          permissaoCusta.submodulo
        ) &&
          !item.dt_Dev && (
            <CButton
              color="danger"
              size="sm"
              className="ml-1"
              disabled={
                !checkPermission(
                  permissaoCusta.modulo,
                  "Delete",
                  permissaoCusta.submodulo
                )
              }
              onClick={() => handleDelete(item)}
              title={inforPermissions(permissaoCusta).delete}
            >
              <i className="cil-trash" />
            </CButton>
          )}

        {checkPermission(
          permissaoCusta.modulo,
          "Delete",
          permissaoCusta.submodulo
        ) &&
          item.dt_Dev && (
            <CButton
              color="warning"
              size="sm"
              className="ml-1"
              disabled={
                !checkPermission(
                  permissaoCusta.modulo,
                  "Delete",
                  permissaoCusta.submodulo
                )
              }
              onClick={() => handleReactive(item)}
              title={"Reativar Custa"}
            >
              <i className="cil-reload" />
            </CButton>
          )}

        {checkPermission(
          permissaoCusta.modulo,
          "Edit",
          permissaoCusta.submodulo
        ) &&
          !item.dt_Dev && (
            <CButton
              color="info"
              size="sm"
              className="ml-2"
              disabled={
                !checkPermission(
                  permissaoCusta.modulo,
                  "Edit",
                  permissaoCusta.submodulo
                )
              }
              onClick={() => handleEdit(item)}
              title={inforPermissions(permissaoCusta).edit}
            >
              <i className="cil-pen" />
            </CButton>
          )}
      </div>
    );
  };

  const fieldsAutomation = [
    { key: "insertDate", label: "Data Solicitação Automação" },
    {
      key: "tipoAcao",
      label: "Tipo de Ação",
      className: "nowrap-cell",
      formatter: (value) => {
        switch (value) {
          case 1:
            return "Inclusão";
          case 2:
            return "Edição";
          case 3:
            return "Exclusão";
          default:
            return value;
        }
      },
    },
    { key: "nrContrato", label: "Contrato" },
    { key: "dtDespesa", label: "Data Despesa" },
    { key: "dtComprov", label: "Data Comprovante" },
    { key: "tipo", label: "Tipo Comprovante" },
    { key: "nrComprov", label: "Documento" },
    { key: "vlDespesa", label: "Valor Despesa" },
    { key: "motivo", label: "Motivo" },
    { key: "StatusAutomacao", label: "Status Automação" },
    { key: "StatusProcesso", label: "Status Processo" },
    { key: "MsgAutomacao", label: "Mensagem Automação" },
  ];

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";
  const payload = {
    IdContrato: financiadoData.id_Agrupamento || null,
    idAgrupamento: financiadoData.id_Agrupamento || null,
  };

  const updateViewCusta = (_) => {
    if (
      !checkPermission(permissaoCusta.modulo, "View", permissaoCusta.submodulo)
    )
      return;
    if (payload.idAgrupamento !== null) {
      setLoading(true);
      getCustas(payload)
        .then((data) => {
          if (data && data.length > 0) {
            updateCustas(data);
            data.map((item) => {
              item.dt_Inc = formatDate(item.dt_Inc);
              item.dt_Despesa = formatDate(item.dt_Despesa);
              item.vl_Despesa = formatCurrency(item.vl_Despesa);
            });
            setDataCusta(data);
            const devolvidas = data
              .filter((item) => item.dt_Dev)
              .reduce(
                (total, item) =>
                  Math.round((total + item.vl_saldo) * 100) / 100,
                0
              );
            const geral = data.reduce(
              (total, item) => Math.round((total + item.vl_saldo) * 100) / 100,
              0
            );
            const total = Math.round((geral - devolvidas) * 100) / 100;
            setGeral(geral);
            setDevolucao(devolvidas);
            setTotal(total);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const updateViewAutomation = (_) => {
    if (
      !checkPermission(permissaoCusta.modulo, "View", permissaoCusta.submodulo)
    )
      return;
    if (payload.idAgrupamento !== null) {
      setLoadingAutomation(true);
      getCustas(payload, "getcustastelaunica")
        .then((data) => {
          if (data && data.length > 0) {
            data.map((item) => {
              item.insertDate = formatDate(item.insertDate);
              item.dtComprov = formatDate(item.dtComprov);
              item.dtDespesa = formatDate(item.dtDespesa);
              item.vlDespesa = formatCurrency(item.vlDespesa);
              item.StatusAutomacao = item.processado
                ? "Processado"
                : "Não Processado";
              item.StatusProcesso = item.processado
                ? item.sucesso
                  ? "Inserido"
                  : item.msgErro === "Executando"
                  ? "Executando"
                  : "Falha"
                : "";
              item.MsgAutomacao = item.msgErro ? item.msgErro : "";
            });
            setDataAutomation(data);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoadingAutomation(false);
        });
    }
  };

  useEffect(() => {
    if (selectedTabIndex === 1 && updateTable) updateViewCusta();
    if (selectedTabIndex === 2 && updateTable) updateViewAutomation();
  }, [updateTable]);

  const handlePastaChange = (pasta) => {
    setPasta(pasta);
  };

  const tabs = [
    {
      id: 1,
      label: "Lançamentos CRM",
      icon: "cil-money",
      content: loading ? (
        <LoadingCustas />
      ) : (
        <>
          <CRow className="d-flex mb-2 mx-3">
            <CCol md="6" sm="12" className="px-0 mb-2"></CCol>
            <CCol></CCol>
            <CCol
              md="4"
              sm="12"
              className="d-flex px-0 mb-2 justify-content-end"
            >
              {checkPermission(
                permissaoCusta.modulo,
                "Create",
                permissaoCusta.submodulo
              ) && (
                <CButton
                  color="info"
                  disabled={
                    payload.idAgrupamento == null ||
                    !checkPermission(
                      permissaoCusta.modulo,
                      "Create",
                      permissaoCusta.submodulo
                    )
                  }
                  onClick={() => {
                    setModalShow(true);
                    setUpdateTable(false);
                  }}
                >
                  <i className="cil-plus" /> Adicionar Custa
                </CButton>
              )}
              {checkPermission(
                permissaoCusta.modulo,
                "View",
                permissaoCusta.submodulo
              ) && (
                <CButton
                  className={"ml-3"}
                  color="success"
                  disabled={payload.idAgrupamento == null}
                  onClick={() => updateViewCusta()}
                  title="Atualizar os dados da Tabela buscado as informações no CRM"
                >
                  <i className="cil-reload" />
                </CButton>
              )}
            </CCol>
          </CRow>
          <CRow>
            <CCol>
              <CustasTable
                dataCustas={dataCustas}
                fields={fields}
                total={total}
                lancadas={geral}
                devolvidas={devolucao}
              />
            </CCol>
          </CRow>
        </>
      ),
    },

    {
      id: 2,
      label: "Acompanhamento da Automação",
      icon: "cil-media-play",
      content: loadingAutomation ? (
        <LoadingCustas />
      ) : (
        <>
          <CRow className="d-flex mb-2 mx-3">
            <CCol md="6" sm="12" className="px-0 mb-2"></CCol>
            <CCol></CCol>
            <CCol
              md="4"
              sm="12"
              className="d-flex px-0 mb-2 justify-content-end"
            >
              {checkPermission(
                permissaoCusta.modulo,
                "View",
                permissaoCusta.submodulo
              ) && (
                <CButton
                  className={"ml-3"}
                  color="success"
                  disabled={payload.idAgrupamento == null}
                  onClick={() => updateViewAutomation()}
                  title="Atualizar os dados da Tabela buscado as informações da automaçao no GVC Manager"
                >
                  <i className="cil-reload" />
                </CButton>
              )}
            </CCol>
          </CRow>
          <CRow>
            <CCol>
              <TableSelectItens
                data={dataAutomation}
                columns={fieldsAutomation}
                onSelectionChange={(_) => {}}
                defaultSelectedKeys={[]}
                selectable={false}
                heightParam="320px"
              />
            </CCol>
          </CRow>
        </>
      ),
    },
  ];

  const [selectedTabIndex, setSelectedTabIndex] = useState(1);
  const [currentTab, setCurrentTab] = useState("Lançamentos CRM");
  const handleTabSelect = (tab) => {
    setCurrentTab(tab.label);
    setSelectedTabIndex(tab.id);
    if (tab.label === "Lançamentos CRM") {
      updateViewCusta();
    }
    if (tab.label === "Acompanhamento da Automação") {
      updateViewAutomation();
    }
  };

  return (
    <div>
      <CRow className="mx-3 mb-2">
        <h1>Custas</h1>
      </CRow>

      <div className="container-fluid">
        {payload.idAgrupamento == null ? (
          <div>Não há um financiado Selecionado</div>
        ) : (
          <CardContratos pasta={pasta} onLoadProcessos={() => {}} />
        )}
      </div>

      <div className="container-fluid">
        <CCard>
          <CTabs onSelect={handleTabSelect} activeTab={"Lançamentos CRM"}>
            <CNav className="custom-nav">
              {tabs.map((tab) => (
                <CNavItem
                  key={tab.id}
                  className={currentTab === tab.label ? "" : "nonactive-tab"}
                >
                  <CNavLink
                    data-tab={tab.label}
                    onClick={() => handleTabSelect(tab)}
                  >
                    <i className={tab.icon} /> {tab.label}
                  </CNavLink>
                </CNavItem>
              ))}
            </CNav>
            <CTabContent
              className="px-3 overflow-auto"
              style={{ minHeight: "450px", height: "450px", padding: "5px" }}
            >
              {tabs.map((tab) => (
                <CTabPane key={tab.id} data-tab={tab.label}>
                  {tab.content}
                </CTabPane>
              ))}
            </CTabContent>
          </CTabs>
        </CCard>
      </div>

      <div className="container-fluid">
        {checkPermission(
          permissaoJuridico.modulo,
          "View",
          permissaoJuridico.submodulo
        ) && (
          <TabProcessos
            onPastaChange={handlePastaChange}
            tabSelected="custas"
          />
        )}
      </div>

      {modalShow && (
        <FormCustasModal
          isOpen={modalShow}
          onClose={handleClose}
          edit={edit}
          deleteCusta={deleteCusta}
          dataEdit={dataEdit}
        />
      )}
      {deleteCustasModalShow && (
        <DeleteCustasModal
          isOpen={deleteCustasModalShow}
          onClose={handleClose}
          custaId={dataEdit?.id_Custas}
          contratoId={dataEdit?.id_Contrato}
        />
      )}
      {reativeCustasModalShow && (
        <ReactiveCustasModal
          isOpen={reativeCustasModalShow}
          onClose={handleClose}
          custaId={dataEdit?.id_Custas}
          contratoId={dataEdit?.id_Contrato}
        />
      )}
    </div>
  );
};

export default Custas;
