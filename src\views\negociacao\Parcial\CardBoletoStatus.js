import React, { useEffect, useState } from "react";
import {
  CCard,
  CCardBody,
  CLabel,
  CModalHeader,
  CCol,
  CRow,
  CModal,
  CModalBody,
  CModalFooter,
  CButton,
} from "@coreui/react";
import { POST_DATA, GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { useMyContext } from "src/reusable/DataContext";
import { convertCurrencyToFloat, formatCurrency } from "src/reusable/helpers";

const PostData = async (payload, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), null, true, true, id);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(
        getURI(endpoint),
        null,
        true,
        true,
        payload
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const CardBoletoStatus = ({ onClose }) => {
  const { data } = useMyContext();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const id = data.id_Agrupamento;

  useEffect(() => {
    GetData(id, "getJokerTicketListByGrouping").then((response) => {
      setList(response);
    });
    const searchStatus = async () => {
      GetData(id, "getJokerTicketListByGrouping").then((response) => {
        setList(response);
      });
    };

    const intervalId = setInterval(searchStatus, 600000);
    return () => clearInterval(intervalId);
  }, [id]);

  const sumValues = (item) => {
    return (
      convertCurrencyToFloat(item.commision) +
      convertCurrencyToFloat(item.correctedValue) +
      convertCurrencyToFloat(item.costs) +
      convertCurrencyToFloat(item.fareValue) +
      convertCurrencyToFloat(item.fees) +
      convertCurrencyToFloat(item.fine) +
      convertCurrencyToFloat(item.honor) +
      convertCurrencyToFloat(item.mainValue) +
      convertCurrencyToFloat(item.notification)
    );
  };

  const labelContract = (item) => {
    let label = "";

    try {
      const obj = JSON.parse(item);
      obj.map((value, index) => {
        label += value.contract + " - " + value.installment.join(" | ") + "\n";
      });
    } catch (e) {
      label = "Erro ao carregar dados";
    }
    return label;
  };

  return (
    <CModal size="lg" show={true} onClose={onClose} closeOnBackdrop={false}>
      <CModalBody>
        <CCard style={{ marginBottom: "0px" }}>
          <CCardBody>
            <CRow style={{ fontWeight: "bold" }}>
              <CCol xs="4">
                <CLabel>Contratos/Parcelas</CLabel>
              </CCol>
              <CCol xs="4">
                <CLabel>Valor</CLabel>
              </CCol>
              <CCol xs="4">
                <CLabel>Status</CLabel>
              </CCol>
            </CRow>
            <div
              style={{
                overflow: "auto",
                overflowX: "hidden",
                maxHeight: "400px",
              }}
            >
              {list.length === 0 ? (
                <CRow>
                  <CCol style={{ textAlign: "center" }}>
                    <CLabel>Nenhuma negociação encontrada</CLabel>
                  </CCol>
                </CRow>
              ) : (
                list.map((item, index) => {
                  return (
                    <CRow
                      key={index}
                      style={{
                        borderBottom: "1px solid rgb(182 182 182)",
                        marginBottom: "5px",
                      }}
                    >
                      <CCol xs="4">
                        <CLabel style={{ whiteSpace: "pre-wrap" }}>
                          {labelContract(item.installmentJson)}
                        </CLabel>
                      </CCol>
                      <CCol xs="4">
                        <CLabel>{formatCurrency(sumValues(item))}</CLabel>
                      </CCol>
                      <CCol xs="4">
                        <CLabel>{item.statusAutomation}</CLabel>
                      </CCol>
                    </CRow>
                  );
                })
              )}
            </div>
          </CCardBody>
        </CCard>
      </CModalBody>
      <CModalFooter>
        <CButton color="danger" onClick={onClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default CardBoletoStatus;
