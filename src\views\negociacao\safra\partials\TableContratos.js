import React, { useState, useEffect } from "react";
import { formatCurrency } from 'src/reusable/helpers';

const TableContratos = ({ dataTable, contratoIndex, onContratoClick}) => {
  return (
    <div className="table-responsive">
      <table className='table'>
        <thead>
          <tr>
              <th>Contrato</th>
              <th><PERSON>or Principal</th>
              <th>Valor Devedor</th>
              <th>Valor Principal <PERSON></th>
              <th>Valor Juros CDI</th>
              <th>Valor Juros</th>
              <th>Valor Multa</th>
              <th>Valor Tarifa</th>
              <th>Valor Cust<PERSON></th>
              <th>Valor Alvara</th>
              <th>Valor Jur Tx Contr</th>
              <th>Valor Devedor Encargos</th>
          </tr>
        </thead>
        <tbody>
          {dataTable.map((item, index) => (
            <tr key={index}
            onClick={() => onContratoClick(item)}
            className={contratoIndex != null && contratoIndex.dadosSafra.contrato == item.dadosSafra.contrato ? 'bg-success' : ''}
            style={{cursor: 'pointer'}}
            >
              <td>{item.dadosSafra.contrato}</td>
              <td>{formatCurrency(item.dadosSafra.vlrPrincipal)}</td>
              <td>{formatCurrency(item.dadosSafra.vlrDevedor)}</td>
              <td>{formatCurrency(item.dadosSafra.vlrPrincDesc)}</td>
              <td>{formatCurrency(item.dadosSafra.vlrJurosCDI)}</td>
              <td>{formatCurrency(item.dadosSafra.vlrJuros)}</td>
              <td>{formatCurrency(item.dadosSafra.vlrMulta)}</td>
              <td>{formatCurrency(item.dadosSafra.vlrTarifa)}</td>
              <td>{formatCurrency(item.dadosSafra.vlrCustas)}</td>
              <td>{formatCurrency(item.dadosSafra.vlrAlvara)}</td>
              <td>{formatCurrency(item.dadosSafra.vlrJurTxContr)}</td>
              <td>{formatCurrency(item.dadosSafra.vlrDevedorEncargos)}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TableContratos;
