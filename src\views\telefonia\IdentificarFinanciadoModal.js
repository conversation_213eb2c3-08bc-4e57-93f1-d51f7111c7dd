import React, { useState, useEffect } from "react";
import {
  CBadge,
  <PERSON>utton,
  CCard,
  CCardBody,
  CCol,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CRow,
} from "@coreui/react";
import { confirmarLigacao } from "src/config/telephonyFunctions";
import MudarClienteModal from "src/reusable/MudarClienteModal";
import LoadingComponent from "src/reusable/Loading";
import TableSelectItens from "src/reusable/TableSelectItens";
import { toast } from "react-toastify";

import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { useMyContext } from "src/reusable/DataContext";
import { formatDocument } from "src/reusable/helpers";
import { GET_ClientData, GET_FINANCIADO } from "src/reusable/functions";
import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";

const IdentificarFinanciadoModal = ({
  isOpen,
  onClose,

}) => {
  const { data, updateData } = useMyContext();

  const [financiadoData, setDadosFinanciados] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  const [mudarClienteModal, setMudarClienteModal] = useState(false);
  const [tableContratosData, setTableContratosData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const [temContratoPermitido, setTemContratoPermitido] = useState(false);

  const [origem, setOrigem] = useState("");

  const { message, dadosLigacao } = useWebsocketTelefoniaContext()

  async function confirmarFinanciado(financiado = null) {
    const callData = message;

    if (callData && (callData.callId || callData.callIdTactium)) {
      const idString = callData.callId
        ? callData.callId.toString()
        : callData.callIdTactium.toString();

      const financiadoParaConfirmar = financiado ?? financiadoData;

      //GRUPO
      const walletString = !financiadoParaConfirmar
        ? ""
        : financiadoParaConfirmar?.grupo +
        " - " +
        financiadoParaConfirmar.cliente;

      const payload = {
        callId: idString,
        wallet: walletString,
        financedName: financiadoParaConfirmar.nome,
        idAgrupamento: financiadoParaConfirmar.id_Agrupamento,
        document: financiadoParaConfirmar.cpfCnpj,
        group: financiadoParaConfirmar?.grupo,
        idGroup: financiadoParaConfirmar.id_Grupo,
      };
      setIsLoading(true);
      await confirmarLigacao(payload, callData);
      setIsLoading(false);
    }
    // setFinanciadoSelecionado(true);
    onClose(true);
  }

  function buscarOutroFinanciado() {
    setMudarClienteModal(true);
  }

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const userProfile = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : "";

  const updateConnection = async (idcrm) => {
    const payload = {
      activeConnection: idcrm,
      userId: userProfile.id,
    };
    await postActiveConnection(payload, "postUserConnection")
      .then((data) => {
        if (data) {
          const newActiveConnection = {
            ...userProfile,
            activeConnection: idcrm,
          };
          localStorage.setItem("user", JSON.stringify(newActiveConnection));
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const postActiveConnection = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const buscarFinanciado = async (item) => {
    const payload = {
      ActiveConnection: item.crm,
      agrupamentoId: item.idAgrupamento,
    };
    // const payload = { ActiveConnection: "GVC", agrupamentoId: 35199 };
    setIsLoading(true);
    await updateConnection(item.crm);
    await GetData(payload, "getDadosContratoLigacao")
      .then((resultado) => {
        if (resultado && resultado.length > 0) {
          // updateFinanciado(data[0]);
          localStorage.setItem("financiadoData", JSON.stringify(resultado[0])); //Atualizando o financiadoData no localStorage
          updateData(resultado[0]); //Atualizando o financiadoData na telaPrincipal
          //setDadosFinanciados(resultado[0]); //Atualizando o financiadoData nesse arquivo
          //confirmarFinanciado(); //Confirmando a ligação com a API
        } else {
          //Se apareceu o contrato e teve permissão de clicar, nunca deveria falhar aqui
          toast.error("Erro: Dados não encontrados");
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  const updateFinanciado = async (financiado) => {
    await GET_FINANCIADO(financiado);
    await GET_ClientData(financiado);
    GET_ContratosAtivos(financiado);
  };

  const GET_ContratosAtivos = (financiado) => {
    const payload = {
      IdFinanciado: financiado.id_Financiado,
      IdAgrupamento: financiado.id_Agrupamento,
    };
    setIsLoading(true);
    getContratosAtivos(payload, "getContratosAtivos").then((data) => {
      if (data) {
        localStorage.setItem("contratosAtivos", JSON.stringify(data));
      }
    });
  };

  const getContratosAtivos = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const tableFields = [
    { key: "grupo", label: "Grupo", formatter: (value) => renderCell(value) },
    {
      key: "cliente",
      label: "Cliente",
      formatter: (value) => renderCell(value),
    },
    {
      key: "cpfcnpj",
      label: "CPF/CNPJ",
      formatter: (value) => formatDocument(value),
    },
    {
      key: "financiado",
      label: "Nome",
      formatterByObject: (item) => renderBotaoNome(item),
    },
    // { key: "status", label: "Status Contrato" },
    {
      key: "contrato",
      label: "Contrato",
      formatter: (value) => renderCell(value),
    },
    // { key: "fase", label: "Fase" },
  ];

  const renderCell = (item) => {
    return <div style={{ whiteSpace: "nowrap" }}>{item}</div>;
  };

  const renderBotaoNome = (item) => {
    if (item.isPermitted) {
      setTemContratoPermitido(true);
      return (
        <CButton
          onClick={() => buscarFinanciado(item)}
          className="flat px-2 pt-0 pb-1"
          style={{ color: "blue", whiteSpace: "nowrap" }}
        >
          {item.financiado}
        </CButton>
      );
    } else
      return (
        <span
          className="px-2 pt-0 pb-1"
          style={{ color: "grey", whiteSpace: "nowrap" }}
        >
          {item.financiado}
        </span>
      );
  };

  function stringOrigem(origem = 0) {
    if (typeof origem == "number") {
      if (origem === 1) return "DISCADOR";
      if (origem === 2) return "MANUAL";
      if (origem === 3) return "LIGAÇÃO URA";
      if (origem === 0) return "RECEPTIVA";
    }

    return "DISCADOR";
  }

  useEffect(() => {
    if (isOpen && localStorage.getItem("financiadoData")) {
      setDadosFinanciados(JSON.parse(localStorage.getItem("financiadoData")));
    }
    if (isOpen && dadosLigacao?.callContractSearch) {
      setTableContratosData(dadosLigacao.callContractSearch);
    }
    if (isOpen) {
      setOrigem(stringOrigem(dadosLigacao?.callType));
    }
  }, [isOpen]);

  const handleClose = () => {
    setTemContratoPermitido(false);
    onClose();
  };

  useEffect(() => {
    if (data && isOpen) {
      setDadosFinanciados(data);
      confirmarFinanciado(data);
      handleClose();
    }
  }, [data]);

  return (
    <CModal
      centered={true}
      show={isOpen}
      onClose={handleClose}
      size="xl"
      closeOnBackdrop={false}
    >
      <CModalHeader closeButton={!temContratoPermitido}>
        <CModalTitle
          style={{
            width: "100%",
            display: "grid",
            gridTemplateColumns: "40% 50%",
          }}
        >
          Identifique o Financiado
          <CBadge
            color={"warning"}
            style={{
              width: "45%",
              padding: "10px",
              textWrap: "wrap",
            }}
          >
            ATENÇÃO - LIGAÇÃO {origem}
          </CBadge>
        </CModalTitle>
      </CModalHeader>
      <CModalBody className="pb-0">
        {isLoading ? (
          <CRow>
            <CCol style={{ justifyContent: "center" }} className="my-2">
              <LoadingComponent />
            </CCol>
          </CRow>
        ) : (
          //Se houver dados da tabela pela ligação, mostrar eles
          <>
            {tableContratosData ? (
              <>
                <CCard>
                  <TableSelectItens
                    data={tableContratosData}
                    columns={tableFields}
                    onSelectionChange={(_) => { }}
                    defaultSelectedKeys={[]}
                    selectable={false}
                    heightParam="520px"
                  />
                </CCard>
                {/* {!temContratoPermitido && ( */}
                <CModalFooter>
                  <CButton color="secondary" onClick={buscarOutroFinanciado}>
                    Pesquisar novo cliente
                  </CButton>
                </CModalFooter>
                {/* )} */}
              </>
            ) : (
              <>
                <div style={{ textAlign: "center" }}>
                  <CLabel
                    style={{
                      color: "grey",
                      fontSize: "smaller",
                      marginBottom: "18px",
                    }}
                  >
                    Cliente não encontrado! Clique abaixo para pesquisar de
                    outras formas!
                  </CLabel>
                  {/* {financiadoData && (
                    <CRow>
                      <CCard className="mb-2 mx-1 w-100 ">
                        <CCardBody>
                          <CButton
                            onClick={confirmarFinanciado}
                            style={{ width: "100%" }}
                          >
                            <strong>{financiadoData.nome} </strong>
                            <br />
                            {financiadoData.cliente} - Contrato{" "}
                            {financiadoData.numero_Contrato}
                          </CButton>
                        </CCardBody>
                      </CCard>
                    </CRow>
                  )} */}
                  <CRow style={{ textAlign: "end", marginBottom: "12px" }}>
                    <CCol>
                      <CButton
                        // Testar se atualiza direito as coisas quando mudar,
                        // já que talvez não apareça de novo o botão de confirmar a ligação,
                        // precisa confirmar direto depois de pesquisar/talvez seja no telaPrincipal isso
                        color="primary"
                        onClick={buscarOutroFinanciado}
                        style={{ marginRight: "12px" }}
                      >
                        Buscar outro financiado para o atendimento.
                      </CButton>
                      <CButton color="secondary" onClick={handleClose}>
                        Fechar
                      </CButton>
                    </CCol>
                  </CRow>
                </div>
              </>
            )}
          </>
        )}
      </CModalBody>
      {mudarClienteModal && (
        <MudarClienteModal onClose={() => setMudarClienteModal(false)} />
      )}
    </CModal>
  );
};

export default IdentificarFinanciadoModal;
