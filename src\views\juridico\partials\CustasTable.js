import React from 'react';
import { CDataTable } from '@coreui/react';
import { formatCurrency } from 'src/reusable/helpers';
import TableSelectItens from 'src/reusable/TableSelectItens';


const CustasTable = ({ dataCustas, fields, total, lancadas, devolvidas }) => {
  return (
    <div>
      <TableSelectItens data={dataCustas}
        columns={fields}
        onSelectionChange={_ => { }}
        defaultSelectedKeys={[]}
        selectable={false}
        heightParam="310px"
      />
      <div style={{ textAlign: 'right', marginTop: '10px' }}>
      <div>
          <strong>Lançadas: {formatCurrency(lancadas)}</strong>
        </div>
      <div>
          <strong>Devolvidas: {formatCurrency(devolvidas)}</strong>
        </div>
        <div>
          <strong>Total: {formatCurrency(total)}</strong>
        </div>
      </div>
    </div>
  );
};

export default CustasTable;
