import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>on, <PERSON>ard, <PERSON>ardBody, CCardHeader } from "@coreui/react";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardAvisos from "src/reusable/CardAvisos";
import CardLoading from "src/reusable/CardLoading";
import TableAcordos from "./TableAcordos";
import CardParcelasAcordo from "./CardParcelasAcordo";
import FormCancelarNegociacaoModal from "./FormCancelarNegociacaoModal";
import FormEnviarEmailModal from "./FormEnviarEmailModal";

const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(
        getURI(endpoint),
        null,
        true,
        true,
        payload
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const PostData = async (payload, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI(endpoint),
        payload,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const CardAcordos = (comunicacaoState) => {
  let financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";

  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;
  const parcelasAbertas = contratosAtivos?.flatMap((item) =>
    item.parcelas.filter(
      (pItem) =>
        pItem.status === "A" && !pItem.nr_Acordo /* && pItem.atraso > 0  ||
      (pItem.status === "A" && !pItem.nr_Acordo && pItem.atraso === 0 && pItem.nome_Tipo_Parcela === "DIF_PARCELAS")*/
    )
  );
  const [dataRepository, setDataRepository] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");
  const [msgAviso, setMsgAviso] = useState("");
  const [titleAviso, setTitleAviso] = useState("");

  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");

  const [showTableContrato, setShowTableContrato] = useState(false);
  const [contratosSelecionados, setContratosSelecionados] = useState(null);

  const [modalShow, setModalShow] = useState(false);
  const [modalEnviarEmailShow, setEnviarEmailShow] = useState(false);
  const [idContratoDataCob, setIdContratoDataCob] = useState(null);

  const buscarAcordo = async () => {
    setTitleAviso("");
    setMsgAviso("");
    setShowTableContrato(false);
    setContratosSelecionados(null);
    setMsgAvisoLoading(`Buscando Acordos Safra`);
    setTitleAvisoLoading("Consultando Acordos");
    setLoading(true);
    setLoadingAction("VarifyParam");
    await GetData(
      financiadoData.cpfCnpj,
      "negociacaoSafraAcordoListarAcordoPorCpfCnpj"
    )
      .then((data) => {
        if (data !== undefined && data !== null && data.length > 0) {
          setDataRepository(data);
          setShowTableContrato(true);
        } else {
          setTitleAviso("Não há dados");
          setMsgAviso("");
        }
      })
      .catch((err) => {
        setTitleAviso(`Erro na chamada das APIS`);
        setMsgAviso(
          `Erro na chamada API de Neogciação Lista de Acordos, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`
        );
      })
      .finally(() => {
        setLoading(false);
        setLoadingAction("empty");
        setMsgAvisoLoading("");
        setTitleAvisoLoading("");
      });
  };

  useEffect(() => {
    if (comunicacaoState.comunicacaoState === "OK") {
      buscarAcordo();
    }
  }, [comunicacaoState]);

  useEffect(() => {
    if (!modalShow) buscarAcordo();
  }, [modalShow]);

  const handleContratoSelection = (contrato) => {
    setContratosSelecionados(contrato);
  };
  const handleClose = () => {
    setModalShow(false);
    setEnviarEmailShow(false);
  };

  const DownloadBoletos = async () => {
    if (contratosSelecionados != null) {
      setLoading(true);
      setLoadingAction("VarifyParam");
      await GetData(
        `/${contratosSelecionados.id}/${contratosSelecionados.idAcordo}`,
        "cyberSafraObterBase64Boleto"
      )
        .then((data) => {
          if (data !== undefined && data !== null) {
            const url = URL.createObjectURL(data);
            window.open(url, "_blank");
            setTitleAvisoLoading("Dados do Boleto Aberto Safra Gerado");
            setMsgAvisoLoading(
              "Caso não abra uma segunda janela com o Boleto, verifique os Bloqueios de Pop-ups do navegador"
            );
          } else {
            setTitleAvisoLoading("Não há boletos em aberto");
            setMsgAvisoLoading("");
          }
        })
        .catch((err) => {
          setTitleAvisoLoading("Não há boletos em aberto");
          setMsgAvisoLoading("");
        })
        .finally(() => {
          limparAvisos();
        });
    }
  };

  const limparAvisos = () => {
    setTimeout(() => {
      setLoading(false);
      setLoadingAction("empty");
      setMsgAvisoLoading("");
      setTitleAvisoLoading("");
      buscarAcordo();
    }, 3000);
  };

  const AtualizarStatusDataCob = async (status) => {
    if (contratosSelecionados != null) {
      setTitleAvisoLoading("Atualizando Status Acordo");
      setMsgAvisoLoading(`Enviando...`);
      setLoading(true);
      setLoadingAction("VarifyParam");
      await PostData(
        status,
        "negociacaoSafraAtualizarAtualizarStatusDataCob",
        contratosSelecionados.id
      )
        .then((data) => {
          if (data.success) {
            setTitleAvisoLoading("Status Atualizando do Acordo");
            setMsgAvisoLoading("");
          } else {
            setTitleAvisoLoading("Status Não Atualizando do Acordo");
            setMsgAvisoLoading(data.message);
          }
        })
        .catch((err) => {
          setTitleAvisoLoading("Erro ao Tentar atualizar Status do Acordo");
          setMsgAvisoLoading("");
        })
        .finally(() => {
          limparAvisos();
        });
    }
  };

  const EnviarGvcManager = (id) => {
    if (contratosSelecionados != null) {
      setLoading(true);
      setLoadingAction("VarifyParam");
      let action = 0;
      if (contratosSelecionados.status === "Cancelado") action = 1;

      if (id === null) {
        setTitleAvisoLoading("Falha na busca do Id Contrato Tela Única");
        setMsgAvisoLoading("");
        limparAvisos();
        return;
      }

      setTitleAvisoLoading("Enviando solicitação ao GVC Manager");
      setMsgAvisoLoading("Enviando Acordo...");

      const dateString = contratosSelecionados.dtParcelaEntrada;
      const dateObj = new Date(dateString);

      const formattedDate = `${dateObj.getFullYear()}-${String(
        dateObj.getMonth() + 1
      ).padStart(2, "0")}-${String(dateObj.getDate()).padStart(2, "0")}`;

      PostData(
        {
          type_action: action,
          id_agreements: id,
          nr_installment: null,
          id_agreements_tu: contratosSelecionados.id,
          prazoSimulado: contratosSelecionados.qtParcelasAtivas,
          idAcordoDataCob: contratosSelecionados.idAcordoDataCob,
          valorFinanciadoSimulado:
            contratosSelecionados.valorFinanciadoSimulado,
          valorHonorario: contratosSelecionados.valorHonorarios,
          contratoDataCob: contratosSelecionados.contratoDataCob,
          dtParcelaEntrada: formattedDate,
          valorEntrada: contratosSelecionados.valorEntrada,
          cpfCnpj: financiadoData.cpfCnpj,
        },
        "gvcmanagerCyberSafraAcoesContrato"
      )
        .then((data) => {
          if (data.success) {
            setTitleAvisoLoading("Acordo Enviado ao GVC Manager");
            setMsgAvisoLoading("");
            AtualizarStatusDataCob(
              `Enviado GVC Manager Comando de ${
                action === 0 ? "criação" : "cancelamento"
              } de Acordo`
            );
          } else {
            AtualizarStatusDataCob("Falha");
            limparAvisos();
          }
        })
        .catch((err) => {
          setTitleAvisoLoading("Falha ao Enviar Acordo ao GVC Manager");
          setMsgAvisoLoading("");
          limparAvisos();
        });
    }
  };

  const buscarContratoTelaUnica = async () => {
    if (contratosSelecionados != null) {
      setIdContratoDataCob(null);
      setLoading(true);
      setLoadingAction("VarifyParam");

      const payload = {
        agreementId: contratosSelecionados.agreement_id,
        negotiationData: {
          idAgrupamento: financiadoData.id_Agrupamento,
          parcelas: [parcelasAbertas[0]?.id_Parcela],
          dtNegociacao: contratosSelecionados.dtParcelaEntrada,
          valorPrincipal: contratosSelecionados.parcelas[0].vlrParcela,
          valorCorrecao: 0,
          juros: contratosSelecionados.parcelas[0].vlrJuros,
          multa: contratosSelecionados.parcelas[0].vlrImposto,
          comissaoPermanencia: 0,
          honorarios: 0,
          descontoAutorizado: true,
          custas: 0,
          notificacao: 0,
          valorTarifa: 0,
        },
      };
      await PostData(payload, "negociacaoSafraIntegracao").then((data) => {
        if (data !== undefined && data !== null) {
          buscarAcordo();
          setIdContratoDataCob(contratosSelecionados.contract_id);
          // EnviarGvcManager(data.id_Contrato);
        }
      });
    }
  };

  const HandleModalEmail = () => {
    setEnviarEmailShow(true);
  };

  return (
    <div>
      <CCard>
        <CCardHeader>
          <h5 className="d-flex justify-content-between">
            <span>Acordos Safra</span>
            <div>
              {contratosSelecionados != null &&
                contratosSelecionados.status !== "Cancelado" &&
                (contratosSelecionados.statusDataCob === "" ||
                  contratosSelecionados.statusDataCob === "Falha" ||
                  typeof contratosSelecionados?.ticket?.idTicket !==
                    "number") && (
                  <CButton
                    className={"mr-2"}
                    color="primary"
                    onClick={buscarContratoTelaUnica}
                  >
                    Integrar DataCob
                  </CButton>
                )}
              {contratosSelecionados != null &&
              contratosSelecionados.status !== "Cancelado" ? (
                <CButton
                  className={"mr-2"}
                  color="success"
                  onClick={() => setModalShow(true)}
                >
                  Cancelar Acordo
                </CButton>
              ) : (
                ""
              )}
              {contratosSelecionados != null &&
              contratosSelecionados.status !== "Cancelado" ? (
                <CButton
                  className={"mr-2"}
                  color="success"
                  onClick={DownloadBoletos}
                >
                  Download Boleto Aberto
                </CButton>
              ) : (
                ""
              )}
              {contratosSelecionados != null &&
              contratosSelecionados.status === "Ativo" ? (
                <CButton
                  className={"mr-2"}
                  color="success"
                  onClick={HandleModalEmail}
                >
                  Enviar Email
                </CButton>
              ) : (
                ""
              )}
              <CButton
                color="success"
                disabled={financiadoData.id_Agrupamento == null || loading}
                onClick={() => buscarAcordo()}
              >
                <i className="cil-reload" />
              </CButton>
            </div>
          </h5>
        </CCardHeader>
        <CCardBody>
          {titleAviso !== "" ? (
            <CardAvisos Title={titleAviso} Msg={msgAviso} />
          ) : loading && loadingAction === "VarifyParam" ? (
            <CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />
          ) : showTableContrato && dataRepository !== null ? (
            <TableAcordos
              dataTable={dataRepository}
              contratoIndex={contratosSelecionados}
              onContratoClick={handleContratoSelection}
            />
          ) : (
            ""
          )}
        </CCardBody>
      </CCard>
      <div>
        {contratosSelecionados != null ? (
          <CCard>
            <CCardBody>
              <CardParcelasAcordo dataParcelas={contratosSelecionados} />
            </CCardBody>
          </CCard>
        ) : (
          ""
        )}
      </div>

      {modalShow && contratosSelecionados != null && (
        <FormCancelarNegociacaoModal
          idAcordo={contratosSelecionados.idAcordo}
          id={contratosSelecionados.id}
          contratoSelecionado={contratosSelecionados}
          isOpen={modalShow}
          onClose={handleClose}
        />
      )}
      {modalEnviarEmailShow && contratosSelecionados != null && (
        <FormEnviarEmailModal
          idAcordo={contratosSelecionados.idAcordo}
          id={contratosSelecionados.id}
          isOpen={modalEnviarEmailShow}
          onClose={handleClose}
        />
      )}
    </div>
  );
};

export default CardAcordos;
