import React, { useState, useEffect } from "react";
import { <PERSON>utton, CCardBody, CBadge, CCardHeader } from "@coreui/react";
import { GET_DATA, POST_DATA } from "src/api";
import TableSelectItens from "src/reusable/TableSelectItens";
import CardLoading from "src/reusable/CardLoading";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import { toast } from "react-toastify";
import AdicionarEmail from "./CardContratos/OutrosModals/AdicionarEmail";
import EditarEmail from "./CardContratos/OutrosModals/EditarEmail";
import { getTiposEmails } from "src/reusable/functions";
import { useAuth } from "src/auth/AuthContext";

const CardEmails = ({ atualizarCard = "", onHandleSave }) => {
  const { checkPermission, inforPermissions } = useAuth();

  const permissao = {
    modulo: "Tela Principal",
    submodulo: "Email",
  };

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const [tableData, setTableData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showModalAdicionar, setShowModalAdicionar] = useState(false);
  const [showModalEditar, setShowModalEditar] = useState(false);
  const [tipoEmail, setTipoEmail] = useState([]);
  const [email, setEmail] = useState(null);

  const handleCloseAddModal = () => {
    setShowModalAdicionar(false);
  };

  const handleCloseEditModal = () => {
    setShowModalEditar(false);
  };

  const handleTipoEmail = async () => {
    const res = await getTiposEmails();
    setTipoEmail(res);
  };

  const handleAdicionar = async (newData) => {
    const postSuccess = await dataPost(newData);
    if (postSuccess.success) {
      const data = (await getDadosFinanciado(financiadoData.id_Financiado, financiadoData.numero_Contrato)) ?? null;
      localStorage.setItem("clientData", JSON.stringify(data));

      if (onHandleSave) onHandleSave();
      toast.success("Dados do financiado alterados com sucesso!");
    } else {
      toast.warning(postSuccess.message);
    }
  };

  const dataPost = async (newData) => {
    const data = {
      cpfCnpj: financiadoData.cpfCnpj,
      idGrupo: financiadoData.id_Grupo,
      nome: financiadoData.nome,
      emails: [{ ...newData }],
    };
    const result = await POST_DATA("Datacob/DadosCadastrais/Financiado", data);
    return result;
  };

  const tableColumns = [
    {
      key: "ordenacao",
      label: " ",
      defaultSortColumn: true,
      defaultSort: "ascending",
      className: "text-white",
      style: { display: "none" },
      cellStyleCondicional: (_) => ({ display: "none" }),
    },
    {
      key: "status_Email",
      label: "Status",
      formatterByObject: (item) => renderStatus(item),
    },
    { key: "endereco_Email", label: "Email" },
    {
      key: "action",
      label: "Alterar status",
      formatterByObject: (item) => renderAction(item),
    },
  ];

  const IconButton = ({ icon, color, titulo, onClick }) => {
    return (
      <CButton
        title={titulo}
        className="mr-1"
        style={{
          border: "solid 1px",
          borderColor: color,
          color: color,
          padding: "2px 4px",
        }}
        onClick={onClick}
      >
        <i className={icon} />
      </CButton>
    );
  };

  const ordenacaoEmails = (items) => {
    const clientEmailsSortData = items.emails.map((item) => {
      item.ordenacao = ordenacaoStatus(item);
      return item;
    });

    const sortEmails = clientEmailsSortData.sort((a, b) => {
      if (a.ordenacao < b.ordenacao) {
        return -1;
      }
      if (a.ordenacao > b.ordenacao) {
        return 1;
      }
      return 0;
    });
    return sortEmails;
  };

  const handleChangeStatus = async (item, status_Email) => {
    const data = {
      cpfCnpj: financiadoData.cpfCnpj,
      idGrupo: financiadoData.id_Grupo,
      nome: financiadoData.nome,
      emails: [
        {
          enderecoEmail: item.endereco_Email,
          tipoEmail: item.id_Tipo_Email,
          status: status_Email,
          malaDireta: true,
        },
      ],
    };

    const postSuccess = await enderecoPost(data);
    if (postSuccess) {
      const updateEmail = await getDadosFinanciado(financiadoData.id_Financiado, financiadoData.numero_Contrato);
      localStorage.setItem("clientData", JSON.stringify(updateEmail));

      const sortEmails = ordenacaoEmails(updateEmail);

      setTableData(sortEmails);
      toast.info("Status do email alterado com sucesso");
    } else {
      console.log("Erro no POST");
    }
  };

  const enderecoPost = async (data) => {
    const result = await POST_DATA("Datacob/DadosCadastrais/Financiado", data);
    return result.success;
  };

  const getDadosFinanciado = async () => {
    setIsLoading(true);
    const data = { IdFinanciado: financiadoData.id_Financiado, numeroContrato: financiadoData.numero_Contrato };
    const newDadosFinanciados = await GET_DATA(
      "Datacob/DadosFinanciadoAdjacente",
      data
    );

    if (newDadosFinanciados && newDadosFinanciados.emails) {
      localStorage.setItem("clientData", JSON.stringify(newDadosFinanciados));
      const sortEmails = ordenacaoEmails(newDadosFinanciados);
      setTableData(sortEmails);
    }

    if (tipoEmail?.length ?? 0) await fetchData();

    setIsLoading(false);
    return newDadosFinanciados;
  };

  const fetchData = async () => {
    try {
      await Promise.all([handleTipoEmail()]);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(async () => {
    if (financiadoData) {
      if ((tableData?.length ?? 0) == 0 || !isLoading)
        await getDadosFinanciado(financiadoData.id_Financiado, financiadoData.numero_Contrato);
      atualizarCard = "";
    }
  }, [atualizarCard]);

  const ordenacaoStatus = (item) => {
    return item.status_Email === 1 ? 2 : item.status_Email === 2 ? 1 : 3;
  };

  const renderStatus = (item) => {
    return item.status_Email === 1 ? (
      <CBadge color={"success"}>Ativo</CBadge>
    ) : item.status_Email === 2 ? (
      <CBadge color={"info"}>Efetivo</CBadge>
    ) : (
      <CBadge color={"danger"}>Inativo</CBadge>
    );
  };

  const renderAction = (item) => {
    return (
      <div style={{ display: "flex" }}>
        <IconButton
          icon={"cil-warning"}
          color={"blue"}
          titulo={"Efetivar"}
          onClick={() => handleChangeStatus(item, 2)}
        />
        <IconButton
          icon={"cil-warning"}
          color={"red"}
          titulo={"Inativar"}
          onClick={() => handleChangeStatus(item, 0)}
        />
        <IconButton
          icon={"cil-check"}
          color={"green"}
          titulo={"Ativar"}
          onClick={() => handleChangeStatus(item, 1)}
        />
        <IconButton
          icon={"cil-pencil"}
          color="secondary"
          onClick={() => {
            setEmail(item);
            setShowModalEditar(true);
          }}
          size="sm"
          className="mr-2"
          title={
            !item
              ? "Nenhum financiado Selecionado"
              : inforPermissions(permissao).edit
          }
          disabled={
            !item ||
            !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
          }
        />
      </div>
    );
  };
  return (
    <>
      <CCardHeader style={{ display: "flex", alignItems: "center" }}>
        <i className="cil-envelope-open mr-2" />
        Histórico de Emails
        <div className="mr-10" style={{ marginLeft: "auto" }}>
          <CButton
            className="btn-custom-outline-green"
            icon="cil-plus"
            color={"green"}
            titulo={"Cadastrar Novo Email"}
            onClick={() => {
              fetchData();
              setShowModalAdicionar(true);
            }}
            title={
              !financiadoData
                ? "Nenhum financiado Selecionado"
                : inforPermissions(permissao).create
            }
            disabled={
              !financiadoData ||
              !checkPermission(permissao.modulo, "Create", permissao.submodulo)
            }
          >
            <i className="cil-plus"></i>Cadastrar Novo Email
          </CButton>
          {showModalAdicionar && (
            <AdicionarEmail
              isOpen={showModalAdicionar}
              onClose={handleCloseAddModal}
              onSave={handleAdicionar}
              tipoEmail={tipoEmail}
            />
          )}
        </div>
      </CCardHeader>
      {tableData && tableData.length > 0 ? (
        <CCardBody className="mt-2">
          {isLoading ? (
            <CardLoading msg={"Atualizando..."} />
          ) : (
            <TableSelectItens
              data={tableData}
              columns={tableColumns}
              onSelectionChange={(_) => {}}
              defaultSelectedKeys={[]}
              selectable={false}
              heightParam="290px"
            />
          )}
          {showModalEditar && (
            <EditarEmail
              onClose={handleCloseEditModal}
              isOpen={showModalEditar}
              editData={email}
              onEdit={handleAdicionar}
              tipoEmail={tipoEmail}
            />
          )}
        </CCardBody>
      ) : (
        <CCardBody>
          <NaoHaDadosTables />
        </CCardBody>
      )}
    </>
  );
};

export default CardEmails;
