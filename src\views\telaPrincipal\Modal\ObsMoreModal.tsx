import React from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CButton,
  CModalTitle,
} from "@coreui/react";
import "react-toastify/dist/ReactToastify.css";

const ObsMoreModal = ({
  onClose,
  obs,
}: {
  obs: string;
  onClose: () => void;
}) => {
  return (
    <CModal
      show={true}
      onClose={onClose}
      closeOnBackdrop={false}
      className="custom-modal"
      size="lg"
    >
      <CModalHeader>
        <CModalTitle>Observação</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <textarea
          className="form-control"
          rows={10}
          placeholder="Observação"
          value={obs}
          disabled
        ></textarea>
      </CModalBody>
      <CModalFooter>
        <CButton color="info" onClick={onClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default ObsMoreModal;
