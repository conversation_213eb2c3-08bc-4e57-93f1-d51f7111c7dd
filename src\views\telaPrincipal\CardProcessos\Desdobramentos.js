import React, { useState, useEffect } from "react";
import { <PERSON>adge, CCardBody } from "@coreui/react";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";
import LoadingComponent from "src/reusable/Loading";
import TableSelectItens from "src/reusable/TableSelectItens";

const Desdobramentos = ({ pasta, selected, idProcesso }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [first, setFirst] = useState(true);

  const payload = {
    IdProcess: idProcesso || null,
  };

  const updateView = (_) => {
    if (payload.IdProcess !== null) {
      setFirst(true);
      setIsLoading(true);
      getDesdobramentos(payload, "getdesdobramentosprojuris")
        .then((data) => {
          if (data && data.length > 0) {
            // const updatedTableData = data.map((item) => ({
            //   ...item,
            //   numero_atual_processo: processos[0].numero_atual_processo,
            //   jurisdicao_Atual: processos[0].jurisdicao_Atual,
            //   comarca: processos[0].comarca,
            //   tribunal: processos[0].tribunal,
            // }));
            // setTableData(data)
            const listaDistinta = data.filter(
              (objeto, index, self) =>
                index ===
                self.findIndex(
                  (o) =>
                    o.data_Cadastro === objeto.data_Cadastro &&
                    o.fase === objeto.fase
                )
            );
            setTableData(listaDistinta);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  };

  const getDesdobramentos = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const tableColumns = [
    { key: "atual", label: "Atual" },
    { key: "data_Cadastro", label: "Data" },
    // {
    //   key: "documento_Principal_Adverso",
    //   label: "Núm. Processo",
    // },
    { key: "fase", label: "Fase" },
    { key: "jurisdicao", label: "Jurisdição" },
    { key: "comarca", label: "Comarca" },
    { key: "uf", label: "UF" },
    { key: "tribunal", label: "Tribunal" },
  ];

  useEffect(() => {
    if (selected === true && pasta && tableData.length === 0 && first) {
      updateView();
    }
  }, [selected]);

  useEffect(() => {
    setFirst(true);
    setTableData([]);
  }, [pasta]);

  return (
    <CCardBody>
      {isLoading ? (
        <>
          <LoadingComponent
            text={
              "Aguarde! Buscando dados no Projuris! Isso pode demorar um pouco."
            }
          />
        </>
      ) : (
        <TableSelectItens
          data={tableData}
          columns={tableColumns}
          onSelectionChange={(_) => {}}
          defaultSelectedKeys={[]}
          selectable={false}
          heightParam="290px"
        />
      )}
    </CCardBody>
  );
};

export default Desdobramentos;
