import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalFooter,
  CRow,
  CCol,
  CCard,
  CButton,
  CModalHeader,
} from "@coreui/react";

const DetalheContratoModal = ({ isOpen, onClose, dados }) => {
  const handleClose = () => {
    onClose();
  };

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="">
      <CModalHeader>
        <CCol style={{ textAlign: "center" }}>Informações Valor</CCol>
      </CModalHeader>
      <CModalBody>
        <CRow>
          <CCol className="pr-1">
            <strong>Nome:</strong>&nbsp;
            <label>{dados?.nome}</label>
          </CCol>
        </CRow>
        <hr />
        <CRow>
          <CCol className="pr-1">
            <strong>Valor:</strong>&nbsp;
            <label>{dados?.valor}</label>
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default DetalheContratoModal;
