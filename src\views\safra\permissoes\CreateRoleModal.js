import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CFormGroup,
  CLabel,
} from "@coreui/react";
import Select from "react-select";
import { getApi } from "src/reusable/functions";
import { toast } from "react-toastify";
import { postManager } from "src/reusable/helpers.js";

function CreateRoleModal({ isOpen, usersExist, onRoleCreate, onClose }) {
  const [userList, setUserList] = useState([]);
  const [userSelected, setUserSelected] = useState(null);
  const [perms, setPerms] = useState({
    view: false,
    create: false,
    edit: false,
    remove: false,
  });
  
  const token = localStorage.getItem("token");

  const handleCreateRole = () => {
    if (userSelected === null) {
      toast.warning(`Por favor preencha o nome da função.`);
      return;
    }
    onRoleCreate({
      userid: userSelected.id,
      ...perms,
    });
    handleClose();
  };

  const handleClose = () => {
    onClose();
  };

  const getUsers = async () => {
    try {
      let response = await getApi(null, "getAllUsersSimplified");
      if (response?.length > 0) {
        response = await Promise.all(response.map(async (user) => {
          user.email = await postManager(token, user.email, 2);
          user.name = await postManager(token, user.name, 2);
          return user;
        }));

        setUserList(response.filter((x) => !usersExist.includes(x.id)));
      }
      else setUserList([]);
    } catch (error) {
      console.warn("error", error);
    }
  };

  useEffect(() => {
    getUsers();
  }, []);

  return (
    <CModal show={isOpen} onClose={handleClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>
        <CModalTitle> {"Adicionar nova Permissão"} </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CFormGroup className={"text-left"}>
          <CLabel>Usuário</CLabel>
          <Select
            options={userList}
            getOptionLabel={(option) => option.name}
            getOptionValue={(opt) => opt.id}
            placeholder={"Selecione..."}
            onChange={(e) => setUserSelected(e)}
          />
          {userSelected && (
            <div className="mt-2 text-center d-flex justify-content-center mr-4">
              <div className="mr-2">
                <input
                  type="checkbox"
                  id="view"
                  style={{ width: "50px", textAlign: "center" }}
                  onChange={(e) =>
                    setPerms({ ...perms, view: e.target.checked })
                  }
                />
                <label for="view">Ver</label>
              </div>
              <div className="mr-2">
                <input
                  id={"create"}
                  type="checkbox"
                  style={{ width: "50px", textAlign: "center" }}
                  onChange={(e) =>
                    setPerms({ ...perms, create: e.target.checked })
                  }
                />
                <label for="create">Criar</label>
              </div>
              <div className="mr-2">
                <input
                  type="checkbox"
                  id="edit"
                  style={{ width: "50px", textAlign: "center" }}
                  onChange={(e) =>
                    setPerms({ ...perms, edit: e.target.checked })
                  }
                />
                <label for="edit">Editar</label>
              </div>
              <div className="mr-2">
                <input
                  type="checkbox"
                  id="remove"
                  style={{ width: "50px", textAlign: "center" }}
                  onChange={(e) =>
                    setPerms({ ...perms, remove: e.target.checked })
                  }
                />
                <label for="remove">Deletar</label>
              </div>
            </div>
          )}
        </CFormGroup>
      </CModalBody>
      <CModalFooter>
        <CButton color="primary" onClick={handleCreateRole}>
          Salvar
        </CButton>
      </CModalFooter>
    </CModal>
  );
}

export default CreateRoleModal;
