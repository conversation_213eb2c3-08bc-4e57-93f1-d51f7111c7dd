import { CButton } from "@coreui/react";
import { GET_DOWNLOAD, MountURIWebSocket } from "src/api";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { getUriWebSocket } from "./apiConfig";

const CustomToast = ({ closeToast, fileName }) => (
  <div>
    <div>O documento {fileName} está pronto para ser baixado.</div>
    <CButton
      className="py-0"
      color="info"
      size="sm"
      onClick={() => {
        downloadFile(fileName);
        closeToast();
      }}
    >
      Baixar documento
    </CButton>
  </div>
);

const downloadFile = async (fileName) => {
  await GET_DOWNLOAD(`Projuris/Documentos/Download/${fileName}`);
};

const showCustomToast = (message) => {
  toast.success(<CustomToast fileName={message} />, {
    autoClose: false,
    closeOnClick: false,
    newestOnTop: true,
  });
};

function reactivateWS() {
  webSocketDocService.initialize();
}

class WebSocketDocService {
  constructor() {
    this.socket = null;
  }

  initialize() {
    const token = window.localStorage.getItem("token");

    if (!this.socket) {
      this.socket = new WebSocket(MountURIWebSocket(getUriWebSocket("wsProjuris"), { AccessToken: token }, true));

      this.socket.onerror = function () {       
        console.clear();
      };
      this.socket.addEventListener("open", () => {
        this.send(`{"protocol":"json","version":1}`);
      });

      this.socket.addEventListener("message", (event) => {
        let message = event.data;
        message = message.slice(0, -1);
        try {
          const parsedMessage = JSON.parse(message);
          if (parsedMessage.target === "ResponseDocument") {
            const fileName = parsedMessage.arguments[0].pathUrl;
            showCustomToast(fileName);
          }
        } catch (error) {
          console.error("Erro parsing JSON:");
        }
      });
      this.socket.addEventListener("close", (event) => {
        if (event.wasClean) {
          console.log("Conexão com WebSocket fechada sem problemas:");
        } else {
          console.error("Conexão com WebSocket fechada de forma inesperada:");
          reactivateWS();
        }
        this.socket = null;
      });
      this.socket.addEventListener("error", (error) => {
        console.error("WebSocket error:");
      });
    }
  }

  send(message) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(message);
    }
  }

  close() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }
}

const webSocketDocService = new WebSocketDocService();
export default webSocketDocService;
