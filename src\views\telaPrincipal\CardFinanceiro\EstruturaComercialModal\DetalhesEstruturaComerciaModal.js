import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CCol,
  CRow,
} from "@coreui/react";
import { GET_DATA } from "src/api";
import { formatThousands, formatDate } from "src/reusable/helpers";
import LoadingComponent from "src/reusable/Loading";
import { getURI } from "src/config/apiConfig";
import TableSelectItens from "src/reusable/TableSelectItens";
import CardLoading from "src/reusable/CardLoading";

const DetalhesEstruturaComercialModal = ({ isOpen, onClose, item }) => {
  const [tableData, setTableData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleClose = () => {
    onClose();
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    if (isOpen && item) {
      setIsLoading(true);
      GetData(
        `/${item.idEstruturaComercial}/${item.idComissionado}`,
        "getNewConEstruturaDetalhes"
      )
        .then((data) => {
          setTableData(data);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal"
      size="lg"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Estrura Comercial</h5>
      </CModalHeader>
      <CModalBody>
        {isLoading ? (
          <CardLoading />
        ) : (
          <>
            {/* <table className="table table-hover calculo">
              <tbody> */}
                <CRow>
                  <CCol md="6" style={{ textAlign: "end" }}>
                    <strong>Estrutura Comerical:</strong>
                  </CCol>
                  <CCol md="6">
                    {tableData?.commercialStructure
                      ? tableData?.commercialStructure
                      : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol md="6" style={{ textAlign: "end" }}>
                    <strong>Comissionado:</strong>
                  </CCol>
                  <CCol md="6">
                    {tableData?.comissioned ? tableData?.comissioned : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol md="6" style={{ textAlign: "end" }}>
                    <strong>E-mail:</strong>
                  </CCol>
                  <CCol md="6">
                    {tableData?.email ? tableData?.email : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol md="6" style={{ textAlign: "end" }}>
                    <strong>Telefone:</strong>
                  </CCol>
                  <CCol md="6">
                    {tableData?.phone ? tableData?.phone : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol md="6" style={{ textAlign: "end" }}>
                    <strong>Vigência:</strong>
                  </CCol>
                  <CCol md="6">
                    {tableData?.vigency ? tableData?.vigency : "---"}
                  </CCol>
                </CRow>
              {/* </tbody>
            </table> */}
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default DetalhesEstruturaComercialModal;
