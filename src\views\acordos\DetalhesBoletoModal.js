import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  <PERSON>ow,
  CCol,
  CCard,
  CButton,
} from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";
import ReenvioBoletoModal from "./ReenvioBoletoModal";
import CancelarBoletoModal from "./CancelarBoletoModal";
import { useAuth } from "src/auth/AuthContext";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";

const DetalhesBoletoModal = ({
  isOpen,
  onClose,
  updateModal,
  dados,
  parcela,
}) => {
  const { checkPermission, inforPermissions } = useAuth();

  const permissaoNegociacaoBoleto = {
    modulo: "Negociação",
    submodulo: "Boleto",
  };

  const acordos = localStorage.getItem("acordos")
    ? JSON.parse(localStorage.getItem("acordos"))
    : "";

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";

  const userData = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const [tableDataL, setTableDataL] = useState(null);
  const [tableDataR, setTableDataR] = useState(null);
  const [idBoleto, setIdBoleto] = useState(null);
  const [boletoData, setBoletoData] = useState([]);
  const [showBoletoModal, setShowBoletoModal] = useState(false);
  const [showModalCancelarBoleto, setShowModalCancelarBoleto] = useState(false);
  const [isAcordo, setIsAcordo] = useState(false);

  const handleClose = () => {
    onClose("DetalhesBoleto");
  };

  const handleCancelarBoleto = () => {
    setShowModalCancelarBoleto(true);
  };

  const handleGerarBoleto = () => {
    if (dados.status === "P") {
      toast.info(
        "Esta negociação esta com o status Pago, impossibilitando a emissão de outro boleto."
      );
      return;
    }
    if (dados.status === "C") {
      toast.warning("Este boleto está cancelado");
      return;
    } else {
      const dateNow = new Date(new Date().setHours(-3)).toISOString();
      const dataBoleto = {
        dataVencimento: dados.dt_Venc,
        vlTotal: dados.vl_Boleto,
        dtNegociacao: dateNow,
        idAgrupamento: financiadoData.id_Agrupamento,
        IdBoleto: dados.id_Boleto,
        idGrupo: financiadoData.id_Grupo,
      };
      setBoletoData(dataBoleto);
      setShowBoletoModal(true);
      return;
    }
  };

  async function getFuncoesSupervisorasConfig() {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "funcoes_supervisoras"
    );
    try {
      return JSON.parse(response);
    } catch (err) {
      return [];
    }
  }

  const [allowCancelTicket, setAllowCancelTicket] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (
        dados?.dt_Venc < new Date().toISOString().slice(0, 10) &&
        dados?.status === "A"
      ) {
        setAllowCancelTicket(true);
      } else {
        if (
          dados?.cobrador_Gerador !== null &&
          dados?.cobrador_Gerador !== undefined
        ) {
          getFuncoesSupervisorasConfig().then((funcoes) => {
            if (funcoes.length > 0 && funcoes.indexOf(userData?.role.id) > -1) {
              setAllowCancelTicket(true);
            } else if (dados?.cobrador_Gerador === userData?.username) {
              setAllowCancelTicket(true);
            }
          });
        }
      }

      setIdBoleto(dados?.id_Boleto);
      setIsAcordo(acordos !== null);
      setTableDataL([
        {
          label: "Banco",
          value: dados.banco,
        },
        {
          label: "Conta",
          value: dados.conta,
        },
        {
          label: "Moeda",
          value: dados.moeda,
        },
        {
          label: "Fase",
          value: dados.fase,
        },
        {
          label: "Número Boleto",
          value: dados.nr_Boleto,
        },
        {
          label: "Nosso Número",
          value: dados.nosso_Nr,
        },
        {
          label: "Valor Boleto",
          value: dados.vl_Boleto ? formatThousands(dados.vl_Boleto) : "",
        },
        {
          label: "Tx Bancária",
          value: dados.vl_Tx_Banc ? formatThousands(dados.vl_Tx_Banc) : "",
        },
        {
          label: "Data Vencimento",
          value: dados.dt_Venc ? formatDate(dados.dt_Venc) : "",
        },
        {
          label: "Data Pagamento",
          value: dados.dt_Pago ? formatDate(dados.dt_Pago) : "",
        },
        {
          label: "Valor Pago",
          value: dados.vl_Pago ? formatThousands(dados.vl_Pago) : "",
        },
        {
          label: "Status",
          value: dados.status,
        },
        {
          label: "Data Status",
          value: dados.dt_Status ? formatDate(dados.dt_Status) : "",
        },
        {
          label: "Tipo de Envio",
          value: renderTipoEnvio(dados.tipo_Envio),
        },
      ]);
      setTableDataR([
        {
          label: "E-mail",
          value: dados.email_Bol,
        },
        {
          label: "Núm. Teleone",
          value: dados.telfax,
        },
        {
          label: "Aos Cuidados",
          value: dados.cuidados,
        },
        {
          label: "Boleto Sobre",
          value: dados.bol_Sobre,
        },
        {
          label: "Motivo Canc",
          value: dados.descricao_Motivo_Cancel,
        },
        {
          label: "Descrição Canc",
          value: dados.descricao_Motivo_Cancel,
        },
        {
          label: "Dt Inconsistência",
          value: dados.dt_Inco ? formatDate(dados.dt_Inco) : "",
        },
        {
          label: "Dt Agendamento",
          value: dados.dt_Agenda_Envio ? formatDate(dados.dt_Agenda_Envio) : "",
        },
        {
          label: "Usuário Cancelou",
          value: dados.user_canc,
        },
        {
          label: "Dt Cancelou",
          value: dados.dt_canc ? formatDate(dados.dt_canc) : "",
        },
        {
          label: "Usuário Reativou",
          value: dados.user_reat,
        },
        {
          label: "Dt Reativou",
          value: dados.dt_reat ? formatDate(dados.dt_reat) : "",
        },
        {
          label: "Situação de Registro",
          value: dados.reg_sit,
        },
        {
          label: "Dt Situação Registro",
          value: dados.dt_reg_sit ? formatDate(dados.dt_reg_sit) : "",
        },
      ]);
    }
  }, [isOpen]);

  const renderStatus = (value) => {
    switch (value) {
      case "P":
        return "Pago";
      case "D":
        return "Devolvido";
      case "A":
        return isAcordo ? "Acordo" : "Aberto";
      case "C":
        return "Cancelado";
      case "Ab":
        return "Aberto";
      default:
        break;
    }
  };

  const renderBoletoSobre = (value) => {
    switch (value) {
      case "P":
        return "Parcela";
      case "D":
        return "Devolvido";
      case "A":
        return isAcordo ? "Acordo" : "Aberto";
      case "Ab":
        return "Aberto";
      default:
        break;
    }
  };

  const renderTipoEnvio = (value) => {
    switch (value) {
      case 4:
        return "SMS";
      case 1:
        return "E-mail";
      case 99:
        return "Impressão";
      default:
        return value;
    }
  };

  const handleBoletoCancelado = (sucesso) => {
    if (sucesso) {
      updateModal(sucesso);
    }
  };

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="xl">
      <CModalHeader closeButton>Detalhes do Boleto</CModalHeader>
      <CModalBody>
        {tableDataL && (
          <>
            <CRow>
              <CCol className="pr-1">
                <CCard>
                  <table className="table table-hover calculo">
                    <tbody>
                      {tableDataL.map((row) => (
                        <tr key={row.label}>
                          <td>{row.label}</td>
                          <td>
                            {row.label === "Status"
                              ? renderStatus(row.value)
                              : row.value}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </CCard>
              </CCol>
              <CCol className="pl-1">
                <CCard>
                  <table className="table table-hover calculo">
                    <tbody>
                      {tableDataR.map((row) => (
                        <tr key={row.label}>
                          <td>{row.label}</td>
                          <td>
                            {row.label === "Boleto Sobre"
                              ? renderBoletoSobre(row.value)
                              : row.value}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </CCard>
              </CCol>
            </CRow>
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton
          color="success"
          onClick={handleGerarBoleto}
          title={inforPermissions(permissaoNegociacaoBoleto).create}
          disabled={
            !checkPermission(
              permissaoNegociacaoBoleto.modulo,
              "Create",
              permissaoNegociacaoBoleto.submodulo
            )
          }
        >
          Reenviar Boleto
        </CButton>
        <CButton
          color="danger"
          onClick={handleCancelarBoleto}
          title={inforPermissions(permissaoNegociacaoBoleto).delete}
          disabled={
            !checkPermission(
              permissaoNegociacaoBoleto.modulo,
              "Delete",
              permissaoNegociacaoBoleto.submodulo
            ) || !allowCancelTicket
          }
        >
          Cancelar Boleto
        </CButton>
        <CButton color="secondary" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
      <CancelarBoletoModal
        isOpen={showModalCancelarBoleto}
        onClose={() => setShowModalCancelarBoleto(false)}
        boleto={idBoleto}
        onSubmit={handleBoletoCancelado}
      />
      <ReenvioBoletoModal
        isOpen={showBoletoModal}
        onClose={() => setShowBoletoModal(false)}
        dados={boletoData}
      />
    </CModal>
  );
};

export default DetalhesBoletoModal;
