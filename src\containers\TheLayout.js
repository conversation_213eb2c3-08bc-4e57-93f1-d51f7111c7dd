import React, { useEffect } from "react";
import {
  The<PERSON>ontent,
  TheSidebar,
  // The<PERSON>ooter,
  TheHeader,
} from "./index";
import { useMyContext } from "src/reusable/DataContext";
import ErrorScren from "../reusable/ErrorScren";
import ErrorBoundary from "src/reusable/ErrorBoundary.tsx";

const TheLayout = () => {
  const context = useMyContext();
  const financiadoDados = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;
  const custas = localStorage.getItem("custas")
    ? JSON.parse(localStorage.getItem("custas"))
    : null;
  const custasProjuris = localStorage.getItem("custasProjuris")
    ? JSON.parse(localStorage.getItem("custasProjuris"))
    : null;
  const appSettings = localStorage.getItem("appSettings")
    ? JSON.parse(localStorage.getItem("appSettings"))
    : null;
  const processos = localStorage.getItem("processos")
    ? JSON.parse(localStorage.getItem("processos"))
    : null;
  const boletos = localStorage.getItem("boletos")
    ? JSON.parse(localStorage.getItem("boletos"))
    : null;

  useEffect(() => {
    async function fetchData() {
      if (financiadoDados !== null) context.updateData(financiadoDados);
      context.updateCustasProjuris(custas);
      context.updateCustas(custasProjuris);
      if (appSettings !== null) context.updateAppSettings(appSettings);
      if (processos !== null) context.updateProcessos(processos);
      context.updateBoletos(boletos);
    }
    fetchData();
  }, []);

  return (
    <div className="c-app c-default-layout">
      <TheSidebar />
      <ErrorBoundary errorElement={ErrorScren}>
        <div className="c-wrapper">
          <TheHeader />
          <div className="c-body">
            <TheContent />
          </div>
          {/* <TheFooter/> */}
        </div>
      </ErrorBoundary>
    </div>
  );
};

export default TheLayout;
