import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalHeader,
  CModalBody,
  CRow,
  CCol,
  CLabel,
} from "@coreui/react";
import { formatThousands, formatDate } from "src/reusable/helpers";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import TableSelectItens from "src/reusable/TableSelectItens";

const DetalhesDevolverModal = ({ isOpen, onClose, payload }) => {
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const fields = [
    {
      key: "idMoviment",
      label: "ID",
    },
    {
      key: "movimentName",
      label: "Movimento",
    },
    {
      key: "dtAccounting",
      label: "Data Contabilização",
      formatter:(item) => formatDate(item)
    },
    {
      key: "dtRevenue",
      label: "Data Rendimento",
      formatter:(item) => formatDate(item)
    },
    {
      key: "vlLaunch",
      label: "Valor",
      formatter:(item) => formatThousands(item)
    },
  ];

  const PostData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const updateView = () => {
    setIsLoading(true);
    PostData(payload, "postNewconValorDevolverDet")
      .then((data) => {
        if (data) {
          setData(data.data);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      updateView();
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={true}
      size="lg"
      className="custom-modal"
      centered
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Consulta de Valores a Devolver Analítico</h5>
      </CModalHeader>
      <CModalBody>
        {isLoading ? (
          <CardLoading />
        ) : (
          <>
            <CRow className="mb-2">
              <CCol>
                <CLabel className="mr-2">Devolução de pagamento a maior</CLabel>
                <span>
                  {data?.bigPayment ? formatThousands(data.bigPayment) : "0,00"}
                </span>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Devolução do encerramento</CLabel>
                <span>
                  {data?.closure ? formatThousands(data.closure) : "0,00"}
                </span>
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <TableSelectItens
                  data={data?.details}
                  columns={fields}
                  onSelectionChange={(_) => {}}
                  defaultSelectedKeys={[]}
                  selectable={false}
                  heightParam="270px"
                />
              </CCol>
            </CRow>
          </>
        )}
      </CModalBody>
    </CModal>
  );
};

export default DetalhesDevolverModal;
