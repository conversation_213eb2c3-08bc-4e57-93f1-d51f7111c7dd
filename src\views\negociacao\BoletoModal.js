import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CButton,
  CForm,
  CFormGroup,
  CLabel,
  CInput,
  CCard,
  CRow,
  CCol,
  CCardBody,
  CCardHeader,
  CInputRadio,
} from "@coreui/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import Select from "react-select";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { useAuth } from "src/auth/AuthContext";
import {
  formatDate,
  formatThousands,
  formatCurrency,
} from "src/reusable/helpers";
import AlertaModal from "./AlertaModal";

import CardLoading from "src/reusable/CardLoading";
import LoadingComponent from "src/reusable/Loading";
import DadosBoletoModal from "./Parcial/DadosBoletoModal";
import {
  formatarTelefone,
  getApi,
  getApiInline,
  postApi,
} from "src/reusable/functions";
import DadosContatoModal from "./Parcial/DadosContatoModal";

const BoletoModal = ({
  isOpen,
  onClose,
  parcelas,
  dados,
  onSubmit,
  freeCalcValue = null,
  freeCalc = false,
  calcResponse = null,
}) => {
  const { userAtual } = useAuth();
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : "";

  const clientData = localStorage.getItem("clientData")
    ? JSON.parse(localStorage.getItem("clientData"))
    : "";

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";

  const [selectedTelefoneNumber, setSelectedTelefoneNumber] = useState(null);
  const [selectedTelefoneDDD, setSelectedTelefoneDDD] = useState(null);
  const [selectedTelefone, setSelectedTelefone] = useState("");
  const [telefoneList, setTelefoneList] = useState([]);

  const [email, setEmail] = useState("");
  const [selectedEmail, setSelectedEmail] = useState(null);
  const [emailList, setEmailList] = useState([]);

  const [selectedTipoEnvio, setSelectedTipoEnvio] = useState(null);
  const [descricao, setDescricao] = useState("");
  const [checkboxValue, setCheckboxValue] = useState(false);

  const [showAlertaModal, setShowAlertaModal] = useState(false);
  const [alertaMessage, setAlertaMessage] = useState("");

  const [selectedNegociador, setSelectedNegociador] = useState(null);
  const [negociadorOptions, setNegociadorOptions] = useState([]);

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [checkbuttonToSend, setCheckbuttonToSend] = useState(false);

  const [detalheModal, setDetalheModal] = useState(false);
  const [contatoModal, setContatoModal] = useState(false);

  const [cartRni, setCartRni] = useState("");
  const [rniPerm, setRniPerm] = useState(false);

  const handleTipoEnvioChange = (target) => {
    setSelectedTipoEnvio(target);
  };

  const handleTelefoneChange = (target) => {
    setSelectedTelefone(target);
    setSelectedTelefoneNumber(target.fone.trim());
    setSelectedTelefoneDDD(target.ddd.trim());
  };

  const handleEmailChange = (target) => {
    setSelectedEmail(target);
    setEmail(target.label);
  };

  const handleInputChange = (event) => {
    setEmail(event.target.value);
  };

  const handleDescricaoChange = (event) => {
    setDescricao(event.target.value);
  };

  const handleCheckboxChange = (event) => {
    setCheckboxValue(event.target.checked);
  };

  const impressaoBoleto = async (item) => {
    const data = { IdBoleto: item.idBoleto };
    const boleto = await GET_DATA("Datacob/DownloadBoleto", data);

    // Defina um título para a nova janela
    const windowTitle = "Visualização de PDF";

    // Crie uma nova janela com título
    const newWindow = window.open(
      "",
      "_blank",
      `width=800,height=600,toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,title=${windowTitle}`
    );

    // Escreva o conteúdo do PDF na nova janela
    newWindow.document.open();
    newWindow.document.write(
      '<embed width="100%" height="100%" src="data:application/pdf;base64,' +
        boleto +
        '" type="application/pdf"/>'
    );
    newWindow.document.close();
  };

  async function getParamFormaLivreConfig() {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "param_forma_livre"
    );
    try {
      return Number(response);
    } catch (err) {
      return 0;
    }
  }

  const boletoPost = async (boletoData) => {
    let formaDesconto = boletoData.formaDesconto;
    if (
      financiadoData.coddatacob === "Rodobens" &&
      financiadoData.id_Grupo === 3
    ) {
      formaDesconto = 2;
    }
    const payload = {
      ...boletoData,
      email: email,
      ddd: selectedTelefoneDDD,
      telefone: selectedTelefoneNumber,
      formaDesconto: formaDesconto,
      mensagem: descricao,
      usuarioNegociacao: selectedNegociador.value,
      tipoEnvio: selectedTipoEnvio.value,
    };
    const result = await POST_DATA("Datacob/Negociacoes/GerarBoleto", payload);
    return result;
  };

  const boletoCoringaPost = async (boletoData) => {
    try {
      const payNeg = {
        idAgrupamento: financiadoData.id_Agrupamento,
        parcelas: boletoData.parcelas,
        dtNegociacao: boletoData.dtNegociacao,
        valorPrincipal: freeCalcValue.mainValue,
        valorCorrecao: freeCalcValue.correctedValue,
        juros: freeCalcValue.fees,
        multa: freeCalcValue.fine,
        comissaoPermanencia: freeCalcValue.commission,
        honorarios: freeCalcValue.honor,
        descontoAutorizado: true,
        custas: freeCalcValue.costs,
        notificacao: freeCalcValue.notification,
        valorTarifa: freeCalcValue.fareValue,
      };

      const negRes = await postApi(payNeg, "postNegociacaoLivre");
      if (
        negRes?.success &&
        negRes?.data?.idNegociacao !== null &&
        negRes?.data?.idNegociacao !== undefined &&
        negRes?.data?.idNegociacao > 0
      ) {
        const payload = {
          idNegociacao: negRes?.data?.idNegociacao,
          idAgrupamento: financiadoData.id_Agrupamento,
          mensagemAdicional: descricao,
          usuarioNegociacao: selectedNegociador.value,
          idNegociacaoReplica: negRes?.data?.replica?.idNegociacao ?? null,
        };
        const result = await postApi(payload, "postGerarBoletoNegociacaoSalva");
        if (result?.success) {
          return { success: true, message: "Boleto gerado com sucesso!" };
        }
      }
    } catch (err) {
      console.warn("erro", err);
    }
    return { success: false, message: "Ocorreu um problema ao gerar boleto!" };
  };

  //GPT :)
  function formatParcelas(parcelas) {
    // Primeiro, ordena o array
    parcelas.sort((a, b) => a - b);

    let result = [];
    let inicio = parcelas[0];
    let fim = parcelas[0];

    for (let i = 1; i < parcelas.length; i++) {
      if (parcelas[i] === fim + 1) {
        fim = parcelas[i]; // continua a sequência
      } else {
        if (inicio === fim) {
          result.push(`${inicio}`); // se for único número
        } else {
          result.push(`${inicio} a ${fim}`); // se for uma sequência
        }
        inicio = fim = parcelas[i]; // reinicia a sequência
      }
    }

    // Adiciona a última sequência
    if (inicio === fim) {
      result.push(`${inicio}`);
    } else {
      result.push(`${inicio} a ${fim}`);
    }

    return `Sobre parcela: ${result.join(", ")}`;
  }

  const mountObs = (calc, boletoData) => {
    const contratos = [
      ...new Set(
        calc.parcelas
          .filter((x) => x.parcelaSelecionada)
          .map((x) => x.nrContrato)
      ),
    ];

    let obs =
      `Data Vencimento: ${formatDate(
        boletoData.dtNegociacao
      )} - Valor: ${formatCurrency(calc.vlNegociacao, false)} \n` +
      `Dt. de Envio: ${new Date().toLocaleString("pt-BR")} \n` +
      `Tipo de Envio: ${selectedTipoEnvio.label} \n` +
      `Email: ${email} \n` +
      `Descrição: ${descricao} \n` +
      `Carteira RNI: ${cartRni} \n`;

    for (const contr in contratos) {
      let parcelas = calc.parcelas.filter(
        (x) => x.parcelaSelecionada && x.nrContrato === contratos[contr]
      );

      obs += `\nContrato ${contratos[contr]} ${formatParcelas(
        parcelas.map((x) => parseInt(x.nrParcela))
      )}\n`;

      for (const numero in parcelas) {
        obs += `Parcela ${parcelas[numero].nrParcela} - Vencimento ${formatDate(
          parcelas[numero].dtVencimento
        )} \n`;
      }
    }

    const juros = calc.parcelas
      .filter((x) => x.parcelaSelecionada)
      .reduce((x, y) => x + y.vlJurosNegociado, 0);
    const multa = calc.parcelas
      .filter((x) => x.parcelaSelecionada)
      .reduce((x, y) => x + y.vlMultaNegociado, 0);
    const valorPrincipal = calc.parcelas
      .filter((x) => x.parcelaSelecionada)
      .reduce((x, y) => x + y.vlOriginal, 0);
    const valorHo = calc.parcelas
      .filter((x) => x.parcelaSelecionada)
      .reduce((x, y) => x + y.vlHoNegociado, 0);

    obs +=
      `\nValor Juros: ${formatCurrency(juros, false)} \n` +
      `Valor HO: ${formatCurrency(valorHo, false)} \n` +
      `Valor Multa: ${formatCurrency(multa, false)} \n` +
      `Valor Principal: ${formatCurrency(valorPrincipal, false)}`;
    return obs;
  };

  const sendOccurrence = async (boletoData) => {
    try {
      if (calcResponse === null) throw new Error();

      const calc = calcResponse.negociacaoDto[0];

      const tiposOcorrencia = await getApi({}, "getOcorrencias");
      if (
        tiposOcorrencia === null ||
        tiposOcorrencia === undefined ||
        tiposOcorrencia?.length === 0
      )
        throw new Error();

      const idOcorrencia = tiposOcorrencia.find(
        (x) => x.cod_Ocorr_Sistema === "020"
      )?.id_Ocorrencia_Sistema;

      if (idOcorrencia === null || idOcorrencia === undefined)
        throw new Error();

      const data = {
        login: user?.username,
        id_Contrato: financiadoData?.id_Contrato,
        id_Ocorrencia_Sistema: idOcorrencia,
        observacao: mountObs(calc, boletoData),
        complemento: formatDate(boletoData.dtNegociacao),
        telefones: [
          formatarTelefone(selectedTelefone?.ddd + selectedTelefone?.fone),
        ],
        telefoneParaRetorno: formatarTelefone(
          selectedTelefone?.ddd + selectedTelefone?.fone
        ),
      };
      const ocorrencia = await postApi(data, "postHistoricoAdicionar");
      if (ocorrencia.success) toast.info("Ocorrência do boleto gerada!");
      else throw new Error();
    } catch {
      toast.error("Falha ao gerar ocorrência do boleto!");
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);

    try {
      if (freeCalc) {
        const result = await boletoCoringaPost(dados);
        if (result.success) {
          toast.info("Boleto gerada com sucesso!");
          if (rniPerm) sendOccurrence(dados);
          onSubmit();
        } else {
          toast.warning(result.message);
        }
      } else {
        const result = await boletoPost(dados);
        if (result.success) {
          toast.success("Boleto gerado com sucesso!");
          if (rniPerm) sendOccurrence(dados);
          if (selectedTipoEnvio.value === "Impressao") {
            await impressaoBoleto(result.data);
          }
          onClose();
        } else {
          toast.warning(result.message);
        }
      }
    } catch (error) {
      console.error("An error occurred during form submission:", error);
      toast.warn(error);
    } finally {
      setIsLoading(false);
      onClose();
    }
  };

  const handleClose = () => {
    onClose();
  };

  const handleNegociadorChange = (target) => {
    setSelectedNegociador(target);
  };

  const handleAlertaClose = () => {
    setShowAlertaModal(false);
  };

  const getUsers = async () => {
    setIsLoadingUsers(true);
    const currentUser = user;
    const payload = { ActiveConnection: user.activeConnection };
    getData(payload, "getDatacobUsers")
      .then((data) => {
        if (data) {
          const options = data.map((x) => {
            return {
              label: x.nome,
              value: x.id_Usuario,
            };
          });
          setNegociadorOptions(options);
          const findUser = data.find((user) => currentUser.name === user.nome);
          if (findUser) {
            const negociador = {
              label: findUser.nome,
              value: findUser.id_Usuario,
            };
            setSelectedNegociador(negociador);
          }
        }
      })
      .catch((err) => {
        console.log(err);
      });
    setIsLoadingUsers(false);
  };

  const getData = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const getConfigRni = async () => {
    try {
      const config = await getApiInline(
        "ocorrencia_boleto_grupo",
        "getConfigUnicaByKey"
      );
      let perm = false;
      if (!config || config === undefined) {
        setRniPerm(false);
      } else {
        const grupos = JSON.parse(config);
        for (const item of user?.groups) {
          if (grupos.indexOf(item.id) > -1) {
            perm = true;
            break;
          }
        }
        setRniPerm(perm);
      }
    } catch (error) {
      setRniPerm(false);
    }
  };

  useEffect(() => {
    if (clientData) {
      setTelefoneList(clientData.telefones);
      setEmailList(clientData.emails);
      // setEmail(clientData.emails[0]);
    }
  }, []);

  useEffect(() => {
    if (isOpen) {
      getUsers();
      getConfigRni();
    }
  }, [isOpen]);

  const foneOptions = [
    ...telefoneList
      .filter((x) => x.status > 0)
      .map((x) => ({
        value: x.id_Telefone,
        label: x.ddd + x.fone,
        ddd: x.ddd,
        fone: x.fone,
      })),
  ];

  const emailOptions = [
    ...emailList.map((x) => ({
      value: x.id_Email,
      label: x.endereco_Email,
    })),
  ];

  const optionsTipoEnvio = [
    { value: "Email", label: "E-mail" },
    { value: "Impressao", label: "Impressão" },
  ];

  useEffect(() => {
    if (selectedTelefone.length === 0) {
      setCheckbuttonToSend(false);
      return false;
    }

    if (selectedTipoEnvio === null) {
      setCheckbuttonToSend(false);
      return false;
    }

    if (selectedTipoEnvio.value === "Email" && selectedEmail === null) {
      setCheckbuttonToSend(false);
      return false;
    }
    setCheckbuttonToSend(true);
  }, [email, selectedTipoEnvio, selectedTelefone]);

  return (
    <>
      <CModal size="lg" show={isOpen} onClose={onClose} closeOnBackdrop={false}>
        <CModalHeader closeButton>Emissão de Boleto</CModalHeader>
        <CModalBody>
          {isLoading ? (
            <CardLoading Title={"Gerando boleto..."} />
          ) : (
            <CRow>
              <CCol>
                <CCard>
                  <CCardHeader className="py-1">
                    Telefones do atendimento
                  </CCardHeader>
                  <CCardBody className="py-1">
                    <Select
                      placeholder="Selecione"
                      value={selectedTelefone}
                      options={foneOptions}
                      onChange={handleTelefoneChange}
                    />
                  </CCardBody>
                </CCard>
                <CCard>
                  <CCardHeader className="py-1">Dados de Envio</CCardHeader>
                  <CCardBody className="py-1">
                    <CForm>
                      <CFormGroup>
                        <CRow>
                          <CCol>
                            <CLabel>Tipo de Envio</CLabel>
                            <Select
                              value={selectedTipoEnvio}
                              options={optionsTipoEnvio}
                              placeholder="Selecione"
                              onChange={handleTipoEnvioChange}
                              // isDisabled
                            />
                          </CCol>
                        </CRow>
                        <CLabel>Enviar boleto como</CLabel>
                        <CFormGroup variant="checkbox">
                          <CInputRadio
                            id="pdf"
                            name="checkbox"
                            value="pdf"
                            checked={true}
                            onChange={handleCheckboxChange}
                            disabled
                          />
                          <CLabel htmlFor="pdf">PDF</CLabel>
                        </CFormGroup>
                        <CLabel>Emails disponíveis</CLabel>
                        <Select
                          value={selectedEmail}
                          options={emailOptions}
                          onChange={handleEmailChange}
                          placeholder="Selecione"
                        />
                        <CLabel>Email destinatário</CLabel>
                        <CInput
                          type="text"
                          value={email}
                          onChange={handleInputChange}
                        />
                      </CFormGroup>
                    </CForm>
                  </CCardBody>
                </CCard>
              </CCol>
              <CCol>
                <CCard>
                  <CCardBody
                    className="py-1 px-2"
                    style={{ minHeight: "180px" }}
                  >
                    {isLoadingUsers ? (
                      <CardLoading />
                    ) : (
                      <CForm>
                        <CFormGroup>
                          <CLabel className="mt-2">Nome do Negociador</CLabel>
                          <Select
                            value={selectedNegociador}
                            options={negociadorOptions}
                            onChange={handleNegociadorChange}
                            placeholder="Selecione"
                          />
                          <CLabel className="mt-2">Descrição</CLabel>
                          <CInput
                            type="text"
                            value={descricao}
                            onChange={handleDescricaoChange}
                          />
                          {rniPerm && (
                            <>
                              <CLabel className="mt-2">Carteina RNI</CLabel>
                              <CInput
                                type="text"
                                value={cartRni}
                                onChange={(e) => setCartRni(e.target.value)}
                              />
                            </>
                          )}
                        </CFormGroup>
                      </CForm>
                    )}
                  </CCardBody>
                </CCard>
                <CCard>
                  <CCardHeader>Valores</CCardHeader>
                  <CCardBody>
                    <CRow>
                      <CCol>
                        <CLabel>Valor Atualizado</CLabel> <br />
                        <CLabel>R$ {formatThousands(dados.vlNegociado)}</CLabel>
                      </CCol>
                      <CCol>
                        <CLabel>Taxa Bancária</CLabel> <br />
                        <CLabel>0,00</CLabel>
                      </CCol>
                    </CRow>
                    <CRow>
                      <CCol>
                        <CLabel>Total do Boleto</CLabel> <br />
                        <CLabel>R$ {formatThousands(dados.vlNegociado)}</CLabel>
                      </CCol>
                      <CCol>
                        <CLabel>Vencimento</CLabel> <br />
                        <CLabel>{formatDate(dados.dtNegociacao)}</CLabel>
                      </CCol>
                    </CRow>
                  </CCardBody>
                </CCard>
              </CCol>
            </CRow>
          )}
          <CRow>
            <CCol className="d-flex justify-content-end">
              <CButton
                color="primary"
                className="mr-2"
                onClick={() => setContatoModal(true)}
                disabled={isLoading}
              >
                Dados de Contato
              </CButton>
              <CButton
                color="warning"
                className="mr-2"
                onClick={() => setDetalheModal(true)}
                disabled={isLoading}
              >
                Prévia Boleto
              </CButton>
              <CButton
                color="secondary"
                className="mr-2"
                onClick={handleClose}
                disabled={isLoading}
              >
                Cancelar
              </CButton>
              <CButton
                type="submit"
                color="primary"
                onClick={handleSubmit}
                disabled={!checkbuttonToSend || isLoading}
              >
                {isLoading ? <LoadingComponent /> : "Ok"}
              </CButton>
            </CCol>
          </CRow>
        </CModalBody>
        <AlertaModal
          isOpen={showAlertaModal}
          onClose={handleAlertaClose}
          dados={alertaMessage}
        />
      </CModal>
      {detalheModal && (
        <DadosBoletoModal
          isOpen={detalheModal}
          onClose={() => setDetalheModal(false)}
          dados={dados}
          parcelas={parcelas}
        />
      )}
      {contatoModal && (
        <DadosContatoModal
          isOpen={contatoModal}
          onClose={() => setContatoModal(false)}
          clientData={clientData}
        />
      )}
    </>
  );
};

export default BoletoModal;
