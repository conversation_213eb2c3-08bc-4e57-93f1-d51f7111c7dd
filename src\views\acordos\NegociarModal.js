import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CCardBody,
  CDataTable,
  CInputCheckbox,
  CButton,
  CLabel,
} from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";
import { POST_DATA, GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { useAuth } from "src/auth/AuthContext";
import GerarBoletoAcordoModal from "./GerarBoletoAcordoModal";
// import BoletoModal from "../negociacao/BoletoModal";
import AlertaModal from "../negociacao/AlertaModal";
import ConfirmarDataModal from "./Partials/ConfirmarDataModal";
import TableSelectItens from "src/reusable/TableSelectItens";

import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const NegociarModal = ({ isOpen, onClose, acordo, dados }) => {
  const { checkPermission, inforPermissions } = useAuth();

  const permissaoNegociacaoBoleto = {
    modulo: "Negociação",
    submodulo: "Boleto",
  };

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const [diasPermitidosAcordo, setDiasPermitidosAcordo] = useState(null);
  const [parcelasBol, setParcelasBol] = useState([]);
  const [totalAtualizado, setTotalAtualizado] = useState(null);
  const [parcelasSel, setParcelasSel] = useState([]);

  const [boletoData, setBoletoData] = useState([]);
  const [showBoletoModal, setShowBoletoModal] = useState(false);
  const [showConfirmarDataModal, setShowConfirmarDataModal] = useState(false);
  const [confirmarDataMessage, setConfirmarDataMessage] = useState("");
  const [datas, setDatas] = useState("");

  const [selectedDate, setSelectedDate] = useState(new Date());
  const [primeiroVencimento, setPrimeiroVencimento] = useState(null);
  const [maxDate, setMaxDate] = useState(null);

  const [isLoading, setIsLoading] = useState(false);
  const [showAlertaModal, setShowAlertaModal] = useState(false);
  const [alertaMessage, setAlertaMessage] = useState("");

  const handleDateChange = (date) => {
    let nextWorkingDay = date;
    if (isWeekend(date)) {
      nextWorkingDay = calculateNextWorkingDay(date);
      const dias = {
        diaSelecionado: date,
        proximoDiaUtil: nextWorkingDay,
        diaLimite: maxDate,
      };
      setSelectedDate(date);
      setDatas(dias);
      setShowConfirmarDataModal(true);
    } else {
      setSelectedDate(date);
    }
  };

  const isWeekend = (date) => {
    const dayOfWeek = date.getDay();
    return dayOfWeek === 0 /* Domingo */ || dayOfWeek === 6 /* Sábado */;
  };

  const calculateNextWorkingDay = (date) => {
    const nextDay = new Date(date);
    while (isWeekend(nextDay)) {
      nextDay.setDate(nextDay.getDate() + 1);
    }
    return nextDay;
  };

  function calculateDaysDifference(date1, date2) {
    const utcDate1 = Date.UTC(
      date1.getFullYear(),
      date1.getMonth(),
      date1.getDate()
    );
    const utcDate2 = Date.UTC(
      date2.getFullYear(),
      date2.getMonth(),
      date2.getDate()
    );
    const timeDifference = utcDate1 - utcDate2;
    const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
    return daysDifference;
  }

  const parcelasColumns = [
    { key: "selecionar" },
    { key: "nr_Parcela", label: "Parcela" },
    { key: "nr_Plano", label: "Plano" },
    {
      key: "dt_Vencimento",
      label: "Vencimento",
    },
    {
      key: "vl_Parcela",
      label: "Valor Parcela",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "vl_Saldo", label: "Valor Saldo", _style: { whiteSpace: "nowrap" } },
    {
      key: "dt_Pagamento",
      label: "Dt. Pagamento.",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "atraso",
      label: "Dias Atraso",
      _style: { whiteSpace: "nowrap" },
      // formatterByObject:(item) => renderDiasAtraso(item)
    },
    {
      key: "vl_Atualizado",
      label: "Valor Atualizado",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dt_Atualizacao",
      label: "Dt. Atualização",
      _style: { whiteSpace: "nowrap" },
      // formatterByObject:(item) => renderDataAtualizacao(item)
    },
    {
      key: "dt_Venc_Boleto",
      label: "Dt. Vencimento Boleto",
      _style: { whiteSpace: "nowrap" },
    },
  ];
  const renderDataAtualizacao = (item) => {
    const dataVencimento = item.dt_Vencimento;
    if (parcelasBol.includes(item.nr_Parcela)) {
      return <td>{formatDate(selectedDate)}</td>;
    } else {
      return <td>{formatDate(dataVencimento)}</td>;
    }
  };

  const renderDiasAtraso = (item) => {
    const toDateObject = new Date(item.dt_Vencimento);
    if (selectedDate > toDateObject) {
      const diasAtraso = calculateDaysDifference(selectedDate, toDateObject);
      return <td>{diasAtraso}</td>;
    } else {
      return <td>0</td>;
    }
  };

  const handleCheckbox = (target, item) => {
    const number = parseInt(target.name, 10);

    setParcelasBol((prevSelected) => {
      if (prevSelected.includes(number)) {
        return prevSelected.filter((num) => num !== number);
      } else {
        const lastSelectedNumber = prevSelected[prevSelected.length - 1];
        if (number === lastSelectedNumber + 1) {
          return [...prevSelected, number];
        } else {
          alert(
            "Existem parcelas não selecionadas que não foram pagas, para acordo não é permitido negociar parcelas puladas."
          );
          return [dados[0].nr_Parcela];
        }
      }
    });
  };

  useEffect(() => {
    if (dados) {
      const selectedItems = dados.filter((item) =>
        parcelasBol.includes(item.nr_Parcela)
      );
      const newTotalValue = selectedItems.reduce(
        (total, item) => total + item.vl_Atualizado,
        0
      );
      setTotalAtualizado(newTotalValue);
    }
  }, [parcelasBol]);

  const handleGerarBoleto = () => {
    const data = {
      idAcordo: acordo,
      dataVencimento: selectedDate,
      vlTotal: totalAtualizado,
      parcelas: parcelasBol,
    };
    setBoletoData(data);
    setShowBoletoModal(true);
  };

  const getDaysDeal = async () => {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "dias_apos_venc_acordo"
    );
    try {
      const res = JSON.parse(response);
      return financiadoData?.coddatacob === "GVC" ? res?.gvc : res?.rodobens;
    } catch (err) {
      return 0;
    }
  };

  const updateView = async () => {
    const IdAcordo = acordo;
    setIsLoading(true);
    const response = await GetData(`/${IdAcordo}`, "getAcordoCalcData")
      .then(async (data) => {
        if (data) {
          setDiasPermitidosAcordo(data.dias_Stadby_Acordo);
          const limiteAcordo = new Date(dados[0].dt_Vencimento);
          const days = await getDaysDeal();
          limiteAcordo.setDate(limiteAcordo.getDate() + days);
          setMaxDate(limiteAcordo);
        } else {
          toast.info(response.message);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleClose = () => {
    onClose();
  };

  const handleAlertaClose = () => {
    setShowAlertaModal(false);
  };

  useEffect(() => {
    if (isOpen && dados && dados.length > 0) {
      setPrimeiroVencimento(new Date(dados[0].dt_Vencimento));
      setSelectedDate(new Date(dados[0].dt_Vencimento));
      setParcelasBol([dados[0].nr_Parcela]);
      updateView();
    }
  }, [isOpen]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="xl">
      <CModalHeader closeButton>Negociar</CModalHeader>
      <CModalBody>
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "flex-end",
            alignItems: "center",
          }}
        >
          <CLabel className="mr-2" style={{ marginBottom: "0" }}>
            Data para Cálculo:
          </CLabel>
          <ReactDatePicker
            selected={selectedDate}
            onChange={handleDateChange}
            className="form-control"
            minDate={primeiroVencimento}
            maxDate={maxDate}
            dateFormat="dd/MM/yyyy"
            disabled={isLoading}
          />
          {/* {dataCalculo()} */}
        </div>
        <div style={{ maxHeight: "400px", overflowX: "auto" }}>
          <div style={{ width: "fit-content", overflowY: "auto" }}>
            <CCardBody>
              {dados && (
                // <TableSelectItens
                //   data={dados}
                //   columns={parcelasColumns}
                //   onSelectionChange={HandleInstallmentChange}
                //   defaultSelectedKeys={dados}
                //   selectable={true}
                //   heightParam="480px"
                // />
                <CDataTable
                  items={dados}
                  fields={parcelasColumns}
                  scopedSlots={{
                    selecionar: (item) => (
                      <td style={{ textAlign: "center", paddingLeft: "40px" }}>
                        <CInputCheckbox
                          name={item.nr_Parcela}
                          onChange={(e) => handleCheckbox(e.target, item)}
                          checked={parcelasBol.includes(item.nr_Parcela)}
                        />
                      </td>
                    ),
                    dt_Vencimento: (item) =>
                      item.dt_Vencimento ? (
                        <td>{formatDate(item.dt_Vencimento)}</td>
                      ) : (
                        <td>---</td>
                      ),
                    dt_Pagamento: (item) =>
                      item.dt_Pagamento ? (
                        <td>{formatDate(item.dt_Pagamento)}</td>
                      ) : (
                        <td>---</td>
                      ),
                    dt_Venc_Boleto: (item) =>
                      item.dt_Venc_Boleto ? (
                        <td>{formatDate(item.dt_Venc_Boleto)}</td>
                      ) : (
                        <td>---</td>
                      ),
                    dt_Atualizacao: (item) => renderDataAtualizacao(item),
                    vl_Parcela: (item) =>
                      item.vl_Parcela ? (
                        <td>{formatThousands(item.vl_Parcela)}</td>
                      ) : (
                        <td>0,00</td>
                      ),
                    vl_Saldo: (item) =>
                      item.vl_Saldo ? (
                        <td>{formatThousands(item.vl_Saldo)}</td>
                      ) : (
                        <td>0,00</td>
                      ),
                    vl_Atualizado: (item) =>
                      item.vl_Atualizado ? (
                        <td>{formatThousands(item.vl_Atualizado)}</td>
                      ) : (
                        <td>0,00</td>
                      ),
                    atraso: (item) => renderDiasAtraso(item),
                  }}
                />
              )}
            </CCardBody>
          </div>
        </div>
      </CModalBody>
      <CModalFooter>
        {/* <CButton color="secondary" onClick={handleGerarRecibo}>
          Gerar Recibo
        </CButton> */}
        <CButton
          color="secondary"
          onClick={handleGerarBoleto}
          title={inforPermissions(permissaoNegociacaoBoleto).create}
          disabled={
            !checkPermission(
              permissaoNegociacaoBoleto.modulo,
              "Create",
              permissaoNegociacaoBoleto.submodulo
            )
          }
        >
          Gerar Boleto
        </CButton>
        <CButton color="secondary" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
      <GerarBoletoAcordoModal
        isOpen={showBoletoModal}
        onClose={() => setShowBoletoModal(false)}
        dados={boletoData}
      />
      <AlertaModal
        isOpen={showAlertaModal}
        onClose={handleAlertaClose}
        dados={alertaMessage}
      />
      <ConfirmarDataModal
        isOpen={showConfirmarDataModal}
        onClose={() => setShowConfirmarDataModal(false)}
        onSubmit={setSelectedDate}
        datas={datas}
      />
    </CModal>
  );
};

export default NegociarModal;
