import { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CNav,
  CCard,
  CTabs,
  CTabPane,
  CTabContent,
  CNavLink,
  CBadge,
  CCol,
  CNavItem,
  CRow,
  CTooltip,
  CDataTable,
  CButton,
  CInputCheckbox,
} from "@coreui/react";
import {
  formatDate,
  formatDocument,
  formatThousands,
} from "src/reusable/helpers";
import { GET_DATA } from "src/api";
import DetalhesBoletoModal from "src/views/acordos/DetalhesBoletoModal";
import DetalhesReciboModal from "src/views/acordos/DetalhesReciboModal";
import DetalhesNegociacaoModal from "./DetalhesNegociacaoModal";

import ComponenteContratos from "../ComponenteContratos";
import LoadingComponent from "src/reusable/Loading";
import CalculosNegociacaoModal from "src/views/acordos/CalculosNegociacaoModal";
import { toast } from "react-toastify";
import TableBoletos from "../Parcial/TableBoletos";

const NegociacoesModal = ({ isOpen, onClose, dados }) => {
  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";

  const [dadosNegociacao, setDadosNegociacao] = useState(null);
  const [parcelaDados, setParcelaDados] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);

  const [IdNegociacao, setIdNegociacao] = useState(null);
  const [idParcela, setIdParcela] = useState(null);
  const [numParcela, setNumParcela] = useState(null);
  const [idBoleto, setIdBoleto] = useState(null);

  const [tableBoletos, setTableBoletos] = useState([]);
  const [tableRecibos, setTableRecibos] = useState(null);

  const [showModalDetalhesBoleto, setShowModalDetalhesBoleto] = useState(false);
  const [showModalDetalhesRecibo, setShowModalDetalhesRecibo] = useState(false);
  const [detalhesBoleto, setDetalhesBoleto] = useState(null);
  const [detalhesRecibo, setDetalhesRecibo] = useState(null);

  const [showModalDetalhesNegociacao, setShowModalDetalhesNegociacao] =
    useState(false);
  const [detalhesNegociacao, setDetalhesNegociacao] = useState(null);

  const [currentTab, setCurrentTab] = useState("Boletos");
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingTable2, setIsLoadingTable2] = useState(false);

  const [downBol, setDownBol] = useState(false);

  const rowClassName = (item) => {
    return item.id_Negociacao === selectedRow ? "selected-row" : "";
  };

  const acordosFields = [
    { key: "id_Negociacao", label: "ID" },
    { key: "status" },
    { key: "liberado", label: "Liberado" },
    { key: "descricao" },
    {
      key: "dt_Negociacao",
      label: "Data Negociação",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "vl_Negociacao",
      label: "Valor Negociação",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "calc_todas", _style: { whiteSpace: "nowrap" } },
    { key: "calc_parcelas", _style: { whiteSpace: "nowrap" } },
    { key: "negociacao", label: "Negociação" },
    {
      key: "userBoleto",
      label: "Nome Operador Gerador",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "userNegociacao",
      label: "Nome do Negociador",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "mensagem",
      label: "Descrição",
      _style: { whiteSpace: "nowrap" },
    },
  ];


  const recibosFields = [
    { key: "detalhes" },
    { key: "nr_Recibo", label: "Nº Recibo", _style: { whiteSpace: "nowrap" } },
    {
      key: "dt_Emiss",
      label: "Data Emissão",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dt_Calculo",
      label: "Data Cálculo",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dt_Prestc",
      label: "Data Prestação",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dt_Pagto",
      label: "Data Pagamento",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "tipo_Pagto", _style: { whiteSpace: "nowrap" } },
    { key: "status" },
    { key: "recibo_Sobre", _style: { whiteSpace: "nowrap" } },
    {
      key: "recibo_Boleto_Obrigatorio",
      label: "Boleto Obrigatório",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "forma_Pagamento_Boleto", _style: { whiteSpace: "nowrap" } },
    {
      key: "cheque_Terceiro",
      label: "Cheque 3º",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "origem_Boleto", _style: { whiteSpace: "nowrap" } },
    { key: "observacao", label: "Observação" },
  ];

  const handleDetalhesRecibo = async (item) => {
    const data = { IdNegociacao: IdNegociacao, numeroContrato : financiadoData.numero_Contrato };
    const detRecibos = await GET_DATA(
      "Datacob/Negociacoes/Parcelas/Recibo",
      data
    );
    setDetalhesRecibo(detRecibos[0]);
    setShowModalDetalhesRecibo(true);
  };

  const handleDetalhesBoleto = async (item) => {
    const data = { IdNegociacao: IdNegociacao, numeroContrato : financiadoData.numero_Contrato };
    const detBoletos = await GET_DATA(
      "Datacob/Negociacoes/Parcelas/Boleto",
      data
    );
    setDetalhesBoleto(detBoletos[0]);
    setShowModalDetalhesBoleto(true);
  };

  const handleVisualizarBoleto = async (item) => {
    const data = { IdBoleto: item.id_Boleto };
    const boleto = await GET_DATA("Datacob/DownloadBoleto", data);

    // Defina um título para a nova janela
    const windowTitle = "Visualização de PDF";

    // Crie uma nova janela com título
    const newWindow = window.open(
      "",
      "_blank",
      `width=800,height=600,toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,title=${windowTitle}`
    );

    // Escreva o conteúdo do PDF na nova janela
    newWindow.document.open();
    newWindow.document.write(
      '<embed width="100%" height="100%" src="data:application/pdf;base64,' +
        boleto +
        '" type="application/pdf"/>'
    );
    newWindow.document.close();
  };

  const handleBaixarBoleto = async (item) => {
    setDownBol(true);
    try {
      const data = { IdBoleto: item.id_Boleto };
      const boleto = await GET_DATA("Datacob/DownloadBoleto", data);
      const linkSource = `data:application/pdf;base64,${boleto}`;
      const downloadLink = document.createElement("a");
      const time = new Date().toLocaleTimeString().replaceAll(":", "");
      const date = new Date().toLocaleDateString().replaceAll("/", "");
      const fileName = `${item.nr_Boleto}-${date}${time}.pdf`;
      downloadLink.href = linkSource;
      downloadLink.download = fileName;
      downloadLink.click();
    } catch (err) {
      toast.danger("Erro ao baixar boleto");
    }
    setDownBol(false);
  };

  const tabs = [
    {
      id: 1,
      label: "Boletos",
      icon: "cil-barcode",
      content: (
        <TableBoletos
          downBol={downBol}
          tableBoletos={tableBoletos}
          handleDetalhesBoleto={handleDetalhesBoleto}
          handleVisualizarBoleto={handleVisualizarBoleto}
          handleBaixarBoleto={handleBaixarBoleto}
        />
      ),
    },
    {
      id: 2,
      label: "Recibos",
      icon: "cil-notes",
      content: (
        <CDataTable
          items={tableRecibos}
          fields={recibosFields}
          hover
          responsive
          scopedSlots={{
            detalhes: (item) => (
              <td>
                <CButton
                  className="button-link py-0 px-0"
                  onClick={() => handleDetalhesRecibo(item)}
                >
                  Detalhes
                </CButton>
              </td>
            ),
            nr_Boleto: (item) => (
              <td className="nowrap-cell">{item.nr_Boleto}</td>
            ),
            status: (item) => <td>{renderStatus(item.status)}</td>,
            recibo_Sobre: (item) => renderReciboSobre(item.recibo_Sobre),
            dt_Emiss: (item) =>
              item.dt_Emiss ? (
                <td>{formatDate(item.dt_Emiss)}</td>
              ) : (
                <td>---</td>
              ),
            dt_Calculo: (item) =>
              item.dt_Calculo ? (
                <td>{formatDate(item.dt_Calculo)}</td>
              ) : (
                <td>---</td>
              ),
            dt_Prestc: (item) =>
              item.dt_Prestc ? (
                <td>{formatDate(item.dt_Prestc)}</td>
              ) : (
                <td>---</td>
              ),
            dt_Pagto: (item) =>
              item.dt_Pagto ? (
                <td>{formatDate(item.dt_Pagto)}</td>
              ) : (
                <td>---</td>
              ),
            recibo_Boleto_Obrigatorio: (item) => (
              <td
                style={{
                  paddingLeft: "40px",
                  textAlign: "center",
                }}
              >
                <CInputCheckbox
                  id={item.id_Boleto}
                  defaultChecked={item.recibo_Boleto_Obrigatorio}
                  disabled
                />
              </td>
            ),
            forma_Pagamento_Boleto: (item) => (
              <td className="nowrap-cell">{item.forma_Pagamento_Boleto}</td>
            ),
            cheque_Terceiro: (item) => (
              <td
                style={{
                  paddingLeft: "40px",
                  textAlign: "center",
                }}
              >
                <CInputCheckbox
                  id={item.id_Boleto}
                  defaultChecked={item.cheque_Terceiro}
                  disabled
                />
              </td>
            ),
            origem_Boleto: (item) => (
              <td
                style={{
                  paddingLeft: "40px",
                  textAlign: "center",
                }}
              >
                <CInputCheckbox
                  id={item.id_Boleto}
                  defaultChecked={item.origem_Boleto}
                  disabled
                />
              </td>
            ),
            descricao_Motivo_Cancel: (item) =>
              item.descricao_Motivo_Cancel ? (
                <td>{item.descricao_Motivo_Cancel}</td>
              ) : (
                <td>---</td>
              ),
          }}
        />
      ),
    },
  ];

  const renderStatus = (status) => {
    switch (status) {
      case "P":
        return <CBadge color="success">Pago</CBadge>;
      case "A":
        return <CBadge color="info">Aberto</CBadge>;
      case "C":
        return <CBadge color="danger">Cancelado</CBadge>;
      default:
        break;
    }
  };

  const renderReciboSobre = (status) => {
    switch (status) {
      case "A":
        return <td>Acordo</td>;
      default:
        return <td>---</td>;
    }
  };

  const setParcelasValues = async (item) => {
    setIdNegociacao(item.id_Negociacao);
    setSelectedRow(item.id_Negociacao);
    await getBoletosParcelas(item.id_Negociacao);
    // setNumParcela(item.nr_Parcela);
  };

  const handleDoubleClick = (item) => {};

  const handleClose = () => {
    setSelectedRow(null);
    onClose();
  };

  const getNegociacoes = async (parcela) => {
    const data = {
      IdAgrupamento: financiadoData.id_Agrupamento,
      IdParcela: parcela.id_Parcela,
      DetalheParcela: true,
    };
    setParcelaDados(parcela);
    setIsLoading(true);
    const negociacoes = await GET_DATA("Datacob/Negociacoes", data);
    if (negociacoes) {
      setDadosNegociacao(negociacoes);
      setIsLoading(false);
      setSelectedRow(negociacoes[0].id_Negociacao);
      await getBoletosParcelas(negociacoes[0].id_Negociacao);
      setIdNegociacao(negociacoes[0].id_Negociacao);
    } else {
      setDadosNegociacao([]);
      setIsLoading(false);
      setTableBoletos([]);
      setTableRecibos([]);
    }
    return negociacoes;
  };

  const handleDetalhesNegociacao = async (item) => {
    const data = { IdNegociacao: item.id_Negociacao };
    const detNegocaicao = await GET_DATA("Datacob/Negociacoes/Detalhes", data);
    setDetalhesNegociacao(detNegocaicao[0]);
    setShowModalDetalhesNegociacao(true);
  };

  /* const handleVisualizarBoleto = async (item) => {
    const data = { IdBoleto: item.id_Boleto };
    const boleto = await GET_DATA("Datacob/DownloadBoleto", data);

    const anchor = document.createElement("a");
    anchor.download = `${item.id_Boleto}.pdf`;
    anchor.href = "data:application/octet-stream;base64," + boleto;
    anchor.target = "_blank";
    document.body.appendChild(anchor);
    anchor.click();

    // Clean up the URL object after the download starts
    // URL.revokeObjectURL(pdfURL);
  }; */

  async function getBoletosParcelas(Id_Negociacao) {
    setIsLoadingTable2(true);
    const data = { IdNegociacao: Id_Negociacao, numeroContrato : financiadoData.numero_Contrato };
    const boletos = await GET_DATA("DataCob/Negociacoes/Parcelas/Boleto", data);
    const recibos = await GET_DATA("DataCob/Negociacoes/Parcelas/Recibo", data);

    if (!boletos) {
      setTableBoletos([]);
    } else {
      setTableBoletos(boletos);
      setIdBoleto(boletos[0].id_Boleto);
    }
    if (!recibos) {
      setTableRecibos([]);
    } else {
      setTableRecibos(recibos);
    }

    // setCurrentTab("Boletos");
    setIsLoadingTable2(false);
    return;
  }

  const handleUpdateModal = (sucesso) => {
    if (isOpen && sucesso && dados) {
      getNegociacoes(dados);
    }
  };

  useEffect(() => {
    if (isOpen && dados) {
      getNegociacoes(dados);
    }

    // if (isOpen && acordos) {
    //   setDadosNegociacao(acordos);
    //   setIdNegociacao(acordos[0].id_Negociacao);
    //   setSelectedRow(acordos[0].id_Negociacao);
    //   getBoletosParcelas(acordos[0].id_Negociacao);
    //   //   setNumParcela(dados[0].nr_Parcela);
    // }
  }, [isOpen]);

  const [calculoNegociacao, setCalculoNegociacao] = useState(null);
  const [showModalCalculoNegociacao, setShowModalCalculoNegociacao] =
    useState(false);
  const handleDetalhesModalNegociacao = async (item) => {
    const data = { IdNegociacao: item.id_Negociacao };
    const calculos = await GET_DATA("Datacob/Negociacoes/Calculo", data);
    setCalculoNegociacao(calculos);
    setShowModalCalculoNegociacao(true);
  };
  const handleCloseModalCalculoNegociacao = () => {
    setShowModalCalculoNegociacao(false);
  };

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="xl"
    >
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h3>Detalhes das Parcelas</h3>
      </CModalHeader>
      <CModalBody>
        <CCard className="mb-3">
          <ComponenteContratos
            singleClick={getNegociacoes}
            doubleClick={handleDoubleClick}
          />
        </CCard>
        {isLoading ? (
          <LoadingComponent />
        ) : (
          <CCard
            className="mb-3"
            style={{ maxHeight: "200px", overflowX: "auto" }}
          >
            <CDataTable
              items={dadosNegociacao}
              fields={acordosFields}
              scopedSlots={{
                // id_Negociacao: (item) => (
                //   <td
                //     className={rowClassName(item)}
                //     style={{ cursor: "pointer" }}
                //     onClick={() => setParcelasValues(item)}
                //   >
                //     {item.id_Negociacao}
                //   </td>
                // ),
                id_Negociacao: (item) => (
                  <td className={rowClassName(item)}>
                    <CButton
                      onClick={() => setParcelasValues(item)}
                      className="flat py-0 px-0 button-link"
                    >
                      <strong>{item.id_Negociacao}</strong>
                    </CButton>
                  </td>
                ),
                status: (item) => (
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={() => setParcelasValues(item)}
                  >
                    {" "}
                    {renderStatus(item.status)}{" "}
                  </td>
                ),
                descricao: (item) => (
                  <td
                    className="nowrap-cell"
                    style={{ cursor: "pointer" }}
                    onClick={() => setParcelasValues(item)}
                  >
                    {item.descricao}
                  </td>
                ),
                vl_Negociacao: (item) =>
                  item.vl_Negociacao ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {formatThousands(item.vl_Negociacao)}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                vl_Tx_Banc: (item) =>
                  item.vl_Tx_Banc ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {formatThousands(item.vl_Tx_Banc)}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                dt_Negociacao: (item) =>
                  item.dt_Negociacao ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {formatDate(item.dt_Negociacao)}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                liberado: (item) => (
                  <td
                    style={{
                      cursor: "pointer",
                      paddingLeft: "40px",
                      textAlign: "center",
                    }}
                    onClick={() => setParcelasValues(item)}
                  >
                    <CInputCheckbox
                      id={item.id_Parcela}
                      defaultChecked={item.liberado}
                      disabled
                    />
                  </td>
                ),
                calc_todas: (item) => (
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={() => setParcelasValues(item)}
                  >
                    ---
                  </td>
                ),
                calc_parcelas: (item) => (
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={() => setParcelasValues(item)}
                  >
                    ---
                  </td>
                ),
                negociacao: (item) => (
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={() => setParcelasValues(item)}
                  >
                    <CButton
                      className="button-link"
                      onClick={() => handleDetalhesModalNegociacao(item)}
                    >
                      Negociação
                    </CButton>
                  </td>
                ),
                userBoleto: (item) => (
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={() => setParcelasValues(item)}
                  >
                    {item.userBoleto ?? "---"}
                  </td>
                ),
                userNegociacao: (item) => (
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={() => setParcelasValues(item)}
                  >
                    {item.userNegociacao ?? "---"}
                  </td>
                ),
                mensagem: (item) => (
                  <td
                    style={{
                      cursor: "pointer",
                      minWidth: "300px",
                      whiteSpace: "normal",
                      overflow: "visible",
                      textOverflow: "unset",
                    }}
                    onClick={() => setParcelasValues(item)}
                  >
                    {item.mensagem ?? "---"}
                  </td>
                ),
              }}
            ></CDataTable>
          </CCard>
        )}

        <div>
          <CCard className="mt-4">
            <CTabs activeTab={currentTab}>
              <CRow>
                <CCol md="1" className="pr-0">
                  <CNav variant="tabs" className="flex-column">
                    {tabs.map((tab) => (
                      <CNavItem key={tab.id}>
                        <CTooltip content={tab.label}>
                          <CNavLink
                            className="d-flex justify-content-center py-3"
                            data-tab={tab.label}
                            onClick={() => setCurrentTab(tab.label)}
                          >
                            <i className={tab.icon} />
                          </CNavLink>
                        </CTooltip>
                      </CNavItem>
                    ))}
                  </CNav>
                </CCol>
                {isLoadingTable2 ? (
                  <CCol md="11">
                    <LoadingComponent />
                  </CCol>
                ) : (
                  <CCol
                    md="11"
                    className="pl-0"
                    style={{ maxHeight: "250px", overflowX: "auto" }}
                  >
                    <div style={{ overflowY: "auto" }}>
                      <CTabContent className="overflow-auto">
                        {tabs.map((tab) => (
                          <CTabPane key={tab.id} data-tab={tab.label}>
                            {tab.content}
                          </CTabPane>
                        ))}
                      </CTabContent>
                    </div>
                  </CCol>
                )}
              </CRow>
            </CTabs>
          </CCard>
        </div>
      </CModalBody>
      {showModalDetalhesNegociacao && (
        <DetalhesNegociacaoModal
          isOpen={showModalDetalhesNegociacao}
          onClose={() => setShowModalDetalhesNegociacao(false)}
          dados={detalhesNegociacao}
          dadosParcela={parcelaDados}
        />
      )}
      {showModalDetalhesBoleto && (
        <DetalhesBoletoModal
          isOpen={showModalDetalhesBoleto}
          onClose={() => setShowModalDetalhesBoleto(false)}
          dados={detalhesBoleto}
          parcela={numParcela}
          updateModal={handleUpdateModal}
        />
      )}
      {showModalDetalhesRecibo && (
        <DetalhesReciboModal
          isOpen={showModalDetalhesRecibo}
          onClose={() => setShowModalDetalhesRecibo(false)}
          dados={detalhesRecibo}
        />
      )}
      {showModalCalculoNegociacao && (
        <CalculosNegociacaoModal
          isOpen={showModalCalculoNegociacao}
          onClose={handleCloseModalCalculoNegociacao}
          dados={calculoNegociacao}
        />
      )}
    </CModal>
  );
};

export default NegociacoesModal;
