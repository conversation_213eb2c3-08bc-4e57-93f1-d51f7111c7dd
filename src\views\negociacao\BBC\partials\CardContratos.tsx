import React, { useState, useEffect } from "react";
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CFormGroup,
  CLabel,
  CSelect,
} from "@coreui/react";
import CardLoading from "src/reusable/CardLoading";
import CardAvisos from "src/reusable/CardAvisos";
import { getURI } from "src/config/apiConfig";
import { GET_DATA, POST_DATA } from "src/api";
import CardParcelas from "./CardParcelas.tsx";
import TableContratos from "./TableContratos.tsx";
import FormNegociacaoModal from "./FormNegociacaoModal.tsx";
import { ApiResponse } from "src/types/common";
import { BBCConsultaSaldo, BBCElegibilidade } from "src/types/commonBBC";
import { useBBCContext } from "../pageContext/BBCContext.tsx";
import {
  convertCurrencyToFloat,
  formatCurrency,
} from "src/reusable/helpers.js";
import { useAuth } from "src/auth/AuthContext.js";

const PostData = async (payload, endpoint = "BBCcheckEligibility") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), payload, true, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const GetData = async (data, endpoint = "BBCcheckEligibility") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(getURI(endpoint), data, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const CardContratos = () => {
  const BBCContext = useBBCContext();
  const { checkPermission } = useAuth();

  const financiadoData = BBCContext.financiadoData;
  const contratosData = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : "";

  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");
  const [msgAviso, setMsgAviso] = useState("");
  const [titleAviso, setTitleAviso] = useState("");

  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");

  const [contratosSelecionados, setContratosSelecionados] = useState(null);
  const [dadosContratos, setDataContratos] = useState(null);
  const [showTableContrato, setShowTableContrato] = useState(false);

  const [modalShow, setModalShow] = useState(false);

  const ConsultarContratosElegiveis = async (numeroContrato) => {
    let ret: boolean | BBCElegibilidade = false;
    const payload = {
      contract: numeroContrato,
      cpfCnpj: financiadoData.cpfCnpj,
      product: BBCContext.produtoSelecionado["product"],
    };

    setMsgAvisoLoading(`Consultando contrato ${numeroContrato}`);
    setTitleAvisoLoading("Consultando contratos Elegíveis");
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(payload)
      .then((data: ApiResponse<BBCElegibilidade>) => {
        if (data.success) {
          ret = data.data;
        }
      })
      .catch((err) => {
        setTitleAviso(`Erro na chamada das APIS`);
        setMsgAviso(
          `Erro na chamada API BBC Consultar Elegibilidade, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`
        );
      })
      .finally(() => {
        setLoading(false);
        setLoadingAction("empty");
        setMsgAvisoLoading("");
        setTitleAvisoLoading("");
      });

    return ret;
  };

  const ConsultarSaldoContratos = async (contrato) => {
    let ret: object | boolean = false;
    const data = {
      nrContrato: contrato.numero_Contrato,
    };
    setMsgAvisoLoading(
      `Consultando contrato ${contrato?.numero_Contrato} Dados BBC`
    );

    setTitleAvisoLoading("Consultando Dados BBC, aguarde...");
    setLoading(true);
    setLoadingAction("VarifyParam");

    await GetData(data, "BBCcheckBalance")
      .then((data: BBCConsultaSaldo[]) => {
        if (data.length > 0) {
          /* Pegando somente as parcelas que são listadas no dataCob */
          const numerosParcelas = [];
          for (const key in contrato.parcelas) {
            numerosParcelas.push(contrato.parcelas[key].nr_Parcela);
          }

          /* Filtrando pelas parcelas */
          let devedor = data.filter((item) => {
            //return true;
            return numerosParcelas.includes(Number.parseInt(item.numParc));
          });

          devedor = devedor.map((item) => {
            const parcelaNoDatacob = contrato.parcelas.filter(
              (i) => i.nr_Parcela === Number.parseInt(item.numParc)
            );

            let tempItem = { ...item };
            tempItem.atraso = 0;
            if (parcelaNoDatacob.length > 0) {
              tempItem.atraso = parcelaNoDatacob[0].atraso;
            }

            return tempItem;
          });

          const objRetorno = {
            iof: 0,
            mora: 0,
            multa: 0,
            pmt: 0,
            saldoCurva: 0,
            valor: 0,
            parcelas: [],
          };

          /* Somando os valores, para exibir no card Contratos Financiado */
          devedor.forEach((item) => {
            objRetorno.iof += convertCurrencyToFloat(item.iof);
            objRetorno.mora += convertCurrencyToFloat(item.mora);
            objRetorno.multa += convertCurrencyToFloat(item.multa);
            objRetorno.pmt += convertCurrencyToFloat(item.pmt);
            objRetorno.saldoCurva += convertCurrencyToFloat(item.saldoCurva);
            objRetorno.valor += convertCurrencyToFloat(item.valor);
          });

          /* Tratando os valores */
          objRetorno.iof = formatCurrency(objRetorno.iof);
          objRetorno.mora = formatCurrency(objRetorno.mora);
          objRetorno.multa = formatCurrency(objRetorno.multa);
          objRetorno.pmt = formatCurrency(objRetorno.pmt);
          objRetorno.saldoCurva = formatCurrency(objRetorno.saldoCurva);
          objRetorno.valor = formatCurrency(objRetorno.valor);
          objRetorno.parcelas = devedor;

          ret = objRetorno;
        } else {
          setTitleAviso("Contratos Sem Dados BBC");
          setMsgAviso(
            `O contrato ${contrato.numero_Contrato} não possui dados para cobrança no BBC!`
          );
        }
      })
      .catch((err) => {
        setTitleAviso(`Erro na chamada das APIS`);
        setMsgAviso(
          `Erro na chamada API BBC Consultar Elegibilidade, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`
        );
      })
      .finally(() => {
        setLoading(false);
        setLoadingAction("empty");
        setMsgAvisoLoading("");
        setTitleAvisoLoading("");
      });

    return ret;
  };

  const validaConstratoElegiveis = async () => {
    const contratosElegiveis = [];
    setTitleAviso("");
    setMsgAviso("");
    setShowTableContrato(false);
    setContratosSelecionados(null);
    BBCContext.setContratoNegociar(null);

    if (contratosData !== null && contratosData.length > 0) {
      for (const contrato of contratosData) {
        const ret = await ConsultarContratosElegiveis(contrato.numero_Contrato);
        if (ret) {
          contrato.elegivel = ret;
          contratosElegiveis.push(contrato);
        }
      }

      if (contratosElegiveis.length > 0) {
        for (const contrato of contratosElegiveis) {
          const saldo = await ConsultarSaldoContratos(contrato);
          if (saldo) {
            contrato.dadosBBC = saldo;
          }
        }
        setDataContratos(contratosElegiveis);
        setShowTableContrato(true);
      } else {
        setTitleAviso("Não há contratos Elegíveis");
        setMsgAviso("Não há contratos Elegíveis para acordo");
      }
    } else {
      setTitleAviso("Validação de contratos Ativos");
      setMsgAviso("Não há contratos Ativos");
    }
  };

  useEffect(() => {
    if (BBCContext.produtoSelecionado !== null) {
      validaConstratoElegiveis();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [BBCContext.produtoSelecionado]);

  const handleContratoSelection = (contrato) => {
    // contrato.dadosSafra.vlrHonorario = 0;
    setContratosSelecionados(contrato);
    BBCContext.setContratoNegociar(contrato);
    BBCContext.setComunicacaoState("");
  };

  const handleClose = () => {
    setModalShow(false);
    BBCContext.setComunicacaoState("OK");
  };

  return (
    <div>
      <CCard>
        <CCardHeader>
          <h5 className="d-flex justify-content-between">
            <span>Contratos Financiado</span>
            <div>
              {contratosSelecionados != null &&
              checkPermission("Negociação", "Create", "BBC") ? (
                <CButton
                  className={"mr-2"}
                  color="success"
                  onClick={() => setModalShow(true)}
                >
                  Negociar
                </CButton>
              ) : (
                ""
              )}
              <CButton
                color="success"
                disabled={financiadoData?.id_Agrupamento == null || loading}
                onClick={() => validaConstratoElegiveis()}
              >
                <i className="cil-reload" />
              </CButton>
            </div>
          </h5>
        </CCardHeader>
        <CCardBody>
          {/* Select Produtos */}
          <div style={{ display: "none" }}>
            <CFormGroup>
              <CLabel htmlFor="products">Produtos</CLabel>
              <CSelect
                name="products"
                id="products"
                onChange={(e) => {
                  BBCContext.setProdutoSelecionado(e.currentTarget.value);
                }}
              >
                {BBCContext.produtos &&
                  BBCContext.produtos.map((prod, key) => {
                    return (
                      <option key={key} value={prod["product"]}>
                        {prod["label"]}
                      </option>
                    );
                  })}
              </CSelect>
            </CFormGroup>
          </div>
          {/* Select Produtos */}

          {titleAviso !== "" && msgAviso !== "" ? (
            <CardAvisos Title={titleAviso} Msg={msgAviso} />
          ) : loading && loadingAction === "VarifyParam" ? (
            <CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />
          ) : showTableContrato && dadosContratos !== null ? (
            <TableContratos
              dataTable={dadosContratos}
              contratoIndex={contratosSelecionados}
              onContratoClick={handleContratoSelection}
            />
          ) : (
            ""
          )}
        </CCardBody>
      </CCard>
      <div>
        {contratosSelecionados != null ? (
          <CCard>
            <CCardBody>
              <CardParcelas
                dataParcelas={contratosSelecionados.dadosBBC.parcelas}
              />
            </CCardBody>
          </CCard>
        ) : (
          ""
        )}
      </div>
      {modalShow && (
        <FormNegociacaoModal
          isOpen={modalShow}
          onClose={handleClose}
          contrato={contratosSelecionados}
        />
      )}
    </div>
  );
};

export default CardContratos;
