import React from "react";

const _nav = [
  {
    _tag: "CSidebarNavItem",
    name: "Tela Principal",
    to: "/telaprincipal",
    icon: <i className="cil-house c-sidebar-nav-icon" />,
    module: "Tela Principal",
  },
  {
    _tag: "CSidebarNavDropdown",
    name: "Negociação",
    route: "/negociar",
    icon: "cil-dollar",
    module: "Negociação",
    _children: [
      {
        _tag: "CSidebarNavItem",
        name: "Negociar",
        to: "/negociar/cotas",
        module: "Negociação",
        submodule: "Simular",
      },
      {
        _tag: "CSidebarNavItem",
        name: "<PERSON>fra",
        to: "/negociar/safra",
        module: "Negociação",
        submodule: "Safra",
      },
      {
        _tag: "CSidebarNavItem",
        name: "BBC",
        to: "/negociar/BBC",
        module: "Negociação",
        submodule: "BBC",
      },
    ],
  },
  {
    _tag: "CSidebarNavDropdown",
    name: "<PERSON>obe<PERSON>",
    route: "/rodobens",
    icon: "cil-dollar",
    module: "Randon",
    _children: [
      {
        _tag: "CSidebarNavItem",
        name: "Newcon",
        to: "/rodobens/newcon",
        module: "Randon",
        submodule: "NewCon",
      },
    ],
  },
  {
    _tag: "CSidebarNavDropdown",
    name: "Acordos",
    route: "/acordos",
    icon: "cil-spreadsheet",
    module: "Acordos",
    _children: [
      {
        _tag: "CSidebarNavItem",
        name: "Visualizar Acordos",
        to: "/acordos/visualizar",
        icon: "cil-spreadsheet",
        module: "Acordos",
        submodule: "Visualizar Acordos",
      },
      {
        _tag: "CSidebarNavItem",
        name: "Criar Acordos",
        to: "/acordo/criar",
        icon: "cil-spreadsheet",
        module: "Acordos",
        submodule: "Criar Acordos",
      },
      {
        _tag: "CSidebarNavItem",
        name: "Criar Acordos Manuais",
        to: "/acordo/manual/criar",
        icon: "cil-spreadsheet",
        module: "Acordos",
        submodule: "Criar Acordos Manuais",
      },
    ],
  },
  {
    _tag: "CSidebarNavItem",
    name: "Avalista/Terceiros",
    to: "/avalistas",
    icon: "cil-people",
    module: "Avalista/Terceiros",
  },
  {
    _tag: "CSidebarNavDropdown",
    name: "Jurídico CRM",
    route: "/juridico",
    icon: <i className="cil-institution c-sidebar-nav-icon" />,
    module: "Jurídico CRM",
    _children: [
      {
        _tag: "CSidebarNavItem",
        name: "Custas",
        to: "/juridico/custas",
        module: "Jurídico CRM",
        submodule: "Custas",
      },
    ],
  },
  {
    _tag: "CSidebarNavDropdown",
    name: "Histórico e registros",
    route: "/historico",
    icon: <i className="cil-clock c-sidebar-nav-icon" />,
    module: "Histórico e registros",
    _children: [
      {
        _tag: "CSidebarNavItem",
        name: "Adicionar ocorrências",
        to: "/historico/ocorrencias",
        module: "Histórico e registros",
        submodule: "Adicionar ocorrências",
      },
    ],
  },
  {
    _tag: "CSidebarNavItem",
    name: "Aprovação Abaixo da Régua",
    to: "/aprovacaoAbaixoRegua",
    module: "Aprovação Abaixo da Régua",
    icon: "cilShieldAlt",
  },
  {
    _tag: "CSidebarNavTitle",
    _children: ["Safra"],
  },
  {
    _tag: "CSidebarNavItem",
    name: "Cadastro de Campanha",
    to: "/safraCadastroCampanha",
    module: "Campanhas Safra",
  },
  {
    _tag: "CSidebarNavItem",
    name: "Lista de Campanhas",
    to: "/safraCampanhas",
    module: "Campanhas Safra",
  },
  {
    _tag: "CSidebarNavItem",
    name: "Permissões",
    to: "/safraUsuarios",
    module: "Permissão de Campanhas",
  },
  {
    _tag: "CSidebarNavTitle",
    _children: ["Gerenciamento Tela Única"],
  },
  {
    _tag: "CSidebarNavDropdown",
    name: "Mailing",
    route: "/mailings",
    module: "Mailing",
    _children: [
      {
        _tag: "CSidebarNavItem",
        name: "Gerenciar Mailings",
        to: "/mailings/mailing",
        module: "Mailing",
        submodule: "Gerenciar Mailings",
      },
    ],
  },
  {
    _tag: "CSidebarNavDropdown",
    name: "Configurações",
    route: "/configuracoes",
    module: "Configurações",
    _children: [
      {
        _tag: "CSidebarNavItem",
        name: "Gerenciar Usuários",
        to: "/configuracoes/gerenciarusuario",
        module: "Configurações",
        submodule: "Gerenciar Usuários",
      },
      {
        _tag: "CSidebarNavItem",
        name: "Grupos e Clientes",
        to: "/configuracoes/gruposClientes",
        module: "Configurações",
        submodule: "Grupos e Clientes",
      },
      {
        _tag: "CSidebarNavItem",
        name: "Logs Integrações",
        to: "/configuracoes/logIntegracoes",
        module: "Configurações",
        submodule: "LogIntegracoes",
      },
      {
        _tag: "CSidebarNavItem",
        name: "Config. SMTP CRM",
        to: "/configuracoes/configurationCrm",
        module: "Configurações",
        submodule: "CRM",
      },
      {
        _tag: "CSidebarNavItem",
        name: "Funções",
        to: "/configuracoes/funcoes",
        module: "Configurações",
        submodule: "Funções",
      },
      {
        _tag: "CSidebarNavItem",
        name: "Cadastro FAQS",
        to: "/faqs/topics",
        module: "Configurações",
        submodule: "FAQS",
      },
      {
        _tag: "CSidebarNavItem",
        name: "Config. Integração",
        to: "/configuracoes/IntegrationConfiguration",
        module: "Configurações",
        submodule: "IntegrationConfiguration",
      },
    ],
  },
  {
    _tag: "CSidebarNavItem",
    name: "Configurações Tela Única",
    to: "/TelaUnicaSettings",
    module: "Configurações Tela Única",
  },
  {
    _tag: "CSidebarNavItem",
    name: "Lista Login Usuarios",
    to: "/ListLogsUsers",
    module: "Lista Login Usuario",
  },
  {
    _tag: "CSidebarNavItem",
    name: "Faixas de Atraso",
    to: "/faixaAtraso",
    module: "Faixas de Atraso",
  },
  {
    _tag: "CSidebarNavDropdown",
    name: "Telefonia",
    route: "/telefonia",
    module: "Telefonia",
    _children: [
      {
        _tag: "CSidebarNavItem",
        name: "Campanhas de Ligação",
        to: "/telefonia/campanhas",
        // icon: <i className="cil-address-book c-sidebar-nav-icon" />,
        module: "Telefonia",
        submodule: "Campanhas de Ligação",
      },
      {
        _tag: "CSidebarNavItem",
        name: "Pausas Tactium",
        to: "/telefonia/parametriPausas",
        // icon: <i className="cil-address-book c-sidebar-nav-icon" />,
        module: "Telefonia",
        submodule: "Pausas Tactium",
      },
    ],
  },

  {
    _tag: "CSidebarNavItem",
    name: "Simulações de Cálculos",
    to: "/simulacaoCalculos",
    module: "Simulações de Cálculos",
  },
  {
    _tag: "CSidebarNavDropdown",
    name: "Aprovação Jurídica",
    route: "/AprovacaoJuridica",
    module: "Aprovação Jurídica",
    _children: [
      {
        _tag: "CSidebarNavItem",
        name: "Adicionar Restrição de Fase",
        to: "/AdicionarRestricoesNegociacao",
        module: "Aprovação Jurídica",
        submodule: "Cadastro Fase Restrição",
      },
      {
        _tag: "CSidebarNavItem",
        name: "Pedidos Análise Restrições",
        to: "/ListaPedidoAnaliseRestricao",
        module: "Aprovação Jurídica",
        submodule: "Pedidos Análise Restrições",
      },
    ],
  },
  {
    _tag: "CSidebarNavItem",
    name: "Indicativo Jurídico",
    to: "/indicativoJuridico",
    module: "Indicativo Jurídico",
  },
  {
    _tag: "CSidebarNavDropdown",
    name: "Simulações",
    module: "Simulações",
    _children: [
      {
        _tag: "CSidebarNavItem",
        name: "Atraso Aditamento",
        to: "/AtrasoAditamento",
        module: "Simulações",
        submodule: "Atraso Aditamento",
      },
    ],
  },
  {
    _tag: "CSidebarNavItem",
    name: "Parcelamento BBC",
    to: "/parcelamentoBBC",
    icon: <i className="cil-calculator c-sidebar-nav-icon" />,
    module: "Parcelamento BBC",
  },
  {
    _tag: "CSidebarNavItem",
    name: "Desconto BBC",
    to: "/descontoBBC",
    icon: <i className="cil-calculator c-sidebar-nav-icon" />,
    module: "Desconto BBC",
  },
  {
    _tag: "CSidebarNavItem",
    name: "Controle de Boletos por Usuário",
    to: "/ControleUsuarios",
    module: "Controle de Usuários",
  },
  {
    _tag: "CSidebarNavItem",
    name: "Cadastro de Prestadores de Serviço",
    to: "/prestadores_servicos",
    module: "Cadastro de Prestadores de Serviço",
    submodule: "Cadastro de Prestadores de Serviço",
  },
  {
    _tag: "CSidebarNavItem",
    name: "Regras Ocorrências",
    to: "/regrasOcorrencias",
    module: "Regras Ocorrências",
  },
  {
    _tag: "CSidebarNavItem",
    name: "Ajuda",
    to: "/ajuda",
    module: "Ajuda",
  },
  {
    _tag: "CSidebarNavDivider",
    className: "m-2",
  },
];

export default _nav;
