import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useHistory } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>erNav, <PERSON>utton, CLabel } from "@coreui/react";
import { useMyContext } from "src/reusable/DataContext";
import QualificarOcorrenciaModal from "src/views/telefonia/QualificarOcorrenciaModal";
import MudarClienteModal from "src/reusable/MudarClienteModal";
import LigacoesCaidasModal from "src/reusable/LigacoesCaidasModal";
import TheHeaderDropdownCall from "./TheHeaderDropdownCall";
import IdentificarFinanciadoModal from "src/views/telefonia/IdentificarFinanciadoModal";
import AcionarPausaModal from "src/views/telefonia/AcionarPausaModal";
import HistCallModal from "src/views/telefonia/HistCallModal";
import {
  desligarLigacao,
  despausarLigacao,
  postAutenticar,
  transferirLigacao,
} from "src/config/telephonyFunctions";

import { TheHeaderDropdown } from "./index";

import _ from "lodash";
import { toast } from "react-toastify";
import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";
import ModalConectaTelefonia from "src/websocketProvider/ModalConectaTelefonia.tsx";
import ConfirmModal from "src/reusable/ConfirmModal.js";
import AdicionarTelefone from "src/views/telaPrincipal/CardContratos/OutrosModals/AdicionarTelefone.js";
import {
  getDadosFinanciado,
  getTiposTelefone,
} from "src/reusable/functions.js";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import AudioCapture from "./TranscriptionsView/AudioCapture";
import CIcon from "@coreui/icons-react";
import { IconWithBadge } from "src/config/iconWithBadge";
import TheHeaderMailing from "./TheHeaderMailing"
import { formatDateToGlobal } from "src/reusable/helpers";

const TheHeader = () => {
  const { data, appSettings, updateAppSettings } = useMyContext();

  const {
    passCode,
    passCodeRef,
    stateCall,
    callTypeBanner,
    phoneBanner,
    dadosLigacao,
    emAtendimento,
    emLigacao,
    pauseButtonIcon,
    qualificarLigacaoModal,
    setQualificarLigacaoModal,
    setShowModalPausa,
    showModalPausa,
    confirmarFinanciado,
    isLoading,
    setIsLoading,
    message,
    showIdentificarFinanciado,
    setShowIdentificarFinanciadoModal,
    statusRef,
    messageRef,
    showModalReconnectRef,
    logouUmaVez,
    tableData,
    showNumberInsertModal,
    setShowNumberInsertModal,
    getIsActiveTelephony,
    emPausa,
    telefoneAdicionadoRef,
  } = useWebsocketTelefoniaContext();

  const [financiadoData, setDadosFinanciados] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  const dispatch = useDispatch();
  const sidebarShow = useSelector((state) => state.sidebarShow);
  const history = useHistory();

  const [inserirTelefone, setInserirTelefone] = useState(null);
  const [tipoTelefone, setTipoTelefone] = useState([]);
  const [showModalAdicionarTel, setShowModalAdicionarTel] = useState(false);

  const [mudarClienteModal, setMudarClienteModal] = useState(false);
  const [ligacaoTerminadaModal, setLigacaoTerminadaModal] = useState(false);

  const [showModalHistCall, setShowModalHistCall] = useState(false);
  const [loadingAuthCod, setLoadingAuthCod] = useState(false);

  const [openModalReconnectOlos, setOpenModalReconnectOlos] = useState(false);

  const [timer, setTimer] = useState(-1);

  const [mailings, setMailings] = useState([]);
  const [isMailing, setIsMailing] = useState(false);
  const [countMailings, setCountMailings] = useState(0);

  const userProfile = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  if (localStorage.getItem("telephonyData") === "undefined") {
    localStorage.setItem("telephonyData", "{}");
  }

  const telephonyData = localStorage.getItem("telephonyData")
    ? JSON.parse(localStorage.getItem("telephonyData"))
    : "";

  const toggleSidebar = () => {
    const val = [true, "responsive"].includes(sidebarShow)
      ? false
      : "responsive";
    dispatch({ type: "set", sidebarShow: val });
  };

  const toggleSidebarMobile = () => {
    const val = [false, "responsive"].includes(sidebarShow)
      ? true
      : "responsive";
    dispatch({ type: "set", sidebarShow: val });
  };

  const handleMudarCliente = async () => {
    setMudarClienteModal(true);
  };

  const handleLigacaoTerminada = async () => {
    let callData = getUpdatedCallData();
    setIsLoading(true);
    await desligarLigacao(callData);
    await new Promise((resolve) => setTimeout(resolve, 5000));
    setIsLoading(false);
  };

  const handleLigacaoTransferir = async () => {
    let callData = getUpdatedCallData();
    setIsLoading(true);
    await transferirLigacao(callData);
    await new Promise((resolve) => setTimeout(resolve, 5000));
    setIsLoading(false);
  };

  //Não adianta settar variaveis fora da websocket pra ler dentro dela, o tabulacaoSuccess não serve pra nada aqui
  const handleLigacaoCaidaModal = (tabulacaoSuccess = false) => {
    setLigacaoTerminadaModal(false);
  };

  const colocarEmPausa = (state) => {
    if (state) {
      setShowModalPausa(false);
    }
  };

  const getUpdatedCallData = () => {
    const callAtualizada = message || null;
    return callAtualizada;
  };

  const togglePause = async () => {
    const callData = message;
    if (callData.status) {
      if (callData.status === "Pause") {
        setIsLoading(true);
        await despausarLigacao(telephonyData.agentId);
        await new Promise((resolve) => setTimeout(resolve, 1500));
        setIsLoading(false);
      } else {
        if (callData.manualMode) {
          toast.warning(
            "Não é possível requisitar pausas em ligações manuais."
          );
          return;
        }
        if (
          callData.status === "TalkingWithPause" ||
          callData.status === "WrapWithPause" ||
          callData.status === "ConsultingWithPause" ||
          callData.status === "AnalyzingWithPause" ||
          callData.status === "AttemptingWithPause" ||
          callData.status === "HoldingWithPause"
        ) {
          toast.info(
            "Pausa já requisitada, entrará em pausa após qualificar a ligação atual."
          );
          return;
        }
        if (
          callData.status === "TalkingWithEnding" ||
          callData.status === "WrapWithEnding" ||
          callData.status === "ConsultingWithEnding" ||
          callData.status === "AnalyzingWithEnding" ||
          callData.status === "AttemptingWithEnding" ||
          callData.status === "HoldingWithEnding"
        ) {
          toast.info(
            "Não é possível solicitar pausa com pedido de deslogar requisitado."
          );
          return;
        } else {
          closeModals();
          setShowModalPausa(true);
        }
      }
    } else {
      toast.info("Sem conexão com a telefonia.");
    }
  };

  function clickShowModalHistCall() {
    setShowModalHistCall(true);
  }

  async function closeModals() {
    setQualificarLigacaoModal(false);
    setShowModalPausa(false);
    setLigacaoTerminadaModal(false);
    setShowIdentificarFinanciadoModal(false);

    await GET_DATA(getURI("getGrupoLista"), { id: financiadoData?.id_Grupo }, true).then(result => {
      setTimer(result[0]?.timeEndCall ?? -1);
    });
  }

  const intervalRef = useRef(null);

  async function getMailing(user) {
    let startDate = formatDateToGlobal(new Date().toLocaleDateString());
    await GET_DATA(`${getURI()}/mailing/user`, { startDate: startDate, userId: user.id }, true).then(async result => {
      if (result?.length) {
        setCountMailings(result.reduce((count, mailing) => { return count + (mailing.mailingContracts?.length ?? 0); }, 0));
        setMailings(result || []);
      }
    });
  }

  useEffect(() => {
    if (userProfile && (mailings?.length ?? 0) === 0) {
      getMailing(userProfile);
    }
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [countMailings]);

  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer((prev) => {
          const newTimer = prev - 1;

          if (newTimer === 0) {
            toast.warning(
              <>
                <strong>Atenção!</strong>
                <br />
                O tempo de registro de tabulação acabou.
                <br />
                Você poderá receber ligações do discador vindas de outros
                clientes.
                <br />
                <strong>Salve o que está sendo feito.</strong>
              </>
            );
          }
          return newTimer;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [timer]);

  const formatTimer = () => {
    const minutes = Math.floor(timer / 60);
    const seconds = timer % 60;
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  };

  const getTimerColor = () => {
    return timer > 0 ? "yellow" : "red";
  };

  const addTelefoneProcess = async () => {
    setShowModalAdicionarTel(true);
    const res = await getTiposTelefone();
    setTipoTelefone(res);
  };

  useEffect(() => {
    if (data) {
      setDadosFinanciados(data);
      if (inserirTelefone) {
        addTelefoneProcess();
      }
    }
  }, [data]);

  useEffect(() => {
    if (
      showModalReconnectRef.current === true &&
      message?.status === "Logout" &&
      message?.agentId !== null &&
      emPausa === false
    ) {
      if (userProfile.automaticReconnectionOlos && !isSoftphoneOlosAllowed) {
        postAutenticar().finally(() => {
          showModalReconnectRef.current = true;
          setTimeout(() => {
            reconnect();
          }, 8000);
        });
      } else {
        setOpenModalReconnectOlos(true);
      }
    }
  }, [showModalReconnectRef.current]);

  const reconnect = () => {
    if (messageRef.current?.agentId && passCodeRef.current && !isSoftphoneOlosAllowed) {
      const newWindow = window.open(
        `https://rodobens.oloschannel.com.br/softphonewebrtc_unextended/?remote_address=rodobens-ecs01.oloschannel.com.br&passcode=${passCodeRef.current}&agent_id=${messageRef.current?.agentId}`,
        "_blank"
      );
      if (
        !newWindow ||
        newWindow.closed ||
        typeof newWindow.closed === "undefined",
        !isSoftphoneOlosAllowed
      ) {
        alert(
          "Pop-up bloqueado! Siga as instruções para desbloquear:\n" +
          "- No Google Chrome: clique no ícone de bloqueio na barra de endereços e permita pop-ups.\n" +
          "- No Firefox: permita pop-ups no aviso exibido na parte superior."
        );
      }
    }
  };

  const onCloseClienteModal = (missingCrm = null) => {
    setMudarClienteModal(false);
  };

  function stringOrigem(origem = 0) {
    if (typeof origem == "number") {
      if (origem === 1) return "Discador";
      if (origem === 2) return "Manual";
      if (origem === 3) return "Ligação URA";
      if (origem === 0) return "Receptivo";
    }

    return "Receptivo";
  }

  const handleCloseModalReconnect = () => {
    logouUmaVez.current = false;
    setOpenModalReconnectOlos(false);
  };

  const dataPost = async (newData) => {
    const data = {
      cpfCnpj: financiadoData.cpfCnpj,
      idGrupo: financiadoData.id_Grupo,
      nome: financiadoData.nome,
      telefones: [{ ...newData }],
    };
    const result = await POST_DATA("Datacob/DadosCadastrais/Financiado", data);
    return result;
  };

  const handleAdicionar = async (newData) => {
    if (tableData?.callId !== null) {
      toast.success("Enviando informações!");
      const postSuccess = await dataPost(newData);
      if (postSuccess.success) {
        const updatedData = await getDadosFinanciado(
          financiadoData.id_Financiado, financiadoData.numero_Contrato
        );
        localStorage.setItem("clientData", JSON.stringify(updatedData));
        confirmarFinanciado(tableData?.callId);
        telefoneAdicionadoRef.current = true;
        toast.success("Dados do financiado alterados com sucesso!");
      } else {
        toast.warning(postSuccess.message);
      }
    } else {
      toast.warning("Não foi possível encontrar o telefone para cadastrar!");
    }
  };

  const [isVisibleTranscription, setIsVisibleTranscription] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isUserAllowed, setIsUserAllowed] = useState(false);
  const [isPermissions, setIsPermissions] = useState(false);

  const allowedStatuses = ["Talking", "ManualCall", "Manual", "TalkingWithEnding", "TalkingWithManualCall", "TalkingWithPause"];
  const shouldShowButton = allowedStatuses.includes(message?.status || "");

  const checkUserPermission = async () => {
    try {
      const transcriptionCallUsers = await GET_DATA(
        getURI("getConfigByKey"),
        null,
        true,
        true,
        "transcription_call_users"
      );

      if (transcriptionCallUsers && userProfile) {
        setIsUserAllowed(transcriptionCallUsers.includes(userProfile.id));
      }
    } catch (error) {
      console.error("Error checking user permissions:", error);
    }
  };

  const openModalTranscription = async () => {
    try {
      if (!isConnected) {
        console.error("Conecte-se ao WebSocket antes de iniciar a transcrição.");
        return;
      }

      if (!isPermissions) {
        if (window.requestAudioPermissionsCallback) {
          console.log("Solicitando permissões de áudio...");
          await window.requestAudioPermissionsCallback();
        }
        setIsPermissions(true);
      }

      setIsVisibleTranscription(true);
      setIsButtonDisabled(true);


      if (window.startRecordingCallback) {
        console.log("Iniciando gravação...");
        await window.startRecordingCallback();
      } else {
        console.error("startRecordingCallback não está definido.");
      }
    } catch (error) {
      console.error("Erro ao iniciar a gravação:", error);
    }
  };

  const closeModalTranscription = () => {
    setIsVisibleTranscription(false);
    setIsButtonDisabled(false);
    if (window.stopRecordingCallback) {
      window.stopRecordingCallback();
    }
  };

  const toggleConnection = async () => {
    try {
      if (isConnected) {
        console.log("Desconectando...");
        if (window.disconnectWebSocketCallback) {
          window.disconnectWebSocketCallback();
        }
        setIsConnected(false);
      } else {
        console.log("Conectando...");
        if (window.connectWebSocketCallback) {
          await window.connectWebSocketCallback();
        }
        setIsConnected(true);
      }
    } catch (error) {
      console.error("Erro ao alternar a conexão:", error);
    }
  };

  useEffect(() => {
    checkUserPermission();
    checkUserPermissionSoftphoneOlos();

    if (isUserAllowed) {
      toggleConnection();
    }
  }, [isUserAllowed]);

  const handleClick = () => {
    history.push('/faqs/faqsscreen');
  };

  const handleMailingClick = () => {
    setIsMailing(!isMailing);
  };

  const [isSoftphoneOlosAllowed, setIsSoftphoneOlosAllowed] = useState(false);
  const checkUserPermissionSoftphoneOlos = async () => {
    try {
      const softphoneOlosCallUsers = await GET_DATA(
        getURI("getConfigByKey"),
        null,
        true,
        true,
        "softphone_olos_users"
      );

      if (softphoneOlosCallUsers && userProfile) {
        setIsSoftphoneOlosAllowed(softphoneOlosCallUsers.includes(userProfile.id));
      }
    } catch (error) {
      console.error("Error checking user permissions:", error);
    }
  };

  return (
    <>
      <ModalConectaTelefonia
        isOpen={openModalReconnectOlos}
        onClose={handleCloseModalReconnect}
        agentId={messageRef.current?.agentId}
        isSoftphoneOlosAllowed={isSoftphoneOlosAllowed}
      />

      <CHeader colorScheme="dark" className="custom-header-1">
        <CToggler
          inHeader
          className="ml-md-3 d-lg-none"
          onClick={toggleSidebarMobile}
        />
        <CToggler
          inHeader
          className="ml-3 d-md-down-none"
          onClick={toggleSidebar}
        />
        {/* <CHeaderBrand className="mx-auto d-lg-none" to="/">
        <CIcon name="logo" height="48" alt="Logo"/>
      </CHeaderBrand> */}

        <CHeaderNav className="d-md-down-none mr-auto">
          {/* <IconButton icon="cil-user" /> */}
          {/* <CButton
            className="px-2 mr-2"
            style={{
              color: "rgba(255, 255, 255, 0.6)",
            }}
          >
            <i className={"cil-user"} />
          </CButton> */}
          <CLabel className="mr-3 pt-1 mb-0">
            <i className={"cil-user"} />{" "}
          </CLabel>
          <CLabel className="mr-2 mb-0">
            {financiadoData ? financiadoData.nome : "Buscar cliente "}
          </CLabel>
          <CButton
            className="subtle px-2 mr-2"
            onClick={handleMudarCliente}
            disabled={emAtendimento}
            title={
              emAtendimento
                ? "Não é possível pesquisar outro financiado durante atendimento"
                : ""
            }
          >
            <i className={"cil-swap-horizontal"} />
          </CButton>

          {timer >= 0 && (
            <CLabel
              className="mr-3 pt-1 mb-0"
              style={{
                color: getTimerColor(),
                fontWeight: "bold",
                fontSize: "1.2rem",
              }}
            >
              <i className="cil-clock" /> <span>{formatTimer()}</span>
            </CLabel>
          )}
        </CHeaderNav>
        <CHeaderNav>
          {shouldShowButton && isUserAllowed && (
            <div>
              <CButton
                className="px-2 mr-3"
                color="primary"
                onClick={openModalTranscription}
                disabled={!isConnected || isButtonDisabled}
              >
                Transcrição
              </CButton>
            </div>
          )}
          <AudioCapture
            visible={isVisibleTranscription}
            onClose={closeModalTranscription}
            requestAudioPermissionsCallback={(callback) => {
              window.requestAudioPermissionsCallback = callback
            }}
            startRecordingCallback={(callback) => {
              window.startRecordingCallback = callback;
            }}
            stopRecordingCallback={(callback) => {
              window.stopRecordingCallback = callback;
            }}
            connectWebSocketCallback={(callback) => {
              window.connectWebSocketCallback = callback;
            }}
            disconnectWebSocketCallback={(callback) => {
              window.disconnectWebSocketCallback = callback;
            }}
          />
        </CHeaderNav>
        {userProfile && userProfile.telephony && getIsActiveTelephony() && (
          <>

            <CHeaderNav className="d-md-down-none mr-auto">
              {timer >= 0 && (
                <CLabel
                  className="mr-3 pt-1 mb-0"
                  style={{
                    color: getTimerColor(),
                    fontWeight: "bold",
                    fontSize: "1.2rem",
                  }}
                >
                  <i className="cil-clock" /> <span>{formatTimer()}</span>
                </CLabel>
              )}

              <div
                className={
                  stateCall === "Atendeu" ? "bg-success p-2" : "bg-warning p-2"
                }
                style={{ borderRadius: "0.25rem", fontWeight: "bold" }}
                hidden={
                  stateCall === undefined ||
                  stateCall === null ||
                  stateCall === ""
                }
              >
                {stateCall} - {stringOrigem(callTypeBanner)} -{" "}
                {phoneBanner ?? "Não identificado"}
              </div>
            </CHeaderNav>

            <CHeaderNav>
              <CButton
                className="px-2 mr-3"
                color="danger"
                onClick={clickShowModalHistCall}
                disabled={emAtendimento || emLigacao}
              >
                Histórico de Ligações
              </CButton>
              {userProfile && userProfile.telephonyId === 2 && (
                <div>
                  {passCode && (
                    <CLabel className="mr-3 mb-0">Cod OLOS: {passCode}</CLabel>
                  )}
                </div>
              )}{" "}
              <TheHeaderDropdownCall
                // callButton={buttonClick}
                isDisabled={isLoading}
                setLoadingAuthCod={setLoadingAuthCod}
                loadingAuthCod={loadingAuthCod}
                isSoftphoneOlosAllowed={isSoftphoneOlosAllowed}
              />
              <CButton
                className="header-icon-blue px-2 mr-3"
                onClick={togglePause}
                disabled={
                  // (statusRef.current !== "Idle" &&
                  //   statusRef.current !== "Pause") ||
                  isLoading
                }
              >
                <i className={pauseButtonIcon + "  header-icon-lg"} />
              </CButton>
              <CButton
                className={`${emLigacao
                  ? "px-2 mr-3 header-icon-red"
                  : "px-2 mr-3 header-icon-red"
                  } `}
                onClick={handleLigacaoTerminada}
              >
                <i className={`cil-phone header-icon-lg`} />
              </CButton>
              <CButton
                className={`px-2 mr-3 header-icon-orange`}
                onClick={handleLigacaoTransferir}
                disabled={!emLigacao || isLoading}
              >
                <i className={`cil-transfer header-icon-lg`} />
              </CButton>
              <div className="separator"></div>
            </CHeaderNav>
          </>
        )}
        <CHeaderNav className="px-3">
          {/* <TheHeaderDropdownNotif /> */}
          {/* <TheHeaderDropdownTasks/>
        <TheHeaderDropdownMssg/> */}
          <CLabel className="mx-1">
            {/* {localStorage.getItem("userName")} */}
            {userProfile ? String(userProfile.name) : "---"}
          </CLabel>
          <TheHeaderDropdown />

          <CIcon
            name="cil-comment-square"
            size="2xl"
            title="Ajuda"
            onClick={handleClick}
          />
          <IconWithBadge
            count={countMailings}
            name="cil-people"
            size="2xl"
            title="Mailings"
            onClick={handleMailingClick}
          />
        </CHeaderNav>
      </CHeader>
      {mudarClienteModal && <MudarClienteModal onClose={onCloseClienteModal} />}
      <LigacoesCaidasModal
        isOpen={ligacaoTerminadaModal}
        onClose={() => setLigacaoTerminadaModal(false)}
        onSubmit={(result) => handleLigacaoCaidaModal(result)}
      />
      <IdentificarFinanciadoModal
        isOpen={showIdentificarFinanciado}
        onClose={() => setShowIdentificarFinanciadoModal(false)}
        dadosLigacao={message}
      />
      <QualificarOcorrenciaModal
        isOpen={qualificarLigacaoModal}
        onClose={() => setQualificarLigacaoModal(false)}
      />
      <AcionarPausaModal
        isOpen={showModalPausa}
        onClose={() => setShowModalPausa(false)}
        onSubmit={colocarEmPausa}
      />
      {showModalHistCall && (
        <HistCallModal
          isOpen={showModalHistCall}
          onClose={() => setShowModalHistCall(false)}
        />
      )}
      {showNumberInsertModal && (
        <ConfirmModal
          isOpen={showNumberInsertModal}
          texto={
            "Número não encontrado! Deseja cadastrar telefone em um cliente existente?"
          }
          onClose={(t) => {
            if (t) {
              if (phoneBanner) {
                setInserirTelefone(phoneBanner);
              }
              setMudarClienteModal(true);
            }
            setShowNumberInsertModal(false);
          }}
        />
      )}

      {showModalAdicionarTel && (
        <AdicionarTelefone
          onClose={() => setShowModalAdicionarTel(false)}
          isOpen={showModalAdicionarTel}
          onSave={handleAdicionar}
          tipoTelefone={tipoTelefone}
          inserirTelefone={inserirTelefone}
        />
      )}
      {isMailing && (mailings?.length ?? 0) > 0 && (<TheHeaderMailing mailings={mailings} />)}
      {/* {crmAuthModal && (
        <CrmAuthModal
          onClose={() => {
            setCrmAuthModal(false);
            setMudarClienteModal(true);
          }}
          chosenCrm={chosenCrm}
        />
      )} */}
    </>
  );
};

export default TheHeader;
