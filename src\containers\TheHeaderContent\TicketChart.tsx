import React from "react";
import { CChart } from "@coreui/react-chartjs";

function TicketChart({
  Datasets = [],
  Labels = [],
  title = "",
  displayTitle = false,
  legendPosition = "bottom",
}: {
  Datasets: Chart.ChartDataSets[];
  Labels: string[];
  title: string;
  displayTitle: boolean;
  legendPosition: Chart.PositionType;
}) {
  return (
    <CChart
      type="pie"
      datasets={Datasets}
      labels={Labels}
      options={{
        legend: {
          position: legendPosition,
        },
        title: {
          display: displayTitle,
          text: title,
        },
      }}
    />
  );
}

export default TicketChart;
