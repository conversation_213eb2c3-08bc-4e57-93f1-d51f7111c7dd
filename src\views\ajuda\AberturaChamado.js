import { useEffect, useState } from "react";
import SpeechRecognitionText from "src/hooks/useSpeechRecognitionHook.ts";

import { CButton, CCard, CCardBody, CForm, CFormGroup } from "@coreui/react";

import { useAuth } from "src/auth/AuthContext";
import { postApi } from "src/reusable/functions";
import { toast } from "react-toastify";
import LoadingComponent from "src/reusable/Loading";

const AberturaChamado = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Ajuda",
    submodulo: null,
  };

  const [observation, setObservation] = useState("");
  const [loading, setLoading] = useState(false);

  const handleObservationChange = (event) => {
    const newText = event.target.value;
    setObservation(newText);
  };

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const {
    hasRegognitionSupport,
    isListening,
    startListening,
    stopListening,
    speech,
  } = SpeechRecognitionText();

  useEffect(() => {
    if (speech !== "" && speech !== null && speech !== undefined) {
      setObservation(observation + " " + speech);
    }
  }, [speech]);

  const sendTicket = async () => {
    if (user === null || user === undefined) {
      toast.error("Falha ao buscar informações do usuário.");
      return;
    }
    setLoading(true);
    try {
      const res = await postApi(
        {
          description: observation,
          name: user?.name,
          email: user?.email,
        },
        "postAberturaChamado"
      );
      if (res.success) {
        toast.success("Ticket enviado com sucesso.");
        setObservation("");
      } else {
        toast.error("Falha ao enviar ticket de chamado.");
      }
    } catch (error) {
      toast.error("Falha ao enviar ticket de chamado.");
    }
    setLoading(false);
  };

  return (
    <div>
      <h3>Ajuda</h3>
      <p style={{ color: "gray" }}>
        Descreva (ou fale usando o microfone) sua dúvida ou problema:
      </p>
      <div
        style={{
          display: "flex",
        }}
      >
        <CCard
          style={{
            width: "100%",
          }}
        >
          <CCardBody>
            <CForm>
              <CFormGroup>
                <div style={{ display: "flex", gap: "5px" }}>
                  <textarea
                    style={{
                      width: "100%",
                      minHeight: "150px",
                      borderRadius: "5px",
                    }}
                    placeholder=" Insira aqui suas observações."
                    value={observation}
                    onChange={handleObservationChange}
                    // rows={5}
                  />
                  {hasRegognitionSupport && (
                    <div>
                      {isListening && (
                        <button
                          className="btn btn-warning"
                          onClick={stopListening}
                          style={{ height: "100%" }}
                        >
                          <i
                            className="cil-microphone"
                            style={{ fontSize: "20px", padding: "10px" }}
                          ></i>
                        </button>
                      )}
                      {!isListening && (
                        <button
                          className="btn btn-info"
                          onClick={startListening}
                          style={{ height: "100%" }}
                        >
                          <i
                            className="cil-microphone"
                            style={{ fontSize: "20px", padding: "10px" }}
                          ></i>
                        </button>
                      )}
                    </div>
                  )}
                </div>
                {/* {erros.Descricao && (
                  <div className="text-danger">{erros.Descricao}</div>
                )} */}
              </CFormGroup>
              <div className="d-flex justify-content-between">
                <div>
                  <CButton
                    title={inforPermissions(permissao).create}
                    disabled={
                      !checkPermission(
                        permissao.modulo,
                        "Create",
                        permissao.submodulo
                      )
                    }
                    color="info"
                    onClick={() => {
                      window.open(
                        "https://chamados.gvcsolucoes.com.br/index.php",
                        "_blank",
                        "noopener"
                      );
                      return false;
                    }}
                  >
                    Abrir sistema de chamado
                  </CButton>
                </div>
                <div>
                  <CButton color="warning">Limpar</CButton>
                  <CButton
                    title={inforPermissions(permissao).create}
                    disabled={
                      !checkPermission(
                        permissao.modulo,
                        "Create",
                        permissao.submodulo
                      )
                    }
                    color="primary"
                    className={"ml-2"}
                    onClick={sendTicket}
                  >
                    {loading && <LoadingComponent />}
                    {!loading && "Enviar para análise"}
                  </CButton>
                </div>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </div>
    </div>
  );
};

export default AberturaChamado;
