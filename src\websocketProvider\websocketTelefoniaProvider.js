import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

import * as signalR from "@microsoft/signalr";
import { GET_DATA, MountURIWebSocket, POST_DATA } from "src/api";
import { getURI, getUriWebSocket } from "src/config/apiConfig";
import {
  desligarLigacao,
  despausarLigacao,
  confirmarLigacao,
  transferirLigacao,
  acionarTabulacao,
  sairTabulacao,
  postAutenticar,
  deslogarAgente,
} from "src/config/telephonyFunctions";
import { GET_ClientData, GET_FINANCIADO } from "src/reusable/functions";
import { toast } from "react-toastify";
import { useAuth } from "src/auth/AuthContext";
import { useMyContext } from "src/reusable/DataContext";

const WebsocketTelefoniaContext = createContext(null);

export function WebsocketTelefoniaProvider({ children }) {
  const [dadosLigacao, setDadosLigacao] = useState(null);
  const [stateCall, setStateCall] = useState(null);
  const [phoneBanner, setPhoneBanner] = useState(null);
  const [callTypeBanner, setCallTypeBanner] = useState(null);
  const [passCode, setPassCode] = useState(null);
  const passCodeRef = useRef(null);
  const [connection, setConnection] = useState(null);
  const [message, setMessage] = useState(null);
  const [pauseButtonIcon, setPauseButtonIcon] = useState("cil-media-pause");
  const [emLigacao, setEmLigacao] = useState();
  const [emAtendimento, setEmAtendimento] = useState(false);
  const [qualificarLigacaoModal, setQualificarLigacaoModal] = useState(false);
  const [showIdentificarFinanciado, setShowIdentificarFinanciadoModal] =
    useState(false);
  const [showNumberInsertModal, setShowNumberInsertModal] = useState(false);

  const [showModalPausa, setShowModalPausa] = useState(false);
  const [ligacaoTerminadaModal, setLigacaoTerminadaModal] = useState(false);
  const ligacaoTerminadaModalRef = useRef(false);
  const [isLoading, setIsLoading] = useState(false);
  const [verificaModal, setVerificaModal] = useState(false);
  const primeiraAberturaModal = useRef(false);

  const [tableData, setTableData] = useState([]);
  const tableDataRef = useRef([]);
  const [status, setStatus] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [emPausa, setEmPausa] = useState(false);
  const [origem, setOrigem] = useState("");
  const [agentId, setAgentId] = useState(null);
  const [target, setTarget] = useState(null);

  const [lastInvocationTime, setLastInvocationTime] = useState(0);
  const confirmandoContratoFinanciadoRef = useRef(false);
  const [entrouLigacaoManual, setEntrouLigacaoManual] = useState(false);
  const statusRef = useRef(null);

  const callIdAgrupamentoRef = useRef(null);
  const messageRef = useRef(null);
  const showModalReconnectRef = useRef(false);
  const telefoneAdicionadoRef = useRef(false);
  const logouUmaVez = useRef(false);

  const getIsOpen = () => {
    return isOpen;
  };
  const setNewIsOpen = (value) => {
    setIsOpen(value);
  };
  // const [authToken, setAuthToken] = useState(localStorage.getItem('token'));
  const { authToken, user } = useAuth();
  const { data, updateData, updateCustas, updateCustasProjuris } =
    useMyContext();

  const intervalRef = useRef(null);

  function qualificarLigacao() {
    // const currentUrl = window.location.href;
    // if (!currentUrl.includes("/historico/ocorrencias")) {
    setShowIdentificarFinanciadoModal(false);
    setShowModalPausa(false);
    setLigacaoTerminadaModal(false);
    ligacaoTerminadaModalRef.current = false;
    setQualificarLigacaoModal(true);
    // }
  }
  const userProfile = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  useEffect(() => {
    if (shouldIgnoreWebsocketByRoute()) return;
    // Cria a conexão com o servidor SignalR
    // Obtém o valor do sessionStorage
    const isActiveTelephony = getIsActiveTelephony();

    if (authToken && isActiveTelephony) {
      const telephonyData =
        JSON.parse(localStorage.getItem("telephonyData")) || null;

      const newuser = user || userProfile;
      if (newuser) {
        const ramalTactium =
          JSON.parse(localStorage.getItem("ramalTactium")) || null;
        autenticarAgente(telephonyData, newuser, ramalTactium);
        const newConnection = new signalR.HubConnectionBuilder()
          .withUrl(
            MountURIWebSocket(
              getUriWebSocket("wsTelefonia"),
              { AccessToken: authToken },
              true
            ).toString(),
            {
              skipNegotiation: true,
              transport: signalR.HttpTransportType.WebSockets,
            }
          )
          .withAutomaticReconnect()
          .configureLogging(signalR.LogLevel.None)
          .build();
        setConnection(newConnection);
      }
    }
    return () => {
      // Limpa a conexão ao desmontar o componente
      if (connection) {
        connection
          .stop()
          .then(() => console.log("WebSocket SignalR foi fechado."))
          .catch((error) =>
            console.error("Erro ao fechar o WebSocket SignalR:", error)
          );
      }
    };
  }, [authToken, user]);

  useEffect(() => {
    return () => {
      // Limpa a conexão ao desmontar o componente
      if (connection) {
        connection.stop();
      }
      setConnection(null);
      setMessage(null);
      setStatus("Desconectado");
      setTableData([]);
      setPassCode(null);
    };
  }, []);

  useEffect(() => {
    if (connection && agentId && target) {
      connection
        .start()
        .then(() => {
          // Recebe mensagens do servidor
          connection.on("ReceiveMessage", async (message) => {
            if (agentId && target && message)
              connection.send(target, agentId, message);
          });

          connection.on("ResponseEvent", (message_event) => {
            messageRef.current = message_event;
            setMessage(message_event);
            if (message_event && message_event.passCode) {
              setPassCode(message_event.passCode);
              passCodeRef.current = message_event.passCode;
            }
            // if (
            //   message_event &&
            //   message_event.passCode &&
            //   passCodeRef.current !== message_event.passCode
            // ) {
            //   passCodeRef.current = message_event.passCode;
            // }

            statusRef.current = message_event?.status;
            callIdAgrupamentoRef.current = message_event?.callIdAgrupamento;

            flowCall(message_event);
            flowDropDownCall(message_event);
          });
        })
        .catch((error) => console.log("Erro ao conectar ao SignalR:", error));
    }
  }, [connection, agentId, target]);

  useEffect(() => {
    if (
      primeiraAberturaModal.current &&
      verificaModal &&
      (tableData !== null || tableData !== "") &&
      tableData.status === "Talking"
    ) {
      if (tableData?.callContractSearch?.length > 0) {
        setShowIdentificarFinanciadoModal(true);
        primeiraAberturaModal.current = false;
      }
      if (tableData?.callContractSearch == null && tableData?.name === null) {
        setShowNumberInsertModal(true);
        primeiraAberturaModal.current = false;
      }
    }
  }, [verificaModal, tableData]);

  const autenticarAgente = async (telephonyData, user, ramalTactium) => {
    let agent = null;
    let response = null;
    let idLogon = null;
    let args = null;

    if (telephonyData && (telephonyData.agentId || telephonyData.idLogon)) {
      agent = telephonyData.agentId;
      idLogon = telephonyData.idLogon;
    } else {
      //Se usuário for Tactium
      if (user.telephonyId === 1) {
        response = await postAutenticar(ramalTactium, args);
        if (response?.success) {
          agent = response.data.agentId;
          idLogon = response.data.idLogon;
          toast.success("Conectado ao Tactium com sucesso.");
        }
      }

      //Se usuário for Olos
      if (user.telephonyId === 2) {
        response = await postAutenticar();
        if (response?.success) {
          agent = response.data.agentId;
          toast.info("Buscando código OLOS...");
        } else {
          if (response?.success === false) {
            toast.warning("Falha na autenticação com a OLOS.");
            return;
          }
        }
      }
    }
    setAgentId(agent);
    setTarget(getTargetTelephony(user.telephonyId));
  };

  const flowCall = (dataWebsocket) => {
    try {
      const callData = dataWebsocket;

      if (callData?.stateCall !== undefined) {
        setStateCall(
          callData?.stateCall !== null || callData?.stateCall !== ""
            ? callData?.stateCall
            : null
        );
        setPhoneBanner(callData?.phone);
        setCallTypeBanner(callData?.callType);
      }
      if (callData?.status === "Idle") {
        //Fazer um resetState() com tudo isso depois
        setPauseButtonIcon((prevIcon) => "cil-media-pause");
        setEmLigacao(false);
        setEmAtendimento(false);
        setQualificarLigacaoModal(false);
        setEntrouLigacaoManual(false);
        // setConfirmandoContratoFinanciado(false);
        confirmandoContratoFinanciadoRef.current = false;
        ligacaoTerminadaModalRef.current = false;
        localStorage.removeItem("talkingOccurWrap");
      }
      if (
        callData?.status === "Wrap" ||
        callData?.status === "WrapWithEnding" ||
        callData?.status === "WrapWithManualCall" ||
        callData?.status === "WrapWithPrivateCallback" ||
        callData?.status === "WrapWithPause"
      ) {
        const currentTime = Date.now();
        if (currentTime - lastInvocationTime >= 20000) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;

          const talkingOccurWrap = localStorage.getItem("talkingOccurWrap")
            ? JSON.parse(localStorage.getItem("talkingOccurWrap"))
            : null;

          if (talkingOccurWrap !== null) {
            execQualificarLigacao(talkingOccurWrap);
          } else {
            if (
              (callData?.name !== null && callData.name !== "") ||
              callData?.callType === "Manual"
            ) {
              if (callData?.callId !== null) {
                const financiadoData = localStorage.getItem("financiadoData")
                  ? JSON.parse(localStorage.getItem("financiadoData"))
                  : null;
                if (
                  financiadoData.status === "Encerrado" &&
                  stringOrigem(callData.callType) === "Receptivo"
                ) {
                  desligaCobradoEncerrado(callData);
                } else {
                  qualificarLigacao();
                }
              }
            } else {
              if (telefoneAdicionadoRef.current === true) {
                qualificarLigacao();
              } else {
                ligacaoCaida(callData);
              }
            }
          }

          localStorage.removeItem("talkingOccurWrap");

          setLastInvocationTime(currentTime);
        }
        setEmLigacao(false);
        setEmAtendimento(true);
      }
      if (
        callData?.status === "ManualCall"
        // && callData.manualMode === true
      ) {
        if (!confirmandoContratoFinanciadoRef.current && !entrouLigacaoManual) {
          if (callData.callId !== null && callData.callId > 0) {
            confirmarFinanciado(callData.callId);
            // setConfirmandoContratoFinanciado(true);
            confirmandoContratoFinanciadoRef.current = true;
            setEntrouLigacaoManual(true);
          }
          if (callData.callIdTactium) {
            confirmarFinanciado(callData.callIdTactium);
            confirmandoContratoFinanciadoRef.current = true;
            // setConfirmandoContratoFinanciado(true);
            setEntrouLigacaoManual(true);
          }
        }
        setEmLigacao(true);
        setEmAtendimento(true);
      }
      if (
        callData.status === "Talking" ||
        callData.status === "TalkingWithEnding" ||
        callData.status === "TalkingWithManualCall" ||
        callData.status === "TalkingWithPause"
      ) {
        if (
          confirmandoContratoFinanciadoRef.current === false &&
          callData.manualMode === false
        ) {
          confirmandoContratoFinanciadoRef.current = true;
          // setConfirmandoContratoFinanciado(true);
          if (callData.callCrm != null && callData.callIdAgrupamento) {
            if (callData.callId !== null) {
              buscarFinanciadoEConfirmar(callData, "olos");
            }
            if (callData.callIdTactium) {
              buscarFinanciadoEConfirmar(callData, "tactium");
            }
          } else {
            if (
              !showIdentificarFinanciado &&
              callData.callId !== null &&
              callData.callId > 0
            ) {
              primeiraAberturaModal.current = true;
              setDadosLigacao(callData);
              setVerificaModal(true);
            }
          }
        }

        setEmLigacao(true);
      }
      if (callData.status === "Pause") {
        setPauseButtonIcon("cil-media-play");
        setEmLigacao(false);
      }
      if (callData.status === "Ending") {
      }
      if (callData.status === "Closed") {
        if (callData.manualCallMessage && callData.manualCallMessage !== "") {
          toast.warning(callData.manualCallMessage);
        }
      }
      if (callData.status === "Logout") {
        callIdAgrupamentoRef.current = null;
        if (logouUmaVez.current === true) {
          showModalReconnectRef.current = true;
        }
      }
      if (callData.callId === null && callData.callIdTactium === null) {
        setEmLigacao(false);
        setEmAtendimento(false);
      }
      if (
        confirmandoContratoFinanciadoRef.current &&
        callData.name &&
        callData.name !== ""
      ) {
        //setEmAtendimento(true);
      }
    } catch (error) {
      console.error("Erro parsing JSON:", error);
    }
  };

  const flowDropDownCall = (dataWebsocket) => {
    let binaOpenedOnce = false;
    setIsOpen(false);
    try {
      const callData = dataWebsocket;

      if (
        callData.callType !== undefined &&
        callData.callType !== "" &&
        callData.callType !== null
      ) {
        setOrigem(stringOrigem(callData.callType));
      } else {
        setOrigem("");
      }

      if (callData.callType === null) setOrigem("");

      if (!callData.status) {
        setTableData([]);
        tableDataRef.current = [];
        setStatus("Não conectado");
      }
      if (callData.status === "Logged") {
        setIsOpen(true);
      }
      if (callData.status === "Idle") {
        // setIsOpen(true);
        binaOpenedOnce = false;
        setBina("Idle", callData);
        logouUmaVez.current = true;
        showModalReconnectRef.current = false;
      }
      if (callData.status === "Talking" || callData.status === "ManualCall") {
        if (binaOpenedOnce === false) {
          setIsOpen(true);
          binaOpenedOnce = true;
        }
        setBina("Talking", callData);
      }
      if (callData.status === "TalkingWithPause") {
        setBina("TalkingWithPause", callData);
      }
      if (callData.status === "TalkingWithEnding") {
        if (binaOpenedOnce === false) {
          setIsOpen(true);
          binaOpenedOnce = true;
        }
        setBina("TalkingWithEnding", callData);
      }
      if (callData.status === "Pause") {
        setBina("Pause", callData);
        setEmPausa(true);
      }
      if (callData.status === "Wrap") {
        setBina("Wrap", callData);
      }
      if (callData.status === "WrapWithPause") {
        setBina("WrapWithPause", callData);
      }
      if (callData.status === "WrapWithEnding") {
        setBina("WrapWithEnding", callData);
      }
      if (callData.status === "Logout") {
        setBina("Logout", callData);
        setIsOpen(false);
      }
    } catch (error) {
      console.error("Erro parsing JSON:", error);
    }
  };

  function stringOrigem(origem = 0) {
    if (typeof origem == "number") {
      if (origem === 1) return "Discador";
      if (origem === 2) return "Manual";
      if (origem === 3) return "Ligação URA";
      if (origem === 0) return "Receptivo";
    }

    return "Receptivo";
  }
  function setBina(status, callData) {
    switch (status) {
      case "Idle":
        setStatus("Livre");
        setTableData(callData);
        tableDataRef.current = callData;
        setEmPausa(false);
        break;
      case "Talking":
        setStatus("Em ligação");
        setTableData(callData);
        tableDataRef.current = callData;
        break;
      case "TalkingWithPause":
        setStatus("Em ligação (Pausa)");
        setTableData(callData);
        tableDataRef.current = callData;
        break;
      case "TalkingWithEnding":
        setStatus("Em ligação (Logout)");
        setTableData(callData);
        break;
      case "TalkingManualCall":
        setStatus("Em ligação (Logout)");
        setTableData(callData);
        tableDataRef.current = callData;
        break;
      case "Wrap":
        setStatus("Qualificando");
        setTableData(callData);
        tableDataRef.current = callData;
        break;
      case "WrapWithPause":
        setStatus("Qualificando (Pausa)");
        setTableData(callData);
        tableDataRef.current = callData;
        break;
      case "WrapWithManualCall":
        setStatus("Qualificando");
        setTableData(callData);
        tableDataRef.current = callData;
        break;
      case "WrapWithEnding":
        setStatus("Qualificando (Logout)");
        setTableData(callData);
        tableDataRef.current = callData;
        break;
      case "Pause":
        setStatus("Pausado");
        setTableData([]);
        tableDataRef.current = [];
        break;
      case "Logout":
        setStatus("Desconectado");
        setTableData([]);
        tableDataRef.current = [];
        break;
      default:
        setStatus("Livre");
        setTableData([]);
        tableDataRef.current = [];
        break;
    }
  }
  const updateConnection = async (idcrm) => {
    const payload = {
      activeConnection: idcrm,
      userId: userProfile.id,
    };
    await postActiveConnection(payload, "postUserConnection")
      .then((data) => {
        if (data) {
          const newActiveConnection = {
            ...userProfile,
            activeConnection: idcrm,
          };
          localStorage.setItem("user", JSON.stringify(newActiveConnection));
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const getTargetTelephony = (telephonyId) => {
    if (telephonyId === 1) return "PulseTactium";
    if (telephonyId === 2) return "StreamingOlos";
    return null;
  };
  const updateFinanciado = async (financiado) => {
    await GET_FINANCIADO(financiado);
    await GET_ClientData(financiado);
    GET_ContratosAtivos(financiado);
  };

  const getContratosAtivos = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const GET_ContratosAtivos = (financiado) => {
    const payload = {
      IdFinanciado: financiado.id_Financiado,
      IdAgrupamento: financiado.id_Agrupamento,
    };
    getContratosAtivos(payload, "getContratosAtivos").then((data) => {
      if (data) {
        localStorage.setItem("contratosAtivos", JSON.stringify(data));
      }
    });
  };
  async function confirmarFinanciado(currentCallId, financiado = null) {
    if (localStorage.getItem("financiadoData")) {
      const financiadoAtualizado =
        financiado ?? JSON.parse(localStorage.getItem("financiadoData"));
      const idString = currentCallId.toString();
      const walletString =
        financiadoAtualizado?.grupo + " - " + financiadoAtualizado.cliente; //GRUPO
      const payload = {
        callId: idString,
        wallet: walletString,
        financedName: financiadoAtualizado.nome,
        idAgrupamento: financiadoAtualizado.id_Agrupamento,
        document: financiadoAtualizado.cpfCnpj,
        group: financiadoAtualizado?.grupo,
        idGroup: financiadoAtualizado.id_Grupo,
      };
      await confirmarLigacao(payload, message);
    }
  }
  async function ligacaoCaida(callData) {
    if (!ligacaoTerminadaModalRef.current) {
      setShowIdentificarFinanciadoModal(false);
      setQualificarLigacaoModal(false);
      setShowModalPausa(false);
      // setLigacaoTerminadaModal(true);
      ligacaoTerminadaModalRef.current = true;
      if (callData?.callId !== 0 && callData?.callId !== undefined) {
        const payload = {
          dispositionCode: "126",
          agentId: callData?.agentId,
          callId: callData?.callId,
          description: "126 - CLIENTE DESCONHECIDO",
        };
        await acionarTabulacao(payload);
      }
    }
  }

  async function desligaCobradoEncerrado(callData) {
    if (!ligacaoTerminadaModalRef.current) {
      setShowIdentificarFinanciadoModal(false);
      setQualificarLigacaoModal(false);
      setShowModalPausa(false);
      // setLigacaoTerminadaModal(true);
      ligacaoTerminadaModalRef.current = true;
      if (callData?.callId !== 0 && callData?.callId !== undefined) {
        const payload = {
          dispositionCode: "126",
          agentId: callData?.agentId,
          callId: callData?.callId,
          description: "021 - RECEPTIVO - ATEND CLIENTE",
        };
        await acionarTabulacao(payload);
      }
    }
  }

  const getAgentId = (telephonyData) => {
    if (telephonyData) {
      if (telephonyData.agentId) return telephonyData.agentId;
      if (telephonyData.idLogon) return telephonyData.idLogon;
    }
    return null;
  };
  async function execQualificarLigacao(talkingOccurWrap) {
    await acionarTabulacao(talkingOccurWrap);
    if (talkingOccurWrap.agentId) {
      await sairTabulacao(talkingOccurWrap.agentId);
    }
  }

  const postActiveConnection = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };
  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const buscarFinanciadoEConfirmar = async (callData, telefonia) => {
    const payload = {
      ActiveConnection: callData.callCrm,
      agrupamentoId: callData.callIdAgrupamento,
    };
    setIsLoading(true);
    await updateConnection(callData.callCrm);
    await GetData(payload, "getDadosContratoLigacao")
      .then((data) => {
        if (data && data.length > 0) {
          updateFinanciado(data[0]);
          localStorage.setItem("financiadoData", JSON.stringify(data[0])); //Atualizando o financiadoData no localStorage
          updateData(data[0]); //Atualizando o financiadoData no MyContext
          updateCustas(null);
          updateCustasProjuris(null);
          if (telefonia === "olos") {
            confirmarFinanciado(callData.callId, data[0]);
          }
          if (telefonia === "tactium") {
            confirmarFinanciado(callData.callIdTactium, data[0]);
          }
        } else {
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const LogoutAgente = async () => {
    try {
      const _user = user || userProfile;
      const ramalTactium = localStorage.getItem("ramalTactium")
        ? JSON.parse(localStorage.getItem("ramalTactium"))
        : null;

      const telephonyData = localStorage.getItem("telephonyData")
        ? JSON.parse(localStorage.getItem("telephonyData"))
        : null;

      if (telephonyData) {
        let payload = {};

        if (_user.telephonyId === 1) {
          payload = { ramal: ramalTactium };
        }

        if (_user.telephonyId === 2) {
          payload = {
            agentId: telephonyData.agentId,
          };
        }

        const success = await deslogarAgente(payload);
        console.info('provider deslogue');
        if (success === true) {
          localStorage.removeItem("callData");
          connection.stop();
        }
      }
    } catch (error) {
      console.error("Falha ao deslogar agente:", error);
    } finally {
      // Reset the flag when authentication is completed or failed
      //this.deslogarAgenteInProgress = false;
    }
  };

  const activateTelephony = () => {
    sessionStorage.setItem("activateTelephony", true);
  };

  function getIsActiveTelephony() {
    const isActiveTelephony = sessionStorage.getItem("activateTelephony")
      ? JSON.parse(sessionStorage.getItem("activateTelephony"))
      : false;
    return isActiveTelephony;
  }

  let wakeLock = null;

  // Função para solicitar o Wake Lock
  async function requestWakeLock() {
    try {
      wakeLock = await navigator.wakeLock.request("screen");
      console.log("Wake Lock ativo");

      // Adicionar um listener para detectar se o wake lock foi liberado
      wakeLock.addEventListener("release", () => {
        console.log("Wake Lock liberado");
      });
    } catch (err) {
      console.error(`${err.name}, ${err.message}`);
    }
  }

  // Função para liberar o Wake Lock
  function releaseWakeLock() {
    if (wakeLock !== null) {
      wakeLock.release();
      wakeLock = null;
      console.log("Wake Lock foi liberado manualmente");
    }
  }

  // Solicitar o Wake Lock ao carregar a página
  document.addEventListener("DOMContentLoaded", () => {
    requestWakeLock();

    // Opcional: liberar Wake Lock ao sair da página
    window.addEventListener("beforeunload", () => {
      releaseWakeLock();
    });
  });

  function shouldIgnoreWebsocketByRoute() {
    return window.location.hash.includes("/telefonia/softphone/olos");
  }

  return (
    <WebsocketTelefoniaContext.Provider
      value={{
        passCode,
        passCodeRef,
        stateCall,
        callTypeBanner,
        phoneBanner,
        dadosLigacao,
        emAtendimento,
        emLigacao,
        pauseButtonIcon,
        qualificarLigacaoModal,
        setQualificarLigacaoModal,
        setShowModalPausa,
        showModalPausa,
        confirmarFinanciado,
        isLoading,
        setIsLoading,
        connection,

        tableData,
        tableDataRef,
        status,
        setIsOpen,
        origem,
        emPausa,
        getIsOpen,
        setNewIsOpen,
        LogoutAgente,
        message,
        messageRef,
        showIdentificarFinanciado,
        setShowIdentificarFinanciadoModal,
        statusRef,
        buscarFinanciadoEConfirmar,
        setDadosLigacao,
        callIdAgrupamentoRef,
        showModalReconnectRef,
        logouUmaVez,
        showNumberInsertModal,
        setShowNumberInsertModal,
        activateTelephony,
        getIsActiveTelephony,
        telefoneAdicionadoRef,
      }}
    >
      {children}
    </WebsocketTelefoniaContext.Provider>
  );
}

export const useWebsocketTelefoniaContext = () => {
  const context = useContext(WebsocketTelefoniaContext);
  if (!context) {
    throw new Error(
      "useWebsocketTelefoniaContext must be used within a ContextProvider"
    );
  }
  return context;
};
