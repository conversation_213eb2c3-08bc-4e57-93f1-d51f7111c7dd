import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CFormGroup,
  CLabel,
  CInput,
} from "@coreui/react";

function CreateRoleModal({
  isOpen,
  // toggle,
  role,
  onRoleCreate,
  onSave,
  onClose,
}) {
  const [roleName, setRoleName] = useState("");

  
  // Helper function to check if a field is empty
  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };


  const handleCreateRole = () => {

    if (isFieldEmpty(roleName)) {
      alert(`Por favor preencha o nome da função.`);
      return;
    }
    
    if (role) {
      const confirmed = window.confirm(
        "Tem certeza que deseja alterar o nome dessa função?"
      );
      if (confirmed) {
        const editedRole = { ...role, name: roleName };
        onSave(editedRole);
        handleClose();
      }
    } else {
      const confirmed = window.confirm(
        "Tem certeza que deseja criar essa função?"
      );
      if (confirmed) {
        const newRole = {
          name: roleName,
        };
        onRoleCreate(newRole);
        handleClose()
      }
    }
  };

  useEffect(() => {
    // If a role is provided (editing mode), pre-fill the form fields with its data
    if (role) {
      setRoleName(role.name);
    } else {
      setRoleName("");
    }
  }, [role]);

  const handleClose = () => {
    setRoleName("");
    onClose();
  };

  return (
    <CModal show={isOpen} onClose={handleClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>
        <CModalTitle>
          {" "}
          {role ? "Editar função" : "Criar nova função"}{" "}
        </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CFormGroup>
          <CLabel htmlFor="roleName">Nome da função</CLabel>
          <CInput
            id="roleName"
            type="text"
            placeholder="Insira aqui o nome da função"
            value={roleName}
            onChange={(e) => setRoleName(e.target.value)}
          />
        </CFormGroup>
      </CModalBody>
      <CModalFooter>
        <CButton color="primary" onClick={handleCreateRole}>
          Salvar
        </CButton>
        {/* <CButton color="secondary" onClick={toggle}>
          Cancel
        </CButton> */}
      </CModalFooter>
    </CModal>
  );
}

export default CreateRoleModal;
