import React from "react";
import { getApi } from "./reusable/functions";
import { GET_DATA } from "./api";
import { getURI } from "./config/apiConfig";

const TelaPrincipal = React.lazy(() =>
  import("./views/telaPrincipal/telaPrincipal")
);
const GerenciarUsuarios = React.lazy(() =>
  import("./views/configuracoes/gerenciarUsuarios/gerenciarUsuarios")
);
const Funcoes = React.lazy(() =>
  import("./views/configuracoes/funcoes/funcoes")
);
const FaqsTopics = React.lazy(() =>
  import("./views/configuracoes/faqs/FaqsTopics")
);
const FaqsScreen = React.lazy(() =>
  import("./views/configuracoes/faqs/FaqsScreen")
);
const GruposClientes = React.lazy(() =>
  import("./views/configuracoes/gruposClientes/gruposClientes")
);
const NegociarCotas = React.lazy(() =>
  import("./views/negociacao/negociarCotas")
);
const ConfigurationCrm = React.lazy(() =>
  import("./views/configuracoes/configurationCrm/ConfigurationCrm")
);
const IntegrationConfigurations = React.lazy(() =>
  import(
    "./views/configuracoes/IntegrationConfiguration/IntegrationConfigurations"
  )
);

const Mailings = React.lazy(() => import("./views/mailings/mailing/Mailings"));

const NegociarSafra = React.lazy(() =>
  import("./views/negociacao/safra/Negociar")
);

const NegociarBBC = React.lazy(() =>
  import("./views/negociacao/BBC/index.tsx")
);

const VisualizarAcordos = React.lazy(() => import("./views/acordos/acordos"));
const CriarAcordos = React.lazy(() => import("./views/acordos/CriarAcordos"));
const CriarAcordoManual = React.lazy(() =>
  import("./views/acordos/CriarAcordoManual.js")
);
const AvalistaTerceiros = React.lazy(() =>
  import("./views/avalistaTerceiros/avalistaTerceiros")
);
const Custas = React.lazy(() => import("./views/juridico/custas"));
const FaixaAtraso = React.lazy(() => import("./views/faixaAtraso/faixaAtraso"));
const ParametriCampanhas = React.lazy(() =>
  import("./views/telefonia/parametriCampanhas")
);
const ParametriPausasTactium = React.lazy(() =>
  import("./views/telefonia/parametriPausasTactium")
);

const ParametriCalculos = React.lazy(() =>
  import("./views/configuracoes/parametriCalculos/parametriCalculos")
);

const ParametriRestricoesNegociacao = React.lazy(() =>
  import(
    "./views/configuracoes/parametriRestricoesNegociacao/parametriRestricoesNegociacao"
  )
);
const ListaPedidoAnaliseRestricao = React.lazy(() =>
  import(
    "./views/configuracoes/parametriRestricoesNegociacao/listaPedidoAnaliseRestricao"
  )
);

const CorJuridicoParam = React.lazy(() =>
  import("./views/configuracoes/corJuridicoParam/corJuridicoParam")
);
const ParametriParcelamentoBBC = React.lazy(() =>
  import(
    "./views/configuracoes/parametriParcelamentoBBC/parametriParcelamentoBBC"
  )
);

const ParametriDescontoBBC = React.lazy(() =>
  import("./views/configuracoes/parametriDescontoBBC/parametriDescontoBBC")
);

const ConfigTelaUnica = React.lazy(() =>
  import("./views/configuracoes/configTelaUnica/configTelaUnica")
);
const ListLogsUsers = React.lazy(() =>
  import("./views/configuracoes/listLogsUsers/listLogsUsers")
);

const Ocorrencias = React.lazy(() => import("./views/ocorrencias/ocorrencias"));

const EmDesenvolvimento = React.lazy(() =>
  import("./views/pages/construcao/construcao")
);

const Toaster = React.lazy(() =>
  import("./views/notifications/toaster/Toaster")
);
const Tables = React.lazy(() => import("./views/base/tables/Tables"));

const Breadcrumbs = React.lazy(() =>
  import("./views/base/breadcrumbs/Breadcrumbs")
);
const Cards = React.lazy(() => import("./views/base/cards/Cards"));
const Carousels = React.lazy(() => import("./views/base/carousels/Carousels"));
const Collapses = React.lazy(() => import("./views/base/collapses/Collapses"));
const BasicForms = React.lazy(() => import("./views/base/forms/BasicForms"));

const Jumbotrons = React.lazy(() =>
  import("./views/base/jumbotrons/Jumbotrons")
);
const ListGroups = React.lazy(() =>
  import("./views/base/list-groups/ListGroups")
);
const Navbars = React.lazy(() => import("./views/base/navbars/Navbars"));
const Navs = React.lazy(() => import("./views/base/navs/Navs"));
const Paginations = React.lazy(() =>
  import("./views/base/paginations/Pagnations")
);
const Popovers = React.lazy(() => import("./views/base/popovers/Popovers"));
const ProgressBar = React.lazy(() =>
  import("./views/base/progress-bar/ProgressBar")
);
const Switches = React.lazy(() => import("./views/base/switches/Switches"));

const Tabs = React.lazy(() => import("./views/base/tabs/Tabs"));
const Tooltips = React.lazy(() => import("./views/base/tooltips/Tooltips"));
const BrandButtons = React.lazy(() =>
  import("./views/buttons/brand-buttons/BrandButtons")
);
const ButtonDropdowns = React.lazy(() =>
  import("./views/buttons/button-dropdowns/ButtonDropdowns")
);
const ButtonGroups = React.lazy(() =>
  import("./views/buttons/button-groups/ButtonGroups")
);
const Buttons = React.lazy(() => import("./views/buttons/buttons/Buttons"));
const Charts = React.lazy(() => import("./views/charts/Charts"));
const Dashboard = React.lazy(() => import("./views/dashboard/Dashboard"));
const CoreUIIcons = React.lazy(() =>
  import("./views/icons/coreui-icons/CoreUIIcons")
);
const Flags = React.lazy(() => import("./views/icons/flags/Flags"));
const Brands = React.lazy(() => import("./views/icons/brands/Brands"));
const Alerts = React.lazy(() => import("./views/notifications/alerts/Alerts"));
const Badges = React.lazy(() => import("./views/notifications/badges/Badges"));
const Modals = React.lazy(() => import("./views/notifications/modals/Modals"));
const Colors = React.lazy(() => import("./views/theme/colors/Colors"));
const Typography = React.lazy(() =>
  import("./views/theme/typography/Typography")
);
const Widgets = React.lazy(() => import("./views/widgets/Widgets"));
const Users = React.lazy(() => import("./views/users/Users"));
const User = React.lazy(() => import("./views/users/User"));
const Ajuda = React.lazy(() => import("./views/ajuda/Ajuda"));
const AberturaChamado = React.lazy(() =>
  import("./views/ajuda/AberturaChamado")
);
const NewconIframe = React.lazy(() =>
  import("./views/randon/newcon/NewconIframe")
);
const Campaigns = React.lazy(() => import("./views/safra/campanhas/campanhas"));
const CampaignsRegistry = React.lazy(() =>
  import("./views/safra/cadastroCampanhas/cadastroCampanhas")
);
const SafraPermissoes = React.lazy(() =>
  import("./views/safra/permissoes/permissoes")
);
const ControleUsuarios = React.lazy(() =>
  import("./views/configuracoes/controleUsuarios/controleUsuarios")
);
const ParamDelay = React.lazy(() =>
  import("./views/configuracoes/simulacoes/paramDelay")
);

const RegrasOcorrencias = React.lazy(() =>
  import("./views/configuracoes/regrasOcorrencias/regrasOcorrencias")
);

const LogIntegracoes = React.lazy(() =>
  import("./views/configuracoes/logIntegracoes/LogIntegracoes.tsx")
);

const ListaPendenciaAprovacaoAbaixoRegua = React.lazy(() =>
  import("./views/aprovacaoAbaixoRegua/listaPendenciaAprovacao.tsx")
);

const PrestadoresServicos = React.lazy(() =>
  import("./views/configuracoes/prestadoresServicos/prestadoresServicos.tsx")
);

const CartasETermosFilaAprovacao = React.lazy(() =>
  import("./views/cartasETermos/filaAprovacao/FilaAprovacao.tsx")
);

const routes = [
  {
    path: "/telaprincipal",
    exact: true,
    name: "Tela Principal",
    component: TelaPrincipal,
  },
  {
    path: "/configuracoes/gerenciarusuario",
    exact: true,
    name: "Gerenciar Usuários",
    component: GerenciarUsuarios,
  },
  {
    path: "/configuracoes/funcoes",
    exact: true,
    name: "Funcoes",
    component: Funcoes,
  },
  {
    path: "/configuracoes/gruposClientes",
    exact: true,
    name: "GruposClientes",
    component: GruposClientes,
  },
  {
    path: "/configuracoes/logIntegracoes",
    exact: true,
    name: "LogIntegracoes",
    component: LogIntegracoes,
  },
  {
    path: "/faqs/topics",
    exact: true,
    name: "Faqs",
    component: FaqsTopics,
  },
  {
    path: "/mailings/mailing",
    exact: true,
    name: "Mailings",
    component: Mailings,
  },
  {
    path: "/configuracoes/configurationCrm",
    exact: true,
    name: "ConfigurationCrm",
    component: ConfigurationCrm,
  },
  {
    path: "/configuracoes/IntegrationConfiguration",
    exact: true,
    name: "IntegrationConfiguration",
    component: IntegrationConfigurations,
  },
  {
    path: "/faqs/faqsscreen",
    exact: true,
    name: "Faqs",
    component: FaqsScreen,
  },
  {
    path: "/negociar/cotas",
    exact: true,
    name: "NegociarCotas",
    component: NegociarCotas,
    permission: "Negociação",
  },
  {
    path: "/negociar/safra",
    exact: true,
    name: "NegociarSafra",
    component: NegociarSafra,
    permission: "Negociação",
  },
  {
    path: "/negociar/BBC",
    exact: true,
    name: "NegociarBBC",
    component: NegociarBBC,
    permission: "Negociação",
  },
  {
    path: "/negociar/BBC",
    exact: true,
    name: "NegociarBBC",
    component: NegociarBBC,
    permission: "Negociação",
  },
  {
    path: "/acordos/visualizar",
    exact: true,
    name: "VisualizarAcordos",
    component: VisualizarAcordos,
    permission: "Negociação",
  },
  {
    path: "/acordo/criar",
    exact: true,
    name: "CriarAcordos",
    component: CriarAcordos,
    permission: "Negociação",
  },
  {
    path: "/avalistas",
    exact: true,
    name: "AvalistaTerceiros",
    component: AvalistaTerceiros,
    permission: "Avalista/Terceiros",
  },
  {
    path: "/juridico/custas",
    exact: true,
    name: "Custas",
    component: Custas,
    permission: "Custas",
  },
  {
    path: "/faixaAtraso",
    exact: true,
    name: "FaixaAtraso",
    component: FaixaAtraso,
    permission: "FaixaAtraso",
  },
  {
    path: "/simulacaoCalculos",
    exact: true,
    name: "ParametriCalculos",
    component: ParametriCalculos,
    permission: "ParametriCalculos",
  },
  {
    path: "/AdicionarRestricoesNegociacao",
    exact: true,
    name: "ParametriRestricoesNegociacao",
    component: ParametriRestricoesNegociacao,
    permission: "ParametriRestricoesNegociacao",
  },
  {
    path: "/ListaPedidoAnaliseRestricao",
    exact: true,
    name: "ListaPedidoAnaliseRestricao",
    component: ListaPedidoAnaliseRestricao,
    permission: "ListaPedidoAnaliseRestricao",
  },
  {
    path: "/telefonia/campanhas",
    exact: true,
    name: "ParametriCampanhas",
    component: ParametriCampanhas,
    permission: "ParametriCampanhas",
  },
  {
    path: "/telefonia/parametriPausas",
    exact: true,
    name: "ParametriPausasTactium",
    component: ParametriPausasTactium,
    permission: "ParametriPausasTactium",
  },
  {
    path: "/TelaUnicaSettings",
    exact: true,
    name: "ConfigTelaUnica",
    component: ConfigTelaUnica,
    permission: "ConfigTelaUnica",
  },
  {
    path: "/ListLogsUsers",
    exact: true,
    name: "ListLogsUsers",
    component: ListLogsUsers,
    permission: "ListLogsUsers",
  },
  {
    path: "/historico/ocorrencias",
    exact: true,
    name: "Ocorrencias",
    component: Ocorrencias,
  },
  {
    path: "/rodobens/newcon",
    exact: true,
    name: "RandonNewCon",
    component: NewconIframe,
    permission: "Randon",
  },
  {
    path: "/indicativoJuridico",
    exact: true,
    name: "indicativoJuridico",
    component: CorJuridicoParam,
    permission: "indicativoJuridico",
  },
  {
    path: "/parcelamentoBBC",
    exact: true,
    name: "Parcelamento BBC",
    component: ParametriParcelamentoBBC,
  },
  {
    path: "/descontoBBC",
    exact: true,
    name: "Desconto BBC",
    component: ParametriDescontoBBC,
  },
  {
    path: "/desenvolvimento",
    exact: true,
    name: "EmDesenvolvimento",
    component: EmDesenvolvimento,
  },

  { path: "/", exact: true, name: "Home" },
  { path: "/dashboard", name: "Dashboard", component: Dashboard },
  { path: "/theme", name: "Theme", component: Colors, exact: true },
  { path: "/theme/colors", name: "Colors", component: Colors },
  { path: "/theme/typography", name: "Typography", component: Typography },
  { path: "/base", name: "Base", component: Cards, exact: true },
  { path: "/base/breadcrumbs", name: "Breadcrumbs", component: Breadcrumbs },
  { path: "/base/cards", name: "Cards", component: Cards },
  { path: "/base/carousels", name: "Carousel", component: Carousels },
  { path: "/base/collapses", name: "Collapse", component: Collapses },
  { path: "/base/forms", name: "Forms", component: BasicForms },
  { path: "/base/jumbotrons", name: "Jumbotrons", component: Jumbotrons },
  { path: "/base/list-groups", name: "List Groups", component: ListGroups },
  { path: "/base/navbars", name: "Navbars", component: Navbars },
  { path: "/base/navs", name: "Navs", component: Navs },
  { path: "/base/paginations", name: "Paginations", component: Paginations },
  { path: "/base/popovers", name: "Popovers", component: Popovers },
  { path: "/base/progress-bar", name: "Progress Bar", component: ProgressBar },
  { path: "/base/switches", name: "Switches", component: Switches },
  { path: "/base/tables", name: "Tables", component: Tables },
  { path: "/base/tabs", name: "Tabs", component: Tabs },
  { path: "/base/tooltips", name: "Tooltips", component: Tooltips },
  { path: "/buttons", name: "Buttons", component: Buttons, exact: true },
  { path: "/buttons/buttons", name: "Buttons", component: Buttons },
  {
    path: "/buttons/button-dropdowns",
    name: "Dropdowns",
    component: ButtonDropdowns,
  },
  {
    path: "/buttons/button-groups",
    name: "Button Groups",
    component: ButtonGroups,
  },
  {
    path: "/buttons/brand-buttons",
    name: "Brand Buttons",
    component: BrandButtons,
  },
  { path: "/charts", name: "Charts", component: Charts },
  { path: "/icons", exact: true, name: "Icons", component: CoreUIIcons },
  { path: "/icons/coreui-icons", name: "CoreUI Icons", component: CoreUIIcons },
  { path: "/icons/flags", name: "Flags", component: Flags },
  { path: "/icons/brands", name: "Brands", component: Brands },
  {
    path: "/notifications",
    name: "Notifications",
    component: Alerts,
    exact: true,
  },
  { path: "/notifications/alerts", name: "Alerts", component: Alerts },
  { path: "/notifications/badges", name: "Badges", component: Badges },
  { path: "/notifications/modals", name: "Modals", component: Modals },
  { path: "/notifications/toaster", name: "Toaster", component: Toaster },
  { path: "/widgets", name: "Widgets", component: Widgets },
  { path: "/users", exact: true, name: "Users", component: Users },
  { path: "/users/:id", exact: true, name: "User Details", component: User },
  /* Rota de Ajuda */
  // {
  //   path: "/ajuda",
  //   name: "Ajuda",
  //   component: function () {
  //     window.open(
  //       "https://chamados.ciatecnica.com.br/index.php",
  //       "_blank",
  //       "noopener"
  //     );
  //     return false;
  //   },
  //   exact: true,
  // },
  {
    path: "/ajuda",
    name: "Ajuda",
    component: AberturaChamado,
    exact: true,
  },
  {
    path: "/safraCampanhas",
    exact: true,
    name: "Safra Campanhas",
    component: Campaigns,
  },
  {
    path: "/safraCadastroCampanha",
    exact: true,
    name: "Safra Cadastro de Campanhas",
    component: CampaignsRegistry,
  },
  {
    path: "/safraUsuarios",
    exact: true,
    name: "Usuarios",
    component: SafraPermissoes,
  },
  {
    path: "/controleUsuarios",
    exact: true,
    name: "controleUsuarios",
    component: ControleUsuarios,
  },
  {
    path: "/AtrasoAditamento",
    exact: true,
    name: "AtrasoAditamento",
    component: ParamDelay,
  },
  {
    path: "/regrasOcorrencias",
    exact: true,
    name: "Regras Ocorrencias",
    component: RegrasOcorrencias,
  },
  {
    path: "/aprovacaoAbaixoRegua",
    exact: true,
    name: "AprovacaoAbaixoRegua",
    component: ListaPendenciaAprovacaoAbaixoRegua,
  },
  {
    path: "/acordo/manual/criar",
    exact: true,
    name: "CriarAcordoManual",
    component: CriarAcordoManual,
    permission: "Negociação",
  },
  {
    path: "/cartas-e-termos/fila-de-aprovacao",
    exact: true,
    name: "CartasETermosFilaAprovacao",
    component: CartasETermosFilaAprovacao,
  },
  {
    path: "/prestadores_servicos",
    exact: true,
    name: "PrestadoresServicos",
    component: PrestadoresServicos,
  },
];

export default routes;
