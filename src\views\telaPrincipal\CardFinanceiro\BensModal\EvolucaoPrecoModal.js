import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CDataTable,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CRow,
  CCol,
  CCard,
  CCardBody,
  CLabel,
} from "@coreui/react";
import { formatThousands, formatDate } from "src/reusable/helpers";
import LoadingComponent from "src/reusable/Loading";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import TableSelectItens from "src/reusable/TableSelectItens";

const EvolucaoPrecoModal = ({ isOpen, onClose, idCota }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [headerTableData, setHeaderTableData] = useState([]);
  const [tableData, setTableData] = useState([]);

  const fields = [
    {
      key: "adjustmentDate",
      label: "Data do Reajuste",
      formatter: (value) => formatDate(value),
      defaultSort: "descending",
    },
    {
      key: "effectiveDate",
      label: "Data da Efetivação",
      formatter: (item) => renderCellDate(item),
    },
    {
      key: "percDeal",
      label: "Taxas",
      formatter: (value) => formatThousands(value),
    },
    {
      key: "valuePrice",
      label: "Preço",
      formatter: (value) => formatThousands(value),
    },
    {
      key: "percVariation",
      label: "% Variação",
      // formatter: (value) => formatThousands(value),
    },
    {
      key: "stPrice",
      label: "#",
    },
  ];

  const renderCellDate = (item) => {
    return item ? formatDate(item) : "---";
  };

  const getEvolucaoPreco = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      getEvolucaoPreco({ idCota: idCota }, "postnewconevopreco")
        .then((response) => {
          if (response) {
            const data = [
              {
                codFiscalRegion: response.data.codFiscalRegion,
                valueGood: response.data.valueGood,
                codGood: response.data.codGood,
                nameFiscalRegion: response.data.nameFiscalRegion,
                nameGood: response.data.nameGood,
                valueCategory: response.data.valueCategory,
                peCreditNegoation: response.data.peCreditNegoation,
              },
            ];
            setHeaderTableData([
              {
                label: "Bem",
                value: data[0].nameGood,
              },
              {
                label: "Código do Bem",
                value: data[0].codGood,
              },
              {
                label: "Valor do Bem",
                value: "R$" + formatThousands(data[0].valueGood),
              },
              {
                label: "Valor de Categoria",
                value: "R$" + formatThousands(data[0].valueCategory),
              },
              {
                label: "Percentual de Negociação do Crédito",
                value: data[0].peCreditNegoation,
              },
              {
                label: "Região Comercial da Cota",
                value: data[0].codFiscalRegion + " " + data[0].nameFiscalRegion,
              },
            ]);
            setTableData(response.data.evolutionPriceGoodEntityService);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="lg"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Evolução de Preço do Bem</h5>
      </CModalHeader>
      <CModalBody>
        {isLoading ? (
          <div>
            <LoadingComponent />
          </div>
        ) : (
          <>
            {headerTableData && (
              <>
                <table className="table table-hover calculo">
                  <tbody>
                    {headerTableData.map((row) => (
                      <CRow key={row.label}>
                        <CCol md="6" style={{ textAlign: "end" }}>
                          <strong>{row.label}:</strong>
                        </CCol>
                        <CCol md="6">{row.value ? row.value : "---"}</CCol>
                      </CRow>
                    ))}
                  </tbody>
                </table>
                <CCard style={{ overflowY: "auto" }}>
                  <TableSelectItens
                    data={tableData}
                    columns={fields}
                    onSelectionChange={(_) => {}}
                    defaultSelectedKeys={[]}
                    selectable={false}
                    heightParam="420px"
                  />
                </CCard>
                <CCol className="mx-2">
                  <CRow>
                    <CLabel>Legenda (#)</CLabel>
                  </CRow>
                  <CRow>
                    <CCol>
                      <CRow>I - Preço Inicial</CRow>
                      <CRow>A - Aumento de Preço</CRow>
                    </CCol>
                    <CCol>
                      <CRow>S - Substituição</CRow>
                      <CRow>X - Aumento de Indicador</CRow>
                    </CCol>
                  </CRow>
                </CCol>
              </>
            )}
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default EvolucaoPrecoModal;
