import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CDataTable,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CCard,
  CCardBody,
} from "@coreui/react";
import {
  formatThousands,
  formatDate,
  formatCodigoNewcon,
} from "src/reusable/helpers";
import LoadingComponent from "src/reusable/Loading";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import TableSelectItens from "src/reusable/TableSelectItens";

const ValoresPagosModal = ({ isOpen, onClose, idCota }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [tableData, setTableData] = useState([]);

  const fields = [
    {
      key: "installmentNumber",
      label: "Parcela",
      formatter: (item) => renderParcela(item),
    },
    {
      key: "idCodFinanMovement",
      label: "Cd. Movimento",
      formatter: (item) => formatarNumero(item)
    },
    { key: "nameCodFinanMovement", label: "Histórico",className: "nowrap-cell"},
    {
      key: "accountingDate",
      label: "Contabilização",
      formatter: (item) => formatDate(item),
    },
    {
      key: "dueDate",
      label: "Vencimento",
      formatter: (item) => formatDate(item),
    },
    {
      key: "paymentDate",
      label: "Pagamento",
      formatter: (item) => formatDate(item),
    },
    { key: "idGroupMovement", label: "Aviso" },
    {
      key: "refundNumber",
      label: "Estorno",
      formatter: (item) => formatThousands(item),
    },
    {
      key: "percNormal",
      label: "% Normal",
      formatter: (item) => formatThousands(item),
    },
    {
      key: "percDifference",
      label: "Diferença",
      formatter: (item) => formatThousands(item),
    },
    {
      key: "paidValue",
      label: "Valor",
      formatter: (item) => formatThousands(item),
    },
  ];

  const formatarNumero = (num) => {
    let str = num.toString();
    let length = str.length;

    if (length === 1) {
        // Se tiver apenas 1 dígito, preenche com zeros à esquerda e adiciona o dígito original após o hífen
        return '00' + str + '-' + str;
    } else {
        // Adiciona zeros à esquerda para ter 3 dígitos antes do hífen e coloca o último dígito original após o hífen
        return str.padStart(4, '0').slice(0, 3) + '-' + str.slice(-1);
    }
}

  const renderParcela = (item) => {
    let str = item.toString();
    return <div>{str.padStart(3, '0')}</div>;
  };

  const getValoresPagos = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };
  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      getValoresPagos(`/${idCota}`, "postNewconValoresPagos")
        // getValoresPagos({ idCota: idCota }, "postnewconvalorespagos")
        .then((data) => {
          if (data) {
            setTableData(data.data);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="xl"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Valores Pagos</h5>
      </CModalHeader>
      <CModalBody>
        {isLoading ? (
          <div>
            <LoadingComponent />
          </div>
        ) : (
          <div className="container-fluid">
            <TableSelectItens
              data={tableData}
              columns={fields}
              onSelectionChange={(_) => {}}
              defaultSelectedKeys={[]}
              selectable={false}
              heightParam="580px"
            />
          </div>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default ValoresPagosModal;
