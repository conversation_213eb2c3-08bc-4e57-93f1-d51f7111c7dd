import { useState, useEffect, useMemo } from "react";
import Select from "react-select";
import {
  CModal, CModalHeader, CModalBody, CModalFooter, CModalTitle, CFormGroup, CInput,
  CButton, CLabel, CBadge, CInputGroup, CInputGroupText, CInputGroupAppend
} from "@coreui/react";
import "./cardStyles.css";
import { getURI } from "src/config/apiConfig";
import { GET_DATA, POST_DATA, PUT_DATA } from "src/api";
import CardLoading from "src/reusable/CardLoading";

const PostData = async (payload, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI(endpoint),
        payload,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const PutData = async (payload, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await PUT_DATA(
        getURI(endpoint),
        payload,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(getURI(endpoint), payload, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const CreateGroupModal = ({ isOpen, onClose, dataEdit = null, edit = false }) => {

  const [gruposOptions, setGruposOptions] = useState([]);
  const [crmsOptions, setCrmsOptions] = useState([]);
  const [usersOptions, setUsersOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [textLoading, setTextLoading] = useState("Carregando...");

  const [selectedColor, setSelectedColor] = useState([]);
  const [crmSelected, setCrmSelectd] = useState(null);
  const [grupoSelected, setGrupoSelected] = useState(null);
  const [timeEndCallSelected, setTimeEndCallSelected] = useState(-1);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [inputName, setInputName] = useState("");
  const [erros, setErros] = useState({ name: "", crm: "", grupo: "", users: "", color: "" });
  const resetErros = { name: "", crm: "", grupo: "", users: "", color: "" };

  const handleNameChange = (e) => {
    const { value } = e.target;
    const pattern = /^[a-zA-Z0-9\sáàâãéèêíïóôõöúüçñÁÀÂÃÉÈÊÍÏÓÔÕÖÚÜÇÑ]+$/;
    const isValid = pattern.test(value);

    if (isValid || value === "") {
      setInputName(value);
    }
  };

  const handleTimeEndCallChange = (e) => {
    const { value } = e.target;
    if (value || value > 0) {
      setTimeEndCallSelected(value);
    } else if (value || value <= 0) {
      setTimeEndCallSelected(-1);
    }
  };


  const handleUserChange = (selectedOptions) => {
    setSelectedUsers(selectedOptions);
  };

  const handleCrmsChange = (selectedOptions) => {
    setGrupoSelected(null);
    setCrmSelectd(selectedOptions);
    setGruposOptions(selectedOptions.grupos);
  }

  const handleGrupoChange = (selectedOptions) => {
    if (inputName === "" || inputName === null || inputName === undefined) {

      setInputName(selectedOptions.descricao);
    }
    setGrupoSelected(selectedOptions);
  }

  const handleColorChange = (selectedOption) => {
    setSelectedColor(selectedOption);
  };

  const colors = useMemo(() => [
    {
      idColor: "0",
      nameColor: "Branco",
      classColor: " ",
    },
    {
      idColor: "1",
      nameColor: "Azul",
      classColor: "bg-blue",
    },
    {
      idColor: "2",
      nameColor: "Verde",
      classColor: "bg-green",
    },
    {
      idColor: "3",
      nameColor: "Amarelo",
      classColor: "bg-yellow",
    },
    {
      idColor: "4",
      nameColor: "Laranja",
      classColor: "bg-orange",
    },
    {
      idColor: "5",
      nameColor: "Vermelho",
      classColor: "bg-red",
    },
    {
      idColor: "6",
      nameColor: "Roxo",
      classColor: "bg-purple",
    },
  ], []);

  const colorOptions = useMemo(() =>
    colors.map((color) => ({
      nameColor: color.nameColor,
      idColor: color.idColor,
      classColor: color.classColor,
    })),
    [colors]
  );

  const handleSave = (e) => {
    e.preventDefault();

    if (!validarFomrulario()) return;


    const payload = montarPayload();
    setIsLoading(true);
    setTextLoading("Salvando Grupo...");

    if (edit)
      PutData(payload, "putGrupoAtualizar")
        .then((res) => {
          if (!res.success)
            throw new Error(res.message);

          setTextLoading("Grupo Salvo com Sucesso!");
          setTimeout(() => {
            onClose();
          }, 3000);

        })
        .catch((err) => {
          setTextLoading(err.message);
        })
        .finally(() => {
          setIsLoading(false);
        });

    if (!edit)
      PostData(payload, "postGrupoCriar")
        .then((res) => {
          if (!res.success)
            throw new Error(res.message);

          setTextLoading("Grupo Salvo com Sucesso!");
          setTimeout(() => {
            onClose();
          }, 3000);
        })
        .catch((err) => {
          setTextLoading(err.message);
        })
        .finally(() => {
          setTimeout(() => {
            setIsLoading(false);
          }, 3000)
        });
  };

  const montarPayload = () => {
    const users = JSON.parse(JSON.stringify(selectedUsers));
    const payload = {
      name: inputName,
      groupDataCobId: grupoSelected.id_Grupo,
      nameColor: selectedColor && selectedColor !== null && selectedColor !== undefined ? selectedColor.nameColor : "",
      idColor: selectedColor && selectedColor !== null && selectedColor !== undefined ? selectedColor.idColor : "",
      classColor: selectedColor && selectedColor !== null && selectedColor !== undefined ? selectedColor.classColor : "",
      active: true,
      timeEndCall: timeEndCallSelected,
      idsUsers: users.map((user) => user.id),
      crm: crmSelected?.datacobName
    };

    if (edit)
      payload.id = dataEdit.id;

    return payload;
  }

  const validarFomrulario = () => {
    const erros = { ...resetErros };
    let isValid = true;
    if (!inputName || inputName === undefined || inputName === "") {
      erros.name = "Nome do Grupo é obrigatório";
      isValid = false;
    }
    if (!crmSelected || crmSelected === undefined || crmSelected === "") {
      erros.crm = "CRM é obrigatório";
      isValid = false;
    }
    if (!grupoSelected || grupoSelected === undefined || grupoSelected === "" || grupoSelected.id_Grupo === 0 || grupoSelected.descricao === "") {
      erros.grupo = "Grupo é obrigatório";
      isValid = false;
    }
    if (!timeEndCallSelected || timeEndCallSelected === 0) {
      erros.grupo = "Tempo para finalizar chamado deve ser diferente 0";
      isValid = false;
    }

    setErros(erros);
    return isValid;
  };

  const getUser = (id_Grupo) => {
    setTextLoading("Carregando Users...");
    return GetData(id_Grupo ? { id_Grupo: id_Grupo } : {}, "getTelaUnicaUsers").then((res) => {
      if (res && res !== null && res !== undefined)
        return res;
    }).catch((err) => {
      console.warn(err);
      return null;
    });
  }

  const getCrms = () => {
    setTextLoading("Carregando CRMS...");
    return GetData({}, "getCrms").then((res) => {
      if (res && res !== null && res !== undefined)
        return res;
    }).catch((err) => {
      console.warn(err);
      return null;
    });
  }

  const getGrupoCrm = (crm) => {
    setTextLoading(`Carregando Grupos do CRM ${crm}...`);
    return GetData({ ActiveConnection: crm }, 'getGrupoDataCobLista').then((res) => {
      if (res && res !== null && res !== undefined)
        return res;
    }).catch((err) => {
      console.warn(err);
      return null;
    });
  }

  useEffect(() => {
    const fetchGroupsForCrm = (crm) => {
      return getGrupoCrm(crm?.datacobName).then((groupData) => {
        crm.grupos = groupData;
        return crm;
      });
    };

    if (isOpen) {
      setIsLoading(true);
      Promise.all([getUser(dataEdit?.id), getCrms()]).then(([users, crms]) => {
        // Atualiza estados dos usuários e CRMs
        setUsersOptions(users);
        return Promise.all(crms.map(crm => fetchGroupsForCrm(crm)));
      }).then(crmGroupData => {
        // Aqui você tem os dados dos grupos para cada CRM
        setCrmsOptions(crmGroupData);
        if (edit) {
          const nomeEdit = dataEdit?.name;
          const conexaoEdit = crmGroupData?.find(crm => crm?.datacobName === dataEdit?.crm);
          const grupoEdit = { id_Grupo: dataEdit?.groupDataCobId, descricao: dataEdit?.nameCrm };
          const usersEdit = dataEdit?.users
          const colorEdit = colors.find(color => color.idColor === dataEdit?.idColor);
          setInputName(nomeEdit);
          setCrmSelectd(conexaoEdit);
          setGrupoSelected(grupoEdit);
          setGruposOptions(conexaoEdit?.grupos);
          setSelectedUsers(usersEdit);
          setSelectedColor(colorEdit);
          setTimeEndCallSelected(dataEdit?.timeEndCall ? (dataEdit?.timeEndCall <= 0 ? -1 : dataEdit?.timeEndCall) : -1);
        }
      }).catch((error) => {
        console.warn("Erro ao carregar dados:", error);
      }).finally(() => {
        setIsLoading(false);
      });
    };
  }, [isOpen, dataEdit?.id, dataEdit?.timeEndCall, dataEdit?.crm, dataEdit?.name, dataEdit?.nameCrm, dataEdit?.groupDataCobId, dataEdit?.idColor, dataEdit?.users, edit, colors]);

  const handleClose = () => {
    onClose(true);
  };

  return (
    <CModal show={isOpen} onClose={handleClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>
        <CModalTitle>Parametrização de Grupos</CModalTitle>
      </CModalHeader>
      {isLoading && <CModalBody style={{ minHeight: "470px" }}><CardLoading Title={textLoading} /> </CModalBody>}
      {!isLoading && <CModalBody>
        <CFormGroup>
          <CLabel>Nome do grupo</CLabel>
          <CInputGroup>
            <CInputGroupAppend>
              <CInputGroupText>
                <i className="cil-people"></i>
              </CInputGroupText>
            </CInputGroupAppend>
            <CInput
              type="text"
              value={inputName ?? ""}
              onChange={(e) => { handleNameChange(e); }}
              placeholder="Insira aqui o nome do grupo"
            />
          </CInputGroup>
          {erros.name && (<div className="text-danger">{erros.name}</div>)}
        </CFormGroup>
        <CFormGroup>
          <CLabel>CRM</CLabel>
          <Select
            options={crmsOptions}
            value={crmSelected ?? null}
            onChange={handleCrmsChange}
            getOptionValue={(option) => option.id}
            getOptionLabel={(option) => option.datacobName}
          />
          {erros.crm && (<div className="text-danger">{erros.crm}</div>)}
        </CFormGroup>
        <CFormGroup>
          <CLabel>Grupos CRM</CLabel>
          <Select
            options={gruposOptions}
            value={grupoSelected ?? null}
            onChange={handleGrupoChange}
            getOptionValue={(option) => option.id_Grupo}
            getOptionLabel={(option) => option.descricao}
          />
          {erros?.grupo && (<div className="text-danger">{erros?.grupo}</div>)}
        </CFormGroup>
        <CFormGroup>
          <CLabel>Fim de Chamada</CLabel>
          <CInput
            type="number"
            precision="0"
            value={timeEndCallSelected ?? 0}
            onChange={(e) => { handleTimeEndCallChange(e); }}
            placeholder="Insira aqui tempo em segundo"
          />
        </CFormGroup>
        <CFormGroup>
          <CLabel>Usuários</CLabel>
          <Select
            isMulti
            options={usersOptions}
            value={selectedUsers}
            onChange={handleUserChange}
            getOptionValue={(option) => option.id}
            getOptionLabel={(option) => option.name}
          />
          {erros.users && (<div className="text-danger">{erros.users}</div>)}
        </CFormGroup>
        <CFormGroup>
          <CLabel>Cor do quadro</CLabel>
          <Select
            options={colorOptions}
            value={selectedColor}
            onChange={handleColorChange}
            className="mb-1"
            defaultValue={colors[0]}
            getOptionValue={(option) => option.idColor}
            getOptionLabel={(option) => option.nameColor}
          ></Select>
          {selectedColor && (
            <CBadge size="lg" className={selectedColor.classColor}>
              Exemplo
            </CBadge>
          )}
          {erros.color && (<div className="text-danger">{erros.color}</div>)}
        </CFormGroup>
      </CModalBody>
      }
      <CModalFooter>
        <CButton disabled={isLoading} color="danger" onClick={handleClose}>
          Cancelar
        </CButton>
        <CButton disabled={isLoading} color="info" onClick={handleSave}>
          Salvar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default CreateGroupModal;
