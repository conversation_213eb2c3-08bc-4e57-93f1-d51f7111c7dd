import React from "react";
import {
  CModal<PERSON>ooter,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CRow,
  CCol,
  CCardBody,
  CButton,
} from "@coreui/react";
import "react-toastify/dist/ReactToastify.css";
import "react-datepicker/dist/react-datepicker.css";
import {
  formatCurrency,
  formatDate,
  formatDocument,
} from "src/reusable/helpers.js";

const TicketDetails = ({ tickets }) => {
  const statusBoleto = (status) => {
    if (status === "C") {
      return "Cancelado";
    } else if (status === "P") {
      return "Pago";
    } else if (status === "A") {
      return "Aberto";
    }
    return status;
  };

  return (
    <CCol className={"p-4"}>
      <CRow>
        <div
          className="table-responsive"
          style={{ overflow: "auto", maxHeight: "500px" }}
        >
          <table className="table">
            <thead>
              <tr>
                <th>Nr. Boleto</th>
                <th>CPF/CNPJ do Financiado</th>
                <th>Nome do Financiado</th>
                <th>Contrato do Financiado</th>
                <th>Valor do Boleto</th>
                <th>Dt. Vencimento</th>
                <th>Valor Honorários</th>
                <th>Status</th>
                <th>Cliente</th>
                <th>Grupo</th>
                <th>Operador</th>
              </tr>
            </thead>
            <tbody>
              {tickets?.length < 1 && (
                <tr>
                  <td colspan="7" className="text-center">
                    Nenhum Boleto Encontrado!
                  </td>
                </tr>
              )}
              {tickets?.map((item, index) => {
                return (
                  <tr key={index}>
                    <td>{item.nrBoleto}</td>
                    <td>{formatDocument(item.cpfCnpj)}</td>
                    <td>{item.nomeFinanciado}</td>
                    <td>{item.nrContrato}</td>
                    <td>{formatCurrency(item.valorBoleto)}</td>
                    <td>{formatDate(item.dataVencimento)}</td>
                    <td>{formatCurrency(item.totalHonorarios)}</td>
                    <td>{statusBoleto(item.statusBoleto)}</td>
                    <td>{item.nomeCliente}</td>
                    <td>{item.nomeGrupo}</td>
                    <td>{item.nomeOperador}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </CRow>
    </CCol>
  );
};

export default TicketDetails;
