import React, { useState, useEffect } from "react";
import {
  CButton,
  CForm,
  CFormGroup,
  CInput,
  CDataTable,
  CCard,
  CCardBody,
  CLabel,
  CCol,
} from "@coreui/react";
import Select from "react-select";
import { GET_DATA, POST_DATA, PUT_DATA, DELETE_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import ConfirmModal from "src/reusable/ConfirmModal";
import LoadingComponent from "src/reusable/Loading";
import TableSelectItens from "src/reusable/TableSelectItens";
import { formatThousands } from "src/reusable/helpers";
import { useAuth } from 'src/auth/AuthContext';
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const ParametriCalculos = () => {
  const { checkPermission,inforPermissions } = useAuth();
  const permissao = {
    modulo: "Simulações de Cálculos",
    submodulo: null,
  }
  
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const [selectedDatacob, setSelectedDatacob] = useState(null);
  const [datacobOptions, setDatacobOptions] = useState([]);

  const [selectedGrupoId, setSelectedGrupoId] = useState(null);
  const [selectedGrupoName, setSelectedGrupoName] = useState(null);
  const [groupOptions, setGroupOptions] = useState([]);

  const [selectedFase, setSelectedFase] = useState("");
  const [faseOptions, setFaseOptions] = useState([]);

  const [data, setData] = useState([]);
  const [fromValue, setFromValue] = useState("");
  const [toValue, setToValue] = useState("");
  const [fineValue, setFineValue] = useState("");
  const [fineDiscValue, setFineDiscValue] = useState("");
  const [feeValue, setFeeValue] = useState("");
  const [feeDiscValue, setFeeDiscValue] = useState("");
  const [honoraryValue, setHonoraryValue] = useState("");
  const [selectedItem, setSelectedItem] = useState(null);

  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  async function getGroupCalculation() {
    const grupoCalculo = await GET_DATA("GroupCalculationDatacob");
    if (grupoCalculo) {
      setData(grupoCalculo);
    }
    return;
  }

  const updateDatacobOptions = () => {
    getAllDatacobs(null, "getDatacobs")
      .then((data) => {
        if (data) {
          const uniqueDatacob = [...new Set(data.map((item) => item))];
          const optionsDatacob = [
            // { datacobNumber: null, datacobName: "CRM" },
            ...uniqueDatacob.map((x) => ({
              datacobNumber: x.datacobNumber,
              datacobName: "Datacob " + x.datacobName,
            })),
          ];
          setDatacobOptions(optionsDatacob);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const getAllDatacobs = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  async function updateGrupoOptions(payload) {
    setIsLoading(true);
    getGroups(payload, "getDatacobGroups")
      .then((data) => {
        if (data) {
          const groupList = [
            { id: null, name: "Grupo" },
            ...data.map((group) => ({
              id: group.id_Grupo,
              name: group.descricao,
            })),
          ];
          setGroupOptions(groupList);
        } else {
          toast.warning("Problemas na listagem dos grupos");
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const getGroups = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const updatePhaseOptions = () => {
    const payload = {};
    getGroupPhases(payload, "getGroupPhases")
      .then((data) => {
        if (data) {
          const faseList = [
            { id: null, name: "Fase" },
            ...data.map((fase) => ({
              id: fase.id_Fase,
              name: fase.cod_Fase,
            })),
          ];
          setFaseOptions(faseList);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const getGroupPhases = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  function validate() {
    const isValidFrom = /^[0-9]+$/.test(fromValue);
    const isValidTo = /^[0-9]+$/.test(toValue);

    if (!isValidFrom || !isValidTo) {
      alert("Por favor, insira valores válidos para os dias de atraso.");
      return false;
    }

    const daysFrom = parseInt(fromValue);
    const daysTo = parseInt(toValue);

    if (daysFrom >= daysTo) {
      alert(
        '"Dias de atraso inicial" deve ser menor que "Dias de atraso final".'
      );
      return false;
    }
    if (
      fineValue > 100 ||
      fineDiscValue > 100 ||
      feeValue > 100 ||
      feeDiscValue > 100 ||
      honoraryValue > 100
    ) {
      alert("Valores percentuais não podem exceder 100.");
      return false;
    }
    return true;
  }

  const handleAdd = async () => {
    const validFields = validate();
    if (validFields) {
      const data = {
        datacob: selectedDatacob,
        groupId: selectedGrupoId,
        group: selectedGrupoName,
        phase: selectedFase === "Fase" ? null : selectedFase,
        dayFrom: parseInt(fromValue),
        dayTo: parseInt(toValue),
        fine: fineValue ? parseFloat(fineValue) : 0,
        fineDiscount: fineDiscValue ? parseFloat(fineDiscValue) : 0,
        fee: feeValue ? parseFloat(feeValue) : 0,
        feeDiscount: feeDiscValue ? parseFloat(feeDiscValue) : 0,
        honorary: honoraryValue ? parseFloat(honoraryValue) : 0,
      };
      const postSuccess = await POST_DATA("GroupCalculationDatacob", data);
      if (postSuccess.success) {
        await getGroupCalculation();
        clearInputs();
      } else {
        alert(postSuccess.message);
      }
    }
  };

  const handleEdit = (item) => {
    const element = data.find((el) => el.id === item.id);
    setSelectedItem(element);
    setSelectedDatacob(element.datacob);
    setSelectedGrupoId(element.groupId);
    setSelectedGrupoName(element.group);
    setSelectedFase(element.phase);
    setFromValue(element.dayFrom);
    setToValue(element.dayTo);
    setFineValue(element.fine);
    setFineDiscValue(element.fineDiscount);
    setFeeValue(element.fee);
    setFeeDiscValue(element.feeDiscount);
    setHonoraryValue(element.honorary);
  };

  const handleUpdate = async () => {
    const validFields = validate();
    if (validFields) {
      const data = {
        id: selectedItem.id,
        datacob: selectedDatacob,
        groupId: selectedGrupoId,
        group: selectedGrupoName,
        phase: selectedFase === "Fase" ? null : selectedFase,
        dayFrom: parseInt(fromValue),
        dayTo: parseInt(toValue),
        fine: fineValue ? parseFloat(fineValue) : 0,
        fineDiscount: fineDiscValue ? parseFloat(fineDiscValue) : 0,
        fee: feeValue ? parseFloat(feeValue) : 0,
        feeDiscount: feeDiscValue ? parseFloat(feeDiscValue) : 0,
        honorary: honoraryValue ? parseFloat(honoraryValue) : 0,
      };
      const updateSuccess = await PUT_DATA("GroupCalculationDatacob", data);
      if (updateSuccess.success) {
        await getGroupCalculation();
        clearInputs();
      } else {
        alert(updateSuccess.message);
      }
    }
  };

  const groupCalcDelete = async (item) => {
    setSelectedItem(item);
    setShowConfirmModal(true);
  };

  const handleModalClose = (confirmation) => {
    setShowConfirmModal(false);
    handleDelete(confirmation);
  };

  const handleSelectGrupo = (selectedOption) => {
    setSelectedGrupoId(selectedOption.id);
    setSelectedGrupoName(selectedOption.name);
  };

  const handleSelectFase = (selectedOption) => {
    setSelectedFase(selectedOption.name);
  };

  const handleSelectDatacob = (selectedOption) => {
    setSelectedDatacob(selectedOption.datacobNumber);
  };

  const handleDelete = async (confirmation) => {
    if (confirmation) {
      const deleteSuccess = await DELETE_DATA(
        `GroupCalculationDatacob/${selectedItem.id}`
      );
      if (deleteSuccess.success) {
        await getGroupCalculation();
        clearInputs();
      } else {
        alert(deleteSuccess.message);
      }
    }
  };

  const clearInputs = () => {
    setSelectedDatacob(null);
    setSelectedGrupoId(null);
    setSelectedGrupoName(null);
    setSelectedFase("Fase");
    setFromValue("");
    setToValue("");
    setFineValue("");
    setFineDiscValue("");
    setFeeValue("");
    setFeeDiscValue("");
    setHonoraryValue("");

    setSelectedItem(null);
  };

  const columns = [
    {
      key: "group",
      label: "Grupo",
      filter: true,
    },
    {
      key: "datacob",
      label: "CRM",
      formatterByObject: (item) => renderDatacob(item),
    },
    {
      key: "phase",
      label: "Fase",
      formatterByObject: (item) => checkPhase(item),
    },
    {
      key: "dayFrom",
      label: "Dias de atraso",
    },
    {
      key: "dayTo",
      label: "até",
    },
    {
      key: "fine",
      label: "Multa",
      formatter: (item) => renderValue(item),
    },
    {
      key: "fineDiscount",
      label: "Desconto Multa",
      formatter: (item) => renderValue(item),
    },
    {
      key: "fee",
      label: "Juros",
      formatter: (item) => renderValue(item),
    },
    {
      key: "feeDiscount",
      label: "Desconto Juros",
      formatter: (item) => renderValue(item),
    },
    {
      key: "honorary",
      label: "H.O.",
      formatter: (item) => renderValue(item),
    },
    {
      key: "actions",
      label: "Ações",
      formatterByObject: (item) => renderActionButton(item),
    },
  ];

  const checkPhase = (item) => (item.phase ? item.phase : "---");
  const renderDatacob = (item) =>
    item.datacob === 0 ? "Datacob GVC" : "Datacob Rodobens";
  const renderValue = (value) =>
    value === 0 ? "0,00" : formatThousands(value);

  const renderActionButton = (item) => (
    <div style={{ whiteSpace: "nowrap" }}>
      <CButton
        title="Editar"
        color="info"
        onClick={() => handleEdit(item)}
        className="mr-2"
        disabled={!checkPermission("Simulações de Cálculos", "Edit")}
      >
        <i className="cil-pencil" />
      </CButton>
      <CButton
        title="Deletar"
        color="danger"
        onClick={() => groupCalcDelete(item)}
        disabled={!checkPermission("Simulações de Cálculos", "Delete")}
      >
        <i className="cil-trash" />
      </CButton>
    </div>
  );

  useEffect(() => {
    updateDatacobOptions();
    updatePhaseOptions();
    getGroupCalculation();
  }, []);

  useEffect(() => {
    if (selectedDatacob !== null) {
      const payload = {
        ActiveConnection: selectedDatacob,
      };
      updateGrupoOptions(payload);
    }
  }, [selectedDatacob]);

  return (
    <div>
      <h3>Grupos e cálculos para negociações.</h3>
      <p style={{ color: "gray" }}>
        Definir grupos e seus parametros para simulações de cálculos.
      </p>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard style={{ width: "60%" }}>
          <CCardBody>
            <CForm>
              <CFormGroup>
                <Select
                  value={datacobOptions.find(
                    (option) => option.datacobNumber === selectedDatacob
                  )}
                  placeholder="CRM"
                  onChange={handleSelectDatacob}
                  options={datacobOptions}
                  getOptionValue={(option) => option.datacobNumber}
                  getOptionLabel={(option) => option.datacobName}
                />
              </CFormGroup>
              <CFormGroup>
                {isLoading ? (
                  <LoadingComponent />
                ) : (
                  <Select
                    className="mb-2"
                    placeholder="Grupo"
                    value={groupOptions.find(
                      (option) => option.id === selectedGrupoId
                    )}
                    onChange={handleSelectGrupo}
                    options={groupOptions}
                    getOptionValue={(option) => option.id}
                    getOptionLabel={(option) => option.name}
                    isDisabled={selectedDatacob === null}
                  />
                )}
              </CFormGroup>
              <CFormGroup>
                <Select
                  className="mb-2"
                  placeholder="Fase"
                  value={faseOptions.find(
                    (option) => option.name === selectedFase
                  )}
                  onChange={handleSelectFase}
                  options={faseOptions}
                  getOptionValue={(option) => option.id}
                  getOptionLabel={(option) => option.name}
                  isDisabled={selectedDatacob === null}
                />
              </CFormGroup>
              <CFormGroup row>
                <CCol className="pr-1">
                  <CInput
                    type="text"
                    placeholder="Dias de Atraso Inicial"
                    value={fromValue}
                    onChange={(e) =>
                      setFromValue(e.target.value.replace(/[^0-9]/g, ""))
                    }
                    disabled={!selectedGrupoId}
                  />
                </CCol>
                <CCol className="pl-1">
                  <CInput
                    type="text"
                    placeholder="Dias de Atraso Final"
                    value={toValue}
                    onChange={(e) =>
                      setToValue(e.target.value.replace(/[^0-9]/g, ""))
                    }
                    disabled={!selectedGrupoId}
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CCol className="pr-1">
                  <CInput
                    type="text"
                    placeholder="Multas %"
                    value={fineValue}
                    onChange={(e) =>
                      setFineValue(e.target.value.replace(/[^0-9.]/g, ""))
                    }
                    disabled={!selectedGrupoId}
                  />
                </CCol>
                <CCol className="pl-1">
                  <CInput
                    type="text"
                    placeholder="Desconto Multas %"
                    value={fineDiscValue}
                    onChange={(e) =>
                      setFineDiscValue(e.target.value.replace(/[^0-9.]/g, ""))
                    }
                    disabled={!selectedGrupoId}
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CCol className="pr-1">
                  <CInput
                    type="text"
                    placeholder="Juros %"
                    value={feeValue}
                    onChange={(e) =>
                      setFeeValue(e.target.value.replace(/[^0-9.]/g, ""))
                    }
                    disabled={!selectedGrupoId}
                  />
                </CCol>
                <CCol className="pl-1">
                  <CInput
                    type="text"
                    placeholder="Desconto Juros %"
                    value={feeDiscValue}
                    onChange={(e) =>
                      setFeeDiscValue(e.target.value.replace(/[^0-9.]/g, ""))
                    }
                    disabled={!selectedGrupoId}
                  />
                </CCol>
              </CFormGroup>
              <CInput
                type="text"
                placeholder="Honorário %"
                value={honoraryValue}
                onChange={(e) =>
                  setHonoraryValue(e.target.value.replace(/[^0-9.]/g, ""))
                }
                disabled={!selectedGrupoId}
              />
              <CFormGroup></CFormGroup>
              {selectedItem !== null ? (
                <CButton
                  color="info"
                  onClick={handleUpdate}
                  className="mr-2"
                  disabled={!selectedGrupoId}
                >
                  Salvar
                </CButton>
              ) : (
                <CButton
                  color="info"
                  onClick={handleAdd}
                  className="mr-2"
                  title={inforPermissions(permissao).create}
                  disabled={!selectedGrupoId || !checkPermission(permissao.modulo, "Create", permissao.submodulo)}
                >
                  Adicionar
                </CButton>
              )}
              <CButton color="secondary" onClick={clearInputs}>
                Limpar campos
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </div>{" "}
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard
          style={{
            textAlign: "center",
          }}
        >
          <TableSelectItens
            data={data}
            columns={columns}
            onSelectionChange={(_) => {}}
            defaultSelectedKeys={[]}
            selectable={false}
            heightParam="600px"
          />
        </CCard>
        <ConfirmModal
          isOpen={showConfirmModal}
          onClose={handleModalClose}
          texto={"Tem certeza que deseja deletar esse grupo de cálculo?"}
        />
      </div>
    </div>
  );
};

export default ParametriCalculos;
