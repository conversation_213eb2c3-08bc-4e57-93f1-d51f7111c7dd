import React, { useState, useEffect } from "react";
import {
  CRow,
  <PERSON>ol,
  CLabel,
  CCardBody,
  CButton,
  CInputCheckbox,
  CBadge,
} from "@coreui/react";
import SelecionarModal from "./SelecionarModal";
import AdicionarEndereco from "./OutrosModals/AdicionarEndereco";
import EditarEndereco from "./OutrosModals/EditarEndereco";
import { formatDate } from "src/reusable/helpers";
import { GET_DATA, POST_DATA } from "src/api";
import { getDadosFinanciado, getTiposEndereco } from "src/reusable/functions";
import { useMyContext } from "src/reusable/DataContext";
import { useAuth } from "src/auth/AuthContext";
import { toast } from "react-toastify";
import CIcon from "@coreui/icons-react";
import { IconButton } from "../CardTelefoneFunctions";
import Endereco from "./Endereço";
import LoadingComponent from "src/reusable/Loading";

const EnderecoLista = ({ selected }) => {
  const { checkPermission, inforPermissions } = useAuth();

  const permissao = {
    modulo: "Tela Principal",
    submodulo: "Endereço",
  };

  const { data } = useMyContext();

  const [financiadoData, setFinanciadoData] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );
  const [enderecoList, setEnderecoList] = useState([]);
  const [showDetails, setShowDetails] = useState(null);
  const [showModalAdicionar, setShowModalAdicionar] = useState(false);
  const [loading, setLoading] = useState(false);

  const [tipoEndereco, setTipoEndereco] = useState([]);
  const handleTipoEndereco = async () => {
    const res = await getTiposEndereco();
    setTipoEndereco(res);
  };

  const updateView = async (data = null) => {
    let updatedData;
    setLoading(true);
    if (data !== null)
      updatedData = await getDadosFinanciado(data.id_Financiado);
    else updatedData = await getDadosFinanciado(financiadoData.id_Financiado, financiadoData.numero_Contrato);
    if (updatedData) {
      setEnderecoList(updatedData.enderecos);
      localStorage.setItem("clientData", JSON.stringify(updatedData));
      setFinanciadoData(data);
    }
    try {
      await Promise.all([handleTipoEndereco()]);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const renderStatus = (value) => {
    switch (value) {
      case 0:
        return <CBadge color="danger">Inativo</CBadge>;
      case 1:
        return <CBadge color="success">Ativo</CBadge>;
      case 2:
        return <CBadge color="info">Efetivo</CBadge>;
      default:
        break;
    }
  };

  useEffect(() => {
    if (data) updateView(data);
  }, [data]);

  const handleCloseAddModal = () => {
    setShowModalAdicionar(false);
  };

  const renderActionStatus = (item) => {
    return (
      <div style={{ display: "flex" }}>
        <IconButton
          icon={"cil-lightbulb"}
          color={"blue"}
          titulo={"Efetivar"}
          onClick={() => handleChangeStatus(item, 2)}
          disabled={
            !financiadoData ||
            !checkPermission(permissao.modulo, "Edit", permissao.submodulo) ||
            item.status === -1
          }
        />
        <IconButton
          icon={"cil-check-circle"}
          color={"green"}
          titulo={"Ativar"}
          onClick={() => handleChangeStatus(item, 1)}
          disabled={
            !financiadoData ||
            !checkPermission(permissao.modulo, "Edit", permissao.submodulo) ||
            item.status === -1
          }
        />
        <IconButton
          icon={"cil-warning"}
          color={"red"}
          titulo={"Inativar"}
          onClick={() => handleChangeStatus(item, 0)}
          disabled={
            !financiadoData ||
            !checkPermission(permissao.modulo, "Edit", permissao.submodulo) ||
            item.status === -1
          }
        />
      </div>
    );
  };

  const handleChangeStatus = async (item, status) => {
    await handleAdicionarEndereco({
      logradouro: item.logradouro,
      bairro: item.bairro,
      numero: item.numero,
      complemento: item.complemento,
      uf: item.uf,
      cidade: item.cidade,
      cep: item.cep,
      tipoEndereco: item.tipo_Endereco,
      carta: true,
      status: status,
    });
  };

  const handleMoreDetails = (item) => {
    setShowDetails(item);
  };

  const enderecoPost = async (newEndereco) => {
    const data = {
      cpfCnpj: financiadoData.cpfCnpj,
      idGrupo: financiadoData.id_Grupo,
      nome: financiadoData.nome,
      enderecos: [{ ...newEndereco }],
    };
    const result = await POST_DATA("Datacob/DadosCadastrais/Financiado", data);
    return result;
  };

  const handleAdicionarEndereco = async (newData) => {
    toast.info("Enviando dados para o CRM...");
    const postSuccess = await enderecoPost(newData);
    if (postSuccess.success) {
      const updatedData = await getDadosFinanciado(
        financiadoData.id_Financiado, financiadoData.numero_Contrato
      );
      setEnderecoList(updatedData.enderecos);
      setShowDetails(
        updatedData.enderecos.find(
          (x) => x?.id_Endereco === showDetails?.id_Endereco
        )
      );
      localStorage.setItem("clientData", JSON.stringify(updatedData));
      toast.success("Dados do financiado alterados com sucesso!");
    } else {
      toast.warning(postSuccess.message);
    }
  };

  const handleBack = () => {
    const enderecos = localStorage.getItem("clientData")
      ? JSON.parse(localStorage.getItem("clientData")).enderecos
      : [];
    setEnderecoList(enderecos);
    setShowDetails(null);
  };

  return (
    <CCardBody style={{ overflow: "auto", maxHeight: "320px" }}>
      {showDetails !== null && showDetails !== undefined && (
        <Endereco
          item={showDetails}
          backFunc={handleBack}
          handleAdicionarEndereco={handleAdicionarEndereco}
          tipoEndereco={tipoEndereco}
        />
      )}
      {loading && <LoadingComponent />}
      {(showDetails === null || showDetails === undefined) && !loading && (
        <table className="table">
          <thead>
            <tr>
              <th>Logradouro</th>
              <th>Número</th>
              <th>Cidade</th>
              <th>UF</th>
              <th>Status</th>
              <th>Ações</th>
              <th>
                <CButton
                  color="success"
                  title="Incluir novo endereço"
                  onClick={() => setShowModalAdicionar(true)}
                  disabled={
                    !financiadoData ||
                    !checkPermission(
                      permissao.modulo,
                      "Create",
                      permissao.submodulo
                    )
                  }
                >
                  Incluir novo endereço
                </CButton>
              </th>
            </tr>
          </thead>
          <tbody>
            {enderecoList.map((item, index) => (
              <tr key={index}>
                <td>{item.logradouro}</td>
                <td>{item.numero}</td>
                <td>{item.cidade}</td>
                <td>{item.uf}</td>
                <td>{renderStatus(item.tipo_Status)}</td>
                <td>{renderActionStatus(item)}</td>
                <td>
                  <CButton
                    color="info"
                    title="Ver mais detalhes"
                    onClick={() => handleMoreDetails(item)}
                  >
                    Ver mais detalhes
                  </CButton>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
      {showModalAdicionar && (
        <AdicionarEndereco
          isOpen={showModalAdicionar}
          onClose={handleCloseAddModal}
          onSave={handleAdicionarEndereco}
          tipoEndereco={tipoEndereco}
        />
      )}
    </CCardBody>
  );
};

export default EnderecoLista;
