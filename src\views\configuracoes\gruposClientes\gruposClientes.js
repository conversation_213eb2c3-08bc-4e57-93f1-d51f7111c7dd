import React, { useState, useEffect } from "react";
import { CButton, CCol, CCard, CCardBody, CCardHeader, CRow, CDropdown, CDropdownItem, CDropdownMenu, CDropdownToggle } from "@coreui/react";
import Select from "react-select";
import CreateGroupModal from "./CreateGroupModal";
import GroupUsersModal from "./GroupUsersModal";
import { getURI } from "src/config/apiConfig";
import { useAuth } from "src/auth/AuthContext";
import { GET_DATA, PUT_DATA } from "src/api";
import CardLoading from "src/reusable/CardLoading";
import ConfirmModal from "src/reusable/ConfirmModal";

const PutData = async (payload, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await PUT_DATA(
        getURI(endpoint),
        payload,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(getURI(endpoint), payload, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};


const GruposClientes = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Configurações",
    submodulo: "Grupos e Clientes",
  }

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConfirmModalInativar, setIsConfirmModalInativar] = useState(false);
  const [isConfirmModalAtivar, setIsConfirmModalAtivar] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertText, setAlertText] = useState("");
  const [groups, setGroups] = useState([]);
  const [filter, setFilter] = useState("all");
  const [edicaoGrupo, setEdicaoGrupo] = useState(null);
  const [editarGrupo, setEditarGrupo] = useState(false);
  const handleFilterChange = (target) => {
    setFilter(target.value);
  };

  const filteredGroups = groups.filter((group) => {
    if (filter === "active") {
      return group.active;
    } else if (filter === "inactive") {
      return !group.active;
    }
    return true; // show all groups if filter is "all"
  });

  const filterOptions = [
    { value: "all", label: "Mostrar todos" },
    { value: "active", label: "Grupos ativos" },
    { value: "inactive", label: "Grupos inativos" },
  ];

  const handleDelete = (group) => {
    setEdicaoGrupo(group);
    setShowAlert(true);
    setAlertText(`Inativando Grupo ${group.name}`);
    setIsConfirmModalInativar(true)
  };

  const handleCloseConfirmacaModal = (resposta) => {
    if (resposta) {
      setIsConfirmModalInativar(false);
      setAlertText(`Inativando o Grupo ${edicaoGrupo.name}. Aguarde....`);
      const payload = JSON.parse(JSON.stringify(edicaoGrupo));
      payload.active = false;
      const payloadInativar = {
        id: payload.id,
        active: false
      }
      PutData(payloadInativar, "putGrupoAtualizar")
        .then((response) => {
          if (!response.success)
            throw new Error(response.message);

          setAlertText(`Grupo ${edicaoGrupo.name} inativado com sucesso!`);
        }).catch((error) => {
          setAlertText(`Erro ao inativar o grupo ${error.message}!`);
        }).finally(() => {
          setTimeout(() => {
            setShowAlert(false);
            setEdicaoGrupo(null);
            getGroups()
          }, 3000);
        })
    } else {
      setEdicaoGrupo(null);
      setAlertText("Usuário Cancelou operação!");
      setTimeout(() => {
        setShowAlert(false);
      }, 3000);
    }

  }

  const handleAtivar = (group) => {
    setEdicaoGrupo(group);
    setShowAlert(true);
    setAlertText(`Ativando Grupo ${group.name}`);
    setIsConfirmModalAtivar(true)
  };

  const handleCloseConfirmacaAtivarModal = (resposta) => {
    if (resposta) {
      setIsConfirmModalAtivar(false);
      setAlertText(`Ativando o Grupo ${edicaoGrupo.name}. Aguarde....`);
      const payload = JSON.parse(JSON.stringify(edicaoGrupo));
      const payloadAtivar = {
        id: payload.id,
        active: true
      }
      PutData(payloadAtivar, "putGrupoAtualizar")
        .then((response) => {
          if (!response.success)
            throw new Error(response.message);
          setAlertText(`Grupo ${edicaoGrupo.name} ativado com sucesso!`);
        }).catch((error) => {
          setAlertText(`Erro ao ativar o grupo ${error.message}!`);
        }).finally(() => {
          setTimeout(() => {
            setShowAlert(false);
            setEdicaoGrupo(null);
            getGroups()
          }, 3000);
        })
    } else {
      setEdicaoGrupo(null);
      setAlertText("Usuário Cancelou operação!");
      setTimeout(() => {
        setShowAlert(false);
      }, 3000);
    }

  }

  const handleEdit = (group) => {
    setEdicaoGrupo(group);
    setEditarGrupo(true);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setEdicaoGrupo(null);
    setEditarGrupo(false);
    setIsModalOpen(false);
    getGroups();
  };

  const getGroups = () => {
    setShowAlert(true);
    setAlertText("Carregando Grupos...");
    GetData({}, "getGrupoLista")
      .then((response) => {
        setGroups(response);
      })
      .catch((error) => {
        setGroups([]);
      })
      .finally(() => setShowAlert(false));
  }

  useEffect(() => {
    getGroups();
  }, []);

  return (
    <div>
      <CRow>
        <CCol className="align-items-center" md="9">
          <h2>Grupos e clientes</h2>
          <p style={{ color: "gray", fontSize: "small" }}>
            Crie grupos de clientes e gerencie suas associações. Organize e
            controle as relações entre grupos, clientes e usuários.
          </p>
        </CCol>
        <CCol className="text-right" md="3">
          <CButton
            color="info"
            onClick={() => setIsModalOpen(true)}
            title={inforPermissions(permissao).create}
            disabled={!checkPermission(permissao.modulo, "Create", permissao.submodulo)}
          >
            <i className={"cil-people"} /> Parametrização de Grupos
          </CButton>
        </CCol>
      </CRow>
      <CRow className="mb-3">
        <CCol md="9" />
        <CCol md="3">
          <Select
            value={filterOptions.find((option) => option.value === filter)}
            options={filterOptions}
            onChange={handleFilterChange}
          />
        </CCol>
      </CRow>
      <CRow >
        <CCol>
          <CCard>
            <CCardBody style={{ minHeight: "400px", width: "100%" }}>
              {showAlert && <CardLoading Title={alertText} />}
              {!showAlert && (!groups || groups === null || groups === undefined || groups.length === 0) && (
                <div style={{ minHeight: "400px", display: "flex", justifyContent: "center", alignItems: "center" }}>
                  <div>Não há grupos cadastrados.</div>
                </div>
              )}
              {!showAlert && groups && groups !== null && groups !== undefined && groups.length > 0 && filteredGroups.length === 0 && (
                <div style={{ minHeight: "400px", display: "flex", justifyContent: "center", alignItems: "center" }}>1
                  <div>Não há grupos filtro Selecionado</div>
                </div>
              )}
              {!showAlert && filteredGroups.length > 0 && (
                <div className="row row-cols-1 row-cols-md-3 g-4">
                  {/* row-cols-1: It specifies that by default, there should be only 1 column in the row on all screen sizes.
        row-cols-md-3: It specifies that on medium-sized screens and above, the row should have 3 columns. */}
                  {filteredGroups.map((group) => (
                    <div className="col" key={group.id}>
                      <CCard
                        className={
                          group.active
                            ? group.idColor
                              ? group.classColor
                              : " "
                            : "bg-gray"
                        }
                      >
                        <CCardHeader>
                          <div
                            className="d-flex"
                            style={{ justifyContent: "space-between" }}
                          >
                            {group.active ? group.name : <p><strong> {group.name} - Desativado</strong></p>}
                            <CDropdown className="justify-content-end">
                              <CDropdownToggle caret={false}><i className="cil-options"></i></CDropdownToggle>
                              <CDropdownMenu>
                                {group.active && (
                                  <CDropdownItem
                                    onClick={() => handleEdit(group)}
                                    title={inforPermissions(permissao).edit}
                                    disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
                                  >Editar</CDropdownItem>
                                )}
                                {group.active && (
                                  <CDropdownItem
                                    title={inforPermissions(permissao).edit}
                                    disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
                                    onClick={() => handleDelete(group)}>Inativar</CDropdownItem>
                                )}
                                {!group.active && (
                                  <CDropdownItem
                                    title={inforPermissions(permissao).edit}
                                    disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
                                    onClick={() => handleAtivar(group)}>Ativar</CDropdownItem>
                                )}
                              </CDropdownMenu>
                            </CDropdown>
                          </div>
                        </CCardHeader>
                        <CCardBody>
                          <div>
                            <div style={{ display: "flex", flexDirection: "row" }}>
                              <div style={{ display: "flex", flexDirection: "row" }}>
                                <div style={{ width: "100px" }}><strong>CRM</strong></div>
                                <div><strong>:</strong></div>
                              </div>
                              <div style={{ paddingLeft: "5px" }}>
                                {group.crm}
                              </div>
                            </div>
                            <div style={{ display: "flex", flexDirection: "row" }}>
                              <div style={{ display: "flex", flexDirection: "row" }}>
                                <div style={{ width: "100px" }}><strong>Grupo CRM</strong></div>
                                <div><strong>:</strong></div>
                              </div>
                              <div style={{ paddingLeft: "5px" }}>
                                {group.nameCrm}
                              </div>
                            </div>
                          </div>
                          {" "}   
                          {group.id && (
                            <GroupUsersModal key={group.id} group={group} />
                          )}
                        </CCardBody>
                      </CCard>
                    </div>
                  ))}
                </div>)}
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
      {isModalOpen && (
        <CreateGroupModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          dataEdit={edicaoGrupo}
          edit={editarGrupo}
        />
      )}
      {isConfirmModalInativar && edicaoGrupo !== null && (
        <ConfirmModal texto={`Tem certeza que deseja inativar o grupo ${edicaoGrupo.name}?`}
          isOpen={isConfirmModalInativar}
          onClose={handleCloseConfirmacaModal}
        />
      )}

      {isConfirmModalAtivar && edicaoGrupo !== null && (
        <ConfirmModal texto={`Tem certeza que deseja ativar o grupo ${edicaoGrupo.name}?`}
          isOpen={isConfirmModalAtivar}
          onClose={handleCloseConfirmacaAtivarModal}
        />
      )}
    </div>
  );
};

export default GruposClientes;
