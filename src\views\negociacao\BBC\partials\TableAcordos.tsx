import CIcon from "@coreui/icons-react";
import { CTooltip } from "@coreui/react";
import React from "react";
import { formatCurrency,formatDate } from 'src/reusable/helpers';

// const customTooltipStyle = {
//   '--cui-tooltip-bg': 'white',
//   '--cui-tooltip-border-color': '#28a745', // Cor de sucesso
//   '--cui-tooltip-text-color': '#28a745',   // Cor de sucesso
//   // Outras variáveis personalizadas aqui, se necessário
// };

const TableAcordos = ({ dataTable, contratoIndex, onContratoClick }) => {
  return (
    <div className="table-responsive">
      <table className='table'>
        <thead>
          <tr>
            <th>Contrato</th>
            <th>Número Operação</th>
            <th>Número Proposta</th>
            <th>Valor Financiado</th>
            <th>Data Acordo</th>
            <th>Status DataCob</th>
            <th>Status</th>
          </tr>
        </thead>

        <tbody>
          {dataTable.map((item, index) => {
            return (
              <tr key={index}
                onClick={() => onContratoClick(item)}
                className={contratoIndex != null && contratoIndex.id === item.id ? item.situacaoProposta === "INTEGRADA" || item.situacaoProposta === "" ? 'bg-success' : item.situacaoProposta === "NAO_DEFINIDO" ? "bg-warning" : "bg-info" : ''}
                style={{ cursor: 'pointer' }}
              >
                <td>{item.contratoDataCob}</td>
                <td>{item.numeroOperacao}</td>
                <td>{item.numeroProposta}</td>
                <td>{formatCurrency(item.agreementSimulation.valorFinanciado)}</td>
                <td>{formatDate(item.createdAt)}</td> 
                <td>{item.statusDataCob}</td>
                {item.situacaoProposta !== "INTEGRADA" ?
                  <td  style={{ width: '130px', display:'flex', flexDirection:'row' }}>
                    <div>{item.situacaoProposta}</div>
                    <div className="ml-2">
                    <CTooltip
                      content="Acordo pendente de aprovação pela BBC. Aguarde alguns instantes e tente realizar o download do Boleto"
                      placement="right"
                      /*style={customTooltipStyle}*/
                    >
                      <CIcon name="cilWarning" className={contratoIndex != null && contratoIndex.id === item.id ?"":"text-warning"} />
                    </CTooltip>
                    </div>
                  </td>
                  :
                  <td>{item.situacaoProposta}</td>
                }
  
              </tr>
            )
          })}
        </tbody>
      </table>
    </div>
  );
};

export default TableAcordos;
