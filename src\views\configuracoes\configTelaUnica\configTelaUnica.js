import React, { useState, useEffect } from "react";
import { CButton, CForm, CFormGroup, CInput, CCard, CCardBody } from "@coreui/react";
import { GET_DATA, POST_DATA, PUT_DATA, DELETE_DATA } from "src/api";
import ConfirmModal from "src/reusable/ConfirmModal";
import TableSelectItens from "src/reusable/TableSelectItens";
import { formatDateTime } from "src/reusable/helpers";
import CardLoading from "src/reusable/CardLoading";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useAuth } from 'src/auth/AuthContext';

const ConfigTelaUnica = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Configurações Tela Única",
    submodulo: null,
  }

  const [data, setData] = useState([]);
  const [key, setKey] = useState("");
  const [value, setValue] = useState("");
  const [selectedItem, setSelectedItem] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const [showConfirmModal, setShowConfirmModal] = useState(false);

  async function getConfigs() {
    setIsLoading(true);
    const configsTelaUnica = await GET_DATA("Config");
    if (configsTelaUnica) {
      setData(configsTelaUnica);
    }
    setIsLoading(false);
    return;
  }

  const handleEdit = (item) => {
    const element = data.find((el) => el.id === item.id);
    setSelectedItem(element);
    setKey(element.key);
    setValue(element.value);
  };

  const handleUpdate = async () => {
    const data = {
      key: key,
      value: value,
    };
    const updateSuccess = await PUT_DATA("Config", data);
    if (updateSuccess.success) {
      toast.success(updateSuccess.message);
      await getConfigs();
      clearInputs();
    } else {
      alert(updateSuccess.message);
    }
  };

  const handleModalClose = (confirmation) => {
    setShowConfirmModal(false);
    handleDelete(confirmation);
  };

  const handleDelete = async (confirmation) => {
    if (confirmation) {
      const data = { id: selectedItem.id };
      const deleteSuccess = await DELETE_DATA(`Config`, data);
      if (deleteSuccess.success) {
        toast.success(deleteSuccess.message);
        await getConfigs();
        clearInputs();
      } else {
        alert(deleteSuccess.message);
      }
    }
  };

  const clearInputs = () => {
    setKey("");
    setValue("");
    setSelectedItem(null);
  };

  const columns = [
    {
      key: "id",
    },
    {
      key: "key",
      label: "Chave",
      filter: true,
    },
    {
      key: "value",
      label: "Valor",
      formatterByObject: (item) => renderValue(item),
    },
    {
      key: "updatedAt",
      label: "Atualizada Em",
      formatter: (item) => formatDateTime(item),
    },
    {
      key: "actions",
      label: "Editar",
      formatterByObject: (item) => renderActionButton(item),
    },
  ];


  const renderActionButton = (item) => (
    <>
      <CButton
        color="info"
        onClick={() => handleEdit(item)}
        className="mr-2"
        title={inforPermissions(permissao).edit}
        disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
      >
        <i className="cil-pencil" />
      </CButton>
    </>
  );

  const [truncatedItems, setTruncatedItems] = useState({});

  const toggleTextExpansion = (itemId) => {
    setTruncatedItems((prevState) => ({
      ...prevState,
      [itemId]: !prevState[itemId],
    }));
  };

  const renderValue = (item) => {
    return (
      <div
        className={truncatedItems[item.id] ? "expand-text" : "truncate-text"}
        onClick={() => toggleTextExpansion(item.id)}
        style={{ width: "450px" }}
      >
        {truncatedItems[item.id]
          ? item.value
          : item.value?.length > 200
            ? item.value?.slice(0, 200) + " ..."
            : item.value}
      </div>
    );
  };

  useEffect(() => {
    getConfigs();
  }, []);

  return (
    <div>
      <h3>Configurações Tela Única</h3>
      <p style={{ color: "gray" }}>
        Definir as configurações das APIs da Tela Única.
      </p>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard style={{ width: "80%" }}>
          <CCardBody>
            <CForm>
              <CFormGroup>
                <CInput
                  type="text"
                  placeholder="Chave"
                  value={key}
                  onChange={(e) => setKey(e.target.value)}
                  disabled={true}
                />
              </CFormGroup>
              <CFormGroup>
                <CInput
                  type="text"
                  placeholder="Valor"
                  value={value}
                  onChange={(e) => setValue(e.target.value)}
                />
              </CFormGroup>
              <CButton
                color="info"
                onClick={handleUpdate}
                className="mr-2"
                title={inforPermissions(permissao).edit}
                disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
              >
                Salvar
              </CButton>
              <CButton color="secondary" onClick={clearInputs}>
                Limpar campos
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </div>{" "}
      {isLoading ? (
        <CardLoading />
      ) : (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <CCard>
            <TableSelectItens
              data={data}
              columns={columns}
              onSelectionChange={(_) => { }}
              defaultSelectedKeys={[]}
              selectable={false}
              heightParam="580px"
            />
          </CCard>
          <ConfirmModal
            isOpen={showConfirmModal}
            onClose={handleModalClose}
            texto={"Tem certeza que deseja deletar configuração de API?"}
          />
        </div>
      )}
    </div>
  );
};

export default ConfigTelaUnica;
