import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CButton,
  CDataTable,
} from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";
import {
  calculaValoresDetelhes,
  calculaValoresParcelas,
} from "src/views/negociacao/utils/CalculosNegociacao";

const DetalhesCalculoModal = ({ isOpen, onClose, dados }) => {
  const [tableData, setTableData] = useState([]);

  /* const parametroVazio = [
    {
      dias_De_Calc: 0,
      dias_Ate_Calc: 0,
      perc_Juros: 0,
      perc_Multa: 0,
      perc_Honor: 0,
      perc_Desc_Juros: 0,
      perc_Desc_Multa: 0,
    },
  ];


  function calcularMultaJurosPerc() {
    const percent =
      (dados.vlNegociacao - detParcelasSel.vlOriParc) /
      (dados.vlDividaAtualizadaMaxDesc - detParcelasSel.vlOriParc);

    let MultaTotal = 0;
    let multaMaxDesconto = 0;
    let multaValorAplicado = 0;
    let multaPercAplicado = 0;
    let multaDescAplicado = 0;

    let JurosTotal = 0;
    let jurosMaxDesconto = 0;
    let jurosValorAplicado = 0;
    let jurosPercAplicado = 0;
    let jurosDescAplicado = 0;

    const truncar = (valor, range) => {
      const truncatedValue = parseFloat(valor.toFixed(range));
      return truncatedValue;
    };

    if (parametros && parametros.length > 0) {
      //Calcula a multa
      MultaTotal = jurosMultas.multas;
      if (parametros.perc_Desc_Multa > 0) {
        multaMaxDesconto = MultaTotal * (parametros.perc_Desc_Multa / 100);
        multaValorAplicado = multaMaxDesconto * percent;
        multaPercAplicado =
          (truncar(multaValorAplicado, 2) * 100) / truncar(multaMaxDesconto, 2);
        multaDescAplicado =
          (multaValorAplicado * parametros.perc_Desc_Multa) / multaMaxDesconto;
      }

      //Calcula os juros
      JurosTotal = jurosMultas.juros;
      if (parametros.perc_Desc_Juros > 0) {
        jurosMaxDesconto = JurosTotal * (parametros.perc_Desc_Juros / 100);
        jurosValorAplicado = jurosMaxDesconto * percent;
        jurosPercAplicado =
          (truncar(jurosValorAplicado, 2) * 100) / truncar(jurosMaxDesconto, 2);
        jurosDescAplicado =
          (jurosValorAplicado * parametros.perc_Desc_Juros) / jurosMaxDesconto;
      }
    }
    const multaNegociacao = {
      MultaTotal,
      multaMaxDesconto,
      multaValorAplicado,
      multaPercAplicado,
      multaDescAplicado,
    };

    const jurosNegociacao = {
      JurosTotal,
      jurosMaxDesconto,
      jurosValorAplicado,
      jurosPercAplicado,
      jurosDescAplicado,
    };

    const jurosNegociado = JurosTotal - truncar(jurosValorAplicado, 2);
    const multaNegociada = MultaTotal - truncar(multaValorAplicado, 2);

    const somaTotal = JurosTotal + MultaTotal;
    const somaAplicado = jurosValorAplicado + multaValorAplicado;
    const aplicadoPerc = somaTotal > 0 ? (somaAplicado * 100) / somaTotal : 0;
    const descJurosMulta = {
      multas: multaNegociacao,
      juros: jurosNegociacao,
      valorTotal: somaTotal.toFixed(2),
      valorAplicado: somaAplicado.toFixed(2),
      percDesconto: aplicadoPerc.toFixed(2),
      jurosNegociado: jurosNegociado,
      multaNegociada: multaNegociada,
    };

    return descJurosMulta;
  } */

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    const dadosCalculados = calculaValoresDetelhes(dados);
    const realOriginal = dadosCalculados.realOriginal;
    const atualizadoOriginal = dadosCalculados.atualizadoOriginal;
    const correcaoOriginal = dadosCalculados.correcaoOriginal;
    const jurosOriginal = dadosCalculados.jurosOriginal;
    const multaOriginal = dadosCalculados.multaOriginal;
    const comissaoOriginal = dadosCalculados.comissaoOriginal;
    const subTotalOriginal = dadosCalculados.subTotalOriginal;
    const honorariosOriginal = dadosCalculados.honorariosOriginal;
    const custasOriginal = dadosCalculados.custasOriginal;
    const notificacaoOriginal = dadosCalculados.notificacaoOriginal;
    const tarifaOriginal = dadosCalculados.tarifaOriginal;
    const iofOriginal = dadosCalculados.iofOriginal;
    const totalOriginal = dadosCalculados.totalOriginal;

    const correcaoDescMax = dadosCalculados.correcaoDescMax;
    const jurosDescMax = dadosCalculados.jurosDescMax;
    const multaDescMax = dadosCalculados.multaDescMax;
    const comissaoDescMax = dadosCalculados.comissaoDescMax;
    const honorariosDescMax = dadosCalculados.honorariosDescMax;
    const custasDescMax = dadosCalculados.custasDescMax;
    const notificacaoDescMax = dadosCalculados.notificacaoDescMax;
    const tarifaDescMax = dadosCalculados.tarifaDescMax;
    const iofDescMax = dadosCalculados.iofDescMax;
    const totalDescMax = dadosCalculados.totalDescMax;
    const subTotalDescMax = dadosCalculados.subTotalDescMax;
    const realDescMax = dadosCalculados.realDescMax;
    const atualizadoDescMax = dadosCalculados.atualizadoDescMax;

    const correcaoNegociacao = dadosCalculados.correcaoNegociacao;
    const honorariosNegociacao = dadosCalculados.honorariosNegociacao;
    const jurosNegociacao = dadosCalculados.jurosNegociacao;
    const multaNegociacao = dadosCalculados.multaNegociacao;
    const comissaoNegociacao = dadosCalculados.comissaoNegociacao;
    const custasNegociacao = dadosCalculados.custasNegociacao;
    const notificacaoNegociacao = dadosCalculados.notificacaoNegociacao;
    const tarifaNegociacao = dadosCalculados.tarifaNegociacao;
    const iofNegociacao = dadosCalculados.iofNegociacao;
    const totalNegociacao = dadosCalculados.totalNegociacao;
    const subTotalNegociacao = dadosCalculados.subTotalNegociacao;
    const realNegociacao = dadosCalculados.realNegociacao;
    const atualizadoNegociacao = dadosCalculados.atualizadoNegociacao;

    const percDescMax = dadosCalculados.percDescMax;
    const percNegociacao = dadosCalculados.percNegociacao;

    const percCorrecaoDescMax = dadosCalculados.percCorrecaoDescMax;
    const percCorrecaoNegociacao = dadosCalculados.percCorrecaoNegociacao;

    const percAtualizadoDescMax = dadosCalculados.percAtualizadoDescMax;
    const percAtualizadoNegociacao = dadosCalculados.percAtualizadoNegociacao;

    const percJurosDescMax = dadosCalculados.percJurosDescMax;
    const percJurosNegociacao = dadosCalculados.percJurosNegociacao;

    const percMultaDescMax = dadosCalculados.percMultaDescMax;
    const percMultaNegociacao = dadosCalculados.percMultaNegociacao;

    const percComissaoDescMax = dadosCalculados.percComissaoDescMax;
    const percComissaoNegociacao = dadosCalculados.percComissaoNegociacao;

    const percSubTotalDescMax = dadosCalculados.percSubTotalDescMax;
    const percSubTotalNegociacao = dadosCalculados.percSubTotalNegociacao;

    const percHonorariosDescMax = dadosCalculados.percHonorariosDescMax;
    const percHonorariosNegociacao = dadosCalculados.percHonorariosNegociacao;

    const percCustasDescMax = dadosCalculados.percCustasDescMax;
    const percCustasNegociacao = dadosCalculados.percCustasNegociacao;

    const percNotificacaoDescMax = dadosCalculados.percNotificacaoDescMax;
    const percNotificacaoNegociacao = dadosCalculados.percNotificacaoNegociacao;

    const percTarifaDescMax = dadosCalculados.percTarifaDescMax;
    const percTarifaNegociacao = dadosCalculados.percTarifaNegociacao;

    const percIofDescMax = dadosCalculados.percIofDescMax;
    const percIofNegociacao = dadosCalculados.percIofNegociacao;

    const percTotalDescMax = dadosCalculados.percTotalDescMax;
    const percTotalNegociacao = dadosCalculados.percTotalNegociacao;

    setTableData([
      {
        label: "(Valores)",
        Original: "(R$)",
        Desconto_Perc: "(%)",
        Desconto_Real: "(R$)",
        Negociacao_Perc: "(%)",
        Negociacao_Real: "(R$)",
      },
      {
        label: "Real",
        Original: formatThousands(realOriginal),
        Desconto_Perc: formatThousands(percDescMax),
        Desconto_Real: formatThousands(realDescMax),
        Negociacao_Perc: formatThousands(percNegociacao),
        Negociacao_Real: formatThousands(realNegociacao),
      },
      {
        label: "Correção",
        Original: formatThousands(correcaoOriginal),
        Desconto_Perc: formatThousands(percCorrecaoDescMax),
        Desconto_Real: formatThousands(correcaoDescMax),
        Negociacao_Perc: formatThousands(percCorrecaoNegociacao),
        Negociacao_Real: formatThousands(correcaoNegociacao),
      },
      {
        label: "Atualizado",
        Original: formatThousands(atualizadoOriginal),
        Desconto_Perc: formatThousands(percAtualizadoDescMax),
        Desconto_Real: formatThousands(atualizadoDescMax),
        Negociacao_Perc: formatThousands(percAtualizadoNegociacao),
        Negociacao_Real: formatThousands(atualizadoNegociacao),
      },
      {
        label: "Juros",
        Original: formatThousands(jurosOriginal),
        Desconto_Perc: formatThousands(percJurosDescMax),
        Desconto_Real: formatThousands(jurosDescMax),
        Negociacao_Perc: formatThousands(percJurosNegociacao),
        Negociacao_Real: formatThousands(jurosNegociacao),
      },
      {
        label: "Multa",
        Original: formatThousands(multaOriginal),
        Desconto_Perc: formatThousands(percMultaDescMax),
        Desconto_Real: formatThousands(multaDescMax),
        Negociacao_Perc: formatThousands(percMultaNegociacao),
        Negociacao_Real: formatThousands(multaNegociacao),
      },
      {
        label: "Comissão de Perm.",
        Original: formatThousands(comissaoOriginal),
        Desconto_Perc: formatThousands(percComissaoDescMax),
        Desconto_Real: formatThousands(comissaoDescMax),
        Negociacao_Perc: formatThousands(percComissaoNegociacao),
        Negociacao_Real: formatThousands(comissaoNegociacao),
      },
      {
        label: "Subtotal",
        Original: formatThousands(subTotalOriginal),
        Desconto_Perc: formatThousands(percSubTotalDescMax),
        Desconto_Real: formatThousands(subTotalDescMax),
        Negociacao_Perc: formatThousands(percSubTotalNegociacao),
        Negociacao_Real: formatThousands(subTotalNegociacao),
      },
      {
        label: "Honorários",
        Original: formatThousands(honorariosOriginal),
        Desconto_Perc: formatThousands(percHonorariosDescMax),
        Desconto_Real: formatThousands(honorariosDescMax),
        Negociacao_Perc: formatThousands(percHonorariosNegociacao),
        Negociacao_Real: formatThousands(honorariosNegociacao),
      },
      {
        label: "Despesas",
        Original: formatThousands(custasOriginal),
        Desconto_Perc: formatThousands(percCustasDescMax),
        Desconto_Real: formatThousands(custasDescMax),
        Negociacao_Perc: formatThousands(percCustasNegociacao),
        Negociacao_Real: formatThousands(custasNegociacao),
      },
      {
        label: "Notificação",
        Original: formatThousands(notificacaoOriginal),
        Desconto_Perc: formatThousands(percNotificacaoDescMax),
        Desconto_Real: formatThousands(notificacaoDescMax),
        Negociacao_Perc: formatThousands(percNotificacaoNegociacao),
        Negociacao_Real: formatThousands(notificacaoNegociacao),
      },
      {
        label: "Tarifa",
        Original: formatThousands(tarifaOriginal),
        Desconto_Perc: formatThousands(percTarifaDescMax),
        Desconto_Real: formatThousands(tarifaDescMax),
        Negociacao_Perc: formatThousands(percTarifaNegociacao),
        Negociacao_Real: formatThousands(tarifaNegociacao),
      },
      {
        label: "IOF",
        Original: formatThousands(iofOriginal),
        Desconto_Perc: formatThousands(percIofDescMax),
        Desconto_Real: formatThousands(iofDescMax),
        Negociacao_Perc: formatThousands(percIofNegociacao),
        Negociacao_Real: formatThousands(iofNegociacao),
      },
      {
        label: "Total",
        Original: formatThousands(totalOriginal),
        Desconto_Perc: formatThousands(percTotalDescMax),
        Desconto_Real: formatThousands(totalDescMax),
        Negociacao_Perc: formatThousands(percTotalNegociacao),
        Negociacao_Real: formatThousands(totalNegociacao),
      },
    ]);
  }, [isOpen]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="lg">
      <CModalHeader closeButton>Detalhes do Calculo</CModalHeader>
      <CModalBody>
        {tableData && (
          <table className="table table-hover calculo">
            <thead>
              <tr>
                <th></th>
                <th>Cálculo Original</th>
                <th colSpan={2}>Desconto Máximo</th>
                <th colSpan={2}>Negociação</th>
              </tr>
            </thead>
            <tbody>
              {tableData.map((row) => (
                <tr
                  key={row.label}
                  className={
                    row.label === "(Valores)"
                      ? "valores-row"
                      : row.label === "Total"
                      ? "total-row"
                      : row.label === "Subtotal"
                      ? "subtotal-row"
                      : "detalhes-row"
                  }
                >
                  <td>{row.label}</td>
                  <td className="column-detalhes-1">{row.Original}</td>
                  <td className="column-detalhes-2">{row.Desconto_Perc}</td>
                  <td className="column-detalhes-1">{row.Desconto_Real}</td>
                  <td className="column-detalhes-2">{row.Negociacao_Perc}</td>
                  <td className="column-detalhes-1">{row.Negociacao_Real}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default DetalhesCalculoModal;
