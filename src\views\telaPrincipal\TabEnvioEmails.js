import React, { useState, useEffect } from "react";
import { <PERSON>utton, CCardBody, CBadge, CCardHeader, CCard } from "@coreui/react";
import { CModal, CModalHeader, CModalBody, CModalFooter } from "@coreui/react";
import { GET_DATA } from "src/api";
import TableSelectItens from "src/reusable/TableSelectItens";
import CardLoading from "src/reusable/CardLoading";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import { useAuth } from "src/auth/AuthContext";
import SendEmail from "./CardContratos/OutrosModals/SendEmail";
import { formatDate } from "src/reusable/helpers";
import { toast } from "react-toastify";

const TabEnvioEmails = (active = false) => {
  const { checkPermission, inforPermissions } = useAuth();

  const permissao = {
    modulo: "Tela Principal",
    submodulo: "EnvioEmail",
  };

  const financiadoData = localStorage.getItem("financiadoData") ? JSON.parse(localStorage.getItem("financiadoData")) : null;
  const user = localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user")) : null;

  const [tableData, setTableData] = useState(null);
  const [loadData, setLoadData] = useState(null);
  const [editEmail, setEditEmail] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedEmailContent, setSelectedEmailContent] = useState(null);

  const handleClose = async () => {
    setShowModal(false);
  };

  const handleEditar = (item) => {
    setEditEmail(item);
    setShowModal(true);
  }

  const handleResend = async (item) => {
    const data = { id: item.id };
    await GET_DATA("SendFromEmail/resend", data);

    toast.success("O email será reenviado em breve!");
  }

  const handleViewMsg = (item) => {
    setSelectedEmailContent(item);
    setShowViewModal(true);
  }

  const handleAdicionar = () => {
    setEditEmail(null);
    setShowModal(true);
  }

  const tableColumns = [
    {
      key: "ordenacao",
      label: " ",
      defaultSortColumn: true,
      defaultSort: "ascending",
      className: "text-white",
      style: { display: "none" },
      cellStyleCondicional: (_) => ({ display: "none" }),
    },
    {
      key: "contract", 
      label: "Usuário",
      formatterByObject: (item) => renderUsuario(item),
    },
    { key: "sendDate", label: "Dt. Envio", formatter: (item) => formatDate(item) },
    { key: "subject", label: "Assunto" },
    {
      key: "status",
      label: "Status",
      formatterByObject: (item) => renderStatus(item),
    },
    {
      key: "action",
      label: "Alterar",
      formatterByObject: (item) => renderAction(item),
    },
  ];

  const IconButton = ({ icon, color, titulo, onClick }) => {
    return (
      <CButton
        title={titulo}
        className="mr-1"
        style={{ border: "solid 1px", borderColor: color, color: color, padding: "2px 4px" }}
        onClick={onClick}
      >
        <i className={icon} />
      </CButton>
    );
  };

  const ordenacaoEmails = (items) => {
    if (!items) return;
    const clientEmailsSortData = items?.map((item) => {
      item.ordenacao = ordenacaoStatus(item);
      return item;
    });

    const sortEmails = clientEmailsSortData.sort((a, b) => {
      if (a.ordenacao < b.ordenacao) {
        return -1;
      }
      if (a.ordenacao > b.ordenacao) {
        return 1;
      }
      return 0;
    });
    return sortEmails;
  };

  const getDados = async () => {
    setIsLoading(true);
    if (financiadoData?.id_Contrato) {
      const data = { idContrato: financiadoData?.id_Contrato, idUser: user?.id };
      const newDados = await GET_DATA("SendFromEmail/user", data);
      setLoadData(data)
      if (newDados) {
        const sortEmails = ordenacaoEmails(newDados);
        setTableData(sortEmails);
      }
    }

    setIsLoading(false);
    return;
  };

  useEffect(async () => {
    let isMounted = true;
    if (active.active && isMounted && financiadoData?.id_Contrato && (loadData?.idContrato !== financiadoData?.id_Contrato) && !isLoading) {
      await getDados();
    }
    return () => { isMounted = false; };
  }, [financiadoData, active]);

  const ordenacaoStatus = (item) => {
    return item.status;
  };

  const renderUsuario = (item) => {
    return (<CBadge color={"success"}>{user.name}</CBadge>);
  }

  const renderStatus = (item) => {
    return item.status === "Create" ? (
      <CBadge color={"info"}>Criado</CBadge>
    ) : item.status === "Sended" ? (
      <CBadge color={"success"}>Enviado</CBadge>
    ) : item.status === "Resend" ? (
      <CBadge color={"success"}>Re-enviado</CBadge>
    ) : item.status === "Failed" ? (
      <CBadge color={"danger"}>Falha no envio</CBadge>
    ) : item.status === "4" ? (
      <CBadge color={"Canceled"}>Cancelado</CBadge>
    ) : (<></>)
  }

  const renderAction = (item) => {
    return (
      <div style={{ display: "flex" }}>
        <IconButton
          icon={"cil-pencil"}
          color="blue"
          onClick={() => handleEditar(item)}
          size="sm"
          className="mr-2"
          title={
            !item
              ? "Editar E-mail"
              : inforPermissions(permissao).edit
          }
          disabled={
            !item ||
            !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
          }
        />
        <IconButton
          icon={"cil-envelope-open"}
          color="green"
          onClick={() => handleResend(item)}
          size="sm"
          className="mr-2"
          title={"Reenvio de E-mail"}
          disabled={
            !item ||
            !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
          }
        />
        <IconButton
          icon={"cil-text-square"}
          color="info"
          onClick={() => handleViewMsg(item)}
          size="sm"
          className="mr-2"
          title={"Visualizar E-mail"}
        />
      </div>
    );
  };

  return (
    <>
      <CCard>
        <CCardHeader style={{ display: "flex", alignItems: "center" }}>
          <i className="cil-envelope-open mr-2" />
          Histórico de Emails Enviados
          <div className="mr-10" style={{ marginLeft: "auto" }}>
            <CButton
              className="btn-custom-outline-green"
              icon="cil-plus"
              color={"green"}
              titulo={"Enviar Email"}
              onClick={() => handleAdicionar()}
              title={
                !financiadoData
                  ? "Nenhum financiado Selecionado"
                  : inforPermissions(permissao).create
              }
              disabled={
                !financiadoData ||
                !checkPermission(permissao.modulo, "Create", permissao.submodulo)
              }
            >
              <i className="cil-plus"></i>Enviar Email
            </CButton>
            {showModal && (
              <SendEmail
                contract={financiadoData}
                user={user}
                editEmail={editEmail}
                isOpen={showModal}
                onClose={handleClose}
              />
            )}
          </div>
        </CCardHeader>
        {(tableData && (tableData?.length ?? 0) > 0) ? (
          <CCardBody className="mt-2">
            {isLoading ? (
              <CardLoading msg={"Atualizando..."} />
            ) : (
              <TableSelectItens
                data={tableData}
                columns={tableColumns}
                onSelectionChange={(_) => { }}
                defaultSelectedKeys={[]}
                selectable={false}
              />
            )}
          </CCardBody>
        ) : (
          <CCardBody>
            <NaoHaDadosTables />
          </CCardBody>
        )}
      </CCard>

      <CModal show={showViewModal} onClose={() => setShowViewModal(false)} closeOnBackdrop={true}>
        <CModalHeader closeButton>Visualizar E-mail</CModalHeader>
        <CModalBody>
          {selectedEmailContent ? (
            <>
              <p><strong>Assunto:</strong> {selectedEmailContent.subject}</p>
              <p><strong>Data de Envio:</strong> {formatDate(selectedEmailContent.sendDate)}</p>
              <p><strong>Contrato:</strong> {selectedEmailContent.contract}</p>
              <p><strong>Destinatário:</strong> {selectedEmailContent.destination}</p>
              <hr />
              <p><strong>Conteúdo:</strong></p>
              <div dangerouslySetInnerHTML={{ __html: selectedEmailContent.content }} />
            </>
          ) : (
            <p>Nenhum conteúdo disponível.</p>
          )}
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setShowViewModal(false)}>
            Fechar
          </CButton>
        </CModalFooter>
      </CModal>
    </>
  );
};

export default TabEnvioEmails;
