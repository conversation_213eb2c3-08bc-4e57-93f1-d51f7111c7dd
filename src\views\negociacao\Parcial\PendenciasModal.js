import {
  CButton,
  CCol,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CRow,
} from "@coreui/react";
import React from "react";
import { formatCurrency, formatDate } from "src/reusable/helpers";

const PendenciasModal = ({ isOpen, onClose, pendencias }) => {
  const handleClose = () => {
    onClose();
  };
  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="lg">
      <CModalHeader>Pendências</CModalHeader>
      <CModalBody>
        <CRow
          className={"m-3"}
          style={{ maxHeight: "232px", overflow: "auto" }}
        >
          <table className="table  table-hover">
            <thead>
              <th>Data</th>
              <th>Valor</th>
              <th>Observações</th>
            </thead>
            <tbody>
              {pendencias.map((item) => (
                <tr key={item.id}>
                  <td>{formatDate(item.dataPendencia)}</td>
                  <td>{formatCurrency(item.valorPendencia)}</td>
                  <td>{item.observacoes}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CRow>
          <CCol className="d-flex justify-content-end">
            <CButton
              color="secondary"
              className="mr-2"
              onClick={() => handleClose()}
            >
              Fechar
            </CButton>
          </CCol>
        </CRow>
      </CModalFooter>
    </CModal>
  );
};

export default PendenciasModal;
