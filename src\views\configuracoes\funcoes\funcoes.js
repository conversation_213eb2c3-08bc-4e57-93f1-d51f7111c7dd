import React, { useState, useEffect } from "react";
import Select from "react-select";
import {
  <PERSON><PERSON>T<PERSON>,
  <PERSON>ard<PERSON>oot<PERSON>,
  <PERSON>ard,
  CCardBody,
  CButton,
  CInputCheckbox,
  CRow,
  CCol,
  CLabel,
  CForm,
  CFormGroup,
} from "@coreui/react";
import CreateRoleModal from "./CreateRoleModal";
import { getURI } from "src/config/apiConfig";
import { useAuth } from "src/auth/AuthContext";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import TreeRole from "./partial/TreeRole";

const Funcoes = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Configurações",
    submodulo: "Funções",
  };

  const token = localStorage.getItem("token");
  const userRole = JSON.parse(localStorage.getItem("user")).role;
  const [selectedRole, setSelectedRole] = useState(null);
  const [permissionsData, setPermissionsData] = useState(null);
  const [roles, setRoles] = useState([]);

  const [isCreateModalOpen, setCreateModalOpen] = useState(false);

  const openModal = () => {
    setSelectedRole(null);
    setCreateModalOpen(true);
  };

  const closeModal = () => {
    setSelectedRole(null);
    setPermissionsData(null);
    setCreateModalOpen(false);
  };

  const getRoles = async () => {
    try {
      const response = await fetch(`${getURI()}/Role`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setRoles(data?.data);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando funções:", error);
    }
  };

  const getUser = async () => {
    const token = localStorage.getItem("token");
    try {
      const response = await fetch(`${getURI()}/User`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem("user", JSON.stringify(data.data));
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando usuários:", error);
    }
  };

  const tableFields = [
    { key: "module", label: "Módulo" },
    {
      key: "view",
      label: "Ver",
      _style: { width: "15%", textAlign: "center" },
    },
    {
      key: "edit",
      label: "Editar",
      _style: { width: "15%", textAlign: "center" },
    },
    {
      key: "delete",
      label: "Deletar",
      _style: { width: "15%", textAlign: "center" },
    },
    {
      key: "create",
      label: "Criar",
      _style: { width: "15%", textAlign: "center" },
    },
  ];

  const handleRoleChange = (selectedOption) => {
    setSelectedRole(selectedOption);
  };

  const handlePermissionChange = (itens) => {
    setPermissionsData(itens);
  };

  /* const handlePermissionChange = (
    moduleName,
    permission,
    isChecked,
    isHeader = true
  ) => {
    if (isHeader) {
      setPermissionsData((prevPermissions) => {
        const updatedPermissions = prevPermissions.map((prevPermission) => {
          if (prevPermission.module === moduleName) {
            return {
              ...prevPermission,
              [permission]: isChecked,
            };
          }
          return prevPermission;
        });
        return updatedPermissions;
      });
    } else {
      const updatedPermissions = permissionsData.map((prevPermission) => {
        if (prevPermission.subModules && prevPermission.subModules.length > 0) {
          const innerPermissions = prevPermission.subModules.map(
            (subPermission) => {
              if (subPermission.module === moduleName) {
                return {
                  ...subPermission,
                  [permission]: isChecked,
                };
              }
              return innerPermissions;
            }
          );
        }
        return updatedPermissions;
      });
    }
  }; */

  const handleSavePermissions = async () => {
    const updatedRoles = [...roles];

    const roleToUpdate = updatedRoles.find(
      (role) => role.id === selectedRole.id
    );

    if (roleToUpdate) {
      roleToUpdate.modules = permissionsData;
      roleToUpdate.roleId = selectedRole.id;
    }

    try {
      const url = `${getURI()}/Role/UpdateRoleModule`;

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(roleToUpdate),
      });

      if (response.ok) {
        toast.success("Função atualizada com sucesso!");
        getRoles();
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro atualizando as permissões:", error);
    }
    if (roleToUpdate.roleId === userRole.id) {
      getUser();
    }
  };

  const handleNewRole = async (newRole) => {
    try {
      const url = `${getURI()}/Role`;

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(newRole),
      });

      const status = await response.json();

      if (response.ok) {
        if (status.success) {
          toast.success("Função criada com sucesso!");
          getRoles();
        } else {
          toast.info("Já existe uma função com esse nome.");
        }
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro criando a função:", error);
    }
  };

  const handleSaveRole = async (editedRole) => {
    try {
      const url = `${getURI()}/Role`;

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(editedRole),
      });

      if (response.ok) {
        toast.success("Função atualizada com sucesso!");
        getRoles();
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro editando a função:", error);
    }
    setSelectedRole(editedRole);
  };

  const handleDeactivateRole = async (role) => {
    try {
      const url = `${getURI()}/Role?id=${role.id}`;

      const response = await fetch(url, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          toast.warning("Função desativada.");
          getRoles();
        } else {
          toast.warning(
            "Não é possível desativar uma função com usuários vinculados."
          );
        }
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro criando a função:", error);
    }
    setSelectedRole(null);
    setPermissionsData(null);
    getRoles();
  };

  const handleDelete = (role) => {
    const confirmDelete = window.confirm(
      "Tem certeza que deseja desativar essa função?"
    );

    if (confirmDelete) {
      handleDeactivateRole(role);
    }
  };

  const roleOptions = roles.map((role) => ({
    id: role.id,
    name: role.name,
    modules: role.modules,
  }));

  useEffect(() => {
    getRoles();
  }, []);

  useEffect(() => {
    if (selectedRole) {
      setPermissionsData(
        selectedRole.modules?.filter((x) => x.module !== "Campanhas Safra")
      );
    }
  }, [selectedRole]);

  return (
    <div>
      <h3>Funções</h3>
      <p style={{ color: "gray" }}>
        Defina as regras e permissões dos usuários.
      </p>
      <CRow>
        <CCol md={5}>
          <CForm>
            <CFormGroup style={{ display: "flex", alignItems: "center" }}>
              <CLabel className="mr-2">Selecione uma função:</CLabel>
              <div className="flex-grow-1">
                <Select
                  options={roleOptions}
                  value={selectedRole}
                  onChange={handleRoleChange}
                  placeholder="Nome da função"
                  className="fixed-select"
                  getOptionValue={(option) => option.id}
                  getOptionLabel={(option) => option.name}
                  title={inforPermissions(permissao).view}
                  disabled={
                    !checkPermission(
                      permissao.modulo,
                      "View",
                      permissao.submodulo
                    )
                  }
                />
              </div>
            </CFormGroup>
          </CForm>
        </CCol>
        <CCol md={7} className="text-right">
          <CButton
            color="danger"
            className="mr-2"
            onClick={() => handleDelete(selectedRole)}
            title={inforPermissions(permissao).delete}
            disabled={
              (selectedRole ? false : true) ||
              !checkPermission(permissao.modulo, "Delete", permissao.submodulo)
            }
          >
            Desativar função
          </CButton>
          <CButton
            color="secondary"
            className="mr-2"
            onClick={() => setCreateModalOpen(true)}
            title={
              !selectedRole
                ? "Selecione uma função"
                : inforPermissions(permissao).edit
            }
            disabled={
              (selectedRole ? false : true) ||
              !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
            }
          >
            Editar função
          </CButton>
          <CButton
            color="info"
            onClick={openModal}
            title={inforPermissions(permissao).create}
            disabled={
              !checkPermission(permissao.modulo, "Create", permissao.submodulo)
            }
          >
            <i
              className={"cil-plus"}
              size="sm"
              style={{
                border: "1px solid white",
                borderRadius: "100%",
                padding: "2px",
              }}
            />{" "}
            Criar nova função
          </CButton>
          {isCreateModalOpen && (
            <CreateRoleModal
              isOpen={isCreateModalOpen}
              role={selectedRole}
              onSave={handleSaveRole}
              onRoleCreate={handleNewRole}
              onClose={closeModal}
            />
          )}
        </CCol>
      </CRow>
      <CCard>
        <CCardBody>
          {permissionsData && (
            <>
              <TreeRole
                initialItems={permissionsData}
                onCheckboxChange={handlePermissionChange}
              />
              {/*  <CDataTable
              items={permissionsData}
              fields={tableFields}
              scopedSlots={{
                view: (item) => (
                  <td style={{ textAlign: "center", paddingLeft: "40px" }}>
                    <CInputCheckbox
                      disabled={selectedRole ? false : true}
                      onChange={(e) =>
                        handlePermissionChange(
                          item.module,
                          "view",
                          e.target.checked
                        )
                      }
                      checked={item.view}
                    />
                  </td>
                ),
                edit: (item) => (
                  <td style={{ textAlign: "center", paddingLeft: "40px" }}>
                    <CInputCheckbox
                      disabled={selectedRole ? false : true}
                      onChange={(e) =>
                        handlePermissionChange(
                          item.module,
                          "edit",
                          e.target.checked
                        )
                      }
                      checked={item.edit}
                    />
                  </td>
                ),
                delete: (item) => (
                  <td style={{ textAlign: "center", paddingLeft: "40px" }}>
                    <CInputCheckbox
                      disabled={selectedRole ? false : true}
                      onChange={(e) =>
                        handlePermissionChange(
                          item.module,
                          "delete",
                          e.target.checked
                        )
                      }
                      checked={item.delete}
                    />
                  </td>
                ),
                create: (item) => (
                  <td style={{ textAlign: "center", paddingLeft: "40px" }}>
                    <CInputCheckbox
                      disabled={selectedRole ? false : true}
                      onChange={(e) =>
                        handlePermissionChange(
                          item.module,
                          "create",
                          e.target.checked
                        )
                      }
                      checked={item.create}
                    />
                  </td>
                ),
              }}
            /> */}
            </>
          )}
        </CCardBody>
        {permissionsData && (
          <CCardFooter>
            <CButton
              color="info"
              onClick={handleSavePermissions}
              title={inforPermissions(permissao).edit}
              disabled={
                !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
              }
            >
              Salvar alterações
            </CButton>
          </CCardFooter>
        )}
      </CCard>
    </div>
  );
};

export default Funcoes;
