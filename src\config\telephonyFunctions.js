import { DELETE_DATA, GET_DATA, POST_DATA, PUT_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { GET_ClientData } from "src/reusable/functions";

export async function postAutenticar(ramal = null, connectionId = null) {
  const payload = {
    ramal: ramal,
    connectionId: connectionId,
  };

  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI("postAutenticarTel"),
        payload,
        true
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  })
    .then((data) => {
      if (data) {
        localStorage.setItem("telephonyData", JSON.stringify(data.data));
        return data;
        //Do function
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      //Finally
    });
}

export async function postCredentials(payload) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI("postCredentials"),
        payload,
        true
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  })
    .then((data) => {
      if (data) {
        localStorage.setItem("telephonyData", JSON.stringify(data.data));
        return data;
        //Do function
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      //Finally
    });
}

export async function listarPausas(AgentId) {
  const apiUrl = `Telephony/Pausa/${AgentId}/Listar`;
  const pausaOptions = await GET_DATA(apiUrl);
  return pausaOptions;
}

export async function listarPausasTactium() {
  const apiUrl = `PauseTactium`;
  const pausaOptions = await GET_DATA(apiUrl);
  return pausaOptions;
}

export async function listarTabulacao(CampaignId) {
  const apiUrl = `Telephony/Tabulacao/${CampaignId}/Listar`;
  const tabulacoesOptions = await GET_DATA(apiUrl);
  return tabulacoesOptions;
}

export async function acionarTabulacao(payload) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI("postAcionarTabulacao"),
        payload,
        true
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  })
    .then((data) => {
      if (data) {
        return data.data;
        //Do function
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      //Finally
    });
}

export async function sairTabulacao(agentId) {
  const payload = {
    agentId: agentId ?? 0,
  };
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI("postSairTabulacao"),
        payload,
        true
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  })
    .then((data) => {
      if (data) {
        return data.data;
        //Do function
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      //Finally
    });
}

export async function acionarLigacao(ddd, phone, callData = null) {
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const telephonyData = localStorage.getItem("telephonyData")
    ? JSON.parse(localStorage.getItem("telephonyData"))
    : null;

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  // const callData = localStorage.getItem("callData")
  //   ? JSON.parse(localStorage.getItem("callData"))
  //   : null;

  const ramalTactium = localStorage.getItem("ramalTactium")
    ? JSON.parse(localStorage.getItem("ramalTactium"))
    : null;

  if (!telephonyData) {
    return 11;
  }
  if (!callData || !callData.status) {
    return 12;
  }

  const validId = await getCampaigns();

  if (validId) {
    let payload = {};

    let campaignId = validId.find(
      (x) =>
        x.groupId === financiadoData.id_Grupo &&
        x.crm === financiadoData.coddatacob
    );
    if (campaignId === undefined) {
      campaignId = validId.find((x) => x.groupId === null && x.crm === null);
    }

    if (user.telephonyId === 1) {
      payload = {
        phone: ddd.trim() + phone,
        ramalTactium: ramalTactium,
      };
    }
    if (user.telephonyId === 2) {
      payload = {
        agentIdOlos: telephonyData.agentId,
        campaignIdOlos: campaignId.campaignId,
        // campaignCodeOlos: callData.campaignIdOlos,
        dddOlos: ddd,
        phone: phone,
      };
    }

    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(
          getURI("postAcionarLigacao"),
          payload,
          true
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    })
      .then((data) => {
        if (data) {
          return data.success;
          //Do function
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        //Finally
      });
  } else {
    return 13;
  }
}
export async function acionarPauseLigacao(ddd, phone, callData = null) {
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const telephonyData = localStorage.getItem("telephonyData")
    ? JSON.parse(localStorage.getItem("telephonyData"))
    : null;
  // const callData = localStorage.getItem("callData")
  //   ? JSON.parse(localStorage.getItem("callData"))
  //   : null;

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const ramalTactium = localStorage.getItem("ramalTactium")
    ? JSON.parse(localStorage.getItem("ramalTactium"))
    : null;

  if (!telephonyData) {
    return 11;
  }
  if (!callData || !callData.status) {
    return 12;
  }

  const validId = await getCampaigns();

  if (validId) {
    let payload = {};

    let campaignId = validId.find(
      (x) =>
        x.groupId === financiadoData.id_Grupo &&
        x.crm === financiadoData.coddatacob
    );
    if (campaignId === undefined) {
      campaignId = validId.find((x) => x.groupId === null && x.crm === null);
    }

    if (user.telephonyId === 1) {
      return 13;
    }
    if (user.telephonyId === 2) {
      payload = {
        agentId: telephonyData.agentId,
        campaignId: campaignId.campaignId,
        // campaignCode: callData.campaignIdOlos,
        ddd: ddd,
        phone: phone,
      };
    }

    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(
          getURI("postAcionarPausaLigacao"),
          payload,
          true
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    })
      .then((data) => {
        if (data) {
          return data.success;
          //Do function
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        //Finally
      });
  } else {
    return 13;
  }
}

export async function confirmarLigacao(payload, callData) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await PUT_DATA(
        getURI("putConfirmarLigacao"),
        payload,
        true
      );
      if (response.success) {
        await activePhoneCall(callData);
      }
      resolve(response);
    } catch (error) {
      reject(error);
    }
  })
    .then((data) => {
      if (data) {
        return data.success;
        //Do function
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      //Finally
    });
}

const activePhoneCall = async (callData) => {
  const financiadoData =
    localStorage.getItem("financiadoData") !== null
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null;
  const clientData =
    localStorage.getItem("clientData") !== null
      ? JSON.parse(localStorage.getItem("clientData"))
      : null;
  // const callData = message
  // localStorage.getItem("callData") !== null
  //   ? JSON.parse(localStorage.getItem("callData"))
  //   : null;

  if (callData.phone !== null || callData.phone !== undefined) {
    let item = clientData.telefones.find(
      (x) =>
        callData.phone
          .replaceAll(" ", "")
          .trim()
          .indexOf(x.ddd.trim() + x.fone.trim()) >= 0 && x.status === 0
    );

    if (item === undefined) return;

    const trimmedFone = item.fone.trim();
    const data = {
      cpfCnpj: financiadoData.cpfCnpj,
      idGrupo: financiadoData.id_Grupo,
      nome: financiadoData.nome,
      telefones: [
        {
          ddd: item.ddd,
          fone: trimmedFone,
          tipoTelefone: item.id_Tipo_Telefone,
          ramal: item.ramal,
          descricao: item.descricao,
          contato: item.contato,
          status: 1,
          isHotNumber: item.isHotNumber,
          isWhatsApp: item.isWhatsApp,
        },
      ],
    };

    const postSuccess = await telefonePost(data);
    if (postSuccess.success) {
      await GET_ClientData(financiadoData);
    } else {
      console.warn("Erro na ativação do telefone");
    }
  }
};

const telefonePost = async (data) => {
  const result = await POST_DATA("Datacob/DadosCadastrais/Financiado", data);
  return result;
};

export async function pausarLigacao(payload) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI("postPausarLigacao"),
        payload,
        true
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  })
    .then((data) => {
      if (data) {
        return data;
        //Do function
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      //Finally
    });
}

export async function despausarLigacao(agentId) {
  const ramalTactium = localStorage.getItem("ramalTactium")
    ? JSON.parse(localStorage.getItem("ramalTactium"))
    : null;

  const payload = {
    agentIdOlos: agentId,
    ramal: ramalTactium,
  };

  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI("postDespausarLigacao"),
        payload,
        true
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  })
    .then((data) => {
      if (data) {
        return data.success;
        //Do function
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      //Finally
    });
}

export async function desligarLigacao(callData) {
  const ramalTactium = localStorage.getItem("ramalTactium")
    ? JSON.parse(localStorage.getItem("ramalTactium"))
    : null;

  // const callIdString = JSON.parse(
  //   localStorage.getItem("callData")
  // ).callId.toString();

  let callIdString = callData?.callIdTactium
    ? callData.callIdTactium
    : callData.callId
  const payload = {
    agentIdOlos: callData.agentId,
    callId: String(callIdString ?? ""),
    ramalTactium: ramalTactium,
  };

  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI("postDesligarLigacao"),
        payload,
        true,
        true
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  })
    .then((data) => {
      if (data) {
        return data.success;
        //Do function
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      //Finally
    });
}

export async function transferirLigacao(callData) {
  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";

  let campaignId = 0;

  const param = await new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(
        getURI("getTransferCampignByClient"),
        null,
        true,
        true,
        `/${financiadoData.coddatacob}/${financiadoData.id_Cliente}`
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  })
    .then((data) => {
      if (data) {
        campaignId = data.campaignId;
        //Do function
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      //Finally
    });

  const payload = {
    agentId: callData.agentId,
    campaignId: campaignId,
  };

  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI("postTransferirLigacao"),
        payload,
        true
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  })
    .then((data) => {
      if (data) {
        return data.success;
        //Do function
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      //Finally
    });
}

export async function deslogarAgente(payload) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI("postDeslogarTel"),
        payload,
        true
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  })
    .then((data) => {
      if (data) {
        return data.success;
        //Do function
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      //Finally
    });
}

export async function getCampaigns() {
  const campaigns = await GET_DATA("CallCampaign");
  return campaigns;
}

export async function postCallCampaign(campaignId, groupId = null, crm = null) {
  const payload = {
    campaignId: campaignId,
    groupId: groupId,
    crm: crm,
  };
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI("postCallCampaign"),
        payload,
        true
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  }).catch((err) => {
    console.log(err);
  });
}

export async function putCallCampaign(id, campaignId) {
  const payload = {
    id: id,
    campaignId: campaignId,
  };
  return new Promise(async (resolve, reject) => {
    try {
      const response = await PUT_DATA(getURI("putCallCampaign"), payload, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  }).catch((err) => {
    console.log(err);
  });
}

export async function deleteCallCampaign(id) {
  const payload = {
    id: id,
  };
  return new Promise(async (resolve, reject) => {
    try {
      const response = await DELETE_DATA(
        getURI("deleteCallCampaign"),
        payload,
        true
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  }).catch((err) => {
    console.log(err);
  });
}
