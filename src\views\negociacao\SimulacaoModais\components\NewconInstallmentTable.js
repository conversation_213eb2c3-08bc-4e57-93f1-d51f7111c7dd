const NewconTableInstallment = ({
  columns,
  selectAll,
  tableData,
  handleSelectAll,
}) => {
  return (
    <div className="table-responsive">
      <table className="table">
        <thead>
          <tr>
            {columns.map((column, key) => (
              <th key={key}>
                {column.label !== "" ? (
                  <div key={key}>{column.label}</div>
                ) : (
                  <div key={key}>
                    <input
                      key={key}
                      type="checkbox"
                      checked={selectAll}
                      onChange={(e) => handleSelectAll(e)}
                    />
                  </div>
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {tableData.map((item, key) => (
            <>
              <tr key={key}>
                {columns.map((column, key) => (
                  <td key={key}>
                    {column?.formatter && column?.formatter(item[column.key])}

                    {column?.formatterByObject &&
                      column?.formatterByObject(item)}

                    {!column?.formatter &&
                      !column?.formatterByObject &&
                      item[column.key]}
                  </td>
                ))}
              </tr>
            </>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default NewconTableInstallment;
