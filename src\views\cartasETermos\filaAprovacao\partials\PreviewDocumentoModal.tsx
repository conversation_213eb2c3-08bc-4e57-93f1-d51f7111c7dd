import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CSpinner,
} from "@coreui/react";
import { toast } from "react-toastify";
import { ItemFilaAprovacaoType } from "../../types/ItemFilaAprovacaoType";
import { postApiQueryFile } from "src/reusable/functions";
import "./PreviewDocumentoModal.css";

interface Props {
  isOpen: boolean;
  item: ItemFilaAprovacaoType | null;
  onClose: () => void;
}

const PreviewDocumentoModal = ({ isOpen, onClose, item }: Props) => {
  const [loading, setLoading] = useState(false);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleClose = () => {
    // Clean up the blob URL when closing
    if (pdfUrl) {
      URL.revokeObjectURL(pdfUrl);
      setPdfUrl(null);
    }
    setError(null);
    onClose();
  };

  const loadPreview = async () => {
    if (!item?.id) {
      setError("ID do documento não encontrado");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await postApiQueryFile(
        "postPedidoCartasEtermosGenerateByPedido",
        `idPedido=${item.id}`
      );

      if (response.ok) {
        const blob = await response.blob();

        // Check if the blob is actually a PDF
        if (blob.type === "application/pdf" || blob.size > 0) {
          const url = URL.createObjectURL(blob);
          setPdfUrl(url);
        } else {
          setError("O documento não está disponível ou não é um PDF válido");
        }
      } else {
        setError("Não foi possível carregar o preview do documento");
      }
    } catch (err) {
      console.error("Erro ao carregar preview:", err);
      setError("Erro ao carregar o preview do documento");
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    if (!item?.id) {
      toast.error("ID do documento não encontrado");
      return;
    }

    try {
      const response = await postApiQueryFile(
        "postPedidoCartasEtermosGenerateByPedido",
        `idPedido=${item.id}`
      );

      if (response.ok) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        const timestamp = new Date().getTime();
        link.download = `documento_${item.nrContrato}_${timestamp}.pdf`;
        document.body.appendChild(link);
        link.click();
        link.remove();
        URL.revokeObjectURL(url);
        toast.success("Download iniciado com sucesso!");
      } else {
        toast.error("Não foi possível baixar o documento");
      }
    } catch (err) {
      console.error("Erro ao baixar documento:", err);
      toast.error("Erro ao baixar o documento");
    }
  };

  useEffect(() => {
    if (isOpen && item) {
      loadPreview();
    }

    // Cleanup on unmount
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [isOpen, item]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="preview-modal"
      centered
    >
      <CModalHeader closeButton>
        <h5>Preview do Documento - {item?.nrContrato}</h5>
      </CModalHeader>

      <CModalBody style={{ minHeight: "600px", padding: "0" }}>
        {loading && (
          <div
            className="d-flex justify-content-center align-items-center"
            style={{ height: "600px" }}
          >
            <div className="text-center">
              <CSpinner
                color="primary"
                style={{ width: "3rem", height: "3rem" }}
              />
              <div className="mt-3">Carregando preview...</div>
            </div>
          </div>
        )}

        {error && (
          <div
            className="d-flex justify-content-center align-items-center"
            style={{ height: "600px" }}
          >
            <div className="text-center">
              <i
                className="cil-warning"
                style={{ fontSize: "3rem", color: "#f86c6b" }}
              ></i>
              <div className="mt-3 text-danger">{error}</div>
              <CButton color="primary" className="mt-3" onClick={loadPreview}>
                Tentar Novamente
              </CButton>
            </div>
          </div>
        )}

        {pdfUrl && !loading && !error && (
          <iframe
            src={pdfUrl}
            style={{
              width: "100%",
              height: "600px",
              border: "none",
            }}
            title="Preview do Documento"
          />
        )}
      </CModalBody>

      <CModalFooter>
        <CButton
          color="success"
          onClick={handleDownload}
          disabled={loading || !!error}
        >
          <i className="cil-cloud-download mr-2"></i>
          Download
        </CButton>
        <CButton color="secondary" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default PreviewDocumentoModal;
