import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CCard,
  CButton,
} from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";

const DetalhesNegociacaoModal = ({ isOpen, onClose, dados , dadosParcela }) => {
  const [tableDataL, setTableDataL] = useState(null);
  const [tableDataR, setTableDataR] = useState(null);

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      setTableDataL([
        {
          label: "Descrição",
          value: dados.descricao,
        },
        {
          label: "Dt. Negociação",
          value: dados.dt_Negociacao ? formatDate(dados.dt_Negociacao) : "",
        },
        {
          label: "Vl. Negociação",
          value: dados.vl_Negociacao,
        },
        {
          label: "Dt Cad. Negoc",
          value: dados.dt_Cadastro_Negociacao ? formatDate(dados.dt_Cadastro_Negociacao) : "",
        },
        {
          label: "Status",
          value: dados.status,
        },
        {
          label: "Liberado",
          value: dados.liberado ? "Sim" : "Não"
        },
        {
          label: "Dt Liberação",
          value: dados.dt_Calculo ? formatDate(dados.dt_Calculo) : "",
        },
        {
          label: "Prop Enviada",
          value: dados.negociacao_Enviada ? "Sim" : "Não",
        },
        {
          label: "Dt Prop Enviada",
          value: dados.dt_Negocicao_Enviada ? formatDate(dados.dt_Negocicao_Enviada) : "",
        },
        {
          label: "Id Faixa Calc",
          value: dados.id_Faixa_Calculo,
        },
        {
          label: "Id Faixa PC",
          value: dados.id_Faixa_Pc,
        },
        {
          label: "Id Parâm Calc",
          value: dados.id_Parametro_Calculo_Pc,
        },
        {
          label: "Atraso",
          value: dadosParcela.atraso,
        },
      ]);
      setTableDataR([
        {
          label: "Desc Autorizado",
          value: dados.desc ? "Sim" : "Não",
        },
        {
          label: "Nº Parcela",
          value: dadosParcela.nr_Parcela,
        },
        {
          label: "Desc Acordo",
          value: dadosParcela.nr_Acordo,
        },
        {
          label: "Saldo Integral",
          value: dadosParcela.saldo,
        },
        {
          label: "Tx Banc Bol",
          value: dados.tx_Banco,
        },
        {
          label: "% Desc Principal",
          value: dados.perc_desc ? dados.perc_desc : "0,00",
        },
        {
          label: "% Honorários",
          value: dados.id_Cheque,
        },
        {
          label: "Vl. Desconto",
          value: dados.cheque_Terceiro,
        },
        {
          label: "% Desconto",
          value: dados.origem_Boleto,
        },
        {
          label: "Desc Excedido",
          value: dados.descricao_Motivo_Cancel,
        },
        {
          label: "Comissão",
          value: dados.comissao,
        },
        {
          label: "Receita",
          value: dados.receita,
        },
        {
          label: "Repasse",
          value: dados.repasse,
        },
        {
          label: "Id Cobrador Lote",
          value: dados.id_Cobrador_Lote,
        },
        {
          label: "Id Cob Gerador",
          value: dados.id_Cobrador_Gerador,
        },
        {
          label: "Id Cob Ext",
          value: dados.id_Cobrador_Externo,
        },
      ]);
    }
  }, [isOpen]);

  const renderStatus = (value) => {
    switch (value) {
      case "P":
        return "Pago";
      case "D":
        return "Devolvido";
      case "A":
        return "Acordo";
      case "C":
        return "Cancelado";
      default:
        break;
    }
  };

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="xl">
      <CModalBody>
        {tableDataL && (
          <>
            <CRow className="mb-2">
              <CCol style={{ textAlign: "center" }}>Detalhes da Negociação</CCol>
            </CRow>
            <CRow>
              <CCol className="pr-1">
                <CCard>
                  <table className="table table-hover calculo">
                    <tbody>
                      {tableDataL.map((row) => (
                        <tr key={row.label}>
                          <td>{row.label}</td>
                          <td>
                            {row.label === "Status"
                              ? renderStatus(row.value)
                              : row.value}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </CCard>
              </CCol>
              <CCol className="pl-1">
                <CCard>
                  <table className="table table-hover calculo">
                    <tbody>
                      {tableDataR.map((row) => (
                        <tr key={row.label}>
                          <td>{row.label}</td>
                          <td>{row.value}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </CCard>
              </CCol>
            </CRow>
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={handleClose}>
          Sair
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default DetalhesNegociacaoModal;
