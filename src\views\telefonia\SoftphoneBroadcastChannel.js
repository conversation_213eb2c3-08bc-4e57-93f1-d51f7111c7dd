export function handleSoftphoneOlos({
  onAlreadyOpen = () => alert("O Softphone já está aberto em outra aba."),
  onOpen = () => { }
} = {}) {
  const channel = new BroadcastChannel("softphone_channel");
  let canOpen = true;

  const timeout = setTimeout(() => {
    if (canOpen) {
      channel.postMessage({ type: "softphone-opened" });
      window.open('/#/telefonia/softphone/olos', '_blank');
      localStorage.setItem('softphoneOpenedBy', 'main');

      const checkClosed = setInterval(() => {
        const opened = localStorage.getItem("softphoneOpenedBy");
        if (opened !== "main") return;

        if (window.closed) {
          clearInterval(checkClosed);
          localStorage.setItem("softphoneAtivo", JSON.stringify(false));
          const cleanupChannel = new BroadcastChannel("softphone_channel");
          cleanupChannel.postMessage({ type: "force-softphone-stop" });
          cleanupChannel.close();
        }
      }, 1000);

      onOpen();
    }
    channel.close();
  }, 300);

  channel.onmessage = (event) => {
    if (event.data?.type === "softphone-already-open") {
      canOpen = false;
      clearTimeout(timeout);
      onAlreadyOpen();
      channel.close();
    }
  };

  channel.postMessage({ type: "check-softphone-open" });
}
