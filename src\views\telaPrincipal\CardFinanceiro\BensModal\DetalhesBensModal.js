import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CCard,
  CModalBody,
  CModalFooter,
  CRow,
  CCol,
  CLabel,
  CCardBody,
} from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";
import LoadingComponent from "src/reusable/Loading";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";

const DetalhesBensModal = ({ isOpen, onClose, IdBem }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(null);

  // const fields = [
  //   {
  //     key: "percIdealAdminstration",
  //     label: "Bem",
  //     _style: { whiteSpace: "nowrap" },
  //   },
  //   {
  //     key: "percIdealCommonFund",
  //     label: "Situação",
  //     _style: { whiteSpace: "nowrap" },
  //   },
  //   { key: "dueDate", label: "Tipo Pgto", _style: { whiteSpace: "nowrap" } },
  //   {
  //     key: "nameCodeFinanMovement",
  //     label: "Tipo Aquisição",
  //     _style: { whiteSpace: "nowrap" },
  //   },
  //   {
  //     key: "idCodeFinanMovement",
  //     label: "Valor do Pagto",
  //     _style: { whiteSpace: "nowrap" },
  //   },
  //   {
  //     key: "installmentNumber",
  //     label: "Valor Antecipação",
  //     _style: { whiteSpace: "nowrap" },
  //   },
  // ];

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      GetData(`/${IdBem}`, "getNewconBensContrato")
        .then((data) => {
          if (data) {
            setData(data);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="xl"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Detalhes do Bem Móvel</h5>
      </CModalHeader>
      
      <CModalBody>
      {isLoading ? (
        <div className="mt-2">
          <LoadingComponent />
        </div>
      ) : data == null ||
        data === undefined ||
        data.length === 0 ? (
        <NaoHaDadosTables />
      ) : (
          <>
            <CCard className="px-1">
              <CCardBody>
                <CRow>
                  <CCol md="8">
                    <CLabel className="mr-2">Consorciado: </CLabel>
                    {data?.consortium ? data?.consortium : "---"}
                  </CCol>
                  <CCol>
                    <CLabel className="mr-2">CPF/CNPJ:</CLabel>{" "}
                    {data?.cnpjCpf ? data?.cnpjCpf : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol md="8">
                    <CLabel className="mr-2">1ª Assembleia:</CLabel>{" "}
                    {data?.firstAssembly ? formatDate(data?.firstAssembly) : "---"}
                  </CCol>
                  <CCol>
                    <CLabel className="mr-2">Encerramento Previsto:</CLabel>{" "}
                    {data?.expectedClosure ? formatDate(data?.expectedClosure) : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol>
                    <CLabel className="mr-2">Bem:</CLabel>{" "}
                    {data?.asset ? data?.asset : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol>
                    <CLabel className="mr-2">Valor do Crédito:</CLabel>{" "}
                    {data?.creditValue ? formatThousands(data?.creditValue) : "---"}
                  </CCol>
                  <CCol>
                    <CLabel className="mr-2">
                      Valor do Crédito Atualizado:
                    </CLabel>{" "}
                    {data?.updatedCreditValue
                      ? formatThousands(data?.updatedCreditValue)
                      : "---"}
                  </CCol>
                  <CCol>
                    <CLabel className="mr-2">Data Contemplação:</CLabel>{" "}
                    {data?.contemplationDate ? formatDate(data?.contemplationDate) : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol>
                    <CLabel className="mr-2">Situação Atual:</CLabel>{" "}
                    {data?.actualSituation ? data?.actualSituation : "---"}
                  </CCol>
                  <CCol>
                    <CLabel className="mr-2">Data Alienação:</CLabel>{" "}
                    {data?.alienationDate ? formatDate(data?.alienationDate) : "---"}
                  </CCol>
                  <CCol>
                    <CLabel className="mr-2">Data Desalienação:</CLabel>{" "}
                    {data?.disalienationDate ? formatDate(data?.disalienationDate) : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol md="8">
                    <CLabel className="mr-2">Retorno Gravame:</CLabel>{" "}
                    {data?.lienReturn ? data?.lienReturn : "---"}
                  </CCol>
                  <CCol>
                    <CLabel className="mr-2">Nº Gravame:</CLabel>{" "}
                    {data?.lienNumber ? data?.lienNumber : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol>
                    <CLabel className="mr-2">Vínculos:</CLabel>{" "}
                    {data?.bonds ? data?.bonds : "Não existe vínculos"}
                  </CCol>
                </CRow>
              </CCardBody>
            </CCard>
            <CRow>
              <CCol>
                <CCard className="px-1">
                  <CCardBody>
                    <CRow>
                      <CCol md="3">
                        <CLabel className="mr-2">Modelo:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.model ? data?.model : "---"}</CCol>
                    </CRow>
                    <CRow>
                      <CCol md="3">
                        <CLabel className="mr-2">Fabricante:</CLabel>{" "}
                      </CCol>
                      <CCol>
                        {data?.manufacturer ? data?.manufacturer : "---"}
                      </CCol>
                    </CRow>
                    <CRow>
                      <CCol md="3">
                        <CLabel className="mr-2">Cor:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.color ? data?.color : "---"}</CCol>
                    </CRow>
                    <CRow>
                      <CCol md="3">
                        <CLabel className="mr-2">Chassi:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.chassis ? data?.chassis : "---"}</CCol>
                    </CRow>
                    <CRow>
                      <CCol md="3">
                        <CLabel className="mr-2">Renavam:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.reindeer ? data?.reindeer : "---"}</CCol>
                    </CRow>
                    <CRow>
                      <CCol md="3">
                        <CLabel className="mr-2">Combustível:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.fuel ? data?.fuel : "---"}</CCol>
                    </CRow>
                    <CRow>
                      <CCol>
                        <CLabel className="mr-2">Ano/Modelo:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.yearModel ? data?.yearModel : "---"}</CCol>
                      <CCol>
                        <CLabel className="mr-2">Ano/Fabricação:</CLabel>{" "}
                      </CCol>
                      <CCol>
                        {data?.yearFabrication ? data?.yearFabrication : "---"}
                      </CCol>
                    </CRow>
                    <CRow>
                      <CCol>
                        <CLabel className="mr-2">Placa:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.plate ? data?.plate : "---"}</CCol>
                      <CCol>
                        <CLabel className="mr-2">UF Placa:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.ufPlate ? data?.ufPlate : "---"}</CCol>
                    </CRow>
                    <CRow>
                      <CCol>
                        <CLabel className="mr-2">Nº Motor:</CLabel>{" "}
                      </CCol>
                      <CCol>
                        {data?.motorNumber ? data?.motorNumber : "---"}
                      </CCol>
                      <CCol>
                        <CLabel className="mr-2">Situação:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.situation ? data?.situation : "---"}</CCol>
                    </CRow>
                    <CRow>
                      <CCol>
                        <CLabel className="mr-2">Valor:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.value ? formatThousands(data?.value) : "---"}</CCol>
                      <CCol>
                        <CLabel className="mr-2">Avaliação:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.avaliation ? formatThousands(data?.avaliation) : "---"}</CCol>
                    </CRow>
                    <CRow>
                      <CCol>
                        <CLabel className="mr-2">Valor Mercado:</CLabel>{" "}
                      </CCol>
                      <CCol>
                        {data?.marketValue ? formatThousands(data?.marketValue) : "---"}
                      </CCol>
                      <CCol>
                        <CLabel className="mr-2">Valor Pgto:</CLabel>{" "}
                      </CCol>
                      <CCol>
                        {data?.valuePayment ? formatThousands(data?.valuePayment) : "---"}
                      </CCol>
                    </CRow>
                  </CCardBody>
                </CCard>
              </CCol>
              <CCol>
                <CCard className="px-1">
                  <CCardBody>
                    <CRow>
                      <CCol>
                        <CLabel className="mr-2">Nº CRLV:</CLabel>{" "}
                        {data?.crlvNumber ? data?.crlvNumber : "--- "}
                      </CCol>
                      <CCol>
                        <CLabel className="mr-2">Licenciamento:</CLabel>{" "}
                        {data?.licensing ? data?.licensing : "--- | "}{" "}
                        {data?.licensingUf ? data?.licensingUf : "---"}
                      </CCol>
                    </CRow>{" "}
                    <hr />
                    <CLabel className="mb-0">
                      (RCV) Recibo Compra e Venda: <br />
                    </CLabel>{" "}
                    <CRow className="mt-0">
                      <CCol>
                        <CLabel className="mr-2">Data:</CLabel>{" "}
                        {data?.dateRcv ? formatDate(data?.dateRcv) : "---"}
                      </CCol>
                      <CCol>
                        <CLabel className="mr-2">Valor:</CLabel>{" "}
                        {data?.valueRcv ? formatThousands(data?.valueRcv) : "---"}
                      </CCol>
                    </CRow>
                    <hr />
                    <CLabel className="mb-0">
                      Dados do Contrato de Alienação: <br />
                    </CLabel>{" "}
                    <CRow>
                      <CCol>
                        <CLabel className="mr-2">Cid. Ass:</CLabel>{" "}
                        {data?.cidAss ? data?.cidAss : "--- | "}{" "}
                        {data?.cidAssUf ? data?.cidAssUf : "---"}
                      </CCol>
                    </CRow>{" "}
                    <CRow>
                      <CCol>
                        <CLabel className="mr-2">VL PC:</CLabel>{" "}
                        {data?.vlPc ? formatThousands(data?.vlPc) : "---"}
                      </CCol>
                      <CCol>
                        <CLabel className="mr-2">PC Pagar:</CLabel>{" "}
                        {data?.pcToPay ? data?.pcToPay : "---"}
                      </CCol>
                    </CRow>
                    <CRow>
                      <CCol>
                        <CLabel className="mr-2">Dt Ass:</CLabel>{" "}
                        {data?.dtAss ? formatDate(data?.dtAss) : "---"}
                      </CCol>
                      <CCol>
                        <CLabel className="mr-2">Saldo Devedor:</CLabel>{" "}
                        {data?.balanceDue ? formatThousands(data?.balanceDue) : "---"}
                      </CCol>
                    </CRow>
                    <CRow>
                      <CCol>
                        <CLabel className="mr-2">Observação:</CLabel>{" "}
                      </CCol>
                    </CRow>
                    <CRow>
                      <CCol>
                        {data?.observation ? data?.observation : "---"}
                      </CCol>
                    </CRow>
                  </CCardBody>
                </CCard>
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CCard className="px-1">
                  <CCardBody>
                    <CLabel className="mr-2">
                      Dados da Consulta do Documento no Megadata
                    </CLabel>
                    <CRow>
                      <CCol>
                        <CLabel className="mr-2">Data da Emissão:</CLabel>
                      </CCol>
                      <CCol>{data?.dateEmited ? formatDate(data?.dateEmited) : "---"}</CCol>
                      <CCol>
                        <CLabel className="mr-2">Agente:</CLabel>{" "}
                      </CCol>
                      <CCol>{data?.agent ? data?.agent : "---"}</CCol>
                    </CRow>
                    <CRow>
                      <CCol md="3">
                        <CLabel className="mr-2">
                          Data da Última Consulta:
                        </CLabel>
                      </CCol>
                      <CCol>
                        {data?.dateLastConsult ? formatDate(data?.dateLastConsult) : "---"}
                      </CCol>
                    </CRow>
                  </CCardBody>
                </CCard>
              </CCol>
            </CRow>
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default DetalhesBensModal;
