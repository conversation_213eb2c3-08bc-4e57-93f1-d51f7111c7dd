import React, { useState, useEffect, useRef } from "react";
import {
  CButton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalTitle,
  CCol,
  CRow,
  CLabel,
} from "@coreui/react";
import CardLoading from "src/reusable/CardLoading";
import TableSelectItens from "src/reusable/TableSelectItens";
import Select from "react-select";
import { POST_DATA, GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import SelecionarMensagensModal from "./SelecionarMensagensModal";
import LoadingComponent from "src/reusable/Loading";
import SimulacaoModal from "../negociacao/SimulacaoModal";
import { acionarTabulacao, sairTabulacao } from "src/config/telephonyFunctions";
import FormFunilSafra from "./partials/FormFunilSafra";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useAuth } from "src/auth/AuthContext";
import {
  formatarTelefone,
  getApi,
  getDadosFinanciado,
  postApi,
} from "src/reusable/functions";
import InputMask from "react-input-mask";
import {
  extractDDD,
  formatCurrency,
  formatDateGlobaltoSimplified,
  formatDocument,
  removeDDDfromPhone,
} from "src/reusable/helpers";
import {
  ordenacaoTelefones,
  telefonePost,
  ordenacaoStatusBadge,
  renderTelefone,
  renderStatusBadge,
  renderActionStatus,
  renderCheckbox,
} from "src/views/telaPrincipal/CardTelefoneFunctions";
import SpeechRecognitionText from "src/hooks/useSpeechRecognitionHook.ts";
import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";
import AlertaModal from "../negociacao/AlertaModal";

const PostData = async (payload, endpoint = "funilSafraCreate", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI(endpoint),
        payload,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const getConfigGrupoCyfer = (payload, endpoint = "getConfigUnicaByKey") => {
  return new Promise((resolve, reject) => {
    try {
      const response = GET_DATA(
        getURI(endpoint),
        null,
        true,
        true,
        `/${payload}`
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const CreateOcorrenciaModal = ({
  isOpen,
  onClose,
  onSave,
  isRpa = false,
  phoneOlos = null,
  typeCallOlos = null,
}) => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Histórico e registros",
    submodulo: "Adicionar ocorrências",
  };

  const { message } = useWebsocketTelefoniaContext();

  const permissaoMktZap = {
    modulo: "Histórico e registros",
    submodulo: "MKTZAP",
  };

  const permissaoNegociacao = {
    modulo: "Negociação",
    submodulo: "Simular",
  };

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingModal, setIsLoadingModal] = useState(false);
  const [isLoadingMktzap, setIsLoadingMktzap] = useState(false);

  const [isLoadingTel, setIsLoadingTel] = useState(false);
  const [filteredData, setFilteredeData] = useState(null);
  const [typeCall, setTypeCall] = useState(typeCallOlos);

  const [occRules, setOccRules] = useState([]);

  const handleChangeStatus = async (item, status_Tel) => {
    const trimmedFone = item.fone.trim();
    const data = {
      cpfCnpj: financiadoData.cpfCnpj,
      idGrupo: financiadoData.id_Grupo,
      nome: financiadoData.nome,
      telefones: [
        {
          ddd: item.ddd,
          fone: trimmedFone,
          tipoTelefone: item.id_Tipo_Telefone,
          ramal: item.ramal,
          descricao: item.descricao,
          contato: item.contato,
          status: status_Tel,
          isHotNumber: item.isHotNumber,
          isWhatsApp: item.isWhatsApp,
        },
      ],
    };

    const postSuccess = await telefonePost(data);
    if (postSuccess.success) {
      const updateTelefone = await getDadosFinanciado(
        financiadoData.id_Financiado, financiadoData.numero_Contrato
      );
      localStorage.setItem("clientData", JSON.stringify(updateTelefone));
      const sortPhones = ordenacaoTelefones(updateTelefone.telefones);
      setFilteredeData(sortPhones);
      if (postSuccess.data !== null && postSuccess.data.success === true) {
        toast.info("Status do telefone alterado com sucesso");
      } else {
        postSuccess?.data?.resultados.forEach((element) => {
          toast.error(element);
        });
      }
    } else {
      console.log("Erro no POST");
    }
  };
  const tableColumns = [
    {
      key: "ordenacao",
      label: " ",
      defaultSortColumn: true,
      defaultSort: "ascending",
      className: "text-white",
      style: { display: "none" },
      cellStyleCondicional: (_) => ({ display: "none" }),
    },
    {
      key: "checkbox",
      label: "#",
      formatterByObject: (item) =>
        renderCheckbox(item, handleTelefonesChange, selectedTelefones),
    },
    {
      key: "id_Telefone",
      label: "Telefone",
      className: "nowrap-cell",
      formatterByObject: (item) => renderTelefone(item),
    },
    {
      key: "status",
      label: "Status",
      formatterByObject: (item) => renderStatusBadge(item),
    },
    {
      key: "alt_status",
      label: "Alterar Status",
      formatterByObject: (item) => renderActionStatus(item, handleChangeStatus),
    },
  ];

  const [fixarTelefoneLigacao, setFixarTelefoneLigacao] = useState(false);
  const [showModalSimulacao, setShowModalSimulacao] = useState(false);
  const [showModalMensagens, setShowModalMensagens] = useState(false);
  const [mensagensDados, setMensagensDados] = useState({});

  const [selectedOcorrencia, setSelectedOcorrencia] = useState("");
  const [selectedTelefones, setSelectedTelefones] = useState([]);
  const [selectedTelefonesOcorrencia, setSelectedTelefonesOcorrencia] =
    useState("");
  const [optionsTelefones, setOptionsTelefones] = useState([]);
  const [optionsOcorrencia, setOptionsOcorrencia] = useState([]);
  const [observation, setObservation] = useState("");
  const [complement, setComplement] = useState("");
  const [historicoPayload, setHistoricoPayload] = useState({});
  const [telefonesSelecionados, setTelefonesSelecionados] = useState([]);
  const [
    showModalAlertaErroContratoInacessivel,
    setShowModalAlertaErroContratoInacessivel,
  ] = useState(false);
  const [erros, setErros] = useState({
    id_Ocorrencia_Sistema: "",
    observacao: "",
    telefones: "",
  });
  const clearErros = () => {
    setErros({
      id_Ocorrencia_Sistema: "",
      observacao: "",
      telefones: "",
    });
  };

  const {
    hasRegognitionSupport,
    isListening,
    startListening,
    stopListening,
    speech,
  } = SpeechRecognitionText();

  useEffect(() => {
    if (speech !== "" && speech !== null && speech !== undefined) {
      setObservation(observation + " " + speech);
    }
  }, [speech]);

  /* Verificacoes formulario Safra */
  const [isFieldsLoaded, setIsFieldsLoaded] = useState(false);
  const formFunilSafraRef = useRef();

  const [ocorrenciaSemFone, setOcorrenciaSemFone] = useState([]);

  //States RPA
  const [selectedRetornoTelefone, setSelectedRetornoTelefone] = useState({
    label: "Selecione",
    value: "",
  });
  const [optionsTelefoneRetorno, setOptionsTelefoneRetorno] = useState([
    {
      label: "Selecione",
      value: "",
    },
  ]);
  const [selectedNewTelRet, setSelectedNewTelRet] = useState("");

  const [maxLength] = useState(1000);

  const getOccurrenceWithNoFone = (param = "ocorrencia_sem_fone") => {
    // setLoading(true);
    getConfigGrupoCyfer(param)
      .then((data) => {
        const dataConfig = JSON.parse(data);
        setOcorrenciaSemFone(dataConfig);
      })
      .finally(() => {
        // setLoading(false);
      });
  };

  const handleOcorrenciaChange = (event) => {
    setSelectedOcorrencia(event);
    if (event.cod_Ocorr_Sistema === "043") {
      const telJson = JSON.parse(localStorage.getItem("clientData"))?.telefones;
      const fone = telJson?.find(
        (x) => x.id_Tipo_Telefone === 1 || x.id_Tipo_Telefone === 2
      );
      let newText = observation ?? "";
      newText += "\n\n";
      newText += "CARTEIRA: " + financiadoData?.cliente + "\n";
      newText += "CPF/CNPJ: " + formatDocument(financiadoData?.cpfCnpj) + "\n";
      newText += "NOME DO CLIENTE: " + financiadoData?.nome + "\n";
      newText +=
        "TELEFONE / WHATSAPP CLIENTE: " +
        ((fone?.ddd?.trim() ?? "") + " " + (fone?.fone?.trim() ?? "")) +
        "\n";
      newText += "MELHOR HORÁRIO P/ CONTATO: \n";
      newText += "FAIXA DE ATRASO: \n";
      newText += "NÚMERO CONTRATO: " + financiadoData?.numero_Contrato + "\n";
      newText += "PLACA: \n";
      newText += "NOME DO OPERADOR: " + user?.name + "\n";
      setObservation(newText);
    } else {
      const rules = occRules?.find(
        (x) =>
          x.codOcc === event.cod_Ocorr_Sistema &&
          x.crm === financiadoData.coddatacob
      );
      if (rules !== undefined) {
        setRulesOcc(rules);
        mountObservationWithRules(rules);
      } else {
        setRulesOcc(null);
      }
    }
  };

  const mountObservationWithRules = (rules) => {
    let obs = "";
    if (rules?.contract) obs += `Contrato: ${rulesContract}\n`;
    if (rules?.name) obs += `Nome: ${rulesName}\n`;
    if (rules?.document) obs += `CPF/CNPJ: ${rulesDoc}\n`;
    if (rules?.activeEmail) obs += `E-mail: ${rulesEmail}\n`;
    if (rules?.activePhone) obs += `Telefone: ${rulesPhone}\n`;
    if (rules?.balance) obs += `Saldo: ${rulesBalance}\n`;
    if (rules?.originalValue) obs += `Valor Original: ${rulesOriginalValue}\n`;
    if (rules?.updatedValue) obs += `Valor Atualizado: ${rulesUpdatedValue}\n`;
    if (rules?.openInstallments)
      obs += `Parcelas em aberto: ${rulesOpenInstallments}\n`;
    if (rules?.occurrenceRulesCustemFields?.length > 0)
      for (const item of rules.occurrenceRulesCustemFields) {
        obs += `${item.field}: \n`;
      }
    setObservation(obs);
  };

  const handleTelefonesChange = (event) => {
    try {
      const value = event.currentTarget.value.trim();
      let selected = selectedTelefones;
      if (event.currentTarget.checked === true)
        selected = [...selectedTelefones, value];
      else
        selected = selectedTelefones.filter(
          (item) => item.replace(/\D/g, "") !== value.replace(/\D/g, "")
        );

      setSelectedTelefones(selected);
    } catch (err) {
      console.error(err);
    }
  };
  const handleTelefoneRetornoChange = (event) => {
    setSelectedRetornoTelefone(event);
  };
  const handleNewTelRetChange = (event) => {
    setSelectedNewTelRet(
      event.target.value
        .replaceAll("_", "")
        .replaceAll("-", "")
        .replaceAll("_", "")
        .replaceAll("(", "")
        .replaceAll(")", "")
        .trim()
    );
  };

  useEffect(() => {
    if (selectedTelefones && selectedTelefones.length > 0) {
      const telSeleciionado = [];
      selectedTelefones.forEach((element) => {
        const stringWithoutSpaces = element.replace(/\s+/g, "");
        const tel = "55" + stringWithoutSpaces;
        telSeleciionado.push(tel);
      });
      setTelefonesSelecionados(telSeleciionado);
    }
  }, [selectedTelefones]);

  useEffect(() => {
    if (telefonesSelecionados.length > 0)
      setHistoricoPayload(telefonesSelecionados[0]);
    else setHistoricoPayload({});
  }, [telefonesSelecionados]);

  async function historicoAdicionar() {    
    const tel = JSON.parse(JSON.stringify(telefonesSelecionados));
    const telOp = tel.map((item) => {
      item = formatarTelefone(item);
      return item;
    });

    const telJson = JSON.parse(localStorage.getItem("clientData"))?.telefones;
    const payl = {
      cpfCnpj: financiadoData.cpfCnpj,
      idGrupo: financiadoData.id_Grupo,
      nome: financiadoData.nome,
      telefones: [],
    };
    for (const item of telJson.filter((x) => x.status === 0)) {
      if (telOp.indexOf(formatarTelefone(item.ddd + item.fone)) > -1) {
        payl.telefones.push({
          ddd: item.ddd,
          fone: item.fone.trim(),
          tipoTelefone: item.id_Tipo_Telefone,
          ramal: item.ramal,
          descricao: item.descricao,
          contato: item.contato,
          status: 0,
          isHotNumber: item.isHotNumber,
          isWhatsApp: item.isWhatsApp,
        });
      }
    }

    const data = {
      login: user?.username,
      id_Contrato: financiadoData?.id_Contrato,
      id_Ocorrencia_Sistema: selectedOcorrencia.value,
      observacao: observation,
      complemento: complement,
      telefones: telOp,
      callType: typeCall,
      telefoneParaRetorno: formatarTelefone(selectedRetornoTelefone?.value),
    };
    const ocorrencia = await POST_DATA("Datacob/historicoAdicionar", data, false, true);
    if (ocorrencia.success) {
      toast.info("Ocorrência adicionada com sucesso");
      if (selectedOcorrencia?.cod_Ocorr_Sistema === "043") {
        await sendEmail43();
      } else {
        if (rulesOcc?.send) {
          await postApi(
            {
              id: rulesOcc.id,
              subject: selectedOcorrencia.label + " - " + rulesName,
              content: observation,
              emails: rulesOcc?.occurrenceRulesEmails.map((x) => x.email),
            },
            "postSendEmailOccRules"
          );
        }
      }

      if (payl.telefones.length > 0) {
        const postSuccess = await telefonePost(payl);
      }
    }
    return ocorrencia;
  }

  async function getHistorico() {
    const data = {
      Telefone: historicoPayload,
      IdContrato: financiadoData?.id_Contrato,
      IdFinanciado: financiadoData?.id_Financiado,
    };
    const result = await GET_DATA("Mktzap/Messages", data);
    if (result !== null) {
      setMensagensDados(result);
      setShowModalMensagens(true);
    } else {
      toast.info("Histórico de mensagens não encontrado.");
    }
    setIsLoadingMktzap(false);
  }

  const handleObservationChange = (event) => {
    const newText = event.target.value;
    if (newText.length <= maxLength) {
      setObservation(newText);
    }
  };

  const handleComplementChange = (event) => {
    setComplement(event.target.value);
  };

  const handleHistorico = async () => {
    setIsLoadingMktzap(true);
    await getHistorico();
  };

  const handleModalMensagemClose = () => {
    setShowModalMensagens(false);
  };

  const handleSaveMessages = (messages) => {
    const truncatedMessages = messages.slice(0, maxLength);
    setObservation(truncatedMessages);
  };

  const handleSaveSimulacao = (simulacao) => {
    const truncatedMessages = simulacao.slice(0, maxLength);
    setObservation(truncatedMessages);
  };

  const handleSave = async () => {
    clearErros();
    if (!validaFomrulario()) return;
    // Salvar ocorrência
    let validationResult = {};
    if (formFunilSafraRef.current) {
      validationResult = formFunilSafraRef.current.validateFunilForm();
      if (validationResult == null) {
        return;
      }
    }

    setIsLoading(true);
    const historicoAdicionarReturn = await historicoAdicionar();
    ticketBTGCreate();
    if (historicoAdicionarReturn.success) {
      if (validationResult != {})
        await PostData(
          {
            seguro: validationResult.seguro,
            garantia: validationResult.garantia,
            trabalho: validationResult.trabalho,
            finalidade: validationResult.finalidade,
            kilometragem: validationResult.kilometragem,
            redesocial: validationResult.redesocial,
            tabulacao: validationResult.tabulacao,
            contratoCurto: validationResult.contratoCurto,
            contratoLongo: validationResult.contratoLongo,
            idContratoDataCob: validationResult.idContratoDataCob,
            telefone: validationResult.telefone,
          },
          "funilSafraCreate"
        )
          .then((data) => {})
          .catch((err) => {
            return false;
          });

      const currentCallData = message;

      const ramalTactium = localStorage.getItem("ramalTactium")
        ? JSON.parse(localStorage.getItem("ramalTactium"))
        : null;

      if (
        currentCallData &&
        (currentCallData?.status === "Wrap" ||
          currentCallData?.status === "WrapWithEnding" ||
          currentCallData?.status === "WrapWithManualCall" ||
          currentCallData?.status === "WrapWithPrivateCallback" ||
          currentCallData?.status === "WrapWithPause")
      ) {
        const payload = {
          dispositionCode: selectedOcorrencia.cod_Ocorr_Sistema,
          agentId: currentCallData?.agentId,
          callId: currentCallData?.callId,
          description: selectedOcorrencia.label,
          ramal: ramalTactium,
          reasonId:
            localStorage.getItem("pausaId") === "null"
              ? null
              : Number(localStorage.getItem("pausaId")),
        };
        await acionarTabulacao(payload);
        if (currentCallData?.agentId) {
          await sairTabulacao(currentCallData?.agentId);
        }
      }

      const talkingOccurWrap = localStorage.getItem("talkingOccurWrap")
        ? JSON.parse(localStorage.getItem("talkingOccurWrap"))
        : null;

      if (
        currentCallData &&
        (currentCallData?.status === "Talking" ||
          currentCallData?.status === "TalkingWithEnding" ||
          currentCallData?.status === "TalkingWithManualCall" ||
          currentCallData?.status === "TalkingWithPause")
      ) {
        if (talkingOccurWrap === null) {
          const payload = {
            dispositionCode: selectedOcorrencia.cod_Ocorr_Sistema,
            agentId: currentCallData?.agentId,
            callId: currentCallData?.callId,
            description: selectedOcorrencia.label,
            ramal: ramalTactium,
          };
          localStorage.setItem("talkingOccurWrap", JSON.stringify(payload));
        }
      }

      onSave(historicoAdicionarReturn);

      setSelectedOcorrencia("");
      setSelectedTelefones("");
      setSelectedTelefonesOcorrencia("");
      setObservation("");

      setIsLoading(false);
      onClose();
    } else {
      const disparaTabulacao = async () => {
        const currentCallData = message;

        setShowModalAlertaErroContratoInacessivel(true);

        const ramalTactium = localStorage.getItem("ramalTactium")
          ? JSON.parse(localStorage.getItem("ramalTactium"))
          : null;

        if (
          currentCallData &&
          (currentCallData?.status === "Wrap" ||
            currentCallData?.status === "WrapWithEnding" ||
            currentCallData?.status === "WrapWithManualCall" ||
            currentCallData?.status === "WrapWithPrivateCallback" ||
            currentCallData?.status === "WrapWithPause")
        ) {
          const payload = {
            dispositionCode: selectedOcorrencia.cod_Ocorr_Sistema,
            agentId: currentCallData?.agentId,
            callId: currentCallData?.callId,
            description: selectedOcorrencia.label,
            ramal: ramalTactium,
            reasonId:
              localStorage.getItem("pausaId") === "null"
                ? null
                : Number(localStorage.getItem("pausaId")),
          };
          await acionarTabulacao(payload);
          if (currentCallData?.agentId) {
            await sairTabulacao(currentCallData?.agentId);
          }
        }
      };

      setIsLoading(false);
      if (historicoAdicionarReturn?.message.startsWith("Retorno Api")) {
        if (historicoAdicionarReturn.message.indexOf("[") > -1) {
          const errorMessage =
            historicoAdicionarReturn.message.match(/\[([^)]+)\]/)[1];

          if (errorMessage === '"Erro inesperado"') {
            await disparaTabulacao();
            toast.warning("Erro ao realizar login no Datacob.");
          } else toast.warning(errorMessage);
        } else {
          toast.warning(historicoAdicionarReturn.message);
        }
      } else {
        toast.warning(historicoAdicionarReturn?.message);
        // Adiciona tabulação
        if (
          historicoAdicionarReturn?.message.startsWith(
            "Sem permissão para acessar o"
          )
        ) {
          setShowModalAlertaErroContratoInacessivel(true);
          await disparaTabulacao();
        }
      }
      return;
    }
  };

  async function getBtgGrupoConfig() {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "btg_grupos"
    );
    try {
      return JSON.parse(response);
    } catch (err) {
      return {
        rodobens: [],
        gvc: [],
      };
    }
  }

  async function ticketBTGCreate() {
    const config = await getBtgGrupoConfig();
    let verif = false;

    try {
      if (financiadoData?.coddatacob === "GVC") {
        if (config.gvc.indexOf(financiadoData?.id_Grupo) > -1) verif = true;
      }
      if (financiadoData?.coddatacob === "Rodobens") {
        if (config.rodobens.indexOf(financiadoData?.id_Grupo) > -1)
          verif = true;
      }
    } catch (err) {
      console.warn("erroConfigBtg", err);
      verif = false;
    }

    if (verif) {
      const cod = Number(selectedOcorrencia.cod_Ocorr_Sistema);
      const data = {
        idAgrupamento: financiadoData?.id_Agrupamento,
        crm: financiadoData?.coddatacob,
        codOccur: cod,
        contract: financiadoData?.numero_Contrato,
        doc: financiadoData?.cpfCnpj,
        phone: removeDDDfromPhone(
          formatarTelefone(telefonesSelecionados[0])
        ).replaceAll("-", ""),
        ddd: extractDDD(telefonesSelecionados[0].slice(2)),
        typeCall: typeCall ?? "Receptivo",
      };

      const ticket = await POST_DATA("BTG/Ticket", data);
      return ticket;
    }
  }

  const validaFomrulario = () => {
    let erros = {
      id_Ocorrencia_Sistema: "",
      observacao: "",
      telefones: "",
    };
    let isValid = true;

    if (
      !selectedOcorrencia ||
      selectedOcorrencia === undefined ||
      selectedOcorrencia.value === undefined ||
      selectedOcorrencia.value === "" ||
      selectedOcorrencia.value === null
    ) {
      erros.id_Ocorrencia_Sistema = "Ocorrência é obrigatório";
      isValid = false;
    }

    if (
      observation === null ||
      observation === undefined ||
      observation === ""
    ) {
      erros.observacao = "Observação é obrigatório";
      isValid = false;
    }

    if (
      (selectedTelefones === null ||
        selectedTelefones === undefined ||
        selectedTelefones === "") &&
      ocorrenciaSemFone.indexOf(selectedOcorrencia.cod_Ocorr_Sistema) === -1
    ) {
      erros.telefones = "Telefone é obrigatório";
      isValid = false;
    }

    setErros(erros);
    return isValid;
  };

  async function getTiposOcorrencia() {
    setIsLoadingModal(true);
    const tiposOcorrencia = await GET_DATA("Datacob/Ocorrencias");
    if (tiposOcorrencia !== null && tiposOcorrencia !== undefined) {
      const options = [
        ...tiposOcorrencia.map((item) => {
          return {
            label: item.cod_Ocorr_Sistema + " - " + item.descricao,
            value: item.id_Ocorrencia_Sistema,
            cod_Ocorr_Sistema: item.cod_Ocorr_Sistema,
          };
        }),
      ];
      setOptionsOcorrencia(options);
    }
    setIsLoadingModal(false);
  }

  const todayFormat = new Date(
    new Date().setHours(new Date().getHours() - 3)
  ).toISOString();

  const CleanCalculoPost = async (
    idsParcelas,
    vlNeg = 0,
    formaDesconto = 0,
    dtNegociacao = todayFormat,
    apiSuccessFunc = (response) => {},
    apiErrorFunc = (response) => {},
    errorFunc = (error) => {},
    finallyFunc = () => {}
  ) => {
    const payload = {
      idAgrupamento: financiadoData.id_Agrupamento,
      dtNegociacao: formatDateGlobaltoSimplified(dtNegociacao),
      vlNegociado: vlNeg,
      parcelas: idsParcelas,
      formaDesconto: formaDesconto,
    };
    PostData(payload, "postCalcularNegociacao")
      .then((response) => {
        if (
          response.data &&
          response.data.negociacaoDto &&
          response.data.negociacaoDto.length > 0
        ) {
          apiSuccessFunc(response.data);
        }
        if (response.success === false) {
          apiErrorFunc(response);
        }
      })
      .catch((error) => {
        errorFunc(error);
      })
      .finally(() => {
        finallyFunc();
      });
  };

  async function getOccRules() {
    const regras = await GET_DATA(`OccurrenceRules`);
    if (regras !== null && regras !== undefined) {
      setOccRules(regras);
      getValuesToRules();
    } else {
      console.error("Falha ao buscar regras ocorrências");
      setOccRules([]);
    }
    return;
  }

  const [rulesOcc, setRulesOcc] = useState(null);
  const [rulesContract, setRulesContract] = useState("");
  const [rulesName, setRulesName] = useState("");
  const [rulesDoc, setRulesDoc] = useState("");
  const [rulesPhone, setRulesPhone] = useState("");
  const [rulesEmail, setRulesEmail] = useState("");
  const [rulesBalance, setRulesBalance] = useState("");
  const [rulesOriginalValue, setRulesOriginalValue] = useState("");
  const [rulesUpdatedValue, setRulesUpdatedValue] = useState("");
  const [rulesOpenInstallments, setRulesOpenInstallments] = useState("");

  const getValuesToRules = async () => {
    // Dispara para api de calculo e pega dados de negociação
    await CleanCalculoPost([], 0, 0, todayFormat, successCalculoPost);

    // Informações de financiado
    const contract = financiadoData?.numero_Contrato;
    const name = financiadoData?.nome;
    const doc = financiadoData?.cpfCnpj;
    setRulesContract(contract);
    setRulesName(name);
    setRulesDoc(formatDocument(doc));

    // Pega primeiro telefone efetivo ou ativo
    const clientDataAtualizada = getUpdatedClientData();
    const telJson = clientDataAtualizada?.telefones;
    const telEfetivo = telJson?.find((x) => x.status === 2);
    if (telEfetivo !== undefined) {
      setRulesPhone(formatarTelefone(telEfetivo.ddd + telEfetivo.fone));
    } else {
      const telAtivo = telJson?.find((x) => x.status === 1);
      if (telAtivo !== undefined) {
        setRulesPhone(formatarTelefone(telAtivo.ddd + telAtivo.fone));
      }
    }

    // Pega primeiro email efetivo ou ativo
    const emJson = clientDataAtualizada?.emails;
    const emailEfetivo = emJson?.find((x) => x.status_Email === 2);
    if (emailEfetivo !== undefined) {
      setRulesEmail(emailEfetivo.endereco_Email);
    } else {
      const emailAtivo = emJson?.find((x) => x.status_Email === 1);
      if (emailAtivo !== undefined) {
        setRulesEmail(emailAtivo.endereco_Email);
      }
    }
  };

  const successCalculoPost = (data) => {
    const neg = data?.negociacaoDto[0] ?? null;
    const parcelas = neg?.parcelas;

    const parcelasStr = parcelas?.map((x) => x.nrParcela)?.join(", ");
    const original = parcelas?.reduce((x, y) => x + y.vlOriginal, 0);
    const saldo = parcelas
      ?.filter((x) => x.parcelaSelecionada)
      ?.reduce((x, y) => x + y.vlAtualizado, 0);

    // Dados da negociação
    setRulesBalance(formatCurrency(saldo));
    setRulesOriginalValue(formatCurrency(original));
    setRulesUpdatedValue(formatCurrency(neg?.vlNegociacao ?? 0));
    setRulesOpenInstallments(parcelasStr);
  };

  const getUpdatedClientData = () => {
    const clientDataAtualizada = localStorage.getItem("clientData")
      ? JSON.parse(localStorage.getItem("clientData"))
      : null;
    return clientDataAtualizada;
  };

  const statusTelefoneName = (item) => {
    return item.status === 1
      ? " Ativo "
      : item.status === 2
      ? " Efetivo "
      : item.status === 3
      ? " Pesquisado "
      : item.status === -1
      ? " Blacklist "
      : " Inativo ";
  };

  useEffect(() => {
    const telJson = JSON.parse(localStorage.getItem("clientData"))?.telefones;
    if (telJson !== null && telJson !== undefined) {
      const tel = telJson;
      const telOp = tel
        .map((item) => {
          let label = statusTelefoneName(item) + " - " + item.ddd + item.fone;
          if (item.descricao !== "" && item.descricao != null)
            label += " - " + item.descricao;

          const ordn = ordenacaoStatusBadge(item);

          return {
            label: label,
            value: item.ddd + item.fone,
            ordenacao: ordn,
          };
        })
        .sort((a, b) => {
          if (a.ordenacao < b.ordenacao) return -1;
          if (a.ordenacao > b.ordenacao) return 1;
          return 0;
        });
      const telRetOp = [...optionsTelefoneRetorno, ...telOp];
      setFilteredeData(ordenacaoTelefones(tel));

      getOccRules();

      getOccurrenceWithNoFone();

      setOptionsTelefoneRetorno(telRetOp);
      setOptionsTelefones(telOp);
      getTiposOcorrencia();
    }

    return () => {
      setRulesBalance("");
      setRulesName("");
      setRulesOcc(null);
      setRulesContract("");
      setRulesDoc("");
      setRulesEmail("");
      setRulesOpenInstallments("");
      setRulesOriginalValue("");
      setRulesPhone("");
      setRulesUpdatedValue("");
    };
  }, [isOpen]);

  useEffect(() => {
    if (optionsTelefones && optionsTelefones.length > 0) {
      // const callDataAtualizada = getUpdatedCallData();
      const callDataAtualizada = message;
      let callType = typeCallOlos;
      if (
        // callData?.callId &&
        callDataAtualizada?.status === "Talking" ||
        callDataAtualizada?.status === "TalkingWithPause" ||
        callDataAtualizada?.status === "TalkingWithEnding" ||
        callDataAtualizada?.status === "TalkingWithManualCall" ||
        callDataAtualizada?.status === "TalkingWithPause" ||
        callDataAtualizada?.status === "ManualCall" ||
        callDataAtualizada?.status === "Wrap" ||
        callDataAtualizada?.status === "WrapWithEnding" ||
        callDataAtualizada?.status === "WrapWithManualCall" ||
        callDataAtualizada?.status === "WrapWithPrivateCallback" ||
        callDataAtualizada?.status === "WrapWithPause"
      ) {
        callType = callDataAtualizada?.callType;
        const options = JSON.parse(JSON.stringify(optionsTelefones));
        const existingPhone = options.filter((fone) => {
          if (
            fone?.value !== null &&
            fone?.value !== undefined &&
            callDataAtualizada?.phone !== null &&
            callDataAtualizada?.phone !== undefined
          ) {
            return (
              fone?.value.replaceAll(" ", "") ===
              callDataAtualizada?.phone.replaceAll(" ", "")
            );
          } else if (phoneOlos !== null && phoneOlos !== undefined) {
            return (
              fone?.value.replaceAll(" ", "") === phoneOlos?.replaceAll(" ", "")
            );
          }
          return false;
        });
        if (existingPhone && existingPhone.length > 0) {
          setSelectedTelefones([existingPhone[0]?.value]);
          if (
            callDataAtualizada?.manualMode ||
            callDataAtualizada?.status === "ManualCall"
          )
            setFixarTelefoneLigacao(true);
        }
      } else if (phoneOlos !== null && phoneOlos !== undefined) {
        const options = JSON.parse(JSON.stringify(optionsTelefones));
        const existingPhone = options.filter((fone) => {
          if (phoneOlos !== null && phoneOlos !== undefined) {
            return (
              fone.value.replaceAll(" ", "") === phoneOlos.replaceAll(" ", "")
            );
          }
          return false;
        });
        if (existingPhone && existingPhone.length > 0) {
          setSelectedTelefones([existingPhone[0]?.value]);
        }
      }
      setTypeCall(callType);
    }
  }, [optionsTelefones]);

  const parcelasAbertas =
    contratosAtivos === null ||
    contratosAtivos === undefined ||
    contratosAtivos === ""
      ? []
      : contratosAtivos.flatMap((item) =>
          item.parcelas?.filter(
            (pItem) =>
              pItem.status === "A" && !pItem.nr_Acordo && pItem.atraso > 0
          )
        );

  const handleClose = () => {
    setFixarTelefoneLigacao(false);
    onClose();
  };

  const sendEmail43 = async () => {
    const res = await postApi(
      {
        content: observation,
      },
      "emailOccurrence43"
    );
    if (!res?.success) {
      toast.error("Ocorreu um problema ao enviar o email");
    }
  };

  return (
    <CModal
      className="custom-modal"
      size="xl"
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
    >
      <CModalHeader closeButton>
        <CModalTitle>Adicionar ocorrência</CModalTitle>
      </CModalHeader>
      <CModalBody>
        {isFieldsLoaded && (
          <>
            <CRow>
              {isLoadingModal ? (
                <CCol style={{ textAlign: "center" }}>
                  <LoadingComponent />
                </CCol>
              ) : (
                <CCol>
                  <CLabel style={{ marginTop: "10px" }}>
                    Tipo de ocorrência
                  </CLabel>
                  <Select
                    value={selectedOcorrencia}
                    options={optionsOcorrencia}
                    onChange={handleOcorrenciaChange}
                    placeholder={"Selecione"}
                    isDisabled={isLoading}
                  />
                  {erros.id_Ocorrencia_Sistema && (
                    <div className="text-danger">
                      {erros.id_Ocorrencia_Sistema}
                    </div>
                  )}
                </CCol>
              )}
            </CRow>
            <CRow>
              <br />
              <CCol>
                <CLabel style={{ marginTop: "10px" }}>
                  Telefones relacionados
                </CLabel>
                {/* <Select
                  isMulti
                  value={selectedTelefones}
                  onChange={handleTelefonesChange}
                  options={optionsTelefones}
                  placeholder={"Selecione"}
                  isDisabled={isLoading || fixarTelefoneLigacao}
                ></Select> */}
                {isLoadingTel ? (
                  <CardLoading msg={"Atualizando..."} />
                ) : (
                  <TableSelectItens
                    data={filteredData}
                    columns={tableColumns}
                    onSelectionChange={(_) => {}}
                    defaultSelectedKeys={[]}
                    selectable={false}
                    heightParam="160px"
                    header={false}
                  />
                )}
                {erros.telefones && (
                  <div className="text-danger">{erros.telefones}</div>
                )}
              </CCol>
            </CRow>
            {/* {useRpa && ( */}
            <CRow>
              <CCol>
                <CLabel>Telefones retorno</CLabel>
                <Select
                  value={selectedRetornoTelefone}
                  onChange={handleTelefoneRetornoChange}
                  options={optionsTelefoneRetorno}
                  placeholder={"Selecione"}
                  isDisabled={isLoading || selectedNewTelRet.trim() !== ""}
                ></Select>
              </CCol>
              <CCol>
                <CLabel>Novo telefone retorno</CLabel> <br />
                <InputMask
                  mask="(99) 99999-9999"
                  maskChar="_"
                  onChange={handleNewTelRetChange}
                  className={"form-control"}
                  disabled={selectedRetornoTelefone.value !== ""}
                />
              </CCol>
            </CRow>
            {/* )} */}
          </>
        )}
        {isFieldsLoaded && (
          <CRow className="mt-2">
            <CCol>
              <CLabel>Observações</CLabel>
              <div style={{ display: "flex", gap: "5px" }}>
                <textarea
                  style={{
                    width: "100%",
                    minHeight: "150px",
                    borderRadius: "5px",
                  }}
                  maxLength={maxLength}
                  placeholder=" Insira aqui suas observações."
                  value={observation}
                  onChange={handleObservationChange}
                  // rows={5}
                />
                {hasRegognitionSupport && (
                  <div>
                    {isListening && (
                      <button
                        className="btn btn-warning"
                        onClick={stopListening}
                        style={{ height: "100%" }}
                      >
                        <i
                          className="cil-microphone"
                          style={{ fontSize: "20px", padding: "10px" }}
                        ></i>
                      </button>
                    )}
                    {!isListening && (
                      <button
                        className="btn btn-info"
                        onClick={startListening}
                        style={{ height: "100%" }}
                      >
                        <i
                          className="cil-microphone"
                          style={{ fontSize: "20px", padding: "10px" }}
                        ></i>
                      </button>
                    )}
                  </div>
                )}
              </div>{" "}
              {erros.observacao && (
                <div className="text-danger">{erros.observacao}</div>
              )}
              <div style={{ fontSize: "small", textAlign: "end" }}>
                {observation.length === maxLength ? (
                  <p style={{ color: "red", margin: "0px" }}>
                    Limite de caracteres atingido
                  </p>
                ) : (
                  <p style={{ margin: "0px" }}>
                    {maxLength - observation.length} caracteres restantes
                  </p>
                )}
              </div>
            </CCol>
          </CRow>
        )}
        {isFieldsLoaded && (
          <CRow>
            <CCol>
              <CLabel>Complemento</CLabel> <br />
              <textarea
                style={{
                  width: "100%",
                  minHeight: "60px",
                  borderRadius: "5px",
                }}
                placeholder=" Insira aqui o complemento."
                value={complement}
                onChange={handleComplementChange}
              />
            </CCol>
          </CRow>
        )}
        <FormFunilSafra
          onParametersVerified={() => setIsFieldsLoaded(true)}
          ref={formFunilSafraRef}
          tabulacao={selectedOcorrencia}
          telefones={selectedTelefones}
          paramOccur={ocorrenciaSemFone}
        />
        {isFieldsLoaded && (
          <CRow className="d-flex justify-content-end px-3">
            <CButton color="secondary" className="mr-2" onClick={onClose}>
              Cancelar
            </CButton>
            <CButton
              color="success"
              className="mr-2"
              onClick={() => setShowModalSimulacao(true)}
              disabled={
                parcelasAbertas.length === 0 ||
                !checkPermission(
                  permissaoNegociacao.modulo,
                  "View",
                  permissaoNegociacao.submodulo
                )
              }
              title={
                parcelasAbertas.length === 0
                  ? "Não há parcelas abertas e em atraso para este agrupamento."
                  : inforPermissions(permissaoNegociacao).create
              }
            >
              Simular
            </CButton>
            <CButton
              title={
                telefonesSelecionados.length === 0
                  ? "Por favor, selecione um telefone para buscar o histórico de mensagens."
                  : inforPermissions(permissaoMktZap).create
              }
              color="success"
              className="mr-2"
              onClick={handleHistorico}
              disabled={
                isLoadingMktzap === true ||
                telefonesSelecionados.length === 0 ||
                !checkPermission(
                  permissaoMktZap.modulo,
                  "View",
                  permissao.submodulo
                )
              }
            >
              {isLoadingMktzap === true ? (
                <LoadingComponent size="sm" />
              ) : (
                "Buscar histórico "
              )}
            </CButton>
            <CButton
              color="info"
              onClick={handleSave}
              title={inforPermissions(permissao).create}
              disabled={
                !checkPermission(
                  permissao.modulo,
                  "Create",
                  permissao.submodulo
                )
              }
            >
              {isLoading ? <LoadingComponent /> : "Adicionar ocorrência"}
            </CButton>
          </CRow>
        )}
      </CModalBody>
      {showModalMensagens && (
        <SelecionarMensagensModal
          isOpen={showModalMensagens}
          onClose={handleModalMensagemClose}
          onSave={handleSaveMessages}
          dados={mensagensDados}
        />
      )}
      {showModalSimulacao && (
        <SimulacaoModal
          isOpen={showModalSimulacao}
          onClose={() => setShowModalSimulacao(false)}
          onSave={handleSaveSimulacao}
          isOcorrencia={true}
        />
      )}
      <AlertaModal
        isOpen={showModalAlertaErroContratoInacessivel}
        onClose={() => setShowModalAlertaErroContratoInacessivel(false)}
        dados="
            Atenção Operador! Não será registrada ocorrência no sistema CRM.
            Apenas o registro de encerramento será feito em telefonia! O
            contrato em questão não permite registro de ocorrência!
          "
      />
    </CModal>
  );
};

export default CreateOcorrenciaModal;
