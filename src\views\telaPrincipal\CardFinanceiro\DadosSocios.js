import React, { useState, useEffect } from "react";
import { CRow, CCol } from "@coreui/react";
import { GET_DATA } from "src/api";
import TableSelectItens from "src/reusable/TableSelectItens";
import CardLoading from "src/reusable/CardLoading";
import { getURI } from "src/config/apiConfig";
import { formatDocument, formatThousands } from "src/reusable/helpers";

const DadosSocio = ({ selected }) => {
  const [tableData, setTableData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const cnscCotas =
    localStorage.getItem("cnscCotas") &&
      localStorage.getItem("cnscCotas") !== "undefined"
      ? JSON.parse(localStorage.getItem("cnscCotas")) ?? []
      : [];

  const fields = [
    { key: "cD_Pessoa", label: "Có<PERSON>" },
    { key: "nM_Pessoa", label: "Nome" },
    {
      key: "cD_Inscricao_Nacional",
      label: "CPF/CNPJ",
      formatter: (item) => formatDocument(item),
    },
    {
      key: "pE_Participacao",
      label: "% Participação",
      formatter: (item) => formatThousands(item),
    },
  ];

  const updateView = async () => {
    const IdCota = cnscCotas.idCota;
    setIsLoading(true);
    GetData(`/${IdCota}`, "getNewconSocios")
      .then((data) => {
        if (data && data.length > 0) {
          setTableData(data);
        } else {
          setTableData(null);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    if (selected === true) {
      updateView();
    }
  }, [selected]);

  return (
    <>
      {isLoading ? (
        <div className="mt-2">
          <CardLoading />
        </div>
      ) : (
        <>
          {tableData ? (
            <TableSelectItens
              data={tableData}
              columns={fields}
              onSelectionChange={(_) => {}}
              defaultSelectedKeys={[]}
              selectable={false}
              heightParam="290px"
            />
          ) : (
            <CRow style={{ textAlign: "center", margin: "12px 0" }}>
              <CCol>
                <div>Não foram encontrados sócios para este financiado.</div>
              </CCol>
            </CRow>
          )}
        </>
      )}
    </>
  );
};

export default DadosSocio;
