type User = {
  username: string;
  telephonyId: number;
  telephony: string;
  role: object;
  name: string;
  isAdmin: boolean;
  id: number;
  groups: Array<object>;
  email: string;
  datacobs: Array<object>;
  crmCred: Array<CrmCred>;
  agentFone: object;
  ad: boolean;
  activeConnection: string;
  active: boolean;
};

type CrmCred = {
  id: string | null;
  login: string;
  crm: string;
};

export { User, CrmCred };
