export function checkPermission(name, perm) {
  const userRole = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user")).role
    : null;
  const foundModule = userRole.modules.find((module) => module.module === name);
  return foundModule[perm];
}

export function checkPermissionV2(name, perm, userPermissions) {
  if (!userPermissions) return false;
  const foundModule = userPermissions.modules.find(module => module.module === name);
  return foundModule ? foundModule[perm] : false;
}
