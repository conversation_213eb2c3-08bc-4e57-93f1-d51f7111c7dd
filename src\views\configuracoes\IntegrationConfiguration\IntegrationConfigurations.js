import React, { useEffect, useState, useCallback } from "react";
import { CButton, CBadge } from "@coreui/react";
import "react-toastify/dist/ReactToastify.css";
import { CCard, CCardBody, CCol, CRow } from "@coreui/react";
import TableSelectItens from "src/reusable/TableSelectItens";
import IntegrationConfigurationModal from "./IntegrationConfigurationModal";
import { getURI } from "src/config/apiConfig";
import { useAuth } from "src/auth/AuthContext";
import { GET_DATA } from "src/api";
import { formatDate } from "src/reusable/helpers";

const IntegrationConfigurations = () => {
  const { inforPermissions } = useAuth();
  const permissao = {
    modulo: "Configurações",
    submodulo: "Integrações",
  };

  const token = localStorage.getItem("token");

  const [selectedIntegrationConfiguration, setSelectedIntegrationConfiguration] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [integrationconfigurationsList, setIntegrationConfigurationsList] = useState([]);

  const fields = [
    {
      key: "crmName",
      label: "Crm",
      defaultSort: "desc",
      defaultSortColumn: true
    },
    {
      key: "groupName",
      label: "Grupo",
      defaultSortColumn: true
    },
    {
      key: "started",
      label: "Data Início",
      formatter: (value) => formatDate(value),
    },
    {
      key: "occurrence",
      label: "Ocorrência",
      formatterByObject: (item) => handleViewActive(item.occurrence),
    },
    {
      key: "agreement",
      label: "Acordo",
      formatterByObject: (item) => handleViewActive(item.agreement),
    },
    {
      key: "bankSlip",
      label: "Boleto",
      formatterByObject: (item) => handleViewActive(item.bankSlip),
    },
    {
      key: "active",
      label: "Status",
      formatterByObject: (item) => handleViewActive(item.active),
    },
    {
      key: "actions",
      label: "Editar",
      formatterByObject: (item) => handleViewActions(item),
    },
  ];

  const getGrupoCrm = useCallback((crm) => {
    return GetData({ ActiveConnection: crm }, 'getGrupoDataCobLista').then((res) => {
      if (res && res !== null && res !== undefined) {
        return res;
      }
    }).catch((err) => {
      console.warn(err);
      return null;
    });
  }, []);

  const getCrms = useCallback(() => {
    return GetData({}, "getCrms").then((res) => {
      if (res) return res;
    }).catch((err) => {
      console.warn(err);
      return null;
    });
  }, []);

  const fetchGroupsForCrm = useCallback(async (crm) => {
    const groupData = await getGrupoCrm(crm.datacobName);
    crm.grupos = groupData;
    return crm;
  }, [getGrupoCrm]);

  const getIntegrationConfigurations = useCallback(async (search, crmsOptionsLocal) => {
    try {
      const response = await fetch(`${getURI()}/IntegrationConfiguration`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const updatedData = data.data?.map((item) => {
          const crm = crmsOptionsLocal?.find((a) => a.id === item.crmId);
          const crmName = crm?.datacobName || "N/A";
          const groupName = crm?.grupos.find((a) => a.id_Grupo === item.groupId)?.descricao || "N/A";
          return {
            ...item,
            crmName,
            groupName,
          };
        });
        setIntegrationConfigurationsList(updatedData);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando:", error);
    }
  }, [token]);

  const handleEdit = (editIntegrationConfiguration) => {
    setSelectedIntegrationConfiguration(editIntegrationConfiguration);
    setIsModalOpen(true);
  };

  const handleCreate = () => {
    setSelectedIntegrationConfiguration(null);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    getIntegrationConfigurations("").then(() => {
      setSelectedIntegrationConfiguration(null);
      setIsModalOpen(false);
    });
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const crms = await getCrms();
        if (crms) {
          const crmGroupData = await Promise.all(crms.map((crm) => fetchGroupsForCrm(crm)));
          await getIntegrationConfigurations("", crmGroupData);
        }
      } catch (error) {
        console.error("Erro ao buscar dados:", error);
      }
    };

    fetchData();
  }, [fetchGroupsForCrm, getIntegrationConfigurations, getCrms]);

  const handleViewActive = (active) => {
    return active ? (
      <CBadge color="success">Ativo</CBadge>
    ) : (
      <CBadge color="danger">Inativo</CBadge>
    );
  };

  const handleViewActions = (item) => {
    return (
      <>
        <CButton
          color="secondary"
          onClick={() => handleEdit(item)}
          title={inforPermissions(permissao).edit}
        >
          <i className="cil-pencil"></i>
        </CButton>
      </>
    );
  };

  return (
    <div>
      <CRow>
        <CCol className="align-items-center" md="5">
          <h2>Configuração de Integração</h2>
          <p style={{ color: "gray", fontSize: "small" }}>
            Difine a configuração para complementar dados que serão selecionados.
          </p>
        </CCol>
        <CCol className="text-right" md="7">
          <CButton
            color="info"
            onClick={() => handleCreate()}
            title={inforPermissions(permissao).create}
          >
            <i className={"cil-plus"} /> Adicionar Config
          </CButton>
        </CCol>
      </CRow>
      <CCard style={{ height: "100hv" }}>
        <CCardBody>
          <TableSelectItens
            data={integrationconfigurationsList}
            columns={fields}
            onSelectionChange={(_) => { }}
            defaultSelectedKeys={[]}
            selectable={false}
            heightParam="100%"
          />
        </CCardBody>
      </CCard>
      {isModalOpen && (
        <IntegrationConfigurationModal
          isOpen={isModalOpen}
          editIntegrationConfiguration={selectedIntegrationConfiguration}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

export default IntegrationConfigurations;
