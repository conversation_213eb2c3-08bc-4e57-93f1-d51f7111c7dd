import React, { useState, useEffect } from "react";
import {
  CButton,
  CForm,
  CFormGroup,
  CInput,
  CCard,
  CCardBody,
  CCardHeader,
} from "@coreui/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LoadingComponent from "src/reusable/Loading";
import TableSelectItens from "src/reusable/TableSelectItens";
import { formatDateTime } from "src/reusable/helpers";
import { GET_DATA, POST_DATA, PUT_DATA, DELETE_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import ConfirmModal from "src/reusable/ConfirmModal";
import { useAuth } from 'src/auth/AuthContext';

const ParametriPausasTactium = () => {
  const { checkPermission,inforPermissions } = useAuth();
  const permissao = {
    modulo: "Telefonia",
    submodulo: "Pausas Tactium",
  }

  const [pauseId, setPauseID] = useState("");
  const [motivoPausa, setMotivoPausa] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [tableData, setTableData] = useState(null);
  const [selectedItem, setSelectedItem] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const fields = [
    { key: "motive", label: "Motivo da Pausa" },
    // { key: "id", label: "ID" },
    {
      key: "created_at",
      label: "Criada Em",
      formatter: (item) => formatDateTime(item),
    },
    {
      key: "updated_at",
      label: "Atualizada Em",
      formatter: (item) => formatDateTime(item),
    },
    {
      key: "action",
      label: "Ações",
      formatterByObject: (item) => renderActionButton(item),
    },
  ];

  const renderActionButton = (item) => {
    return (
      <div style={{ whiteSpace: "nowrap" }}>
        <CButton
          color="info"
          onClick={() => handleEdit(item)}
          className="mr-2"
          title={inforPermissions(permissao).edit}
          disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
        >
          <i className="cil-pencil" />
        </CButton>
        <CButton
          color="danger"
          onClick={() => deletePausa(item)}
          title={inforPermissions(permissao).delete}
          disabled={!checkPermission(permissao.modulo, "Delete", permissao.submodulo)}
        >
          <i className="cil-trash" />
        </CButton>
      </div>
    );
  };

  const handleEdit = (item) => {
    const element = tableData.find((el) => el.id === item.id);
    setSelectedItem(element);
    setMotivoPausa(element.motive);
  };

  const deletePausa = async (item) => {
    setSelectedItem(item);
    setShowConfirmModal(true);
  };

  const handleModalClose = (confirmation) => {
    setShowConfirmModal(false);
    handleDelete(confirmation);
  };

  const handleDelete = async (confirmation) => {
    if (confirmation) {
      const deleteSuccess = await DELETE_DATA(
        `PauseTactium/${selectedItem.id}`
      );
      if (deleteSuccess.success) {
        updateView();
        clearInputs();
      } else {
        alert(deleteSuccess.message);
      }
    }
  };

  const handleUpdate = async () => {
    const validFields = validate();
    if (validFields) {
      const payload = {
        id: selectedItem.id,
        motive: motivoPausa,
      };
      const updateSuccess = await putData(payload);
      if (updateSuccess.success) {
        updateView();
        clearInputs();
      } else {
        alert(updateSuccess.message);
      }
    }
  };

  const handleAdd = async () => {
    setIsLoading(true);
    const validFields = validate();
    if (validFields) {
      const payload = {
        motive: motivoPausa,
      };
      await postData(payload);
      toast.success("Pausa criada com sucesso!");
      updateView();
    }
    clearInputs();
    setIsLoading(false);
  };

  const getData = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  async function postData(payload) {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI("tactiumPause"), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    }).catch((err) => {
      console.log(err);
    });
  }

  async function putData(payload) {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await PUT_DATA(getURI("tactiumPause"), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    }).catch((err) => {
      console.log(err);
    });
  }

  function validate() {
    const trimmedField = motivoPausa.trim();
    if (trimmedField === "") {
      alert("Insira o motivo da pausa.");
      return false;
    }
    return true;
  }

  const clearInputs = () => {
    setPauseID("");
    setMotivoPausa("");
    setSelectedItem(null);
  };

  const updateView = () => {
    setIsLoading(true);
    getData(null, "tactiumPause")
      .then((data) => {
        if (data) {
          setTableData(data);
        } else {
          setTableData(null);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    updateView();
  }, []);

  return (
    <div>
      <h3>Parametrização de Pausas - Tactium</h3>
      <p style={{ color: "gray" }}>
        Definir os motivos de pausas para operadores Tactium. <br /> Os motivos
        de pausas cadastrados aqui devem estar cadastrados também no painel de
        controle do Tactium.
      </p>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard style={{ width: "60%" }}>
          <CCardBody>
            <CForm>
              <CFormGroup>
                <CInput
                  type="text"
                  placeholder="Motivo da pausa"
                  value={motivoPausa}
                  onChange={(e) => setMotivoPausa(e.target.value)}
                />
              </CFormGroup>

              {selectedItem !== null ? (
                <CButton color="info" onClick={handleUpdate} className="mr-2">
                  Salvar
                </CButton>
              ) : (
                <CButton
                  color="info"
                  onClick={handleAdd}
                  className="mr-2"
                  title={inforPermissions(permissao).create}
                  disabled={!checkPermission(permissao.modulo, "Create", permissao.submodulo)}
                >
                  Adicionar
                </CButton>
              )}
              <CButton color="secondary" onClick={clearInputs}>
                Limpar campos
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </div>{" "}
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard style={{ width: "80%" }}>
          <CCardHeader style={{ textAlign: "center" }}>
            <h4>Pausas Registradas</h4>
          </CCardHeader>
          {isLoading ? (
            <div>
              <LoadingComponent />
            </div>
          ) : (
            <CCardBody>
              <TableSelectItens
                data={tableData}
                columns={fields}
                onSelectionChange={(_) => {}}
                defaultSelectedKeys={[]}
                selectable={false}
                heightParam="100%"
              />
            </CCardBody>
          )}
        </CCard>
        <ConfirmModal
          isOpen={showConfirmModal}
          onClose={handleModalClose}
          texto={"Tem certeza que deseja deletar essa pausa?"}
        />
      </div>
    </div>
  );
};

export default ParametriPausasTactium;
