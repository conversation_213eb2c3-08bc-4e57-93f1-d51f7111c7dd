import React, { useState, useEffect } from "react";
import { useHistory, useLocation } from "react-router-dom";
import Select from "react-select";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CForm,
  CFormGroup,
  CLabel,
  CInput,
  CCol,
  CDataTable,
  CRow,
} from "@coreui/react";
import LoadingComponent from "./Loading";

import { MountURI, GET_DATA } from "src/api";
import { formatDocument, formatBuscaDocumento } from "./helpers";
import { GET_FINANCIADO, getProcessos, getApi } from "./functions";
import { getURI } from "src/config/apiConfig";
import NaoHaDadosTables from "./NaoHaDadosTables";
import { useMyContext } from "./DataContext";
import { useAuth } from "src/auth/AuthContext";
import { updateConnection } from "src/config/updateConnection";

const MudarClienteModal = ({ onClose, inserirTelefone = null }) => {
  const { checkGroup } = useAuth();
  const { updateData, updateCustas, updateCustasProjuris } = useMyContext();
  const token = localStorage.getItem("token");

  const userProfile = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : "";

  const history = useHistory();
  const location = useLocation();

  const isHomePage = location.pathname === "/telaprincipal";

  const [selectedDatacob, setSelectedDatacob] = useState(null);
  const [selectedGrupo, setSelectedGrupo] = useState(null);
  const [selectedCliente, setSelectedCliente] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [checkedContratosAberto, setUncheckedContratosAberto] = useState(true);
  const [checkedContratosSafraCyber, setUncheckedContratosSafraCyber] = useState(false);
  const [searchDocumento, setSearchDocumento] = useState("");
  const [searchFinanciado, setSearchFinanciado] = useState("");
  const [searchContrato, setSearchContrato] = useState("");
  const [searchEmail, setSearchEmail] = useState("");
  const [searchTelefone, setSearchTelefone] = useState("");

  const [searchFieldsVisible, setSearchFieldsVisible] = useState(true);
  const [tableVisible, setTableVisible] = useState(false);

  const [tableData, setTableData] = useState([]);
  const [groupOptions, setGroupOptions] = useState([]);
  const [datacobOptions, setDatacobOptions] = useState([]);
  const [clientsOptions, setClientsOptions] = useState([]);
  const [statusOptions, setStatusOptions] = useState([]);

  const [hasMultipleConnections, setHasMultipleConnections] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSelectDatacob = (selectedOption) => {
    setSelectedDatacob(selectedOption);
  };

  const handleSelectGrupo = (selectedOption) => {
    setSelectedGrupo(selectedOption);
  };

  const handleSelectCliente = (selectedOption) => {
    setSelectedCliente(selectedOption);
  };

  const handleSelectStatus = (selectedOption) => {
    setSelectedStatus(selectedOption);
  };

  const handleSearch = async () => {
    setSearchFieldsVisible(false);
    setIsLoading(true);
    await getDadosContratos();
    setIsLoading(false);

    setTableVisible(true);
  };

  const handleNewSearch = () => {
    setSelectedGrupo(null);
    setSelectedCliente(null);
    setSelectedStatus(null);
    setSearchDocumento("");
    setSearchFinanciado("");
    setSearchContrato("");
    setSearchEmail("");
    setSearchTelefone("");

    setTableVisible(false);
    setSearchFieldsVisible(true);
  };

  const handleClick = async (item) => {
    setTableVisible(false);
    setIsLoading(true);    
    const financiado = await GET_FINANCIADO(item);
    await updateConnection({ crm: selectedDatacob, userProfile });
    setIsLoading(false);
    updateData(financiado);
    updateCustas(null);
    updateCustasProjuris(null);
    if (!isHomePage) {
      history.push("/telaprincipal");
    }
    onClose();
  };

  const tableFields = [
    { key: "grupo", label: "Grupo" },
    { key: "cliente", label: "Cliente" },
    { key: "cpfCnpj", label: "CPF/CNPJ" },
    { key: "nome", label: "Nome" },
    { key: "status", label: "Status Contrato" },
    { key: "numero_Contrato", label: "Contrato" },
    { key: "fase", label: "Fase" },
  ];

  const getDadosContratos = async () => {
    let data = { ActiveConnection: selectedDatacob.datacobNumber };

    if (searchDocumento) {
      data = {
        ...data,
        documento: formatBuscaDocumento(searchDocumento),
      };
    }

    if (searchFinanciado) {
      data = {
        ...data,
        nome: searchFinanciado,
      };
    }

    if (searchContrato) {
      data = {
        ...data,
        contrato: searchContrato,
        // agrupamentoId: searchContrato,
      };
    }

    if (searchEmail) {
      data = {
        ...data,
        email: searchEmail,
      };
    }

    if (searchTelefone) {
      data = {
        ...data,
        telefone: searchTelefone,
      };
    }

    if (selectedGrupo) {
      data = {
        ...data,
        groupId: selectedGrupo.id,
      };
    }

    if (selectedCliente) {
      data = {
        ...data,
        clientId: selectedCliente.id,
      };
    }

    if (selectedStatus) {
      data = {
        ...data,
        id_Status_Contrato: selectedStatus.id,
      };
    }

    if (!checkedContratosAberto) {
      data = {
        ...data,
        aberto: 0,
      };
    }
    
    if (!checkedContratosSafraCyber) {
      data = {
        ...data,
        aberto: 0,
      };
    }

        
    if (checkedContratosSafraCyber) {
      data = {
        ...data,
        cyber: checkedContratosSafraCyber ? 1 : 0,
      };
    }

    const url = MountURI("Datacob/BuscaDadosFinanciados", data);

    try {
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTableData(data.data);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando usuários:", error);
    }
  };

  const updateGrupoOptions = () => {
    const payload = {
      ActiveConnection: selectedDatacob.datacobNumber,
    };
    getGroups(payload, "getDatacobGroups")
      .then((data) => {
        if (data) {
          const groupList = data.map((group) => ({
            id: group.id_Grupo,
            name: group.descricao,
          }));

          const allOption = { id: "", name: "Todos" };
          const optionsGroup = [allOption, ...groupList];
          setGroupOptions(optionsGroup);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const getGroups = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const getClients = async () => {
    const data = {
      ActiveConnection: selectedDatacob.datacobNumber,
      GroupId: selectedGrupo.id,
    };
    const url = MountURI("Datacob/Clients", data);

    try {
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const responseData = await response.json();
        const clientOptions = responseData.data.map((x) => ({
          cnpj: x.cnpj,
          id: x.id_Cliente,
          // name_Res: x.nome_Res,
          name: x.nome_Res,
        }));
        setClientsOptions(clientOptions);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando clientes:", error);
    }
  };
  
  const getStatusContrato = async () => {
    const data = {      
      ActiveConnection: selectedDatacob.datacobNumber,
    };
    const url = MountURI("Datacob/Status", data);

    try {
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const responseData = await response.json();
        const statusOptions = responseData.data.map((x) => ({          
          id: x.id_Status_Contrato,
          descricao: x.descricao,
        }));
        setStatusOptions(statusOptions);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando status contrato:", error);
    }
  };

  const updateOptionsAdm = () => {
    setIsLoading(true);
    getAllDatacobs(null, "getDatacobs")
      .then((data) => {
        if (data) {
          const uniqueDatacob = [...new Set(data.map((item) => item))];
          const optionsDatacob = [
            ...uniqueDatacob.map((x) => ({
              datacobNumber: x.datacobNumber,
              datacobName: "Datacob " + x.datacobName,
            })),
          ];
          setDatacobOptions(optionsDatacob);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  const getAllDatacobs = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  async function checkDatacobSelection() {
    if (userProfile && userProfile.isAdmin === true) {
      updateOptionsAdm();
      setHasMultipleConnections(true);
    } else if (userProfile && userProfile.datacobs.length > 1) {
      const uniqueDatacob = [
        ...new Set(userProfile.datacobs.map((item) => item)),
      ];
      const optionsDatacob = [
        ...uniqueDatacob.map((x) => ({
          datacobNumber: x.datacobNumber,
          datacobName: "Datacob " + x.datacobName,
        })),
      ];
      setDatacobOptions(optionsDatacob);
      setHasMultipleConnections(true);
    } else if (userProfile.datacobs.length === 0) {
      setSelectedDatacob({ datacobNumber: userProfile.activeConnection });
    } else {
      setSelectedDatacob(userProfile.datacobs[0]);
    }
  }

  const handleCheckContratosAberto = () => {
    setUncheckedContratosAberto(!checkedContratosAberto);
  };

  const handleCheckContratosSafraCyber = () => {
    setUncheckedContratosSafraCyber(!checkedContratosSafraCyber);
  };
  
  const handleKeyPressEnter = (event) => {
    if (event.key === "Enter") {
      if (
        !(
          !selectedDatacob ||
          (!searchContrato &&
            !searchDocumento &&
            !searchEmail &&
            !searchFinanciado &&
            !searchTelefone &&
            !selectedGrupo)
        )
      ) {
        event.preventDefault();
        handleSearch();
      }
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      await checkDatacobSelection();
    };
    fetchData();
  }, []);

  useEffect(() => {
    if (selectedDatacob) {
      updateGrupoOptions();
    }
  }, [selectedDatacob]);

  useEffect(() => {
    if (selectedGrupo) {
      getClients();            
      getStatusContrato();
    }
  }, [selectedGrupo]);

  return (
    <>
      <CModal
        show={true}
        onClose={onClose}
        closeOnBackdrop={false}
        size={tableVisible ? "xl" : "lg"}
      >
        <CModalHeader closeButton>Troca de Clientes</CModalHeader>
        {searchFieldsVisible && (
          <CModalBody>
            {hasMultipleConnections && (
              <CRow>
                <CCol>
                  <CFormGroup>
                    <CLabel>CRM</CLabel>
                    <Select
                      value={selectedDatacob}
                      onChange={handleSelectDatacob}
                      options={datacobOptions}
                      getOptionValue={(option) => option.datacobNumber}
                      getOptionLabel={(option) => option.datacobName}
                      placeholder={"Selecione..."}
                      isClearable={true}
                    />
                  </CFormGroup>
                </CCol>
              </CRow>
            )}
            <CRow>
              <CCol md="4">
                <CFormGroup>
                  <CLabel>Grupo</CLabel>
                  <Select
                    placeholder="Selecione"
                    value={selectedGrupo}
                    onChange={handleSelectGrupo}
                    options={groupOptions}
                    getOptionValue={(option) => option.id}
                    getOptionLabel={(option) => option.name}
                    isDisabled={!selectedDatacob}
                    isClearable={true}
                  />
                </CFormGroup>
              </CCol>
              <CCol md="4">
                <CFormGroup>
                  <CLabel>Cliente</CLabel>
                  <Select
                    placeholder="Selecione"
                    value={selectedCliente}
                    onChange={handleSelectCliente}
                    options={clientsOptions}
                    getOptionValue={(option) => option.id}
                    getOptionLabel={(option) => option.name}
                    isDisabled={!selectedGrupo}
                    isClearable={true}
                  />
                </CFormGroup>
              </CCol>
              <CCol md="4">
                <CFormGroup>
                  <CLabel>Status do contrato</CLabel>
                  <Select
                    placeholder="Selecione"
                    value={selectedStatus}
                    onChange={handleSelectStatus}
                    options={statusOptions}
                    getOptionValue={(option) => option.id}
                    getOptionLabel={(option) => option.descricao}
                    isDisabled={!selectedGrupo}
                    isClearable={true}
                  />
                </CFormGroup>
              </CCol>
            </CRow>
            <CRow>
              <CCol md="6">
                <div className={"form-check form-switch"}>
                  <input
                    checked={checkedContratosAberto}
                    className={"form-check-input"}
                    type="checkbox"
                    role="switch"
                    id="flexSwitchCheckDefault"
                    onChange={handleCheckContratosAberto}
                  />
                  <label
                    className={"form-check-label"}
                    htmlFor="flexSwitchCheckDefault"
                  >
                    Filtrar por Contratos Abertos
                  </label>
                </div>
              </CCol>
              <CCol md="6">
                <div className={"form-check form-switch"}>
                  <input
                    checked={checkedContratosSafraCyber}
                    className={"form-check-input"}
                    type="checkbox"
                    role="switch"
                    id="flexSwitchCheckDefault"
                    onChange={handleCheckContratosSafraCyber}
                  />
                  <label
                    className={"form-check-label"}
                    htmlFor="flexSwitchCheckDefault"
                  >
                    Contratos Safra - Cyber
                  </label>
                </div>
              </CCol>
            </CRow>
            <hr></hr>
            <div>
              <CForm>
                <CFormGroup>
                  <CLabel>CPF/CNPJ</CLabel>
                  <CInput
                    // id="documento"
                    type="text"
                    value={searchDocumento}
                    onChange={(e) => setSearchDocumento(e.target.value)}
                    onKeyPress={handleKeyPressEnter}
                  />
                </CFormGroup>
                <CFormGroup>
                  <CLabel>Nome do Financiado</CLabel>
                  <CInput
                    // id="financiado"
                    type="text"
                    value={searchFinanciado}
                    onChange={(e) => setSearchFinanciado(e.target.value)}
                    onKeyPress={handleKeyPressEnter}
                  />
                </CFormGroup>
                <CFormGroup>
                  <CLabel>Número do Contrato</CLabel>
                  <CInput
                    // id="contrato"
                    type="text"
                    value={searchContrato}
                    onChange={(e) => setSearchContrato(e.target.value)}
                    onKeyPress={handleKeyPressEnter}
                  />
                </CFormGroup>
                <CFormGroup>
                  <CLabel>Email</CLabel>
                  <CInput
                    // id="email"
                    type="text"
                    value={searchEmail}
                    onChange={(e) => setSearchEmail(e.target.value)}
                    onKeyPress={handleKeyPressEnter}
                  />
                </CFormGroup>
                <CFormGroup>
                  <CLabel>Telefone</CLabel>
                  <CInput
                    // id="fone"
                    type="text"
                    value={searchTelefone}
                    onChange={(e) => setSearchTelefone(e.target.value)}
                    onKeyPress={handleKeyPressEnter}
                  />
                </CFormGroup>
              </CForm>
              <div className="d-flex justify-content-end">
                <CButton color="danger" className="mr-2" onClick={onClose}>
                  Cancelar
                </CButton>
                <CButton
                  color="primary"
                  onClick={handleSearch}
                  disabled={
                    !selectedDatacob ||
                    (!searchContrato &&
                      !searchDocumento &&
                      !searchEmail &&
                      !searchFinanciado &&
                      !searchTelefone &&
                      !selectedGrupo)
                  }
                >
                  Pesquisar
                </CButton>
              </div>
            </div>
          </CModalBody>
        )}
        {isLoading ? (
          <CModalBody>
            {" "}
            <div className="d-flex justify-content-center">
              <LoadingComponent />
            </div>{" "}
          </CModalBody>
        ) : null}
        {tableVisible && (
          <CModalBody>
            {tableData == null ||
            tableData === undefined ||
            tableData.length === 0 ? (
              <NaoHaDadosTables />
            ) : (
              <CDataTable
                items={tableData}
                fields={tableFields}
                hover
                striped
                bordered
                size="lg"
                itemsPerPage={10}
                pagination
                scopedSlots={{
                  numero_Contrato: (item) =>
                    item.numero_Contrato ? (
                      <td className="nowrap-cell">{item.numero_Contrato}</td>
                    ) : (
                      <td></td>
                    ),
                  nome: (item) => (
                    <td>
                      {" "}
                      <CButton
                        onClick={() => handleClick(item)}
                        className="flat px-2 pt-0 pb-1"
                        style={{
                          color: checkGroup(item.id_Grupo) ? "blue" : "black",
                          textWrap: "nowrap",
                        }}
                        disabled={!checkGroup(item.id_Grupo)}
                        title={
                          !checkGroup(item.id_Grupo)
                            ? "Sem permissão para acessar o Grupo"
                            : ""
                        }
                      >
                        <span>{item.nome}</span>
                      </CButton>
                    </td>
                  ),
                  cpfCnpj: (item) => (
                    <td className="nowrap-cell">
                      {formatDocument(item.cpfCnpj)}
                    </td>
                  ),
                }}
              />
            )}
            <CModalFooter>
              <div className="d-flex justify-content-end">
                <CButton color="primary" onClick={handleNewSearch}>
                  Nova pesquisa
                </CButton>
              </div>
            </CModalFooter>
          </CModalBody>
        )}
      </CModal>
    </>
  );
};

export default MudarClienteModal;
