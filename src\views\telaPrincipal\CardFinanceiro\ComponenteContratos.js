import React, { useState } from "react";
import {  CBadge } from "@coreui/react";

import { formatDate, formatThousands } from "src/reusable/helpers";

const ComponenteContratos = ( {doubleClick , singleClick} ) => {
    
  const contratosData = localStorage.getItem("contratosAtivos")
  ? JSON.parse(localStorage.getItem("contratosAtivos"))
  : [];

  const [expandedRows, setExpandedRows] = useState([]);
  const [pressedRow, setPressedRow] = useState(null);

  const toggleRow = (rowId) => {
    if (expandedRows.includes(rowId)) {
      setExpandedRows(expandedRows.filter((id) => id !== rowId));
    } else {
      setExpandedRows([...expandedRows, rowId]);
    }
  };

  const renderRow = (item) => {
    const isExpanded = !expandedRows.includes(item.id_Contrato);
    return (
      <React.Fragment key={item.id_Contrato}>
        <tr onClick={() => toggleRow(item.id_Contrato)}>
          <td colSpan={15}>
            {" "}
            {expandedRows.includes(item.id_Contrato) ? "+" : "-"} Nº Contrato:{" "}
            <strong>{item.numero_Contrato} </strong>{" "}
          </td>
        </tr>
        {isExpanded && renderInnerRows(item.parcelas)}
      </React.Fragment>
    );
  };

  const handleRowClick = (parcela) => {
    setPressedRow(parcela.id_Parcela);
    singleClick(parcela)
  };

  const handleRowDoubleClick = (negociacaoData) => {
    doubleClick(negociacaoData)
  };

  const renderInnerRows = (parcelas) => {
    const sortData = (parcela) => {
      const sortedData = [...parcela];
      sortedData.sort((a, b) => a.nr_Parcela - b.nr_Parcela);
      return sortedData;
    };
    const parcelaOrganizada = sortData(parcelas);

    return parcelaOrganizada.map(
      (
        parcela
      ) => (
        <tr
          className={`expanded-table ${
            pressedRow === parcela.id_Parcela ? "pressed" : ""
          }`}
          key={parcela.id_Parcela}
          onClick={() => handleRowClick(parcela)}
          onDoubleClick={() => handleRowDoubleClick(parcela)}
        >
          <td
            style={{
              borderLeft: "0",
            }}
          >
            {parcela.status === "P" ? (
              <CBadge color={"success"}>Pago</CBadge>
            ) : parcela.status === "D" ? (
              <CBadge color={"danger"}>Devolvido</CBadge>
            ) : parcela.nr_Acordo ? (
              <CBadge color={"info"}>Acordo</CBadge>
            ) : (
              <CBadge color={"warning"}>Aberto</CBadge>
            )}
          </td>
          <td>{parcela.nr_Parcela}</td>
          <td>{parcela.nr_Plano}</td>
          <td>{parcela.nome_Tipo_Parcela}</td>
          <td>
            {parcela.dt_Vencimento ? formatDate(parcela.dt_Vencimento) : "---"}
          </td>
          <td>
            {parcela.vl_Saldo ? formatThousands(parcela.vl_Saldo) : "0,00"}
          </td>
          <td>
            {parcela.vl_Original
              ? formatThousands(parcela.vl_Original)
              : "0,00"}
          </td>
          <td>
            {parcela.vl_Saldo_Atualizado
              ? formatThousands(parcela.vl_Saldo_Atualizado)
              : "0,00"}
          </td>
          <td>{parcela.tipo_Parcela}</td>
          <td>{parcela.atraso}</td>
          <td>
            {parcela.dt_Inclusao ? formatDate(parcela.dt_Inclusao) : "---"}
          </td>
          <td>
                {parcela.nr_Acordo}
          </td>
          <td>
            {parcela.dt_Negociacao ? formatDate(parcela.dt_Negociacao) : "---"}
          </td>
          <td>
            {parcela.dt_Venc_Boleto
              ? formatDate(parcela.dt_Venc_Boleto)
              : "---"}
          </td>
          <td>{parcela.qtde_Boleto_Emitido}</td>
        </tr>
      )
    );
  };

  return (
    <>
      <div style={{overflow:"auto" , height: "230px" }}>
        {contratosData ? (
          <table className="table tabs-table">
            <thead>
              <tr>
                <th>Status</th>
                <th>Parcela</th>
                <th>Plano</th>
                <th>Tipo</th>
                <th>Vencimento</th>
                <th>Saldo</th>
                <th>Original</th>
                <th>Atualizado</th>
                <th>Desc. Máximo</th>
                <th>Atraso</th>
                <th>Entrada</th>
                <th>Nr. Acordo</th>
                <th>Dt. Negociação</th>
                <th>Dt. Boleto</th>
                <th>Qt. Boleto</th>
              </tr>
            </thead>
            <tbody style={{ overflow: "auto" }}>
              {contratosData.map(renderRow)}
            </tbody>
          </table>
        ) : (
          <></>
        )}
      </div>
    </>
  );
};

export default ComponenteContratos;
