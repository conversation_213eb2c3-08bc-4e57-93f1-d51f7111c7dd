import React, { useState, useEffect } from "react";
import {
  CCard,
  CCardBody,
  CCol,
  CDataTable,
  CRow,
  CButton,
  CCardHeader,
  CInput,
  CLabel,
  CTooltip,
} from "@coreui/react";
import DadosContratos from "../telaPrincipal/CardContratos/Contratos";
import { formatDocument } from "src/reusable/helpers";

const AvalistaTerceiros = () => {
  const [tableData, setTableData] = useState([]);

  const dadosAvalista = JSON.parse(localStorage.getItem("avalistasTerceiros"))
    ? JSON.parse(localStorage.getItem("avalistasTerceiros"))
    : null;

  const columns = [
    { key: "id_Cliente", label: "Cliente" },
    { key: "nome" },
    { key: "cpfCnpj", label: "CPF / CNPJ" },
    { key: "rg", label: "RG" },
    { key: "sexo", label: "Sexo" },
    { key: "tipo" },
    { key: "numero_Contrato", label: "Contrato" },
  ];

  useEffect(() => {
    setTableData(dadosAvalista);
  }, []);

  return (
    <div>
      <CRow className="d-flex justify-content-between mx-3 mb-2">
        <h1>Avalista e Terceiros</h1>
      </CRow>
      <div className="container-fluid">
        <CRow>
          <CCol>
            <CCard>
              <CCardBody>
                <DadosContratos />
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
        <CCard>
          <CCardBody>
            <CDataTable items={tableData} fields={columns} scopedSlots={{
              cpfCnpj: (item) => (
                <td>{item.cpfCnpj ? formatDocument(item.cpfCnpj) : "---"}</td>
              ),
            }}/>
          </CCardBody>
        </CCard>
      </div>
    </div>
  );
};

export default AvalistaTerceiros;
