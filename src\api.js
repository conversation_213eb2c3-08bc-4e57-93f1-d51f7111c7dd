import { getURI, getUriWebSocket } from "./config/apiConfig";
import axios from "axios";
//const { REACT_APP_API_URL } = process.env;

const token = window.localStorage.getItem("token");

const baseHader = {
  "Content-Type": "application/json",
};

const baseHaderToken = {
  ...baseHader,
  Authorization: `Bearer ${token}`,
  // Authorization: "Bearer " + window.localStorage.getItem("token"),
};

// const token = localStorage.getItem("token");

export function MountURI(
  path,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  let url;
  if (isFriendlyUrl) {
    url = new URL(useConfig ? path : `${getURI()}/${path}`);
    url = `${url}${dataFriendlyUrl}`;
  } else {
    url = new URL(useConfig ? path : `${getURI()}/${path}`);
    if (Boolean(data) && Object.keys(data).length > 0) {
      url.search = new URLSearchParams(data).toString();
    }
  }

  return url;
}

export function MountURIWebSocket(
  path,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  let url;
  if (isFriendlyUrl) {
    url = new URL(useConfig ? path : `${getUriWebSocket()}`);
    url = `${url}${dataFriendlyUrl}`;
  } else {
    url = new URL(useConfig ? path : `${getUriWebSocket()}`);
    if (Boolean(data) && Object.keys(data).length > 0) {
      url.search = new URLSearchParams(data).toString();
    }
  }
  return url;
}

export function LOGIN_POST(body) {
  return {
    url: MountURI("api/Auth/Login"),
    options: {
      method: "POST",
      headers: { ...baseHader },
      body: JSON.stringify(body),
    },
  };
}

export function USER_GET(data) {
  baseHaderToken.Authorization =
    "Bearer " + window.localStorage.getItem("token");
  return {
    url: MountURI("Employees/list", data),
    options: {
      method: "GET",
      headers: { ...baseHaderToken },
    },
  };
}

export function GET(api, id) {
  baseHaderToken.Authorization =
    "Bearer " + window.localStorage.getItem("token");
  return {
    url: MountURI(`${api}/${id}`),
    options: {
      method: "GET",
      headers: { ...baseHaderToken },
    },
  };
}

export async function GET_Users() {
  try {
    const response = fetch(`${getURI()}/User/AllUsers`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (response.ok) {
      const data = response.json();
      return data.data;
    } else {
      console.error("Erro:", response?.statusText);
    }
  } catch (error) {
    console.error("Erro buscando usuários:", error);
  }
}

export async function GET_DATA(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = "",
  onlyData = true
) {
  // const url = MountURI(api, data);
  try {
    const response = await fetch(
      MountURI(api, data, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        headers: {
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
      }
    );

    if (response.ok) {
      const contentType = response.headers.get("Content-Type");
      if (contentType === "application/pdf") {
        // Abra o PDF em uma nova guia do navegador
        const blobpdf = await response.blob();
        return blobpdf;
      }
      if (
        contentType ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      ) {
        const blob = await response.blob();
        return blob;
      }

      const data = await response.json();
      if (onlyData) {
        return data.data;
      } else {
        return data;
      }
    } else {
      console.error("Erro:", response?.statusText);
      if (response?.status === 401) {
        return response?.status;
      } else return response;
    }
  } catch (error) {
    console.error("Erro na busca:", error);
    return null;
  }
}

export async function GET_PDF(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  // const url = MountURI(api, data);
  try {
    const response = await fetch(
      MountURI(api, data, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        headers: {
          "Content-Type": "application/pdf",
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
      }
    );

    if (response.ok) {
      const data = await response.json();
      return data.data;
    } else {
      console.error("Erro:", response?.statusText);
    }
  } catch (error) {
    console.error("Erro na busca:", error);
  }
}

export async function POST_DATA(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  try {
    const response = await fetch(
      MountURI(api, data, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
        body: JSON.stringify(data),
      }
    );
    const status = await response?.json();
    return status;
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function POST_FILE_DATA(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  try {
    const response = await fetch(
      MountURI(api, data, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        method: "POST",
        headers: {
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
        body: JSON.stringify(data),
      }
    );

    return response;
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function AXIOS_POST(
  api,
  data,
  useConfig,
  isFriendlyUrl,
  dataFriendlyUrl
) {
  try {
    const response = await axios.post(
      MountURI(api, data, useConfig, isFriendlyUrl, dataFriendlyUrl),
      data,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
      }
    );

    const status = await response.json();
    return status;
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function POST_FORMDATA(
  api,
  FormData = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  try {
    const response = await fetch(
      MountURI(api, null, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        method: "POST",
        headers: {
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
        body: FormData,
      }
    );

    const contentType = response.headers.get("Content-Type");
    if (
      contentType ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ) {
      const blob = await response.blob();
      return blob;
    }

    const status = await response.json();
    return status;
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function PUT_DATA(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  try {
    const response = await fetch(
      MountURI(api, null, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
        body: JSON.stringify(data),
      }
    );
    const status = await response.json();
    return status;
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function DELETE_DATA(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  //Needs an ID always? on the fetch/url?
  try {
    const response = await fetch(
      MountURI(api, null, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
        body: JSON.stringify(data),
      }
    );
    const result = await response.json();
    if (result) {
      return result;
    } else {
      console.error("Erro:", response?.statusText);
    }
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function GET_DOWNLOAD(api, options, useConfig = false) {
  fetch(MountURI(api, options, useConfig), {
    headers: {
      Authorization: "Bearer " + window.localStorage.getItem("token"),
    },
  })
    .then((res) => res.blob())
    .then((blob) => {
      var file = window.URL.createObjectURL(blob);
      window.open(file);
    })
    .catch((error) => {
      console.error("Erro fetching:", error);
    });
}
