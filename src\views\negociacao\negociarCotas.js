import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON>ard,
  CCardBody,
  CCol,
  CRow,
  CButton,
  CLabel,
  CSpinner,
  CCardHeader,
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CTooltip,
} from "@coreui/react";
import DadosContratos from "../telaPrincipal/CardContratos/Contratos";
import DetalhesCalculoModal from "./DetalhesCalculoModal";
import BoletoModal from "./BoletoModal";
import SimulacaoModal from "./SimulacaoModal";

import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

import { liberarNegociacao } from "./../../containers/AprovacaoJuridica";

import {
  formatCurrency,
  formatDate,
  formatThousands,
  leftPad,
  formatDateGlobaltoSimplified,
} from "src/reusable/helpers";
import { POST_DATA, GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { useAuth } from "src/auth/AuthContext";
import TableSelectItens from "src/reusable/TableSelectItens";
import NegociacaoBar from "./Parcial/NeogciacaoBar";
import CardLoading from "src/reusable/CardLoading";
import {
  calculaAtrasoParcelasPorDataNegociaco,
  calculaPerncetuaisDesconto,
  calculaValoresParcelas,
} from "./utils/CalculosNegociacao";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import DetalhesParametrosModal from "./Parcial/DetalhesParametrosModal";
import FormaDescontoModal from "./Parcial/FormaDescontoModal";
import CardNegociacaoStatus from "./Parcial/CardNegociacaoStatus";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { useMyContext } from "src/reusable/DataContext";
import CalculoLivreModal from "./Parcial/CalculoLivreModal.tsx";
import CardBoletoStatus from "./Parcial/CardBoletoStatus";
import ConfirmModal from "src/reusable/ConfirmModal.js";
import DetalhesBoletoModal from "src/views/acordos/DetalhesBoletoModal";
import TableInstallment from "./Parcial/TableInstallment.js";
import PendenciasModal from "./Parcial/PendenciasModal.js";

const PostData = async (payload, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI(endpoint),
        payload,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(getURI(endpoint), payload, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const Negociar = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissaoNegociacao = {
    modulo: "Negociação",
    submodulo: "Simular",
  };

  const permissaoNegociacaoDetalhesCalculo = {
    modulo: "Negociação",
    submodulo: "Simular - Detalhes do Cálculo",
  };

  const permissaoNegociacaoBoleto = {
    modulo: "Negociação",
    submodulo: "Boleto",
  };

  const permissaoNegociacaoBoletoDetalhes = {
    modulo: "Negociação",
    submodulo: "Boleto - Detalhes do Boleto",
  };

  const [loading, setLoading] = useState(false);
  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : "";
  const parcelasAbertas =
    contratosAtivos === null || contratosAtivos === undefined
      ? []
      : contratosAtivos?.flatMap((item) =>
          item.parcelas.filter(
            (pItem) =>
              pItem.status === "A" &&
              !pItem.nr_Acordo /* && pItem.atraso > 0  ||
        (pItem.status === "A" && !pItem.nr_Acordo && pItem.atraso === 0 && pItem.nome_Tipo_Parcela === "DIF_PARCELAS")*/
          )
        );
  const parcelasAbertasContratoAtivo = contratosAtivos?.flatMap((item) =>
    item.parcelas.filter((pItem) => pItem.status === "A" && !pItem.nr_Acordo)
  );

  const [textValidacao, setTextValidacao] = useState("");
  const [validadacaoTela, setValidadacaoTela] = useState(true);
  const [parametroCalculo, setParametroCalculo] = useState(null);
  const [showBoletoStatusModal, setShowBoletoStatusModal] = useState(false);

  /* Resposta da Api de Calculos */
  const [afterCalulate, setAfterCalulate] = useState(false);
  const [calcResponse, setCalcResponse] = useState(null);
  const [pendencias, setPendencias] = useState([]);
  /* Valores inciais */
  const [valorNegociado, setValorNegociado] = useState(0);
  const [valorDescMaxNeg, setValorDescMaxNeg] = useState(0);
  const [manualImputCalculo, setManualImputCalculo] = useState(false);
  const [enviadoApi, setEnviadoApi] = useState(false);
  const [atualizarValorRegua, setAtualizarValorRegua] = useState(0);
  /* Constantes Regua */
  const [valorRNegociado, setValorRNegociado] = useState(0);
  const [valorRDescMaxNeg, setValorRDescMaxNeg] = useState(0);
  /*Parametros Calcular */
  const [selectedDate, setSelectedDate] = useState(new Date());
  /* inicio --- Dados da Tabela  */
  const [tableData, setTableData] = useState([]);
  const [selecionadasTable, setSelecionadasTable] = useState([]);
  const [parcelasSelecionadas, setParcelasSelecionadas] = useState([]);
  const [textLoading, setTextLoading] = useState("");
  const [valorInputCalculo, setValorInputCalculo] = useState(0);
  const [isShowDetalhesParcela, setIsShowDetalhesParcela] = useState(false);
  const [showDetalhesParametrosModal, setShowDetalhesParametrosModal] =
    useState(false);
  const [showFormaDescontoModal, setShowFormaDescontoModal] = useState(false);
  const [formaDesconto, setFormaDesconto] = useState(0);
  const [liberarManual, setLiberarManual] = useState(false);
  const [userEnableManualCalcNegotiation, setUsersEnableManualCalcNegotiation] =
    useState(false);

  const [freeCalcValue, setFreeCalcValue] = useState(false);

  const [textConfirmModal, setTextConfirmModal] = useState("");
  const [onCloseConfirmModal, setOnCloseConfirmModal] = useState(
    (choice) => {}
  );
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const [showModalDetalhesBoleto, setShowModalDetalhesBoleto] = useState(false);
  const [detalhesBoleto, setDetalhesBoleto] = useState(null);
  const [idNegTicket, setIdNegTicket] = useState(0);

  const [showModalChangeCalc, setShowModalChangeCalc] = useState(false);
  const [showModalChangeCusta, setShowModalChangeCusta] = useState(false);

  const [validadacaoTelaLoading, setValidadacaoTelaLoading] = useState(true);

  const [selectedContract, setSelectedContract] = useState("");
  const [selectAll, setSelectAll] = useState(false);

  // const [parcelasVencidasChecked, setParcelasVencidasChecked] = useState(null);
  const parcelasVencidasChecked = useRef(null);

  const getParametro = () => {
    setLoading(true);
    setTextLoading("Carregando parâmetros de cálculo...");
    const payload = {
      idclientedatacob: financiadoData.id_Cliente,
      idfasecontrato: financiadoData.id_Fase,
      codorname: financiadoData.coddatacob.toString(),
    };
    GetData(payload, "getParametroCalculo")
      .then((par_calc) => {
        if (!par_calc || par_calc.length === 0 || par_calc === undefined) {
          setTextValidacao(
            `Não há parâmetro de cálculo para o grupo ${financiadoData?.grupo}.`
          );
          setValidadacaoTela(false);
        }
        setParametroCalculo(par_calc);
      })
      .catch((error) => {
        setTextValidacao(
          `Falha: Não há parâmetro de cálculo para o grupo ${financiadoData?.grupo}.`
        );
        setValidadacaoTela(false);
      })
      .finally(() => {
        setLoading(false);
        getLiberarManual();
        getUsersEnableManualCalcNegotiation();
      });
  };

  const getUsersEnableManualCalcNegotiation = () => {
    setLoading(true);
    setTextLoading("Carregando parâmetros de cálculo manual...");
    GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "users_enable_manual_calc_negotiation"
    )
      .then((res) => {
        try {
          const arrayLibera = JSON.parse(res);
          if (user?.isAdmin) {
            setUsersEnableManualCalcNegotiation(true);
          } else {
            if (arrayLibera.indexOf(user.id) > -1) {
              setUsersEnableManualCalcNegotiation(true);
            }
          }
        } catch (e) {
          if (user?.isAdmin) {
            setUsersEnableManualCalcNegotiation(true);
          } else {
            setUsersEnableManualCalcNegotiation(false);
          }
        }
      })
      .catch((error) => {
        if (user?.isAdmin) {
          setUsersEnableManualCalcNegotiation(true);
        } else {
          setUsersEnableManualCalcNegotiation(false);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getLiberarManual = () => {
    setLoading(true);
    setTextLoading("Carregando parâmetros de cálculo manual...");
    GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "liberar_calculo_manual"
    )
      .then((res) => {
        try {
          const arrayLibera = JSON.parse(res);
          if (user?.isAdmin) {
            setLiberarManual(true);
          } else {
            if (arrayLibera.indexOf(user.id) > -1) {
              setLiberarManual(true);
            }
          }
        } catch (e) {
          if (user?.isAdmin) {
            setLiberarManual(true);
          } else {
            setLiberarManual(false);
          }
        }
      })
      .catch((error) => {
        if (user?.isAdmin) {
          setLiberarManual(true);
        } else {
          setLiberarManual(false);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const history = useHistory();
  const context = useMyContext();

  const [calculoLivrePermissao, setCalculoLivrePermissao] = useState(false);

  useEffect(() => {
    async function fetchData() {
      if (!(await liberarNegociacao(context.data))) {
        history.push("/telaprincipal");
      }

      if (financiadoData) {
        await validacoeTela();
        getLiberarManual();
        getUsersEnableManualCalcNegotiation();
      }

      const formaLivre = await getFormaLivreConfig();
      let perm = false;
      try {
        if (financiadoData.coddatacob === "GVC") {
          if (formaLivre.gvc.indexOf(financiadoData.id_Grupo) > -1) {
            perm = true;
          }
        }
        if (financiadoData.coddatacob === "Rodobens") {
          if (formaLivre.rodobens.indexOf(financiadoData.id_Grupo) > -1)
            perm = true;
        }
      } catch (err) {
        console.warn("erroFormaLivre", err);
        perm = false;
      }
      setCalculoLivrePermissao(perm);
    }
    fetchData();
  }, []);

  // useEffect(() => {
  //   if (parametroCalculo) {
  //     const parcelas = JSON.parse(JSON.stringify(parcelasAbertas));
  //     const ordenarParcelas = parcelas.sort((a, b) => {
  //       // Verifica se a.nr_Parcela ou b.nr_Parcela é igual a 0
  //       if (a.nr_Parcela === 0) return 1; // Move 'a' para o final se a.nr_Parcela é 0
  //       if (b.nr_Parcela === 0) return -1; // Move 'b' para o final se b.nr_Parcela é 0

  //       // Caso contrário, ordena normalmente
  //       return a.nr_Parcela - b.nr_Parcela;
  //     });
  //     const ordenarContrato = ordenarParcelas.sort(
  //       (a, b) => a.numero_Contrato - b.numero_Contrato
  //     );

  //     const parcelasSel = ordenarContrato.map((item) => {
  //       item.parcelaSelecionada = item.atraso > 0 ?? item.nr_Parcela === 0;
  //       return item;
  //     });
  //     setManualImputCalculo(false);
  //     recalculorNegociado(parcelasSel);
  //     validacoeTela();
  //   }
  // }, [parametroCalculo]);

  const columns = [
    {
      label: "",
    },
    {
      key: "numero_Contrato",
      defaultSort: "ascending",
      label: "Contrato",
      filter: true,
    },
    {
      key: "nr_Parcela",
      label: "Parcela",
      cellStyleCondicional: (item) => {
        if (item.atraso && item.atraso > 0) {
          return {
            backgroundColor: "red",
            color: "white",
            textAlign: "center",
          };
        }
        return {
          backgroundColor: "white",
          color: "black",
          textAlign: "center",
        };
      },
      formatter: (value) => String(value).padStart(3, "0"),
    },
    { key: "nr_Plano", label: "Plano" },
    {
      key: "nome_Tipo_Parcela",
      label: "Tp. Parcela",
    },
    {
      key: "dt_Vencimento",
      label: "Vencimento",
      formatter: (value) => formatDate(value),
    },
    {
      key: "vl_Saldo",
      label: "Valor Saldo",
      formatter: (value) => formatThousands(value),
    },
    {
      key: "vl_Saldo_Atualizado",
      label: "Valor Total",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "valorNegociado",
      label: "Valor Negociado",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "dt_Pgto",
      label: "Data Pagamento",
      defaultValue: selectedDate ? formatDate(selectedDate) : "---",
    },
    { key: "atraso", label: "Atraso", formatter: (value) => value.toString() },
    //Esses dois campos de desconto são provavelmente calculados aqui no front e carregados na tabela
    // {
    //   key: "desconto",
    //   label: "Desconto",
    //   formatter: (value) => formatCurrency(value, false),
    // },
    // {
    //   key: "percDesconto",
    //   label: "% de Desconto",
    //   formatter: (value) => formatCurrency(value, false),
    // },
  ];

  const todayFormat = new Date(
    new Date().setHours(new Date().getHours() - 3)
  ).toISOString();

  const validacoeTela = async () => {
    setValidadacaoTela(true);
    setLoading(true);
    //setValidadacaoTelaLoading(true);
    setTextLoading("Carregando tela...");
    if (!contratosAtivos || !parcelasAbertas || parcelasAbertas.length === 0) {
      setValidadacaoTela(false);
      setTextValidacao(
        "Não há parcelas abertas e em atraso para este agrupamento."
      );
      return false;
    }

    setLoading(true);
    setTextLoading("Calculando...");
    await CleanCalculoPost(
      parcelasAbertas
        .filter((x) => x.dt_Vencimento <= todayFormat)
        .map((item) => item.id_Parcela),
      0,
      0,
      todayFormat,
      validacoesTelaApiSuccess,
      validacoesTelaApiError,
      cleanCalculoErrorFunc,
      cleanCalculoFinnalyFunc
    );
  };

  const validacoesTelaApiSuccess = (response) => {
    const obj = getInstallmentTable(response);
    setValidadacaoTelaLoading(false);
  };

  const cleanCalculoErrorFunc = (error) => {
    console.warn("error", error);
    setFormaDesconto(0);
  };

  const cleanCalculoFinnalyFunc = () => {
    setLoading(false);
  };

  const validacoesTelaApiError = (response) => {
    try {
      const arrayMsg = response.message
        .replace("Retorno Api. ", "")
        .replace(/\[/g, "")
        .replace(/\]/g, "")
        .replaceAll('"', "");
      setTextValidacao(arrayMsg);
    } catch (e) {
      const errorMessage = "Atenção: Não foi possível calcular os valores! ";

      setTextValidacao(errorMessage);
    }
    setValidadacaoTela(false);
    setValidadacaoTelaLoading(false);
  };

  const getInstallmentTable = (response, valorRegua = 0) => {
    const neg = response.negociacaoDto[0] ?? null;
    const parcelas = neg?.parcelas;
    if (parcelas === undefined || parcelas === null) return [];
    let obj = parcelas.map((item) => {
      const parcAb = parcelasAbertas.find(
        (x) => x.id_Parcela === item.idParcela
      );
      return {
        ...item,
        nome_Tipo_Parcela: parcAb?.nome_Tipo_Parcela,
        dt_Pgto: parcAb?.dt_Pgto,
      };
    });
    const par = obj.filter((item) => item.parcelaSelecionada);
    setParcelasSelecionadas(obj);
    setSelecionadasTable(par);
    const selecionadoFlag = obj.filter((item) => item.parcelaSelecionada);
    handleCheckPendingIssues(selecionadoFlag);
    const vlNegOriginalContratosAtivos = selecionadoFlag.reduce(
      (total, item) => Math.round((total + calcTotalValue(item)) * 100) / 100,
      0
    );
    // if (!enviadoApi) {
    setValorRNegociado(vlNegOriginalContratosAtivos);
    setValorRDescMaxNeg(neg.vlDividaAtualizadaMaxDesc);
    setAtualizarValorRegua(
      valorRegua !== 0 ? valorRegua : vlNegOriginalContratosAtivos
    );
    setValorNegociado(neg.vlNegociacao);
    setValorInputCalculo(neg.vlNegociacao);
    if (parcelasVencidasChecked.current === true) {
      obj = obj.sort((a, b) => {
        const dateA = new Date(a.dtVencimento);
        const dateB = new Date(b.dtVencimento);
        return dateA - dateB;
      });
    }
    setTableData(obj);
    setSelectAll(obj?.every((item) => item.parcelaSelecionada));
    setCalcResponse(response);

    const pendencias = [];
    const negs = response?.negociacaoDto ?? [];
    for (const item in negs) {
      const itemNeg = negs[item];
      pendencias.push(...itemNeg.pendencias);
    }

    setPendencias(pendencias);
    return obj;
  };

  const HandleInstallmentChange = (input, itens) => {
    // if (itens.length === 0) return;
    const parcelasOld = structuredClone(parcelasSelecionadas);
    const selecionadas = structuredClone(parcelasOld).map((item) => {
      if (itens.idParcela === item.idParcela)
        item.parcelaSelecionada = input.currentTarget.checked;
      return item;
    });

    setSelectAll(false);
    const idsSelecionadas = selecionadas
      .filter((item) => item.parcelaSelecionada)
      .map((item) => item.idParcela);

    // if (arraysAreEqual(idsSelecionadas, getIDsParcelas(parcelasOld))) {
    //   return;
    // }

    setLoading(true);
    setTextLoading("Calculando...");
    CleanCalculoPost(
      idsSelecionadas,
      0,
      0,
      selectedDate,
      (response) => {
        const obj = getInstallmentTable(response);
      },
      (x) => {
        toast.error("Erro ao calcular valores!");
      },
      (x) => {
        toast.error("Erro ao calcular valores!");
        cleanCalculoErrorFunc(x);
      },
      cleanCalculoFinnalyFunc
    );
    // }
    // } else {
    //   setEnviadoApi(false);
    //   setManualImputCalculo(false);
    // }
  };
  /* Fim --- Dados da Tabela  */
  async function getParamFormaLivreConfig() {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "param_forma_livre"
    );
    try {
      return Number(response);
    } catch (err) {
      return 0;
    }
  }

  async function getFormaLivreConfig() {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "grupos_forma_livre"
    );
    try {
      return JSON.parse(response);
    } catch (err) {
      return {
        rodobens: [],
        gvc: [],
      };
    }
  }

  const calculoPost = async (
    vlNeg,
    parcelas,
    formaDesconto = 0,
    dueDate = null,
    openBoleto = true
  ) => {
    const data = dueDate === null ? selectedDate : dueDate;
    const dtFormat = formatDateGlobaltoSimplified(data);

    const payload = {
      idAgrupamento: financiadoData.id_Agrupamento,
      dtNegociacao: dtFormat,
      vlNegociado: vlNeg,
      parcelas: parcelas,
      formaDesconto: formaDesconto,
    };
    setLoading(true);
    setTextLoading("Calculando...");
    PostData(payload, "postCalcularNegociacao")
      .then((response) => {
        if (
          response.data &&
          response.data.negociacaoDto &&
          response.data.negociacaoDto.length > 0
        ) {
          // recalculorNegociado(parcelasSelecionadas, false, true);
          // setEnviadoApi(true);
          // setValorNegociado(vlNeg);
          // setValorInputCalculo(vlNeg);
          // setFormaDesconto(formaDesconto);
          // if (dueDate === null) {
          //   setAtualizarValorRegua(Math.round(valorInputCalculo * 100) / 100);
          // } else {
          //   setAtualizarValorRegua(Math.round(vlNeg * 100) / 100);
          //   setValorRNegociado(Math.round(vlNeg * 100) / 100);
          // }
          getInstallmentTable(response.data, vlNeg);
        }
        if (response.success === false) {
          setFormaDesconto(0);
          if (response.message.startsWith("Retorno Api")) {
            //valorNegociadoOriginal();
            try {
              const arrayMsg = response.message
                .replace("Retorno Api. ", "")
                .replace(/\[/g, "")
                .replace(/\]/g, "")
                .replace('"', "");
              toast.warning(arrayMsg, {
                autoClose: 8000,
              });
            } catch (e) {
              const errorMessage =
                "Atenção: Não foi possível calcular os valores! Valores informados ultrapassam o desconto permitido!";
              toast.warning(errorMessage, {
                autoClose: 8000,
              });
            }
          } else {
            toast.warning(response.message);
          }
        }
      })
      .catch((error) => {
        console.warn("error", error);
        setFormaDesconto(0);
      })
      .finally(() => {
        if (openBoleto) {
          setAfterCalulate(true);
        }
        setLoading(false);
      });
  };

  const recalculorNegociado = (
    parcelasRecalcula,
    atualziarRegua = true,
    ignoreAtualizarSelecionadas = false,
    valorNegociadoAtualizar = null
  ) => {
    const parcelas = JSON.parse(JSON.stringify(parcelasRecalcula));
    setLoading(true);
    setTextLoading("Calculando...");

    if (parcelas && parcelas.length === 0) {
      return;
    }

    const recalculoParcelasData = calculaAtrasoParcelasPorDataNegociaco(
      parcelas,
      selectedDate
    );
    const recalculoParcelas = calculaValoresParcelas(
      parametroCalculo,
      recalculoParcelasData,
      valorNegociadoAtualizar ?? valorInputCalculo,
      selectedDate
    );
    const recaculoPercentuaisParcela = calculaPerncetuaisDesconto(
      parametroCalculo,
      recalculoParcelas,
      Math.round((valorNegociadoAtualizar ?? valorInputCalculo) * 100) / 100
    );

    const parcelasTotais = recaculoPercentuaisParcela.filter(
      (item) => item.parcelaSelecionada
    );
    const vlNegContratosAtivos = parcelasTotais.reduce(
      (total, item) => Math.round((total + item.valorNegociado) * 100) / 100,
      0
    );
    const vlNegOriginalContratosAtivos = parcelasTotais.reduce(
      (total, item) =>
        Math.round((total + item.vl_Original_atualizado) * 100) / 100,
      0
    );
    const vlDescNegContratosAtivos = parcelasTotais.reduce(
      (total, item) => Math.round((total + item.vl_Desc_Max) * 100) / 100,
      0
    );

    setParcelasSelecionadas(recaculoPercentuaisParcela);

    if (!ignoreAtualizarSelecionadas) {
      setSelecionadasTable(
        recaculoPercentuaisParcela.filter((item) => item.parcelaSelecionada)
      );
      setTableData(recaculoPercentuaisParcela);
    }

    setValorNegociado(vlNegContratosAtivos);
    setValorInputCalculo(vlNegContratosAtivos);
    setValorDescMaxNeg(vlDescNegContratosAtivos);
    if (atualziarRegua) {
      setValorRNegociado(vlNegOriginalContratosAtivos);
      setValorRDescMaxNeg(vlDescNegContratosAtivos);
    }
    setLoading(false);
    return recaculoPercentuaisParcela;
  };

  const today = new Date();
  const handleDateChange = (date) => {
    setManualImputCalculo(false);
    setSelectedDate(date);

    calculoPost(
      0,
      parcelasSelecionadas
        .filter((item) => item.parcelaSelecionada)
        .map((item) => item.idParcela),
      0,
      date,
      true
    );
  };

  const handleCalculate = async (
    event,
    openModal = true,
    formaDesconto = 0
  ) => {
    const valInput = Math.round(valorInputCalculo * 100) / 100;

    const formaLivre = await getFormaLivreConfig();
    if (openModal) {
      try {
        openModal = false;
        if (financiadoData.coddatacob === "GVC") {
          if (formaLivre.gvc.indexOf(financiadoData.id_Grupo) > -1) {
            openModal = true;
          }
        }
        if (financiadoData.coddatacob === "Rodobens") {
          if (formaLivre.rodobens.indexOf(financiadoData.id_Grupo) > -1)
            openModal = true;
        }
      } catch (err) {
        console.warn("erroFormaLivre", err);
        openModal = false;
      }
    } else {
      openModal = false;
    }

    if (valorRDescMaxNeg > valInput) {
      if (openModal === true) {
        setShowFormaDescontoModal(true);
        return;
      } else {
        setShowFormaDescontoModal(false);
      }
    } else {
      formaDesconto = 0;
    }

    const calculos = recalculorNegociado(parcelasSelecionadas, false, false);
    let valor = valInput;
    if (calculos.filter((x) => x.faixaNova).length > 0) {
      valor = calculos
        .filter((x) => x.parcelaSelecionada)
        .reduce((total, item) => total + item.valorNegociado, 0);
      recalculorNegociado(parcelasSelecionadas, false, false, valor);
      setShowModalChangeCalc(true);
    }
    if (calculos.filter((x) => x.changeCusta).length > 0) {
      valor = calculos
        .filter((x) => x.parcelaSelecionada)
        .reduce((total, item) => total + item.valorNegociado, 0);
      recalculorNegociado(parcelasSelecionadas, false, false, valor);
      setShowModalChangeCusta(true);
    }

    const idsParcelas = parcelasSelecionadas
      .filter((item) => item.parcelaSelecionada)
      .map((item) => item.idParcela);
    calculoPost(valor, idsParcelas, formaDesconto);
  };

  const handleCalculateNeg = async (
    value,
    dueDate,
    selecionadas,
    openBoleto = true
  ) => {
    const valInput = Math.round(value * 100) / 100;
    const paramFormaLivre = await getParamFormaLivreConfig();

    setSelectedDate(dueDate);

    // recalculorNegociado(selecionadas, false, false);
    setValorInputCalculo(valInput);
    calculoPost(
      valInput,
      selecionadas
        .filter((item) => item.parcelaSelecionada)
        .map((item) => item.idParcela),
      paramFormaLivre,
      dueDate,
      openBoleto
    );
  };

  // const handleFreeCalculation = async (value) => {
  //   const valInput = Math.round(value * 100) / 100;
  //   const paramFormaLivre = await getParamFormaLivreConfig();

  //   setValorInputCalculo(valInput);
  //   calculoPost(
  //     valInput,
  //     getIDsParcelas(parcelasSelecionadas),
  //     paramFormaLivre,
  //     selectedDate,
  //     true
  //   );
  // };

  /*async function changeFormaDesconto(honor) {
    if (honor) {
      let arrParc = [];
      let vlHonorMax = 0;
      let vlTotal = 0;
      selecionadasTable.forEach((item) => {
        if (item.parcelaSelecionada) {
          vlHonorMax += item?.vlHoMaxDesc;
          vlTotal += item?.vlAtualizadoDescontoMax;

          let itemArr = arrParc.find((x) => x.contract === item.nrContrato);
          if (itemArr !== undefined) {
            itemArr.installment.push(item.nrParcela);
          } else {
            arrParc.push({
              contract: item.nrContrato,
              installment: [item.nrParcela],
            });
          }
        }
      });
      let resultado = vlTotal - vlHonorMax;
      let vlTotalMenosVlHonorMax = Number(resultado.toFixed(10));

      if (vlTotalMenosVlHonorMax > valorInputCalculo) {
        toast.warning(
          "Atenção: Não foi possível calcular os valores! Valores informados ultrapassam o desconto de honorários permitido!"
        );
        setShowFormaDescontoModal(false);
        return;
      }

      const payload = {
        value: formatCurrency(
          Math.round(valorInputCalculo * 100) / 100,
          false
        ).toString(),
        dueDate: formatDate(selectedDate.toISOString()),
        idGrouping: financiadoData.id_Agrupamento,
        crm: financiadoData.coddatacob,
        installmentJson: JSON.stringify(arrParc),
      };
      PostData(payload, "postSaveNeg")
        .then((x) => {
          if (x.success) {
            toast.success("Negociação enviada com sucesso!");
            handleCalculateNeg(
              valorInputCalculo,
              selectedDate,
              selecionadasTable,
              false
            );
          } else {
            toast.error("Erro ao enviar negociação!");
          }
        })
        .catch((e) => {
          toast.error("Erro ao enviar negociação!");
        })
        .finally((f) => {
          setShowFormaDescontoModal(false);
        });
    } else {
      setShowFormaDescontoModal(false);
    }
  } */

  async function changeFormaDesconto(honor) {
    console.log("honor", honor);
    if (honor > 0) {
      let arrParc = [];
      selecionadasTable.forEach((item) => {
        if (item.parcelaSelecionada) {
          let itemArr = arrParc.find((x) => x.contract === item.nrContrato);
          if (itemArr !== undefined) {
            itemArr.installment.push(item.nrParcela);
          } else {
            arrParc.push({
              contract: item.nrContrato,
              installment: [item.nrParcela],
            });
          }
        }
      });
      await CleanCalculoPost(
        selecionadasTable.flatMap((item) => item.idParcela),
        valorInputCalculo,
        1,
        selectedDate,
        async (response) => {
          getInstallmentTable(response);
          try {
            const parcs = response.negociacaoDto[0]?.parcelas?.filter(
              (x) => x.parcelaSelecionada
            );
            const vlComPermanenciaNegociado = parcs.reduce(
              (total, item) => total + item.vlComPermanenciaNegociado,
              0
            );
            const vlDespesasNegociado = parcs.reduce(
              (total, item) => total + item.vlDespesasNegociado,
              0
            );
            const vlHoNegociado = parcs.reduce(
              (total, item) => total + item.vlHoNegociado,
              0
            );
            const vlJurosNegociado = parcs.reduce(
              (total, item) => total + item.vlJurosNegociado,
              0
            );
            const vlMultaNegociado = parcs.reduce(
              (total, item) => total + item.vlMultaNegociado,
              0
            );
            const vlNotificacaoNegociado = parcs.reduce(
              (total, item) => total + item.vlNotificacaoNegociado,
              0
            );
            const vlTarifaNegociado = parcs.reduce(
              (total, item) => total + item.vlTarifaNegociado,
              0
            );
            const vlPrincipal =
              response.negociacaoDto[0]?.vlNegociacao -
              (vlComPermanenciaNegociado +
                vlDespesasNegociado +
                vlHoNegociado +
                vlJurosNegociado +
                vlMultaNegociado +
                vlNotificacaoNegociado +
                vlTarifaNegociado);

            const payNeg = {
              idAgrupamento: financiadoData.id_Agrupamento,
              nrContrato: response.negociacaoDto[0]?.nrContrato,
              idContrato: response.negociacaoDto[0].idContrato,
              idFinanciado: response.negociacaoDto[0].idFinanciado,
              parcelas: parcs.flatMap((x) => x.idParcela),
              vlNegociacao: response.negociacaoDto[0].vlNegociacao,
              dtNegociacao: selectedDate,
              valorPrincipal: vlPrincipal,
              valorCorrecao: 0,
              juros: vlJurosNegociado,
              multa: vlMultaNegociado,
              comissaoPermanencia: vlComPermanenciaNegociado,
              honorarios: vlHoNegociado,
              descontoAutorizado: true,
              custas: vlDespesasNegociado,
              notificacao: vlNotificacaoNegociado,
              valorTarifa: vlTarifaNegociado,
              vlMinimoRegua:
                response.negociacaoDto[0].vlDividaAtualizadaMaxDesc,
              permiteNegociar: response.negociacaoDto[0].permiteNegociar,
              crm: financiadoData.coddatacob,
            };

            const rep = await PostData(
              payNeg,
              "postSalvarNegociacaoCalculoLivre"
            );
            if (rep?.success === true) {
              toast.success("Negociação enviada para Aprovação!");
            } else {
              toast.error("Ocorreu um problema ao salvar a negociação!");
            }
          } catch (e) {
            toast.error("Ocorreu um problema ao salvar a negociação!");
          }
        },
        (response) => {
          toast.error(
            response?.message
              ?.replace("Retorno Api. ", "")
              ?.replace(/\[/g, "")
              ?.replace(/\]/g, "")
              ?.replace(/"/g, "")
              ?.trim()
          );
        }
      );
      setShowFormaDescontoModal(false);
    } else {
      setShowFormaDescontoModal(false);
    }
  }
  const handleInputCalculo = (value) => {
    setManualImputCalculo(true);
    // Remove caracteres que não são dígitos
    const sanitizedValue = value.replace(/[^\d]/g, "");
    // Formata o valor enquanto o usuário digita
    const formattedValue = formatarParaMoeda(sanitizedValue);
    const formattedValueF = formattedValue.replace(/\./g, "").replace(",", ".");
    setValorInputCalculo(parseFloat(formattedValueF));
  };

  const formatarParaMoeda = (valor) => {
    let valorNumerico = valor.replace(/\D/g, "");
    valorNumerico = (parseInt(valorNumerico, 10) / 100).toFixed(2);
    valorNumerico = valorNumerico.replace(".", ",");

    // Dividindo a string em parte inteira e decimal
    let partes = valorNumerico.split(",");
    let parteInteira = partes[0];
    let parteDecimal = partes[1];

    // Adicionando os pontos como separadores de milhares
    let parteInteiraFormatada = parteInteira.replace(
      /\B(?=(\d{3})+(?!\d))/g,
      "."
    );

    // Reconstruindo o valor formatado
    valorNumerico = parteInteiraFormatada + "," + parteDecimal;

    return valorNumerico;
  };
  /*Fim -- Formulario da Calcular */

  /* Checagem dos contratos com pendencias */
  const [checkPendingIssues, setCheckPendingIssues] = useState(false);
  const handleCheckPendingIssues = (parcelas) => {
    setCheckPendingIssues(
      parcelas.some(
        (parcela) => parcela.dt_Venc_Boleto !== null && parcela.status === "A"
      )
    );
  };

  const showBoletosPendentes = () => {
    if (
      isShowDetalhesParcela &&
      parcelasSelecionadas &&
      parcelasSelecionadas.length > 0
    )
      return parcelasSelecionadas.map((parcela) => {
        if (parcela.dt_Venc_Boleto !== null && parcela.status === "A") {
          return (
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                marginBottom: "5px",
                padding: "5px",
                color: "black",
                boxShadow: "0px 0px 4px -3px #000000bf",
                borderRadius: "5px",
              }}
            >
              <div style={{ display: "flex", flexDirection: "row" }}>
                <div
                  style={{
                    width: "80px",
                    textAlign: "right",
                    paddingInline: "5px",
                  }}
                >
                  Parcela n°:
                </div>
                <div>{String(parcela.nr_Parcela).padStart(3, "0")}</div>
              </div>
              <div style={{ display: "flex", flexDirection: "row" }}>
                <div
                  style={{
                    width: "80px",
                    textAlign: "right",
                    paddingInline: "5px",
                  }}
                >
                  Boleto n°:
                </div>
                <div>{parcela.nr_Boleto}</div>
              </div>
              <div style={{ display: "flex", flexDirection: "row" }}>
                <div
                  style={{
                    width: "80px",
                    textAlign: "right",
                    paddingInline: "5px",
                  }}
                >
                  Data:
                </div>
                <div>{formatDate(parcela.dt_Venc_Boleto)}</div>
              </div>
              <div style={{ display: "flex", flexDirection: "row" }}>
                <div
                  style={{
                    width: "80px",
                    textAlign: "right",
                    paddingInline: "5px",
                  }}
                >
                  Valor:
                </div>
                <div>{formatCurrency(parcela.vl_Boleto, false)}</div>
              </div>
              <div style={{ display: "flex", flexDirection: "row" }}>
                <div
                  style={{
                    width: "80px",
                    textAlign: "right",
                    paddingInline: "5px",
                  }}
                >
                  Observação:
                </div>
                <div>{parcela.neg_Descricao}</div>
              </div>
            </div>
          );
        }
      });
    return "";
  };

  /* Simulacoes */
  const [showSimulacaoModal, setShowSimulacaoModal] = useState(false);
  const handleSimulacaoClick = () => {
    setShowSimulacaoModal(true);
  };

  /* Detalhes dos Caclulos */
  const [showDetalheCalculoModal, setShowDetalheCalculoModal] = useState(false);
  const [showCalculoLivreModal, setShowCalculoLivreModal] = useState(false);

  /* Boletos */
  const [boletoData, setBoletoData] = useState([]);
  const [showBoletoModal, setShowBoletoModal] = useState(false);

  /* Pendências */
  const [showPendenciasModal, setShowPendenciasModal] = useState(false);

  const handleConfirmTicketExpired = async (choice) => {
    if (choice) {
      const boletos = context.boletos.find(
        (x) => x.status === "A" && Date.parse(x.dtVenc) < Date.now()
      );
      await handleDetalhesBoleto(boletos.idNegociacao);
    } else {
      handleOpenGerarBoleto();
    }
    setShowConfirmModal(false);
  };

  const handleConfirmTicketOpen = (choice) => {
    if (choice) {
      handleOpenGerarBoleto();
    }
    setShowConfirmModal(false);
  };

  const handleOpenGerarBoleto = () => {
    const data = {
      idAgrupamento: financiadoData.id_Agrupamento,
      dtNegociacao: selectedDate,
      vlNegociado: valorNegociado,
      parcelas: parcelasSelecionadas
        .filter((item) => item.parcelaSelecionada)
        .map((item) => item.idParcela),
      formaDesconto: formaDesconto,
    };
    setBoletoData(data);
    setShowBoletoModal(true);
  };

  const handleDetalhesBoleto = async (IdNegociacao) => {
    const data = {
      IdNegociacao: IdNegociacao,
      numeroContrato: financiadoData.numero_Contrato,
    };
    const detBoletos = await GET_DATA(
      "Datacob/Negociacoes/Parcelas/Boleto",
      data
    );
    setDetalhesBoleto(detBoletos[0]);
    setShowModalDetalhesBoleto(true);
  };

  const handleGerarBoleto = () => {
    if (pendencias.length > 0) {
      toast.warning("Atenção! Existem pendências para esta negociação!");
      return;
    }
    const boletos = context.boletos.filter((x) => x.status === "A");
    if (boletos.length > 0) {
      const parcelas = parcelasSelecionadas
        .filter((item) => item.parcelaSelecionada)
        .map((item) => item.nr_Parcela);
      let existeBoleto = false;

      for (const nr of parcelas) {
        const search = boletos.find((x) => x.nrParcelas.indexOf(nr) > -1);
        if (search) {
          existeBoleto = true;
          let texto = "";
          if (Date.parse(search.dtVenc) < Date.now()) {
            texto =
              "Atenção! Existe um boleto em aberto mas com data de vencimento expirada. Deseja cancelar esse boleto e gerar um novo?";

            setOnCloseConfirmModal(() => {
              return handleConfirmTicketExpired;
            });
          } else {
            texto = `Atenção! Já existe um boleto gerado para as parcelas ${search?.nrParcelas
              ?.map((x) => String(x ?? 0).padStart(3, "0"))
              .join(", ")}! Deseja continuar?`;

            setOnCloseConfirmModal(() => {
              return handleConfirmTicketOpen;
            });
          }
          setShowConfirmModal(true);
          setTextConfirmModal(texto);
        }
      }
      if (!existeBoleto) {
        handleOpenGerarBoleto();
      }
    } else {
      handleOpenGerarBoleto();
    }
  };

  const CleanCalculoPost = async (
    idsParcelas,
    vlNeg = 0,
    formaDesconto = 0,
    dtNegociacao = todayFormat,
    apiSuccessFunc = () => {},
    apiErrorFunc = () => {},
    errorFunc = (error) => {},
    finallyFunc = () => {}
  ) => {
    const payload = {
      idAgrupamento: financiadoData.id_Agrupamento,
      dtNegociacao: formatDateGlobaltoSimplified(dtNegociacao),
      vlNegociado: vlNeg,
      parcelas: idsParcelas,
      formaDesconto: formaDesconto,
    };
    PostData(payload, "postCalcularNegociacao")
      .then((response) => {
        if (
          response.data &&
          response.data.negociacaoDto &&
          response.data.negociacaoDto.length > 0
        ) {
          apiSuccessFunc(response.data);
        }
        if (response.success === false) {
          apiErrorFunc(response);
        }
      })
      .catch((error) => {
        errorFunc(error);
      })
      .finally(() => {
        finallyFunc();
      });
  };

  const calcTotalValue = (item) => {
    return (
      item.vlOriginal +
      item.vlHoOriginal +
      item.vlJurosOriginal +
      item.vlMultaOriginal +
      item.vlComPermanenciaOriginal +
      item.vlDespesasOriginal +
      item.vlNotificacaoOriginal +
      item.vlTarifaOriginal
    );
  };

  const handleChangeSelectContract = async (e) => {
    setSelectedContract(e.target.value);

    const dt = new Date(
      selectedDate.setHours(selectedDate.getHours() - 3)
    ).toISOString();

    setSelectAll(false);

    setLoading(true);
    setTextLoading("Calculando...");
    // setParcelasVencidasChecked(true);
    await CleanCalculoPost(
      parcelasAbertas
        .filter(
          (x) =>
            x.dt_Vencimento <= todayFormat &&
            x.numero_Contrato.replaceAll(" ", "") ===
              e.target.value.replaceAll(" ", "")
        )
        .map((item) => item.id_Parcela),
      0,
      0,
      dt,
      validacoesTelaApiSuccess,
      validacoesTelaApiError,
      cleanCalculoErrorFunc,
      cleanCalculoFinnalyFunc
    );
  };

  const handleCheckVencidas = async (e) => {
    // setSelectedContract(e.target.value);

    const dt = new Date(
      selectedDate.setHours(selectedDate.getHours() - 3)
    ).toISOString();

    setSelectAll(false);
    parcelasVencidasChecked.current = e.target.checked;

    setLoading(true);
    setTextLoading("Calculando...");
    if (e.target.checked) {
      await CleanCalculoPost(
        parcelasAbertas
          .filter((x) => x.dt_Vencimento <= todayFormat)
          .map((item) => item.id_Parcela),
        0,
        0,
        dt,
        validacoesTelaApiSuccess,
        validacoesTelaApiError,
        cleanCalculoErrorFunc,
        cleanCalculoFinnalyFunc
      );
    } else {
      await CleanCalculoPost(
        parcelasAbertas
          .filter((x) => x.dt_Vencimento > todayFormat)
          .map((item) => item.id_Parcela),
        0,
        0,
        dt,
        validacoesTelaApiSuccess,
        validacoesTelaApiError,
        cleanCalculoErrorFunc,
        cleanCalculoFinnalyFunc
      );
    }
  };

  const handleSelectAll = async (e) => {
    // setParcelasVencidasChecked(false);
    parcelasVencidasChecked.current = false;

    const selecionadas = structuredClone(parcelasSelecionadas)
      .filter((e) => {
        if (selectedContract === "") return true;
        return e.nrContrato === selectedContract;
      })
      .map((item) => {
        item.parcelaSelecionada = e.target.checked;
        return item;
      });

    const idsSelecionadas = selecionadas
      .filter((item) => item.parcelaSelecionada)
      .map((item) => item.idParcela);

    // if (arraysAreEqual(idsSelecionadas, getIDsParcelas(parcelasOld))) {
    //   return;
    // }

    setSelectAll(e.target.checked);

    if (e.target.checked) {
      setLoading(true);
      setTextLoading("Calculando...");
      CleanCalculoPost(
        idsSelecionadas,
        0,
        0,
        selectedDate,
        (response) => {
          getInstallmentTable(response);
        },
        (response) => {},
        cleanCalculoErrorFunc,
        cleanCalculoFinnalyFunc
      );
    } else {
      setTableData(
        tableData.map((item) => {
          item.parcelaSelecionada = false;
          return item;
        })
      );
    }
  };

  return (
    <div>
      <CRow className="mb-2">
        <CCol>
          <h1>Negociar</h1>
        </CCol>
        <CCol md="4" className="d-flex justify-content-end mr-0">
          <CButton
            color="info"
            className="mr-2"
            onClick={() => setShowDetalhesParametrosModal(true)}
            disabled={
              !validadacaoTela ||
              parcelasAbertas.length === 0 ||
              !parcelasAbertas ||
              !checkPermission(
                permissaoNegociacaoDetalhesCalculo.modulo,
                "View",
                permissaoNegociacaoDetalhesCalculo.submodulo
              )
            }
            title={inforPermissions(permissaoNegociacaoDetalhesCalculo).view}
          >
            <i className="cil-calculator mr-2" /> Parâmetros de cálculo
          </CButton>
          <CButton
            color="info"
            className="mr-2"
            onClick={() => setShowDetalheCalculoModal(true)}
            disabled={
              !validadacaoTela ||
              parcelasAbertas.length === 0 ||
              !parcelasAbertas ||
              !checkPermission(
                permissaoNegociacaoDetalhesCalculo.modulo,
                "View",
                permissaoNegociacaoDetalhesCalculo.submodulo
              )
            }
            title={inforPermissions(permissaoNegociacaoDetalhesCalculo).view}
          >
            <i className="cil-calculator mr-2" /> Detalhes do Cálculo
          </CButton>
          <CButton
            color="info"
            className="mr-2"
            onClick={() => setShowCalculoLivreModal(true)}
            disabled={
              !validadacaoTela ||
              parcelasAbertas.length === 0 ||
              !parcelasAbertas ||
              !checkPermission(
                permissaoNegociacaoDetalhesCalculo.modulo,
                "View",
                permissaoNegociacaoDetalhesCalculo.submodulo
              )
            }
            title={inforPermissions(permissaoNegociacaoDetalhesCalculo).view}
            hidden={!(calculoLivrePermissao || userEnableManualCalcNegotiation)}
          >
            Cálculo Livre
          </CButton>
          <CButton
            color="info"
            onClick={handleGerarBoleto}
            disabled={
              !validadacaoTela ||
              parcelasAbertas.length === 0 ||
              !parcelasAbertas ||
              !checkPermission(
                permissaoNegociacaoBoleto.modulo,
                "Create",
                permissaoNegociacaoBoleto.submodulo
              ) ||
              !afterCalulate
            }
            title={inforPermissions(permissaoNegociacaoBoleto).create}
          >
            <i className="cil-file mr-2" /> Emitir Boleto
          </CButton>
        </CCol>
      </CRow>
      {!validadacaoTela || validadacaoTelaLoading ? (
        <CRow>
          <CCol style={{ textAlign: "center", margin: "12px 0" }}>
            <CCard>
              <CCardBody style={{ padding: "24px" }}>
                {!validadacaoTela ? textValidacao : <CardLoading />}
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
      ) : (
        <>
          <CRow>
            <CCol xs="8">
              <CCard>
                <CCardBody>
                  <DadosContratos />
                </CCardBody>
              </CCard>
            </CCol>
            <CCol xs="4">
              <CCard
                style={{
                  backgroundColor: "#153860",
                  color: "white",
                  marginBottom: "5px",
                }}
              >
                <CCardBody>
                  <div
                    style={{
                      fontSize: "20px",
                      fontWeight: "bold",
                    }}
                  >
                    Calculadora
                  </div>
                  <div
                    style={{
                      fontSize: "20px",
                      fontWeight: "bold",
                      color: "#ff5757",
                    }}
                  >
                    <CLabel className="mr-2">Total Negociado:</CLabel>
                    R$ {formatThousands(valorNegociado)}
                  </div>
                  <CRow>
                    <CCol xs="4">
                      <ReactDatePicker
                        selected={selectedDate}
                        onChange={handleDateChange}
                        className="form-control subtle"
                        minDate={today}
                        dateFormat="dd/MM/yyyy"
                        title={inforPermissions(permissaoNegociacao).create}
                        disabled={
                          loading ||
                          !checkPermission(
                            permissaoNegociacao.modulo,
                            "Create",
                            permissaoNegociacao.submodulo
                          )
                        }
                        onKeyDown={(e) => e.preventDefault()}
                      />
                    </CCol>
                    <CCol xs="4">
                      <input
                        style={{
                          width: "100%",
                          paddingLeft: "8px",
                          justifyContent: "start",
                        }}
                        className="subtle"
                        type="text"
                        value={formatCurrency(valorInputCalculo, false)}
                        onChange={(e) => handleInputCalculo(e.target.value)}
                        title={inforPermissions(permissaoNegociacao).create}
                        disabled={
                          loading ||
                          !checkPermission(
                            permissaoNegociacao.modulo,
                            "Create",
                            permissaoNegociacao.submodulo
                          ) ||
                          !liberarManual
                        }
                      />
                    </CCol>
                    <CCol xs="4">
                      <CButton
                        color="info"
                        onClick={handleCalculate}
                        title={inforPermissions(permissaoNegociacao).create}
                        disabled={
                          loading ||
                          !checkPermission(
                            permissaoNegociacao.modulo,
                            "Create",
                            permissaoNegociacao.submodulo
                          )
                        }
                      >
                        {loading ?? <CSpinner size="lg" />}
                        <span>Calcular</span>
                      </CButton>
                    </CCol>
                  </CRow>{" "}
                </CCardBody>
              </CCard>
              <CardNegociacaoStatus
                parcelas={tableData}
                handleCalculateNeg={handleCalculateNeg}
              />
            </CCol>
          </CRow>
          <CRow className="mb-3">
            <CCol
              style={{
                borderRadius: "10px",
              }}
            >
              <CCard>
                <CCardBody>
                  {loading ? (
                    <CardLoading Title={textLoading} />
                  ) : (
                    // parametroCalculo && (
                    <>
                      {/* <CRow className="mb-2">
                        <CCol xs="12" style={{ textAlign: "right" }}>
                          <CButton
                            color="primary"
                            onClick={() => {
                              setShowBoletoStatusModal(true);
                            }}
                          >
                            Visualizar Status Boletos Coringas
                          </CButton>
                        </CCol>
                      </CRow> */}
                      <CRow className="mb-2">
                        <CCol xs="8">
                          <div style={{ padding: "20px" }}>
                            <NegociacaoBar
                              vlNegociacao={
                                Math.round(valorRNegociado * 100) / 100
                              }
                              vlMinimoNegociacao={
                                Math.round(valorRDescMaxNeg * 100) / 100
                              }
                              onValueChange={(value) => {
                                setManualImputCalculo(true);
                                setValorInputCalculo(value);
                              }}
                              atualizarValor={atualizarValorRegua}
                            />
                          </div>
                        </CCol>
                        <CCol
                          xs="4"
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <CRow>
                            <CCard style={{ width: "200px" }}>
                              <CCardHeader
                                style={{
                                  textAlign: "center",
                                  fontWeight: "bold",
                                  fontSize: "17px",
                                  padding: "2px",
                                }}
                              >
                                Total Original
                              </CCardHeader>
                              <CCardBody
                                style={{
                                  textAlign: "center",
                                  fontSize: "16px",
                                  padding: "2px",
                                }}
                              >
                                {formatCurrency(valorRNegociado)}
                              </CCardBody>
                            </CCard>
                            {pendencias?.length > 0 && (
                              <CCard style={{ width: "200px" }}>
                                <CTooltip
                                  content={
                                    <span>
                                      Data: &nbsp;
                                      {formatDate(
                                        pendencias[0]?.dataPendencia
                                      )}{" "}
                                      <br /> Valor:&nbsp;
                                      {formatCurrency(
                                        pendencias[0]?.valorPendencia
                                      )}{" "}
                                      <br /> Obs:&nbsp;
                                      {pendencias[0]?.observacoes}
                                    </span>
                                  }
                                  placement="top"
                                >
                                  <div>
                                    <CCardHeader
                                      style={{
                                        textAlign: "center",
                                        fontWeight: "bold",
                                        fontSize: "17px",
                                        padding: "2px",
                                      }}
                                    >
                                      <i className="cil-warning" /> Pendências
                                    </CCardHeader>
                                    <CCardBody
                                      style={{
                                        textAlign: "center",
                                        fontSize: "16px",
                                        padding: "2px",
                                      }}
                                    >
                                      <CButton
                                        color="warning"
                                        onClick={() =>
                                          setShowPendenciasModal(true)
                                        }
                                      >
                                        Visualizar
                                      </CButton>
                                    </CCardBody>
                                  </div>
                                </CTooltip>
                              </CCard>
                            )}
                          </CRow>
                          {checkPendingIssues && (
                            <CRow>
                              <CCard style={{ width: "200px" }}>
                                <CCardHeader
                                  style={{
                                    textAlign: "center",
                                    fontWeight: "bold",
                                    fontSize: "17px",
                                    padding: "2px",
                                  }}
                                >
                                  Aviso{checkPendingIssues}
                                </CCardHeader>
                                <CCardBody
                                  style={{
                                    textAlign: "center",
                                    fontSize: "16px",
                                    padding: "2px",
                                    color: "red",
                                    position: "relative",
                                    cursor: "help",
                                  }}
                                  onMouseEnter={() =>
                                    setIsShowDetalhesParcela(true)
                                  }
                                  onMouseLeave={() =>
                                    setIsShowDetalhesParcela(false)
                                  }
                                >
                                  Pendência nas Parcelas
                                  {isShowDetalhesParcela && (
                                    <div
                                      style={{
                                        position: "absolute",
                                        zIndex: "999",
                                        background: "white",
                                        fontSize: "12px",
                                        width: "300px",
                                        borderRadius: "5px",
                                        left: "-110px",
                                        top: "30px",
                                      }}
                                    >
                                      {showBoletosPendentes()}
                                    </div>
                                  )}
                                </CCardBody>
                              </CCard>
                            </CRow>
                          )}
                        </CCol>
                      </CRow>
                      <CRow className="mb-4">
                        <CCol>
                          <CButton
                            color="success"
                            block
                            onClick={handleSimulacaoClick}
                            disabled={
                              !contratosAtivos ||
                              loading ||
                              !checkPermission(
                                permissaoNegociacao.modulo,
                                "View",
                                permissaoNegociacao.submodulo
                              )
                            }
                          >
                            {loading ?? <CSpinner size="lg" />}
                            <span>Simular negociações</span>
                          </CButton>
                        </CCol>
                      </CRow>
                      <CRow className="mt-2">
                        <CCol>
                          {/* <TableSelectItens
                            data={tableData}
                            columns={columns}
                            onSelectionChange={HandleInstallmentChange}
                            defaultSelectedKeys={selecionadasTable}
                            selectable={true}
                            heightParam="440px"
                          /> */}
                          <TableInstallment
                            HandleInstallmentChange={HandleInstallmentChange}
                            columns={columns}
                            selectAll={selectAll}
                            selectedContract={selectedContract}
                            selectedDate={selectedDate}
                            contratosAtivos={contratosAtivos}
                            tableData={tableData}
                            handleSelectAll={handleSelectAll}
                            handleChangeSelectContract={
                              handleChangeSelectContract
                            }
                            handleCheckVencidas={handleCheckVencidas}
                            parcelasVencidasChecked={
                              parcelasVencidasChecked.current
                            }
                            calcTotalValue={calcTotalValue}
                          />
                        </CCol>
                      </CRow>
                    </>
                    // )
                  )}
                </CCardBody>
              </CCard>
            </CCol>
          </CRow>
        </>
      )}

      {showBoletoModal && (
        <BoletoModal
          isOpen={showBoletoModal}
          onClose={() => setShowBoletoModal(false)}
          parcelas={parcelasSelecionadas
            .filter((item) => item.parcelaSelecionada)
            .map((item) => item.idParcela)}
          dados={boletoData}
          onSubmit={() => console.log()}
          freeCalcValue={freeCalcValue}
          calcResponse={calcResponse}
        />
      )}
      {showDetalheCalculoModal && (
        <DetalhesCalculoModal
          isOpen={showDetalheCalculoModal}
          onClose={() => setShowDetalheCalculoModal(false)}
          dados={parcelasSelecionadas.filter((item) => item.parcelaSelecionada)}
          parcelasSelecionadas={parcelasSelecionadas}
        />
      )}
      {showPendenciasModal && (
        <PendenciasModal
          isOpen={showPendenciasModal}
          onClose={() => setShowPendenciasModal(false)}
          pendencias={pendencias}
        />
      )}
      {showCalculoLivreModal && (
        <CalculoLivreModal
          isOpen={showCalculoLivreModal}
          onClose={() => {
            setShowCalculoLivreModal(false);
          }}
          valorNegociado={valorNegociado}
          parcelasSelecionadas={parcelasSelecionadas}
          //handleFreeCalculation={handleFreeCalculation}
          openBoletoModal={() => setShowBoletoStatusModal(true)}
          setFreeCalcValue={setFreeCalcValue}
          selectedDate={selectedDate}
        />
      )}
      {showSimulacaoModal && (
        <SimulacaoModal
          isOpen={showSimulacaoModal}
          onClose={() => setShowSimulacaoModal(false)}
          onSave={[]}
          isOcorrencia={false}
          cleanCalculoPost={CleanCalculoPost}
          valorMinRegua={valorRDescMaxNeg}
          valorMaxRegua={valorInputCalculo}
        />
      )}
      {showDetalhesParametrosModal && (
        <DetalhesParametrosModal
          isOpen={showDetalhesParametrosModal}
          onClose={() => setShowDetalhesParametrosModal(false)}
          data={parametroCalculo}
        />
      )}
      {showFormaDescontoModal && (
        <FormaDescontoModal
          isOpen={showFormaDescontoModal}
          onClose={(forma) => changeFormaDesconto(forma)}
        />
      )}
      {showBoletoStatusModal && (
        <CardBoletoStatus
          onClose={() => {
            setShowBoletoStatusModal(false);
          }}
        />
      )}
      {showConfirmModal && (
        <ConfirmModal
          isOpen={showConfirmModal}
          onClose={onCloseConfirmModal}
          texto={textConfirmModal}
        />
      )}
      {showModalDetalhesBoleto && (
        <DetalhesBoletoModal
          isOpen={showModalDetalhesBoleto}
          onClose={() => setShowModalDetalhesBoleto(false)}
          dados={detalhesBoleto}
          updateModal={() => {}}
        />
      )}
      {showModalChangeCalc && (
        <CModal
          show={showModalChangeCalc}
          onClose={() => setShowModalChangeCalc(false)}
          closeOnBackdrop={false}
          className="custom-modal"
        >
          <CModalHeader closeButton>Alteração do Parâmetro</CModalHeader>
          <CModalBody>
            <CRow>
              <CCol>
                <div>
                  Devido as condições de cálculo, houve uma troca de parâmetro.
                  O valor cheio foi aplicado.
                </div>
              </CCol>
            </CRow>
          </CModalBody>
          <CModalFooter>
            <CButton color="info" onClick={() => setShowModalChangeCalc(false)}>
              Ok
            </CButton>
          </CModalFooter>
        </CModal>
      )}
      {showModalChangeCusta && (
        <CModal
          show={showModalChangeCusta}
          onClose={() => setShowModalChangeCusta(false)}
          closeOnBackdrop={false}
          className="custom-modal"
        >
          <CModalHeader closeButton>Alteração do Parâmetro</CModalHeader>
          <CModalBody>
            <CRow>
              <CCol>
                <div>
                  Devido as condições de cálculo, houve uma troca do valor de
                  despesas. O valor cheio foi aplicado.
                </div>
              </CCol>
            </CRow>
          </CModalBody>
          <CModalFooter>
            <CButton
              color="info"
              onClick={() => setShowModalChangeCusta(false)}
            >
              Ok
            </CButton>
          </CModalFooter>
        </CModal>
      )}
    </div>
  );
};

export default Negociar;
