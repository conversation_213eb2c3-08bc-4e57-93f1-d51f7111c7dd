import {
  <PERSON>utton,
  <PERSON>ard,
  CCardBody,
  CCol,
  CDataTable,
  CRow,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import AprovacaoModal from "./partials/aprovacaoModal.tsx";
import EditTermoModal from "./partials/EditTermoModal.tsx";
import { ItemFilaAprovacaoType } from "../types/ItemFilaAprovacaoType.ts";
import { formatDate } from "src/reusable/helpers.js";
import LoadingComponent from "src/reusable/Loading.js";
import { postApiQueryFile } from "src/reusable/functions.js";
import { toast } from "react-toastify";

const FilaAprovacao = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingDocument, setIsLoadingDocument] = useState(false);
  const [filaAprovacao, setFilaAprovacao] = useState([]);

  const [showAprovacaoModal, setShowAprovacaoModal] = useState(false);
  const [showEditTermoModal, setShowEditTermoModal] = useState(false);
  const [itemSelecionado, setItemSelecionado] =
    useState<ItemFilaAprovacaoType>(null);
  const [statusAprovacao, setStatusAprovacao] = useState<string>("");

  const getFilaAprovacaoCartasETermos = async () => {
    setIsLoading(true);
    await GetData(null, "filaAprovacaoCartasETermos")
      .then((resultado: ItemFilaAprovacaoType[]) => {
        if (resultado) {
          setFilaAprovacao(resultado);
        } else {
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const colums = [
    {
      key: "financiado",
      label: "Cliente",
    },
    {
      key: "nrContrato",
      label: "Contrato",
    },
    {
      key: "operador",
      label: "Operador",
    },
    {
      key: "createdAt",
      label: "Data Criação",
      filter: false,
    },
    {
      key: "status",
      label: "Status",
      filter: false,
    },
    {
      key: "tipoDescricao",
      label: "Tipo",
      filter: false,
    },
    {
      key: "actions",
      label: "Ações",
      filter: false,
    },
  ];
  const IconButton = ({
    icon,
    color,
    titulo,
    onClick,
    disabled = false,
    className = "",
  }) => {
    return (
      <CButton
        title={titulo}
        className={className}
        style={{
          border: "solid 1px",
          borderColor: color,
          color: color,
          padding: "2px 4px",
        }}
        onClick={onClick}
        disabled={disabled}
      >
        <i className={icon} />
      </CButton>
    );
  };
  const handleClickAprovar = (item: ItemFilaAprovacaoType, status: string) => {
    setShowAprovacaoModal(true);
    setItemSelecionado(item);
    setStatusAprovacao(status);
  };
  const handleCloseAprovacaoModal = async (updateData = false) => {
    setShowAprovacaoModal(false);
    setItemSelecionado(null);
    setStatusAprovacao("");
    if (updateData) await getFilaAprovacaoCartasETermos();
  };

  const handleClickBaixar = async (item: ItemFilaAprovacaoType) => {
    if (item.id === null || item.id === "") {
      toast.error("Não foi possível baixar o documento");
      return;
    }
    if (item.status !== "Aprovado") {
      toast.warning("Pedido não foi aprovado");
      return;
    }
    setIsLoadingDocument(true);
    try {
      const res = await postApiQueryFile(
        "postPedidoCartasEtermosGenerateByPedido",
        `idPedido=${item.id}`
      );
      if (res.ok) {
        const blob = await res.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        var time = new Date().getTime();
        link.download = `${time}.pdf`;
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
      } else {
        toast.error("Não foi possível baixar o documento");
      }
    } catch (err) {
      toast.error(err);
    }
    setIsLoadingDocument(false);
  };

  const handleClickEditar = (item: ItemFilaAprovacaoType) => {
    if (item.tipo === 3) {
      handleEditTermo(item);
    }
  };

  const handleEditTermo = (item: ItemFilaAprovacaoType) => {
    setShowEditTermoModal(true);
    setItemSelecionado(item);
  };

  const handleCloseEditTermoModal = async (updateData = false) => {
    setShowEditTermoModal(false);
    setItemSelecionado(null);
    if (updateData) await getFilaAprovacaoCartasETermos();
  };

  useEffect(() => {
    getFilaAprovacaoCartasETermos();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const renderActions = (item: ItemFilaAprovacaoType) => {
    return (
      <td>
        <IconButton
          className="ml-2"
          icon={"cil-check"}
          color={"blue"}
          titulo={"Aprovar"}
          onClick={() => handleClickAprovar(item, "Aprovado")}
        />
        <IconButton
          className="ml-2"
          icon={"cil-pencil"}
          color={"orange"}
          titulo={"Editar"}
          onClick={() => handleClickEditar(item)}
        />
        <IconButton
          className="ml-2"
          icon={"cil-x-circle"}
          color={"red"}
          titulo={"Rejeitar"}
          onClick={() => handleClickAprovar(item, "Rejeitado")}
        />
        <IconButton
          className="ml-2"
          icon={"cil-arrow-thick-bottom"}
          color={"blue"}
          titulo={"Baixar Documento"}
          onClick={() => handleClickBaixar(item)}
          disabled={isLoadingDocument}
        />
      </td>
    );
  };
  return (
    <div>
      <CRow>
        <CCol>
          <h1>Fila de Aprovação de Documentos</h1>
        </CCol>
      </CRow>

      <CRow>
        <CCol>
          <CCard>
            <CCardBody>
              {isLoading ? (
                <div className="mt-2">
                  <LoadingComponent />
                </div>
              ) : (
                <>
                  <CDataTable
                    items={filaAprovacao}
                    fields={colums}
                    striped
                    hover
                    columnFilter
                    scopedSlots={{
                      createdAt: (item: ItemFilaAprovacaoType) => (
                        <td>{formatDate(item.createdAt)}</td>
                      ),
                      actions: (item: ItemFilaAprovacaoType) =>
                        renderActions(item),
                    }}
                    itemsPerPage={15}
                    pagination
                    noItemsViewSlot={
                      <h5 className="text-center">
                        Sem resultados para exibir.
                      </h5>
                    }
                  />
                </>
              )}
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
      <CRow className={""}>
        <CCol className={"d-flex justify-content-end"}>
          <CButton color="danger" className={"mr-2"} onClick={() => {}}>
            Sair
          </CButton>
          <CButton color="success" onClick={() => {}}>
            Exportar Excel
          </CButton>
        </CCol>
      </CRow>
      {showAprovacaoModal && (
        <AprovacaoModal
          isOpen={showAprovacaoModal}
          item={itemSelecionado}
          statusAprovacao={statusAprovacao}
          onClose={(updateData) => {
            handleCloseAprovacaoModal(updateData);
          }}
        />
      )}
      {showEditTermoModal && (
        <EditTermoModal
          isOpen={showEditTermoModal}
          item={itemSelecionado}
          onClose={(updateData) => {
            handleCloseEditTermoModal(updateData);
          }}
        />
      )}
    </div>
  );
};

export default FilaAprovacao;
