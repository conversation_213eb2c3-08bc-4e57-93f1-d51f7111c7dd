import React, { useEffect, useState } from "react";

import "react-datepicker/dist/react-datepicker.css";

import {
  CButton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalTitle,
  CLabel,
  CModalFooter,
  CRow,
  CCol,
} from "@coreui/react";
import LoadingComponent from "src/reusable/Loading";
import { getApi, postApi } from "src/reusable/functions";
import Select from "react-select";

const DeleteCustasModal = ({ isOpen, onClose, custaId, contratoId }) => {
  const [loading, setLoading] = useState(true);
  const [motives, setMotives] = useState([]);
  const [selectedMotive, setSelectedMotive] = useState(null);
  const [error, setError] = useState(false);

  const getMotive = async () => {
    try {
      const ret = await getApi({}, "getCustasMotivoDevolucao");
      if (ret?.status === 400) throw new Error(ret.errors);
      setMotives(
        ret.map((mot) => ({
          label: mot.descricao,
          value: mot.id_Motivo_Devolucao,
        }))
      );
    } catch (err) {
      console.log(err);
    }
    setLoading(false);
  };

  const HandleSelectMotive = (e) => {
    setSelectedMotive(e);
  };

  useEffect(() => {
    getMotive();
  }, []);

  const handleSave = async () => {
    setLoading(true);
    if (selectedMotive === null) {
      setError(true);
      return;
    }
    const payload = {
      idContrato: contratoId,
      custas: {
        idCustas: custaId,
        idMotivoDevolucao: selectedMotive.value,
      },
    };
    try {
      const ret = await postApi(payload, "postCustasDevolver");

      if (ret?.status === 400) throw new Error(ret.errors);
      if (!ret?.success) throw new Error(ret.message);

      onClose();
    } catch (err) {
      console.log(err);
    }
    setLoading(false);
  };

  return (
    <CModal
      className="custom-modal"
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
    >
      <CModalHeader closeButton>
        <CModalTitle>Devolução de Despesa</CModalTitle>
      </CModalHeader>
      <CModalBody>
        {loading && <LoadingComponent />}
        {!loading && (
          <CRow>
            <CCol md="2">
              <CLabel>Motivo:</CLabel>
            </CCol>
            <CCol>
              <Select
                options={motives}
                onChange={HandleSelectMotive}
                placeholder={"Selecione"}
                className={error ? "border-danger rounded" : ""}
              />
            </CCol>
          </CRow>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="success" className="mr-2" onClick={handleSave}>
          Confirmar
        </CButton>
        <CButton color="danger" className="mr-2" onClick={onClose}>
          Cancelar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default DeleteCustasModal;
