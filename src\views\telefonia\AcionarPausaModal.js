import React, { useState, useEffect } from "react";
import Select from "react-select";
import {
  CButton,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CRow,
  CCol,
} from "@coreui/react";
import LoadingComponent from "src/reusable/Loading";
import {
  listarPausas,
  listarPausasTactium,
  pausarLigacao,
} from "src/config/telephonyFunctions";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const AcionarPausaModal = ({ isOpen, onClose, onSubmit }) => {
  const telephonyData = localStorage.getItem("telephonyData")
    ? JSON.parse(localStorage.getItem("telephonyData"))
    : [];

  const userTelephony = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : [];

  const [pausaOptions, setPausaOptions] = useState([]);
  const [selectedPausa, setSelectedPausa] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingConfirmar, setIsLoadingConfirmar] = useState(false);

  const handlePausaSelection = (selectedOption) => {
    setSelectedPausa(selectedOption);
  };

  async function getPausas() {
    setIsLoading(true);
    if (userTelephony.telephonyId === 2) {
      const options = await listarPausas(telephonyData.agentId);
      const filteredArray = options.filter((item) => item.id !== null);
      if (filteredArray) {
        const list = filteredArray.map((pausa) => ({
          value: pausa.id,
          label: pausa.description,
          code: pausa.code,
        }));
        setPausaOptions(list);
      }
    }

    if (userTelephony.telephonyId === 1) {
      const options = await listarPausasTactium();

      if (options) {
        const list = options.map((pausa) => ({
          value: pausa.id,
          label: pausa.motive,
        }));
        setPausaOptions(list);
      }
    }

    setIsLoading(false);
  }

  async function handleConfirmarPausa() {
    setIsLoadingConfirmar(true);
    const ramalTactium = localStorage.getItem("ramalTactium")
      ? JSON.parse(localStorage.getItem("ramalTactium"))
      : null;

    const payload = {
      agentIdOlos: telephonyData.agentId,
      reasonIdOlos:
        userTelephony.telephonyId === 2 ? selectedPausa.value : null,
      ramalTactium: userTelephony.telephonyId === 1 ? ramalTactium : null,
      reasonTactium: selectedPausa.label,
    };

    const response = await pausarLigacao(payload);
    if (response.data && response.data.mensagemTactium) {
      toast.info(response.data.mensagemTactium);
    }
    localStorage.setItem("motivoPausa", selectedPausa.label);
    localStorage.setItem("pausaId", selectedPausa.value);
    await new Promise((resolve) => setTimeout(resolve, 2500));
    setIsLoadingConfirmar(false);
    onSubmit(true);
  }

  useEffect(() => {
    if (isOpen) getPausas();
  }, [isOpen]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>
        <CModalTitle>Motivo da Pausa</CModalTitle>
      </CModalHeader>
      <CModalBody className="pb-0 mb-2" style={{ minHeight: "340px" }}>
        {isLoading ? (
          <CRow style={{ justifyContent: "center" }}>
            <LoadingComponent />
          </CRow>
        ) : (
          <CRow>
            <CCol>
              <Select
                size="sm"
                options={pausaOptions}
                value={pausaOptions.find(
                  (option) => option.id === selectedPausa.value
                )}
                onChange={handlePausaSelection}
                defaultMenuIsOpen
                placeholder={"Selecione"}
                isDisabled={isLoadingConfirmar}
              />
            </CCol>
          </CRow>
        )}
      </CModalBody>
      <CModalFooter className="my-0 py-1">
        <CButton
          color="secondary"
          onClick={onClose}
          disabled={isLoadingConfirmar}
        >
          Cancelar
        </CButton>
        <CButton
          color="info"
          onClick={handleConfirmarPausa}
          disabled={isLoadingConfirmar}
        >
          {isLoadingConfirmar ? <LoadingComponent /> : "Ok"}
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default AcionarPausaModal;
