import React, { useState } from "react";

import "react-datepicker/dist/react-datepicker.css";

import {
  <PERSON>ow,
  <PERSON>ard<PERSON><PERSON><PERSON>,
  CCardBody,
  <PERSON>ard,
  <PERSON>ardFooter,
  CButton,
  CCol,
} from "@coreui/react";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import DadosDaSimulacaoSimulacao from "./DadosDaSimulacaoSimulacao.tsx";
import { ApiResponse } from "src/types/common.ts";
import { BBCAcordo } from "src/types/commonBBC.ts";
import { useBBCContext } from "../pageContext/BBCContext.tsx";

const PostData = async (payload, endpoint = "BBCDealRegister", id="") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), payload, true, true, id);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const FormularioSimulacao = ({ onClickNovaSimulacao, onFinshAcordo, contrato }) => {
  const BBCContext = useBBCContext();

  // const contratosAtivos = localStorage.getItem("contratosAtivos")
  //   ? JSON.parse(localStorage.getItem("contratosAtivos"))
  //   : null;
  // const parcelasAbertas = contratosAtivos?.flatMap((item) =>
  //   item.parcelas.filter(
  //     (pItem) =>
  //       pItem.status === "A" && !pItem.nr_Acordo
  //   )
  // );
  // let financiadoData = localStorage.getItem("financiadoData") ? JSON.parse(localStorage.getItem("financiadoData")) : "";
  
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");

  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");

  const payload ={
    idAgrupamento: 0,
    idSimulation: ""
  };


  const GerarAcordo = async () => {
    let ret = false;
    setTitleAvisoLoading("Enviando Simulação para gerar o Acordo BBC")
    setMsgAvisoLoading(`Enviando dados para o Acordo...`)
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(payload, "BBCDealRegister")
      .then((data: ApiResponse<BBCAcordo>) => {
        if (data.success && data.data && data.data.idAcordo != null) {

          let tempContratoNegociar = {...BBCContext.contratoNegociar};
          tempContratoNegociar.dadosBBC.acordo = data.data;
          BBCContext.setContratoNegociar(tempContratoNegociar);

          limparAvisos();
          
        } else {
          setTitleAvisoLoading("Acordo BBC não gerado");
          setMsgAvisoLoading(`Msg BBC: ${data.message}`);
          setTimeout(() => {
            setLoading(false);
            setLoadingAction("empty");
            setMsgAvisoLoading("");
            setTitleAvisoLoading("");
          }, 3000);
        }
      }).catch((err) => {
        setTitleAvisoLoading(`Erro na chamada das APIS`);
        setMsgAvisoLoading(`Erro na chamada API BBC de Gravar o Acordo, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`);
        setTimeout(() => {
          setLoading(false);
          setLoadingAction("empty");
          setMsgAvisoLoading("");
          setTitleAvisoLoading("");
        }, 3000);
      })

    return ret;
  }

  const limparAvisos = () => {
    setTimeout(() => {
      setLoading(false);
      setLoadingAction("empty");
      setMsgAvisoLoading("");
      setTitleAvisoLoading("");
      onFinshAcordo();
    }, 3000);
  }

  const handleGerarAcordo = () => {
    payload.idAgrupamento = BBCContext.financiadoData.id_Agrupamento
    payload.idSimulation = BBCContext.contratoNegociar.dadosBBC.simulacao.simulationId

    GerarAcordo();
  };

  return (
    <CCard style={{ border: "none" }} >
      <CCardBody>
        {loading && loadingAction === "VarifyParam" ? (<CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />) : ""}
        {!loading && loadingAction === "empty" && (
          <CRow>
          <CCol>
            <CCard style={{ border: "none" }} >
              <CCardTitle style={{ fontSize: "1.2rem" }}>
                <strong>Dados do Acordo</strong>
              </CCardTitle>
              <CCardBody>
                <DadosDaSimulacaoSimulacao />
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
        )}
        
      </CCardBody>
      <CCardFooter style={{ display: "flex", justifyContent: "flex-end" }}>
        <CButton
          color="secondary"
          className="mr-2"
          onClick={onClickNovaSimulacao}
          disabled={loading}
        >Nova Simulação</CButton>
        <CButton
          color="info"
          onClick={handleGerarAcordo}
          disabled={loading}
        > Gravar Acordo</CButton>
      </CCardFooter>
    </CCard>);
}

export default FormularioSimulacao;
