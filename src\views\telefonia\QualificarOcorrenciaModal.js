import React, { useEffect, useState } from "react";
import { useHistory } from "react-router-dom";
import {
  CButton,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CRow,
  CCol,
} from "@coreui/react";
import CreateOcorrenciaModal from "../ocorrencias/CreateOcorrenciaModal";
import { useMyContext } from "src/reusable/DataContext";
import { getApi, getApiInline, postApi } from "src/reusable/functions";
import { acionarTabulacao, sairTabulacao } from "src/config/telephonyFunctions";
import CardLoading from "src/reusable/CardLoading";
import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";

export function getElapsedTime() {
  const storedStartTime = localStorage.getItem("startTime");

  if (storedStartTime) {
    const currentTime = new Date().getTime();
    const elapsedTime = currentTime - parseInt(storedStartTime);
    return elapsedTime;
  } else {
    // Timer hasn't started yet
    return 0;
  }
}

const QualificarOcorrenciaModal = ({ isOpen, onClose }) => {
  const history = useHistory();
  const { data } = useMyContext();
  const { message } = useWebsocketTelefoniaContext()

  const [loading, setLoading] = useState(false);
  const [loadingText, setLoadingText] = useState("Carregando...");
  const [modalShow, setModalShow] = useState(false);
  const [phoneOlos, setPhoneOlos] = useState(false);
  const [typeCallOlos, setTypeCallOlos] = useState(false);

  function handleButton() {
    onClose();
    const currentUrl = window.location.href;
    if (!currentUrl.includes("/telaprincipal")) {
      history.push("/telaprincipal");
    } else {
      history.go(0);
    }
  }
  function verificaDiferencaTempo() {
    const startTime = localStorage.getItem("startTime");
    if (startTime === null) {
      return true;
    }
    const horarioAgora = new Date().getTime();
    const diferenca = (horarioAgora - startTime) / 1000; // Converter para segundos
    return diferenca >= 15;
}

  function timer() {
    // Get the current timestamp in milliseconds
    const startTime = new Date().getTime();
    // Store the start time in localStorage
    localStorage.setItem("startTime", startTime.toString());
    onClose();
  }

  const handleOpen = async (callData) => {
    const callDataJson = callData;
    setPhoneOlos(callDataJson.phone);
    setTypeCallOlos(callDataJson.callType);

    if (
      data !== null &&
      data !== undefined &&
      data?.coddatacob === "Rodobens"
    ) {
      const check = await getApiInline(
        data?.id_Agrupamento,
        "getCheckStatusContrato"
      );
      if (check?.isDnr || check?.status === "Devolvido") {
        setLoadingText("Qualificando...");
        const config = await getApiInline(
          "occr_automatica",
          "getConfigUnicaByKey"
        );
        await postApi(
          {
            isDnr: check?.isDnr,
            status: data?.status,
            phone: callDataJson.phone,
            groupingId: data?.id_Agrupamento,
          },
          "restMissedOccurrence"
        );

        const payload = {
          dispositionCode: config,
          agentId: callDataJson?.agentId,
          callId: callDataJson?.callId,
          // callId: currentCallData.callId ?? currentCallData.callIdTactium,
          description: "Ocorrência DNR ou Devolvido",
          reasonId:
            localStorage.getItem("pausaId") === "null"
              ? null
              : Number(localStorage.getItem("pausaId")),
        };
        await acionarTabulacao(payload);
        if (callDataJson?.agentId) {
          await sairTabulacao(callDataJson?.agentId);
        }
        onClose();
        setLoading(false);
      }
    }
    setLoading(false);
  };

  useEffect(() => {
    if (isOpen) {
      const callData = message
      if (callData !== null && callData !== undefined) {
        try {
          setLoading(true);
          handleOpen(callData);
        } catch (err) {
          console.log("erro", err);
        }
      }
    }
  }, [isOpen]);

  return (
    <>
      <CModal
        show={isOpen && verificaDiferencaTempo()}
        onClose={onClose}
        hidden={modalShow}
        closeOnBackdrop={false}
      >
        <CModalHeader>
          <CModalTitle>Qualifique a ligação</CModalTitle>
        </CModalHeader>
        <CModalBody className="pb-0">
          {loading ? (
            <CardLoading Msg={loadingText} />
          ) : (
            <>
              <CRow>
                <CCol>
                  <CLabel>
                    Para finalizar o atendimento é necessário inserir um
                    histórico.
                    <br />
                    Deseja fazer isso agora?
                  </CLabel>
                </CCol>
              </CRow>
            </>
          )}
        </CModalBody>
        <CModalFooter className="my-0 py-1">
          <CButton disabled={loading} color="secondary" onClick={timer}>
            Não
          </CButton>
          <CButton
            disabled={loading}
            color="info"
            onClick={() => {
              localStorage.removeItem("startTime");
              setModalShow(true);
            }}
          >
            Sim
          </CButton>
        </CModalFooter>
      </CModal>

      {modalShow && (
        <CreateOcorrenciaModal
          isOpen={modalShow}
          onClose={() => {
            setModalShow(false);
            handleButton();
          }}
          onSave={() => {
            setModalShow(false);
            handleButton();
          }}
          isRpa={false}
          phoneOlos={phoneOlos}
          typeCallOlos={typeCallOlos}
        />
      )}
    </>
  );
};

export default QualificarOcorrenciaModal;
