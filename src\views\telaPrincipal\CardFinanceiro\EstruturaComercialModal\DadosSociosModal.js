import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CCol,
  CRow,
} from "@coreui/react";
// import { GET_DATA } from "src/api";
// import { formatThousands, formatDate } from "src/reusable/helpers";

const DadosSociosModal = ({ isOpen, onClose, dados }) => {
  const [tableData, setTableData] = useState([]);

  const mockData = [
    {
      cod: "10000",
      nome_socio: "Marcos Antonio",
      cpfCnpj: "123456871",
      perc_Part: "100",
    },
  ];

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      setTableData([
        {
          label: "Código",
          value: mockData[0].cod,
        },
        {
          label: "Nome do sócio",
          value: dados.nome_socio,
        },
        {
          label: "CPF/CNPJ",
          value: mockData[0].cpfCnpj,
        },
        {
          label: "% Participação",
          value: mockData[0].perc_Part,
        },
      ]);
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal"
      size="lg"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Dados do Sócio</h5>
      </CModalHeader>
      <CModalBody>
        {tableData && (
          <>
            <table className="table table-hover calculo">
              <tbody>
                {tableData.map((row) => (
                  <CRow key={row.label}>
                    <CCol md='6' style={{textAlign:"end"}}><strong>{row.label}:</strong></CCol>
                    <CCol md='6'>{row.value ? row.value : "---"}</CCol>
                  </CRow>
                ))}
              </tbody>
            </table>
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default DadosSociosModal;
