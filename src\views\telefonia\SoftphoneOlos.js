import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>, <PERSON>ow, <PERSON><PERSON>, <PERSON>ard, <PERSON>ard<PERSON>ody,
  CCardHeader, CButton, CLabel
} from "@coreui/react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import SoftphoneOlosConnection from "src/views/telefonia/SoftphoneOlosConnection";
import { deslogarAgente } from "src/config/telephonyFunctions";

const SoftphoneOlos = () => {
  const userProfile = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const [softphoneAtivo, setSoftphoneAtivo] = useState(() => {
    const salvo = localStorage.getItem("softphoneAtivo");
    return salvo ? JSON.parse(salvo) : false;
  });

  const [isUserAllowed, setIsUserAllowed] = useState(false);
  const [isSoftphoneOlosAllowed, setIsSoftphoneOlosAllowed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingAuthCod, setLoadingAuthCod] = useState(false);

  const [tableData, setTableData] = useState(null);
  const [status, setStatus] = useState(null);
  const [passCode, setPassCode] = useState(null);
  const [agentId, setAgentId] = useState(null);

  useEffect(() => {
    const channel = new BroadcastChannel("softphone_channel");
    channel.onmessage = (event) => {
      const { type, payload } = event.data;

      if (type === "update-softphone-data") {
        setTableData(payload.tableData);
        setStatus(payload.status);
        setPassCode(payload.passCode);
        setAgentId(payload.agentId);
      }

      if (type === "force-softphone-stop") {
        setSoftphoneAtivo(false);
        localStorage.setItem("softphoneAtivo", JSON.stringify(false));
        stopSoftphone();
        console.info('force-softphone-stop deslogue');
      }
    };

    return () => {
      channel.close();
    };
  }, []);

  const toggleSoftphone = () => {
    const novoEstado = !softphoneAtivo;
    localStorage.setItem("softphoneAtivo", JSON.stringify(novoEstado));
    setSoftphoneAtivo(novoEstado);

    if (!novoEstado) {
      stopSoftphone();
      console.log('novoEstado deslogue');
    }
  };

  function handleSingleTabGuard() {
    const alreadyOpen = localStorage.getItem('softphoneOlosActive');
    if (alreadyOpen === 'true') {
      alert('Já existe uma aba com o Softphone OLOS ativa. Feche-a antes de abrir outra.');
      window.location.href = '/#/telaprincipal';
      return;
    }

    localStorage.setItem('softphoneOlosActive', 'true');

    const channel = new BroadcastChannel("softphone_channel");
    channel.onmessage = (event) => {
      if (event.data?.type === "check-softphone-open") {
        channel.postMessage({ type: "softphone-already-open", from: "softphone-tab" });
      }
    };

    const clearActive = () => {
      localStorage.removeItem('softphoneOlosActive');
      channel.close();
    };

    window.addEventListener('beforeunload', clearActive);
    return () => {
      window.removeEventListener('beforeunload', clearActive);
      clearActive();
    };
  }

  async function checkPermissions() {
    setIsLoading(true);
    try {
      const transcriptionUsers = await GET_DATA(
        getURI("getConfigByKey"),
        null, true, true, "transcription_call_users"
      );
      const softphoneUsers = await GET_DATA(
        getURI("getConfigByKey"),
        null, true, true, "softphone_olos_users"
      );

      if (userProfile) {
        setIsUserAllowed(transcriptionUsers?.includes(userProfile.id));
        setIsSoftphoneOlosAllowed(softphoneUsers?.includes(userProfile.id));
      }
    } catch (error) {
      console.error("Erro ao verificar permissões:", error);
    } finally {
      setIsLoading(false);
    }
  }

  function stopSoftphone() {
    setLoadingAuthCod(true);
    let payloadAgentId = {
      agentId: agentId,
    };
    deslogarAgente(payloadAgentId).finally(() => {
      console.log('softphone deslogue');
      setLoadingAuthCod(false);
    });
  }

  useEffect(handleSingleTabGuard, []);
  useEffect(() => { checkPermissions(); }, []);

  useEffect(() => {
    const channel = new BroadcastChannel("softphone_channel");

    channel.onmessage = (event) => {
      if (event.data?.type === "check-softphone-open") {
        channel.postMessage({ type: "softphone-already-open" });
      }
    };

    return () => channel.close();
  }, []);

  return (
    <>
      <CContainer fluid style={{ maxWidth: "800px" }}>
        <CRow className="mb-4 justify-content-between align-items-center">
          <CCol>
            <h5>Status da Telefonia: <span className="text-danger">{status || "Desconhecido"}</span></h5>
          </CCol>
          <CCol className="text-right">
            {passCode && (
              <CLabel className="font-weight-bold">Cod OLOS: {passCode}</CLabel>
            )}
          </CCol>
        </CRow>

        <CCard>
          <CCardHeader>
            <strong>Informações da Ligação</strong>
          </CCardHeader>
          <CCardBody className="p-4">
            <table className="table table-borderless">
              <tbody>
                <tr>
                  <td className="text-right font-weight-bold" width="30%">Nome</td>
                  <td>{">>"} {tableData?.name || "Não identificado"}</td>
                </tr>
                <tr>
                  <td className="text-right font-weight-bold">Telefone</td>
                  <td>
                    {">>"} {tableData?.phone || "---"}
                  </td>
                </tr>
                <tr>
                  <td className="text-right font-weight-bold">Carteira</td>
                  <td>{">>"} {tableData?.wallet || "-"}</td>
                </tr>
                <tr>
                  <td className="text-right font-weight-bold">Status</td>
                  <td>{">>"} {status || "Desconhecido"}</td>
                </tr>
              </tbody>
            </table>

            {isSoftphoneOlosAllowed ? (
              <CButton
                color={softphoneAtivo ? "danger" : "info"}
                onClick={toggleSoftphone}
                block
                className="mt-3"
                disabled={isLoading}
              >
                {softphoneAtivo ? "Parar Softphone" : "Iniciar Softphone"}
              </CButton>
            ) : (
              !isLoading && (
                <p className="text-muted mt-3">
                  Você não tem permissão para utilizar o Softphone OLOS.
                </p>
              )
            )}
          </CCardBody>
        </CCard>
      </CContainer>

      {softphoneAtivo && (
        <SoftphoneOlosConnection
          shouldConnect={true}
          onEnded={() => {
            localStorage.setItem("softphoneAtivo", JSON.stringify(false));
            setSoftphoneAtivo(false);
          }}
        />
      )}
    </>
  );
};

export default SoftphoneOlos;
