import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CButton,
  CForm,
  CFormGroup,
  CLabel,
  CInput,
  CCard,
  CRow,
  CCol,
  CCardBody,
  CCardHeader,
  CInputRadio,
  CInputCheckbox,
} from "@coreui/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import Select from "react-select";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { formatDate, formatThousands } from "src/reusable/helpers";
import AlertaModal from "../negociacao/AlertaModal";

import CardLoading from "src/reusable/CardLoading";
import LoadingComponent from "src/reusable/Loading";

import { useAuth } from 'src/auth/AuthContext';

const GerarBoletoAcordoModal = ({ isOpen, onClose, dados }) => {
  const { checkPermission,inforPermissions } = useAuth();

  const permissaoNegociacaoBoleto = {
    modulo: "Negociação",
    submodulo: "Boleto",
  }

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : "";

  const clientData = localStorage.getItem("clientData")
    ? JSON.parse(localStorage.getItem("clientData"))
    : "";

  const [selectedTelefoneNumber, setSelectedTelefoneNumber] = useState(null);
  const [selectedTelefoneDDD, setSelectedTelefoneDDD] = useState(null);
  const [selectedTelefone, setSelectedTelefone] = useState("");
  const [telefoneList, setTelefoneList] = useState([]);

  const [email, setEmail] = useState("");
  const [selectedEmail, setSelectedEmail] = useState(null);
  const [emailList, setEmailList] = useState([]);

  const [selectedTipoEnvio, setSelectedTipoEnvio] = useState(null);
  const [descricao, setDescricao] = useState("");
  const [checkboxValue, setCheckboxValue] = useState(false);

  const [showAlertaModal, setShowAlertaModal] = useState(false);
  const [alertaMessage, setAlertaMessage] = useState("");

  const [selectedNegociador, setSelectedNegociador] = useState(null);
  const [negociadorOptions, setNegociadorOptions] = useState([]);

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);

  const handleTipoEnvioChange = (target) => {
    setSelectedTipoEnvio(target);
  };

  const handleTelefoneChange = (target) => {
    setSelectedTelefone(target);
    setSelectedTelefoneNumber(target.fone.trim())
    setSelectedTelefoneDDD(target.ddd.trim())
  };

  const handleEmailChange = (target) => {
    setSelectedEmail(target);
    setEmail(target.label);
  };

  const handleInputChange = (event) => {
    setEmail(event.target.value);
  };

  const handleDescricaoChange = (event) => {
    setDescricao(event.target.value);
  };

  const handleCheckboxChange = (event) => {
    setCheckboxValue(event.target.checked);
  };

  const impressaoBoleto = async (item) => {
    const data = { IdBoleto: item.idBoleto };
    const boleto = await GET_DATA("Datacob/DownloadBoleto", data);

    // Defina um título para a nova janela
    const windowTitle = "Visualização de PDF";

    // Crie uma nova janela com título
    const newWindow = window.open(
      "",
      "_blank",
      `width=800,height=600,toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,title=${windowTitle}`
    );

    // Escreva o conteúdo do PDF na nova janela
    newWindow.document.open();
    newWindow.document.write(
      '<embed width="100%" height="100%" src="data:application/pdf;base64,' +
        boleto +
        '" type="application/pdf"/>'
    );
    newWindow.document.close();
  };

  const boletoPost = async (boletoData) => {
    const payload = {
      ...boletoData,
      email: email,
      ddd: selectedTelefoneDDD,
      telefone: selectedTelefoneNumber,
      mensagem: descricao,
      usuarioNegociacao: selectedNegociador.value,
      tipoEnvio: selectedTipoEnvio.value,
    };
    const result = await POST_DATA("Datacob/Acordos/GerarBoleto", payload);
    return result;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);

    try {
      const result = await boletoPost(dados);
      if (result.success) {
        if (selectedTipoEnvio.value === "Impressao") {
          await impressaoBoleto(result.data);
        }
        toast.success("Boleto gerado com sucesso!");
        onClose();
      } else {
        toast.warning(result.message);
      }
    } catch (error) {
      console.error("An error occurred during form submission:", error);
      toast.warn(error);
    } finally {
      setIsLoading(false);
      onClose();
    }
  };

  const handleClose = () => {
    onClose();
  };

  const handleNegociadorChange = (target) => {
    setSelectedNegociador(target);
  };

  const handleAlertaClose = () => {
    setShowAlertaModal(false);
  };

  const getUsers = async () => {
    setIsLoadingUsers(true);
    const currentUser = await getData(null, "getUser");
    const payload = { ActiveConnection: user.activeConnection };
    getData(payload, "getDatacobUsers")
      .then((data) => {
        if (data) {
          const options = data.map((x) => {
            return {
              label: x.nome,
              value: x.id_Usuario,
            };
          });
          setNegociadorOptions(options);
          const findUser = data.find((user) => currentUser.name === user.nome);
          if (findUser) {
            const negociador = {
              label: findUser.nome,
              value: findUser.id_Usuario,
            };
            setSelectedNegociador(negociador);
          }
        }
      })
      .catch((err) => {
        console.log(err);
      });
    setIsLoadingUsers(false);
  };

  const getData = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    if (isOpen) {
      getUsers();
      if (clientData) {
        setTelefoneList(clientData.telefones);
        setEmailList(clientData.emails);
      }
    }
  }, [isOpen]);

  const foneOptions = [
    ...telefoneList.map((x) => ({
      value: x.id_Telefone,
      label: x.ddd + x.fone,
      ddd: x.ddd,
      fone: x.fone,
    })),
  ];

  const emailOptions = [
    ...emailList.map((x) => ({
      value: x.id_Email,
      label: x.endereco_Email,
    })),
  ];

  const optionsTipoEnvio = [
    { value: "Email", label: "E-mail" },
    { value: "Impressao", label: "Impressão" },
  ];

  return (
    <CModal size="lg" show={isOpen} onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Emissão de Boleto</CModalHeader>
      <CModalBody>
        {isLoading ? (
          <CardLoading Title={"Gerando boleto..."} />
        ) : (
          <CRow>
            <CCol>
              <CCard>
                <CCardHeader className="py-1">
                  Telefones do atendimento
                </CCardHeader>
                <CCardBody className="py-1">
                  <Select
                    placeholder="Selecione"
                    value={selectedTelefone}
                    options={foneOptions}
                    onChange={handleTelefoneChange}
                  />
                </CCardBody>
              </CCard>
              <CCard>
                <CCardHeader className="py-1">Dados de Envio</CCardHeader>
                <CCardBody className="py-1">
                  <CForm>
                    <CFormGroup>
                      <CRow>
                        <CCol>
                          <CLabel>Tipo de Envio</CLabel>
                          <Select
                            value={selectedTipoEnvio}
                            options={optionsTipoEnvio}
                            placeholder="Selecione"
                            onChange={handleTipoEnvioChange}
                            // isDisabled
                          />
                        </CCol>
                      </CRow>
                      <CLabel>Enviar boleto como</CLabel>
                      <CFormGroup variant="checkbox">
                        <CInputRadio
                          id="pdf"
                          name="checkbox"
                          value="pdf"
                          checked={true}
                          onChange={handleCheckboxChange}
                          disabled
                        />
                        <CLabel htmlFor="pdf">PDF</CLabel>
                      </CFormGroup>
                      <CLabel>Emails disponíveis</CLabel>
                      <Select
                        value={selectedEmail}
                        options={emailOptions}
                        onChange={handleEmailChange}
                        placeholder="Selecione"
                      />
                      <CLabel>Email destinatário</CLabel>
                      <CInput
                        type="text"
                        value={email}
                        onChange={handleInputChange}
                      />
                    </CFormGroup>
                  </CForm>
                </CCardBody>
              </CCard>
            </CCol>
            <CCol>
              <CCard>
                <CCardBody className="py-1 px-2" style={{ minHeight: "180px" }}>
                  {isLoadingUsers ? (
                    <CardLoading />
                  ) : (
                    <CForm>
                      <CFormGroup>
                        <CLabel className="mt-2">Nome do Negociador</CLabel>
                        <Select
                          value={selectedNegociador}
                          options={negociadorOptions}
                          onChange={handleNegociadorChange}
                          placeholder="Selecione"
                        />
                        <CLabel className="mt-2">Descrição</CLabel>
                        <CInput
                          type="text"
                          value={descricao}
                          onChange={handleDescricaoChange}
                        />
                      </CFormGroup>
                    </CForm>
                  )}
                </CCardBody>
              </CCard>
              <CCard>
                <CCardHeader>Valores</CCardHeader>
                <CCardBody>
                  <CRow>
                    <CCol>
                      <CLabel>Valor Atualizado</CLabel> <br />
                      <CLabel>R$ {formatThousands(dados?.vlTotal)}</CLabel>
                    </CCol>
                    <CCol>
                      <CLabel>Taxa Bancária</CLabel> <br />
                      <CLabel>0,00</CLabel>
                    </CCol>
                  </CRow>
                  <CRow>
                    <CCol>
                      <CLabel>Total do Boleto</CLabel> <br />
                      <CLabel>R$ {formatThousands(dados?.vlTotal)}</CLabel>
                    </CCol>
                    <CCol>
                      <CLabel>Vencimento</CLabel> <br />
                      <CLabel>{formatDate(dados?.dataVencimento)}</CLabel>
                    </CCol>
                  </CRow>
                </CCardBody>
              </CCard>
            </CCol>
          </CRow>
        )}
        <CRow>
          <CCol className="d-flex justify-content-end">
            <CButton
              color="secondary"
              className="mr-2"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancelar
            </CButton>
            <CButton
              type="submit"
              color="primary"
              onClick={handleSubmit}
              disabled={
                selectedTelefone.length === 0 || email === "" || isLoading || !checkPermission(permissaoNegociacaoBoleto.modulo, "Create",permissaoNegociacaoBoleto.submodulo)
              }
              title={inforPermissions(permissaoNegociacaoBoleto).create}
            >
              {isLoading ? <LoadingComponent /> : "Ok"}
            </CButton>
          </CCol>
        </CRow>
      </CModalBody>
      <AlertaModal
        isOpen={showAlertaModal}
        onClose={handleAlertaClose}
        dados={alertaMessage}
      />
    </CModal>
  );
};

export default GerarBoletoAcordoModal;
