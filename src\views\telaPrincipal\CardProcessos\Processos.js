import React, { useState, useEffect } from "react";
import { CC<PERSON>, CRow, CCardBody, CLabel } from "@coreui/react";
import { strEmptyOrNull } from "src/reusable/helpers";

const Processos = ({ pasta, selected, processos }) => {
  const [docData, setDocData] = useState([]);

  const payloadProcesso = {
    Pasta: pasta || null,
  };

  const updateViewProcessos = () => {
    if (payloadProcesso.Pasta !== null && processos != null) {
      setDocData(
        processos.find((item) => item.pasta === payloadProcesso.Pasta)
      );
    }
  };

  useEffect(() => {
    if (selected === true && pasta) {
      updateViewProcessos();
    }
  }, [selected, pasta]);

  return (
    <CCardBody>
      <CRow>
        <CCol>
          <CRow md="12">
            <CCol md="6">
              <CLabel style={{ color: "gray" }}>Responsável</CLabel> <br />
              <CLabel>
                <strong>
                  {strEmptyOrNull(docData.adverso_Principal) ?? "---"}
                </strong>
              </CLabel>
            </CCol>
            <CCol md="3">
              <CLabel style={{ color: "gray" }}>Pasta</CLabel> <br />
              <CLabel>
                <strong>{strEmptyOrNull(docData.pasta) ?? "---"}</strong>
              </CLabel>
            </CCol>{" "}
            <CCol md="3">
              <CLabel style={{ color: "gray" }}>Número do Processo</CLabel>{" "}
              <br />
              <CLabel>
                <strong>
                  {strEmptyOrNull(docData.numero_atual_processo) ?? "---"}
                </strong>
              </CLabel>
            </CCol>
          </CRow>
          <CRow md="12" className="mt-2">
            <CCol md="6">
              <CLabel style={{ color: "gray" }}>Cliente Principal</CLabel>{" "}
              <br />
              <CLabel>
                <strong>
                  {strEmptyOrNull(docData.cliente_Principal) ?? "---"}
                </strong>
              </CLabel>
            </CCol>
            <CCol md="3">
              <CLabel style={{ color: "gray" }}>Adverso Principal</CLabel>{" "}
              <br />
              <CLabel>
                <strong>
                  {strEmptyOrNull(docData.adverso_Principal) ?? "---"}
                </strong>
              </CLabel>
            </CCol>
            <CCol md="3">
              <CLabel style={{ color: "gray" }}>Notas</CLabel> <br />
              <CLabel>
                <strong>{strEmptyOrNull(docData.nota) ?? "---"}</strong>
              </CLabel>
            </CCol>
          </CRow>
          <CRow md="12" className="mt-2">
            <CCol md="6">
              <CLabel style={{ color: "gray" }}>Condição do Adverso</CLabel>{" "}
              <br />
              <CLabel>
                <strong>{strEmptyOrNull(docData.condicao) ?? "---"}</strong>
              </CLabel>
            </CCol>
            <CCol md="6">
              <CLabel style={{ color: "gray" }}>Atributos</CLabel> <br />
              <CLabel>
                <strong>
                  Grupo/Cota:{" "}
                  {strEmptyOrNull(docData.grupo_Cota_Contrato) ?? "---"}
                </strong>
              </CLabel>
            </CCol>
          </CRow>
          <CRow md="12" className="mt-2">
            <CCol md="6">
              <CLabel style={{ color: "gray" }}>Jurisdição Atual</CLabel> <br />
              <CLabel>
                <strong>
                  {strEmptyOrNull(docData.jurisdicao_Atual) ?? "---"}
                </strong>
              </CLabel>
            </CCol>
            <CCol md="6">
              <CLabel style={{ color: "gray" }}>Tipo de ação</CLabel> <br />
              <CLabel>
                <strong>{strEmptyOrNull(docData.tipo_de_Acao) ?? "---"}</strong>
              </CLabel>
            </CCol>
          </CRow>
        </CCol>
      </CRow>
    </CCardBody>
  );
};

export default Processos;
