import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CFormGroup,
  CLabel,
  CRow,
  CCol,
  CInput,
  CSwitch,
  CInputGroup,
  CInputGroupAppend,
  CInputGroupText,
  CForm,
} from "@coreui/react";
import Select from "react-select";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";
import LoadingComponent from "src/reusable/Loading";
import { postManager } from "src/reusable/helpers.js";

const CreateUserModal = ({
  isOpen,
  originConnection,
  userList,
  onCreateUser,
  editUser,
  onEditUser,
  onClose,
  onEditUserCrmAuth,
}) => {
  const token = localStorage.getItem("token");
  const userIsAdmin = JSON.parse(localStorage.getItem("user")).isAdmin;
  const [user, setUser] = useState({
    userId: "",
    name: "",
    username: "",
    email: "",
    password: "",
    groups: "",
    datacobs: [],
    telephonyId: "",
    userTel: "",
    passTel: "",
    originConnection: "",
    role: "",
    active: true,
    ad: true,
    isAdmin: false,
  });

  const [isLoading, setIsLoading] = useState(false);

  const [labelStatus, setLabelStatus] = useState("Ativo");
  const [groupOptions, setGroupOptions] = useState([]);
  const [roleOptions, setRoleOptions] = useState([]);

  const [searchUserOptions, setSearchUserOptions] = useState([]);

  const [selectedRole, setSelectedRole] = useState([]);
  const [selectedGroups, setSelectedGroups] = useState([]);
  const [selectedCRMs, setSelectedCRMs] = useState([]);

  const [roles, setRoles] = useState([]);
  const [groups, setGroups] = useState([]);
  const [datacobOptions, setDatacobOptions] = useState([]);

  const [telephonyOptions, setTelephonyOptions] = useState([]);
  const [selectedTelephony, setSelectedTelephony] = useState([]);

  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordTel, setShowPasswordTel] = useState(false);

  const toggleShowPassword = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  const toggleShowPasswordTel = () => {
    setShowPasswordTel((prevShowPasswordTel) => !prevShowPasswordTel);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUser((prevUser) => ({ ...prevUser, [name]: value }));
  };

  const handleGroupSelect = (selectedOptions) => {
    setUser((prevUser) => ({ ...prevUser, groups: selectedOptions }));
    setSelectedGroups(selectedOptions);
  };

  const handleCRMSelect = (selectedOptions) => {
    setUser((prevUser) => ({ ...prevUser, datacobs: selectedOptions }));
    setSelectedCRMs(selectedOptions);
  };

  const handleTelephonySelect = (selectedOptions) => {
    setUser((prevUser) => ({
      ...prevUser,
      telephonyId: selectedOptions.value,
    }));
    setSelectedTelephony(selectedOptions);
  };

  const handleRoleSelect = (selectedOption) => {
    setUser((prevUser) => ({ ...prevUser, role: selectedOption }));
    setSelectedRole(selectedOption);
  };

  const handleStatusToggle = () => {
    setUser((prevUser) => ({ ...prevUser, active: !prevUser.active }));
  };

  const handleUserSelect = (selectedOption) => {
    setUser((prevUser) => ({
      ...prevUser,
      name: selectedOption.nome,
      email: selectedOption.email,
      userId: selectedOption.value,
      username: selectedOption.login.trim(),
    }));
  };

  const handleADToggle = () => {
    setUser((prevUser) => ({ ...prevUser, ad: !prevUser.ad, password: "" }));
  };

  const handleIsAdminToggle = () => {
    setUser((prevUser) => ({
      ...prevUser,
      isAdmin: !prevUser.isAdmin,
    }));
  };

  const userOptions = searchUserOptions.map((user) => ({
    value: user.id_Usuario,
    label: user.nome,
    nome: user.nome,
    email: user.email,
    login: user.login,
  }));

  const emailOptions = searchUserOptions.map((user) => ({
    value: user.id_Usuario,
    label: user.email,
    email: user.email,
    nome: user.nome,
    login: user.login,
  }));

  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const requiredFields = [
      { name: "name", displayName: "Nome" },
      { name: "email", displayName: "E-mail" },
      { name: "groups", displayName: "Grupos" },
      { name: "role", displayName: "Função" },
      { name: "datacobs", displayName: "CRM" },
    ];

    if (!user.ad) {
      requiredFields.push({ name: "password", displayName: "Senha" });
    }

    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = user[field.name];

      if (field.name === "datacobs" && user.isAdmin !== true) {
        return fieldValue.length === 0;
      }

      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    const groupIds = user.groups.map((x) => x.id);

    let datacobIds = [];
    if (user.isAdmin) {
      datacobIds = datacobOptions.map((x) => x.value);
    } else datacobIds = user.datacobs.map((x) => x.value);

    const newUser = {
      userId: user.userId,
      name: user.name,
      email: user.email,
      username: user.username,
      password: user.password,
      roleId: user.role.id,
      originConnection: originConnection.value,
      telephonyId: user.telephonyId,
      userTel: user.userTel,
      passTel: user.passTel,
      groupIds: groupIds,
      datacobIds: datacobIds,
      active: user.active,
      ad: user.ad,
      isAdmin: user.isAdmin,
    };

    onCreateUser(newUser);
    resetModal();
    onClose();
  };

  const handleEdit = (e) => {
    e.preventDefault();

    const requiredFields = [
      { name: "name", displayName: "Nome" },
      { name: "email", displayName: "E-mail" },
      { name: "groups", displayName: "Grupos" },
      { name: "role", displayName: "Função" },
      { name: "datacobs", displayName: "CRM" },
    ];

    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = user[field.name];

      if (field.name === "groups") {
        return fieldValue.length === 0; // Check if the groups array is empty
      }

      if (field.name === "datacobs" && user.isAdmin !== true) {
        return fieldValue.length === 0;
      }

      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    const groupIds = user.groups.map((x) => x.id);

    let datacobIds = [];
    if (user.isAdmin) {
      datacobIds = datacobOptions.map((x) => x.value);
    } else datacobIds = user.datacobs.map((x) => x.value);

    const editedUser = {
      userId: editUser.id,
      name: user.name,
      email: user.email,
      password: user.password,
      roleId: user.role.id,
      groupIds: groupIds,
      telephonyId: user.telephonyId,
      userTel: user.userTel,
      passTel: user.passTel,
      originConnection: user.originConnection,
      datacobIds: datacobIds,
      active: user.active,
      ad: user.ad,
      isAdmin: user.isAdmin,
    };

    onEditUser(editedUser);
    resetModal();
    onClose();
  };

  const getRoles = async () => {
    try {
      const response = await fetch(`${getURI()}/Role`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setRoles(data.data);

        const roleList = data.data.map((role) => ({
          label: role.name,
          value: role.id,
          id: role.id,
          name: role.name,
        }));
        setRoleOptions(roleList);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando funções:", error);
    }
  };

  const getGroups = async () => {
    try {
      const response = await fetch(`${getURI()}/Group`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setGroups(data.data);

        const filteredGroups = data.data.filter((group) => {
          return group.active;
        });

        const groupList = filteredGroups.map((group) => ({
          label: group.name,
          value: group.id,
          id: group.id,
          name: group.name,
        }));
        setGroupOptions(groupList);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando grupos:", error);
    }
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  const matchCRM = () => {
    const filteredElements = datacobOptions.filter((options) =>
      editUser.datacobs.some((user) => user.datacobName === options.label)
    );
    setSelectedCRMs(filteredElements);
    setUser((prevUser) => ({ ...prevUser, datacobs: filteredElements }));
  };

  function resetModal() {
    setUser({
      userId: "",
      name: "",
      username: "",
      email: "",
      password: "",
      groups: "",
      telephonyId: "",
      userTel: "",
      passTel: "",
      datacobs: [],
      role: "",
      active: true,
      ad: true,
      isAdmin: false,
    });
  }

  const getDatacobsOptions = () => {
    getDatacobs(null, "getDatacobs")
      .then((data) => {
        if (data) {
          const uniqueDatacob = [...new Set(data.map((item) => item))];
          const optionsDatacob = [
            ...uniqueDatacob.map((x) => ({
              value: x.id,
              label: x.datacobName,
            })),
          ];
          setDatacobOptions(optionsDatacob);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const getDatacobs = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const getDataCobUsers = () => {
    const payload = { ActiveConnection: originConnection.label };
    getAllUsers(payload, "getDatacobUsers")
      .then(async (data) => {
        if (data) {

          data = await Promise.all(data.map(async (user) => {
            user.email = await postManager(token, user.email, 2);
            user.login = await postManager(token, user.login, 2);
            return user;
          }));

          const filteredUsers = data.filter(
            (user) =>
              !userList.some((addedUser) => addedUser.name === user.nome)
          );
          setSearchUserOptions(filteredUsers);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const getAllUsers = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const getTelefoniaOptions = () => {
    getTelefonias(null, "getServicosTelefonicos")
      .then((data) => {
        if (data) {
          const uniqueOptions = [...new Set(data.map((item) => item))];
          const options = [
            ...uniqueOptions.map((x) => ({
              value: x.id,
              label: x.name,
            })),
          ];
          setTelephonyOptions(options);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const getTelefonias = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    if (isOpen && editUser) {
      matchCRM();
    }
  }, [isOpen, datacobOptions]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        await Promise.all([
          getGroups(),
          getRoles(),
          getDatacobsOptions(),
          getTelefoniaOptions(),
        ]);
        if (!editUser) {
          getDataCobUsers();
        }
      } catch (error) {
        console.log(error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && editUser) {
      setUser({
        userId: editUser.id,
        name: editUser.name,
        email: editUser.email,
        groups: editUser.groups,
        telephonyId: editUser.telephonyId,
        userTel: editUser.agentFone ? editUser.agentFone.username : "",
        datacobs: editUser.datacobs,
        role: editUser.role,
        active: editUser.active,
        ad: editUser.ad,
        isAdmin: editUser.isAdmin,
        username: editUser.username,
      });
      setSelectedRole({
        label: editUser.role.name,
        name: editUser.role.name,
        value: editUser.role.id,
        id: editUser.role.id,
      });
      setSelectedGroups(
        editUser.groups.map((group) => ({
          label: group.name,
          name: group.name,
          value: group.id,
          id: group.id,
        }))
      );
      matchCRM();
    }
  }, [isOpen, editUser]);

  useEffect(() => {
    handleCRMSelect([originConnection]);
  }, [originConnection]);

  useEffect(() => {
    setLabelStatus(user.active === true ? "Ativo" : "Inativo");
  }, [user.active]);

  return (
    <>
      <CModal
        show={isOpen}
        onClose={handleClose}
        closeOnBackdrop={false}
        size="lg"
      >
        <CModalHeader closeButton>
          {editUser ? (
            <CModalTitle>Editar usuário</CModalTitle>
          ) : (
            <CModalTitle>Adicionar usuário</CModalTitle>
          )}
        </CModalHeader>
        <CModalBody>
          {isLoading ? (
            <div>
              <LoadingComponent />
            </div>
          ) : (
            <CForm>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="name">Nome</CLabel>
                  <Select
                    id="name"
                    name="name"
                    options={userOptions}
                    value={{ value: user.name, label: user.name }}
                    onChange={handleUserSelect}
                    placeholder="Selecione um usuário"
                    autoComplete="off"
                    isDisabled={editUser && editUser.id}
                    required
                  />
                </CCol>
                <CCol>
                  <CLabel htmlFor="email">E-mail</CLabel>
                  <Select
                    id="email"
                    name="email"
                    options={emailOptions}
                    value={{ value: user.email, label: user.email }}
                    onChange={handleUserSelect}
                    placeholder="Selecione um e-mail"
                    autoComplete="off"
                    isDisabled={editUser && editUser.id}
                    required
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="password">Senha</CLabel>
                  <CInputGroup>
                    <CInput
                      type={showPassword ? "text" : "password"}
                      id="password"
                      name="password"
                      value={!user.ad ? 
                        user.password : ""}
                      onChange={handleInputChange}
                      autoComplete="off"
                      required={!user.ad}
                      disabled={user.ad}
                    />
                    <CInputGroupAppend>
                      <CInputGroupText onClick={toggleShowPassword}>
                        {showPassword ? (
                          <i className="cil-low-vision" />
                        ) : (
                          <i className="cil-scrubber" />
                        )}
                      </CInputGroupText>
                    </CInputGroupAppend>
                  </CInputGroup>
                </CCol>
                <CCol>
                  <CLabel htmlFor="role">Função</CLabel>
                  <Select
                    options={roleOptions}
                    value={selectedRole}
                    onChange={handleRoleSelect}
                    placeholder="Selecione"
                    required
                    isDisabled={
                      editUser && editUser.id === 1 && editUser.isAdmin
                    }
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="groups">Grupos</CLabel>
                  <Select
                    options={groupOptions}
                    value={selectedGroups}
                    onChange={handleGroupSelect}
                    placeholder="Selecione  "
                    isMulti
                    required
                  />
                </CCol>
                <CCol>
                  <CLabel htmlFor="datacobs">CRM</CLabel>
                  <Select
                    placeholder={"Selecione"}
                    options={datacobOptions}
                    value={!user.isAdmin ? selectedCRMs : []}
                    onChange={handleCRMSelect}
                    isMulti
                    isDisabled={user.isAdmin}
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="telephony">Telefonia</CLabel>
                  <Select
                    placeholder={"Selecione"}
                    options={telephonyOptions}
                    value={telephonyOptions.find(
                      (option) => option.value === user.telephonyId
                    )}
                    onChange={handleTelephonySelect}
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="userTel">Usuário Telefonia</CLabel>
                  <CInput
                    type="text"
                    id="userTel"
                    name="userTel"
                    value={user.userTel}
                    onChange={handleInputChange}
                    disabled={!user.telephonyId}
                  />
                </CCol>
                <CCol>
                  <CLabel htmlFor="passTel">Senha Telefonia</CLabel>
                  <CInputGroup>
                    <CInput
                      type={showPasswordTel ? "text" : "password"}
                      id="passTel"
                      name="passTel"
                      value={user.passTel}
                      onChange={handleInputChange}
                      autoComplete="new-password"
                      placeholder={
                        editUser?.agentFone?.password ? "******" : ""
                      }
                      disabled={!user.telephonyId}
                    />
                    <CInputGroupAppend>
                      <CInputGroupText onClick={toggleShowPasswordTel}>
                        {showPasswordTel ? (
                          <i className="cil-low-vision" />
                        ) : (
                          <i className="cil-scrubber" />
                        )}
                      </CInputGroupText>
                    </CInputGroupAppend>
                  </CInputGroup>
                </CCol>
              </CFormGroup>
              <CRow>
                <CCol>
                  <CLabel>
                    <strong>Status</strong>
                  </CLabel>
                </CCol>
                <CCol>
                  <CFormGroup className="d-flex">
                    <CSwitch
                      id="status"
                      name="status"
                      className="mr-4"
                      color="success"
                      checked={user.active}
                      onChange={handleStatusToggle}
                      shape="pill"
                      size="lg"
                      disabled={
                        editUser && editUser.id === 1 && editUser.isAdmin
                      }
                    />
                    <CLabel> {labelStatus} </CLabel>
                  </CFormGroup>
                </CCol>
              </CRow>
              <CRow>
                <CCol>
                  <CLabel>
                    <strong>Usuário vinculado ao AD?</strong>
                  </CLabel>
                </CCol>
                <CCol>
                  <CFormGroup>
                    <CSwitch
                      id="adStatus"
                      name="adStatus"
                      color="success"
                      checked={user.ad}
                      onChange={handleADToggle}
                      shape="pill"
                      size="lg"
                      disabled={
                        editUser && editUser.id === 1 && editUser.isAdmin
                      }
                    />
                  </CFormGroup>
                </CCol>
              </CRow>{" "}
              {userIsAdmin && (
                <CRow>
                  <CCol>
                    <CLabel>
                      <strong>Usuário é administrador? </strong>
                    </CLabel>
                  </CCol>
                  <CCol>
                    <CFormGroup>
                      <CSwitch
                        id="isAdmin"
                        name="isAdmin"
                        color="success"
                        checked={user.isAdmin}
                        onChange={handleIsAdminToggle}
                        shape="pill"
                        size="lg"
                        disabled={
                          editUser && editUser.id === 1 && editUser.isAdmin
                        }
                      />
                    </CFormGroup>
                  </CCol>
                </CRow>
              )}
              {!user.ad && (
                <CRow>
                  <CCol>
                    <CLabel>
                      <strong>Usuário para logar no sistema: </strong>
                    </CLabel>
                  </CCol>
                  <CCol>
                    <CFormGroup>
                      <CInput value={user?.username}></CInput>
                    </CFormGroup>
                  </CCol>
                </CRow>
              )}
            </CForm>
          )}
        </CModalBody>
        <CModalFooter>
          {!editUser && (
            <CButton color="info" onClick={handleSubmit}>
              <i className="cil-user-plus"></i> Adicionar usuário
            </CButton>
          )}
          {editUser && (
            <>
              <CButton color="warning" onClick={onEditUserCrmAuth}>
                Editar Credenciais Crm
              </CButton>
              <CButton color="info" onClick={handleEdit}>
                Salvar
              </CButton>
            </>
          )}
        </CModalFooter>
      </CModal>
    </>
  );
};

export default CreateUserModal;
