import React, { useState, useEffect } from "react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";

// Função para obter a lista de usuários autorizadores
const ehUsuarioAprovador = async () => {
  const config = await getConfigAprovacaoJuridica();
  const usuariologado = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  if (usuariologado !== null) {
    if (config.includes(usuariologado.username)) {
      return true;
    } else {
      return false;
    }
  }
  return false;
};

const showMenuAprovacaoJuridica = async () => {
  if (await ehUsuarioAprovador()) {
    return true;
  }
  return false;
};

// Função para obter a lista de negociações para liberação
const liberarNegociacao = async (contexto) => {
  if (contexto === null) return false;
  const listaRestricoes = await verificaRestricoesNegociacao();
  if (
    listaRestricoes !== null &&
    listaRestricoes.restrigirNegociacao === true
  ) {
    const analisependente = await verificaAnaliseRestricaoPendente();
    if (analisependente === true) {
      return false;
    } else {
      return true;
    }
  }
  return true;
};

const verificaAnaliseRestricaoPendente = async () => {
  const dadosFin = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;
  if (dadosFin) {
    const listaRestricoesPendentesPorFinanciado = await GET_DATA(
      getURI("negociacaoAnalisesRestricaoPorIdFinanciado") +
        "/" +
        dadosFin.id_Financiado,
      null,
      true
    );

    if (
      listaRestricoesPendentesPorFinanciado.length > 0 &&
      listaRestricoesPendentesPorFinanciado[0].status === "Aprovado"
    ) {
      return false;
    } else {
      return true;
    }
  }
};
const financiadoData = localStorage.getItem("financiadoData")
  ? JSON.parse(localStorage.getItem("financiadoData"))
  : null;

const userData = localStorage.getItem("user")
  ? JSON.parse(localStorage.getItem("user"))
  : null;

const getConfigAprovacaoJuridica = async () => {
  try {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "aprovacao_juridica"
    );
    if (response !== "") {
      let listconfig = JSON.parse(response);
      return listconfig;
    }
  } catch (error) {
    return [];
  }
};

async function verificaRestricoesNegociacao() {
  const listaRestricoesNegociacao = await GET_DATA(
    getURI("negociacaoParametriResticaoListar"),
    null,
    true
  );
  if (listaRestricoesNegociacao.length > 0) {
    const processosdados = localStorage.getItem("processos")
      ? JSON.parse(localStorage.getItem("processos"))
      : null;
    const financiadodados = localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null;
    if (processosdados === null || financiadodados === null) {
      return [];
    }
    const fases = processosdados.map((item) => item.fase_Atual);

    const fasesIguais = [];

    for (const valorArray of fases) {
      for (const item of listaRestricoesNegociacao) {
        if (item.name === valorArray) {
          fasesIguais.push(valorArray);
        }
      }
    }
    let restrigirNegociacao = false;
    if (fasesIguais.length > 0) {
      restrigirNegociacao = true;
    }
    const dataFinan = {
      id_financiado: financiadodados.id_Financiado,
      financiado: financiadodados.nome,
      restrigirNegociacao: restrigirNegociacao,
    };
    return dataFinan;
  }
  return [];
}

export {
  liberarNegociacao,
  showMenuAprovacaoJuridica,
  verificaAnaliseRestricaoPendente,
};
