import React from "react";
import { formatCurrency, formatDate } from 'src/reusable/helpers';
const tableStyle = {
  maxHeight: "300px", // Defina a altura máxima desejada
  overflowY: "auto",
  width: "100%"  // Adicione uma barra de rolagem vertical quando o conteúdo exceder a altura
};

const tableHeaderStyle = {
  backgroundColor: "#f2f2f2", // Cor de fundo do cabeçalho da tabela
  position: "sticky",
  top: "0", // Mantenha o cabeçalho colado no topo
};

const TableParcelasSimulacao = ({ dataTable }) => {
  const parcelasRestantes = dataTable.slice(1);
  return (
    <div className="table-responsive-sm" style={tableStyle}>
      <table className='table table-sm'>
        <thead style={tableHeaderStyle}>
          <tr>
            <th>Parcela</th>
            <th>Vencimento</th>
            <th>Valor</th>
            <th><PERSON><PERSON></th>
            <th><PERSON>or <PERSON></th>
            <th><PERSON><PERSON></th>
            <th><PERSON>or <PERSON></th>
          </tr>
        </thead>
        <tbody>
          {parcelasRestantes.map((item, index) => (
            <tr key={index}>
              <td>{item.numeroParcela}</td>
              <td>{formatDate(item.dtVencimento)}</td>
              <td>{formatCurrency(item.vlrParcela)}</td>
              <td>{formatCurrency(item.vlrAmortizacao)}</td>
              <td>{formatCurrency(item.vlrImposto)}</td>
              <td>{formatCurrency(item.vlrJuros)}</td>
              <td>{formatCurrency(item.vlrSaldo)}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TableParcelasSimulacao;
