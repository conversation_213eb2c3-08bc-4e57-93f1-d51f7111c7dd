import { CInput,  CCardTitle, CCol, <PERSON><PERSON>abel, CRow } from "@coreui/react";
import React, { useState } from "react";
import { formatCurrency, formatDate } from 'src/reusable/helpers';
import CardCalcular from "./CardCalcular";
import TableParcelasSimulacao from "./TableParcelasSimulacao";
import Select from "react-select";
import { isEmailValid } from "src/reusable/helpers";

const DadosDaSimulacaoSimulacao = ({ simulacao,onEmailChange,emailValido }) => {
  const [tipoEnvioOutro, setTipoEnvioOutro] = useState(false);
  const entrada = formatCurrency(simulacao.dadosSafra.simulacao.parcelas[0].vlrParcela);
  const dataEntrada = formatDate(simulacao.dadosSafra.simulacao.complementar.dtParcelaEntrada);
  const financiado = simulacao.dadosFinanciado.nome + " - " + simulacao.dadosFinanciado.cpfCnpj;
  //const contrato = simulacao.dadosSafra.contrato;
  const valorParcelas = formatCurrency(simulacao.dadosSafra.simulacao.parcelas[1].vlrParcela);
  const qtdParcelas = simulacao.dadosSafra.simulacao.complementar.qtParcelasAtivas - 1;
  let emailsCliente = localStorage.getItem("clientData") ? JSON.parse(localStorage.getItem("clientData")) : "";
  const optionsContrato = [
    ...emailsCliente.emails.map((item) => {
      return { label: item.endereco_Email, value: item.endereco_Email };
    }),
  ];
  const initialErrors = {
    email: ""
  };
  const [errors, setErrors] = useState(initialErrors);

  const handleTipoEnvio = (value) => {
    setTipoEnvioOutro(value);
  }
  const handleEmail = (value) => {
    if (isEmailValid(value)) {
      setErrors({ email: "" });
    } else {
      setErrors({ email: "E-mail inválido" });
    }
    onEmailChange(value);
  };

  return (
    <div>
      <CRow>
        <CCol xs="7">
          <CRow>
            <CCol xs="6" sm="12">
              <CLabel style={{ color: "gray" }}>Financiado</CLabel> <br />
              <CLabel>
                <strong>{financiado}</strong>
              </CLabel>
            </CCol>
          </CRow>
          <CRow>
            <CCol xs="6" sm="3">
              <CLabel style={{ color: "gray" }}>Entrada</CLabel> <br />
              <CLabel>
                <strong>{entrada}</strong>
              </CLabel>
            </CCol>
            <CCol xs="6" sm="3">
              <CLabel style={{ color: "gray" }}>Data da entrada</CLabel> <br />
              <CLabel>
                <strong>{dataEntrada}</strong>
              </CLabel>
            </CCol>
            <CCol xs="6" sm="3">
              <CLabel style={{ color: "gray" }}>Parcelamento</CLabel> <br />
              <CLabel>
                <strong>{qtdParcelas}</strong>
              </CLabel>
            </CCol>
            <CCol xs="6" sm="3">
              <CLabel style={{ color: "gray" }}>Valor das Parcelas</CLabel> <br />
              <CLabel>
                <strong>{valorParcelas}</strong>
              </CLabel>
            </CCol>
          </CRow>

          <CRow>
          <CCol>
            <CRow>
              <CCol>
                <div className={"form-check form-switch"}>
                  <input className={"form-check-input"} type="checkbox" role="switch" id="flexSwitchCheckDefault" onChange={(e) => { handleTipoEnvio(e.target.checked); }} />
                  <label className={"form-check-label"} htmlFor="flexSwitchCheckDefault">Usar outro e-mail</label>
                </div>
              </CCol>

            </CRow>
              <CRow className={"mt-3"}>
                {!tipoEnvioOutro ? <CCol>
                  <CLabel>E-mail Cadastrado</CLabel> <br />
                  <Select
                    options={optionsContrato}
                    onChange={(e) => { handleEmail(e.value); }}
                    placeholder={"Selecione"}
                    className={errors.email ? 'border-danger rounded' : ''}
                  />
                  {errors.email && <div className="text-danger">{errors.email}</div>}
                  {!emailValido && <div className="text-danger">E-mail inválido</div>}
                </CCol> : <CCol>
                  <CLabel>E-mail</CLabel> <br />
                  <CInput
                    type="email"
                    onChange={(e) => { handleEmail(e.target.value); }}
                    placeholder={"Ex: <EMAIL>"}
                    className={errors.email ? 'border-danger rounded' : ''}
                  />
                  {errors.email && <div className="text-danger">{errors.email}</div>}
                  {!emailValido && <div className="text-danger">E-mail inválido</div>}
                </CCol>}
              </CRow>
            </CCol>
          </CRow>

          <CRow>
            <CCol>

              <CCardTitle style={{ fontSize: "1.2rem", marginTop:"15px" }}>
                <strong>Parcelamento do Acordo</strong>
              </CCardTitle>

                <TableParcelasSimulacao dataTable={simulacao.dadosSafra.simulacao.parcelas} />


            </CCol>
          </CRow>
        </CCol>
        <CCol xs="5">
          <CardCalcular contratoNegociar={simulacao} />
        </CCol>
      </CRow>

    </div>);
};

export default DadosDaSimulacaoSimulacao;
