import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CButton,
  CModalTitle,
  CCardBody,
  CForm,
  CFormGroup,
  CInput,
  CLabel,
  CCardHeader,
} from "@coreui/react";
import { getApi } from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { OccurrenceIndicator } from "src/types/apiGroup/datacob";
import { useMyContext } from "src/reusable/DataContext";

type NrNeg = {
  qtd: number;
};

const OccurrenceIndicatorModal = ({ onClose }: { onClose: () => void }) => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<OccurrenceIndicator[]>([]);
  const [nrNeg, setNrNeg] = useState(0);
  const [error, setError] = useState("");
  const { data } = useMyContext();

  useEffect(() => {
    if (error !== "") {
      toast.info(error);
      setError("");
    }
  }, [error, onClose]);

  const getList = async () => {
    setLoading(true);
    const response = await getApi(
      { idAgrupamento: data.id_Agrupamento },
      "getIndicadoresHistoricos"
    );
    if (response && response.length > 0) {
      setList(response);
    }
    setLoading(false);
  };

  const getNrNeg = async () => {
    setLoading(true);
    const response = (await getApi(
      { idAgrupamento: data.id_Agrupamento },
      "getNumeroNegociacoes"
    )) as NrNeg;
    if (
      response !== null &&
      response !== undefined &&
      response?.qtd !== undefined
    ) {
      setNrNeg(response.qtd);
    }
    setLoading(false);
  };

  useEffect(() => {
    getList();
    getNrNeg();
  }, []);

  return (
    <CModal
      show={true}
      onClose={onClose}
      closeOnBackdrop={true}
      className="custom-modal"
      scrollable={true}
      backdrop={true}
      size="lg"
    >
      <CModalHeader closeButton={true}>
        <CModalTitle className={"bold"}>Atenção operador!</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CRow>
          <CCol>
            <CCardHeader>
              <strong>Este cliente possuí o seguinte histórico:</strong>
            </CCardHeader>
            <CCardBody className="py-1">
              {loading ? (
                <CardLoading Title={""} Msg={""} />
              ) : (
                <>
                  {list.length === 0 && <div>Nenhum histórico encontrado.</div>}
                  {list.length > 0 && (
                    <table className="table table-hover table-striped">
                      <thead className="thead-dark">
                        <tr>
                          <th scope="col" className="text-nowrap">
                            Qtd Ocorrências
                          </th>
                          <th scope="col">Descrição</th>
                          <th scope="col">Nome Operador</th>
                        </tr>
                      </thead>
                      <tbody>
                        {list.map((item) => (
                          <tr>
                            <td className="text-center">
                              {item.qtdOcorrencias}
                            </td>
                            <td>{item.descricao}</td>
                            <td>{item.nome}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  )}
                </>
              )}
            </CCardBody>
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <strong>Negociações geradas até este momento: {nrNeg}</strong>
      </CModalFooter>
    </CModal>
  );
};

export default OccurrenceIndicatorModal;
