import React from "react";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

// Componente wrapper para o DatePicker com estilos customizados
export const CustomDatePicker = ({ selected, onChange, ...props }) => {
  React.useEffect(() => {
    // Adiciona estilos customizados apenas uma vez
    const styleId = "custom-datepicker-styles";
    if (!document.getElementById(styleId)) {
      const styles = `
        .custom-datepicker-calendar {
          border: 1px solid #ced4da;
          border-radius: 0.375rem;
          box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
          font-family: inherit;
        }

        .react-datepicker-wrapper {
          width: 100% !important;
        }

        .react-datepicker__input-container {
          width: 100% !important;
        }

        .react-datepicker__input-container input {
          width: 100% !important;
          height: 38px !important;
          padding: 0.375rem 0.75rem !important;
          font-size: 0.875rem !important;
          line-height: 1.5 !important;
          border: 1px solid #ced4da !important;
          border-radius: 0.375rem !important;
          box-sizing: border-box !important;
          overflow: visible !important;
          text-overflow: clip !important;
          white-space: nowrap !important;
          min-width: 0 !important;
        }

        .react-datepicker__input-container input:focus {
          border-color: #958bef !important;
          outline: 0 !important;
          box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.25) !important;
        }

        .custom-datepicker-calendar .react-datepicker__header {
          background-color: #007bff;
          border-bottom: 1px solid #007bff;
          border-radius: 0.375rem 0.375rem 0 0;
        }

        .custom-datepicker-calendar .react-datepicker__current-month {
          color: white;
          font-weight: 500;
        }

        .custom-datepicker-calendar .react-datepicker__day-name {
          color: white;
          font-weight: 500;
        }

        .custom-datepicker-calendar .react-datepicker__day:hover {
          background-color: #e9ecef;
          border-radius: 0.25rem;
        }

        .custom-datepicker-calendar .react-datepicker__day--selected {
          background-color: #007bff;
          color: white;
          border-radius: 0.25rem;
        }

        .custom-datepicker-calendar .react-datepicker__day--keyboard-selected {
          background-color: #0056b3;
          color: white;
          border-radius: 0.25rem;
        }

        .custom-datepicker-calendar .react-datepicker__day--today {
          background-color: #ffc107;
          color: #212529;
          border-radius: 0.25rem;
          font-weight: 500;
        }

        .custom-datepicker-calendar .react-datepicker__navigation {
          top: 13px;
        }

        .custom-datepicker-calendar .react-datepicker__navigation--previous {
          border-right-color: white;
        }

        .custom-datepicker-calendar .react-datepicker__navigation--next {
          border-left-color: white;
        }


        /* Ajustes específicos para o calendário */
        .custom-datepicker-calendar {
          margin-top: 0 !important;
          margin-bottom: 0 !important;
          max-height: 400px !important;
          overflow-y: auto !important;
        }

        /* Fix for calendar moving when scrolling */
        .react-datepicker-popper {
          position: fixed !important;
          z-index: 9999 !important;
        }
        
        /* Ensure modal doesn't cut the calendar */
        .modal-content {
          overflow: visible !important;
        }
        
        .modal-body {
          overflow: visible !important;
        }
      `;

      const styleSheet = document.createElement("style");
      styleSheet.id = styleId;
      styleSheet.innerText = styles;
      document.head.appendChild(styleSheet);
    }
  }, []);

  return (
    <div style={{ width: "100%", overflow: "visible", position: "relative" }}>
      <ReactDatePicker
        selected={selected}
        onChange={onChange}
        className="form-control"
        dateFormat="dd/MM/yyyy"
        placeholderText="Selecione uma data"
        showPopperArrow={false}
        calendarClassName="custom-datepicker-calendar"
        wrapperClassName="w-100"
        onKeyDown={(e) => e.preventDefault()}
        popperPlacement="top-start"
        popperModifiers={{
          preventOverflow: {
            enabled: true,
            escapeWithReference: false,
            boundariesElement: "viewport",
          },
        }}
        {...props}
      />
    </div>
  );
};
