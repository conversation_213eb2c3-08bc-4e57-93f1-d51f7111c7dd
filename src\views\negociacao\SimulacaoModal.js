import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CButton,
  CCard,
  CCardBody,
  CFormGroup,
} from "@coreui/react";
import Select from "react-select";
import { GET_DATA } from "src/api";
import DescontoPrincipal from "./SimulacaoModais/DescontoPrincipal";
import RegularizacaoAVista from "./SimulacaoModais/RegularizacaoAVista";
import RegularizacaoParcelada from "./SimulacaoModais/RegularizacaoParcelada";
import QuitacaoPrevia from "./SimulacaoModais/QuitacaoPrevia";
import QuitacaoParcelada from "./SimulacaoModais/QuitacaoParcelada";
import SimulacaoEntradaDiluicao from "./SimulacaoModais/SimulacaoEntradaDiluicao";
import SimulacaoEntradaAditamento from "./SimulacaoModais/SimulacaoEntradaAditamento";
import BancoRodobens from "./SimulacaoModais/BancoRodobens";
import SimulacaoEntradaAditamentoConsorcio from "./SimulacaoModais/SimulacaoEntradaAditamentoConsorcio";
// import BancoRodobens from "./SimulacaoModais/BancoRodobens";

const SimulacaoModal = ({
  isOpen,
  onClose,
  onSave,
  isOcorrencia = false,
  cleanCalculoPost,
  valorMinRegua,
  valorMaxRegua,
}) => {
  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : "";

  const cnscCotas =
    localStorage.getItem("cnscCotas") &&
      localStorage.getItem("cnscCotas") !== "undefined"
      ? JSON.parse(localStorage.getItem("cnscCotas")) ?? []
      : [];

  const [contratos, setContratos] = useState([]);
  const [modeloSelecionado, setModeloSelecionado] = useState(null);

  // const [mensagem, setMensagem] = useState("");
  const [selectedOption, setSelectedOption] = useState(null);
  const [optionsModelo, setOptionsModelo] = useState([]);

  async function getTemplates() {
    const templates = await GET_DATA("Simulacoes/Templates");
    if (templates) {
      const options = [...templates];
      setOptionsModelo(options);
    }
    return;
  }

  const handleSave = async (textLines) => {
    const textMessage = textLines.map((msg) => `${msg}`).join("\n");
    onSave(textMessage);
    onClose();
  };

  const handleClose = () => {
    onClose();
  };

  const modelos = [
    {
      name: "Desconto no principal",
      id: 5,
      content: (
        <DescontoPrincipal
          onSave={handleSave}
          onClose={handleClose}
          cleanCalculoPost={cleanCalculoPost}
        />
      ),
    },
    {
      name: "Regularização parcelada",
      id: 4,
      content: (
        <RegularizacaoParcelada
          onSave={handleSave}
          onClose={handleClose}
          ocorrencia={isOcorrencia}
          cleanCalculoPost={cleanCalculoPost}
        />
      ),
    },
    {
      name: "Regularização à vista",
      id: 3,
      content: (
        <RegularizacaoAVista
          onSave={handleSave}
          onClose={handleClose}
          contratos={contratos}
          cleanCalculoPost={cleanCalculoPost}
          ocorrencia={isOcorrencia}
        />
      ),
    },
    {
      name: "Quitação com prévia",
      id: 2,
      content: (
        <QuitacaoPrevia
          onSave={handleSave}
          onClose={handleClose}
          cnscCotas={cnscCotas}
          contratos={contratos}
          ocorrencia={isOcorrencia}
        />
      ),
    },
    {
      name: "Quitação parcelada",
      id: 1,
      content: (
        <QuitacaoParcelada
          onSave={handleSave}
          onClose={handleClose}
          ocorrencia={isOcorrencia}
          cleanCalculoPost={cleanCalculoPost}
          valorMinRegua={valorMinRegua}
          valorMaxRegua={valorMaxRegua}
        />
      ),
    },
    {
      name: "Simulador Entrada Diluição",
      id: 6,
      content: <SimulacaoEntradaDiluicao cleanCalculoPost={cleanCalculoPost} />,
    },
    {
      name: "Simulador Entrada Aditamento",
      id: 7,
      content: (
        <SimulacaoEntradaAditamento cleanCalculoPost={cleanCalculoPost} />
      ),
    },
    {
      name: "Calculadora Banco Rodobens",
      id: 8,
      content: (
        <BancoRodobens
          cleanCalculoPost={cleanCalculoPost}
          onClose={handleClose}
        />
      ),
    },
    {
      //name: "Simulador Entrada Aditamento - Consórcio",
      id: 9,
      content: (
        <SimulacaoEntradaAditamentoConsorcio
          cleanCalculoPost={cleanCalculoPost}
          onClose={handleClose}
        />
      ),
    },
  ];

  const handleOptionChange = (selectedValue) => {
    setModeloSelecionado(selectedValue);
    const selectedModelo = modelos.find(
      (modelo) => modelo.id === selectedValue.id
    );
    setSelectedOption(selectedModelo);
  };

  useEffect(() => {
    getTemplates();

    const contratoOptions = contratosAtivos.map((item) => {
      return { label: item.numero_Contrato, value: item.id_Contrato };
    });
    setContratos(contratoOptions);
  }, [isOpen]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="xl">
      <CModalHeader closeButton>Simuladores disponíveis</CModalHeader>
      <CModalBody>
        {/* <CRow>
          <CCol>
            <CRow className="g-3">
              {optionsModelo.map((item) => (
                <CCol
                  key={item.id}
                  xs="6"
                  sm="4"
                  md="3"
                  lg="2"
                  onClick={handleOptionChange.bind(this, item)}
                >
                  <CCard className="text-center">
                    <CCardBody
                      style={{
                        borderRadius: "0.25rem",
                        cursor: "pointer",
                        backgroundColor: item?.colorCard ?? "#357ab4",
                        color: item?.colorText ?? "#ffffff",
                      }}
                    >
                      {item.name}
                    </CCardBody>
                  </CCard>
                </CCol>
              ))}
            </CRow>
            <h2>{selectedOption?.name}</h2>
          </CCol>
        </CRow> */}
        <CRow>
          <CCol>
            <CRow className="g-3" style={{ display: "flex", flexWrap: "wrap" }}>
              {optionsModelo.map((item) => (
                <CCol
                  key={item.id}
                  xs="6"
                  sm="4"
                  md="3"
                  lg="2"
                  onClick={handleOptionChange.bind(this, item)}
                  style={{ display: "flex", paddingBottom: "30px" }} // Garante que o CCol seja um contêiner flexível
                >
                  <CCard
                    className="text-center"
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      height: "100%",
                    }}
                  >
                    <CCardBody
                      style={{
                        borderRadius: "0.25rem",
                        cursor: "pointer",
                        backgroundColor: item?.colorCard ?? "#357ab4",
                        color: item?.colorText ?? "#ffffff",
                        flex: 1, // Faz o card ocupar toda a altura disponível
                        display: "flex",
                        justifyContent: "center", // Centraliza o conteúdo verticalmente
                        alignItems: "center", // Centraliza o conteúdo horizontalmente
                        textAlign: "center", // Garante que o texto dentro do card esteja centralizado
                      }}
                    >
                      {item.name}
                    </CCardBody>
                  </CCard>
                </CCol>
              ))}
            </CRow>
            <h2>{modeloSelecionado?.name}</h2>
          </CCol>
        </CRow>

        <CRow className="mt-2">
          <CCol>
            {selectedOption ? (
              <>{selectedOption.content}</>
            ) : (
              <div
                className="py-4"
                style={{ color: "gray", textAlign: "center" }}
              >
                Selecione um dos modelos de negociação.
              </div>
            )}
          </CCol>
        </CRow>
      </CModalBody>
    </CModal>
  );
};

export default SimulacaoModal;
