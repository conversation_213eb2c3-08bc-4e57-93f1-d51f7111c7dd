import React, { useState } from "react";

import "react-datepicker/dist/react-datepicker.css";

import {
  CButton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalTitle,
  CLabel,
  CModalFooter,
} from "@coreui/react";
import { postApi } from "src/reusable/functions";
import LoadingComponent from "src/reusable/Loading";

const ReactiveCustasModal = ({ isOpen, onClose, custaId, contratoId }) => {
  const [loading, setLoading] = useState(false);
  const reactiveCustas = async () => {
    setLoading(true);
    const payload = {
      idContrato: contratoId,
      custas: {
        idCustas: custaId,
      },
    };
    try {
      const ret = await postApi(payload, "postCustasReativar");

      if (ret?.status === 400) throw new Error(ret.errors);
      if (!ret?.success) throw new Error(ret.message);

      onClose();
    } catch (err) {
      console.log(err);
    }
    setLoading(false);
  };

  return (
    <CModal
      className="custom-modal"
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
    >
      <CModalHeader closeButton>
        <CModalTitle>Reativar Custa</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CLabel>Tem certeza que deseja reativar a custa?</CLabel>
      </CModalBody>
      <CModalFooter>
        {loading && <LoadingComponent />}
        {!loading && (
          <>
            <CButton color="success" className="mr-2" onClick={reactiveCustas}>
              Sim
            </CButton>
            <CButton color="danger" className="mr-2" onClick={onClose}>
              Não
            </CButton>
          </>
        )}
      </CModalFooter>
    </CModal>
  );
};

export default ReactiveCustasModal;
