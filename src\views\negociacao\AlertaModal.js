import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CButton,
} from "@coreui/react";

const AlertaModal = ({ isOpen, onClose, dados }) => {
  const [mensagem, setMensagem] = useState("");

  const handleClose = () => {
    onClose();
  };

  // Remove unwanted characters and replace escape sequences
  const formattedMessage = dados
    .replace(/\r\n/g, "\n") // Replace Windows-style line breaks with newlines
    .replace(/^(?:\[|\])$/g, "") // Remove leading "[" or trailing "]"
    .replace(/\\n/g, "\n") // Replace escape sequences "\n" with newlines
    .replace(/\\("|')/g, "$1") // Replace escaped double quotes with actual double quotes
    .replace(/\r/g, ""); // Remove carriage return characters

  useEffect(() => {
    setMensagem(formattedMessage);
  }, [isOpen]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton></CModalHeader>
      <CModalBody>
        <CRow>
          <CCol>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                padding: "12px",
              }}
            >
              {mensagem}
            </div>
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter className="py-1">
        <CButton size="sm" color="secondary" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default AlertaModal;
