import React, { useState, useEffect } from "react";
import { CButton, CDataTable, CRow, CCol } from "@coreui/react";
import { POST_DATA, GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import LoadingComponent from "src/reusable/Loading";
import TableSelectItens from "src/reusable/TableSelectItens";

import DetalhesEstruturaComercialModal from "./EstruturaComercialModal/DetalhesEstruturaComerciaModal";
import EquipeVendaModal from "./EstruturaComercialModal/EquipeVendaModal";
import PontoVendaModal from "./EstruturaComercialModal/PontoVendaModal";
import DadosSociosModal from "./EstruturaComercialModal/DadosSociosModal";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";

const EstruturaComercial = ({ selected }) => {
  const cnscCotas =
    localStorage.getItem("cnscCotas") &&
      localStorage.getItem("cnscCotas") !== "undefined"
      ? JSON.parse(localStorage.getItem("cnscCotas")) ?? []
      : [];

  const estruturaVendas = localStorage.getItem("estruturaVendas")
    ? JSON.parse(localStorage.getItem("estruturaVendas"))
    : [];

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const [isLoading, setIsLoading] = useState(false);
  const [tableData, setTableData] = useState([]);

  const [selectedRow, setSelectedRow] = useState(null);

  const [dadosEstruturaVendas, setDadosEstruturaVendas] = useState(null);
  const [dadosEstruturaComercial, setDadosEstruturaComercial] = useState(null);
  const [dadosEquipeVenda, setDadosEquipeVenda] = useState(null);
  const [dadosPontosVenda, setDadosPontoVenda] = useState(null);
  const [dadosSocios, setDadosSocios] = useState(null);

  const [showDetalhesEstrutura, setShowDetalhesEstrutura] = useState(false);
  const [showEquipeVenda, setShowEquipeVenda] = useState(false);
  const [showPontoVenda, setShowPontovenda] = useState(false);
  const [showDadosSocios, setShowDadosSocios] = useState(false);

  const handleClick = async (item) => {
    setSelectedRow(item.cod);
  };

  const handleButtonEquipeVenda = () => {
    setDadosEquipeVenda(cnscCotas.idCota);
    setShowEquipeVenda(true);
  };

  const handleButtonPontoVenda = () => {
    setDadosPontoVenda(cnscCotas.idCota);
    setShowPontovenda(true);
  };

  const handleRowDoubleClick = (item) => {
    setDadosEstruturaComercial(item);
    setShowDetalhesEstrutura(true);
  };

  const rowClassName = (item) => {
    return item.cod === selectedRow ? "selected-message" : "";
  };

  const fields = [
    {
      key: "cod",
      label: "Código",
      formatterByObject: (item) => renderRow(item, "cod"),
    },
    {
      key: "name",
      label: "Nome Estrutura",
      formatterByObject: (item) => renderRow(item, "name"),
    },
    {
      key: "codComissioned",
      label: "Código Comissionado",
      formatterByObject: (item) => renderRow(item, "codComissioned"),
    },
    {
      key: "nameComissioned",
      label: "Nome Comissionado",
      formatterByObject: (item) => renderRow(item, "nameComissioned"),
    },
  ];

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const updateView = async () => {
    const IdCota = cnscCotas.idCota;
    setIsLoading(true);
    GetData(`/${IdCota}`, "getNewConEstruturaComercial")
      .then((data) => {
        if (data && data.length > 0) {
          setDadosEstruturaVendas(data);
          setTableData(data);
        } else {
          setDadosEstruturaVendas(null);
          setTableData(null);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const renderRow = (item, value) => {
    return (
      <div onDoubleClick={() => handleRowDoubleClick(item)}>
        {item[value] ?? "---"}
      </div>
    );
  };

  useEffect(() => {
    if (financiadoData) {
      if (selected === true) {
        updateView();
      }
    }
  }, [selected]);

  return (
    <>
      {" "}
      {isLoading ? (
        <div className="mt-2">
          <LoadingComponent />
        </div>
      ) : tableData == null ||
        tableData === undefined ||
        tableData.length == 0 ? (
        <NaoHaDadosTables />
      ) : (
        <>
          <CRow className="my-2">
            <CCol md="4">
              <CButton
                color="info"
                className="py-2 mr-2 mb-1"
                onClick={handleButtonEquipeVenda}
              >
                Equipe de Venda
              </CButton>
              <CButton
                color="info"
                className="py-2 mb-1"
                onClick={handleButtonPontoVenda}
              >
                Ponto de Venda
              </CButton>
            </CCol>
            <CCol md="8" className="information-text">
            Dê um duplo clique com o botão esquerdo do mouse para ver os detalhes das informações.
            </CCol>
          </CRow>
          <TableSelectItens
            data={tableData}
            columns={fields}
            onSelectionChange={(_) => {}}
            defaultSelectedKeys={[]}
            selectable={false}
            rowDataClass={"double-clickable-table"}
          />
        </>
      )}
      <DetalhesEstruturaComercialModal
        isOpen={showDetalhesEstrutura}
        onClose={() => setShowDetalhesEstrutura(false)}
        item={dadosEstruturaComercial}
      />
      <EquipeVendaModal
        isOpen={showEquipeVenda}
        onClose={() => setShowEquipeVenda(false)}
        dados={dadosEquipeVenda}
      />
      <PontoVendaModal
        isOpen={showPontoVenda}
        onClose={() => setShowPontovenda(false)}
        dados={dadosPontosVenda}
      />
      <DadosSociosModal
        isOpen={showDadosSocios}
        onClose={() => setShowDadosSocios(false)}
        dados={dadosSocios}
      />
    </>
  );
};

export default EstruturaComercial;
