import React from "react";
import { Route, Redirect } from "react-router-dom";

const AuthorizedRoute = ({ component: Component, permission, ...rest }) => {
  const user = JSON.parse(localStorage.getItem("user"));

  const hasPermission = user.role.modules.find(
    (module) => module.module === permission
  );

  return (
    <Route
      {...rest}
      render={(props) =>
        hasPermission || user.isAdmin ? (
          <Component {...props} />
        ) : (
          <Redirect to="/login" />
        )
      }
    />
  );
};

export default AuthorizedRoute;
