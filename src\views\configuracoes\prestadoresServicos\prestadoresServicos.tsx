import React, { useEffect, useState } from "react";
import {
  CModalBody,
  CButton,
  CCard,
  CCardBody,
  CCol,
  CInput,
  CLabel,
  CRow,
  CModal,
  CModalHeader,
  CModalFooter,
  CDataTable,
  CInputCheckbox,
  CCardTitle,
} from "@coreui/react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { toast } from "react-toastify";
import { deleteApiInline, postApi, putApi } from "src/reusable/functions";
import ConfirmModal from "src/reusable/ConfirmModal.js";
import { set } from "core-js/core/dict";

type PrestadoresServicos = {
  id: number;
  nome: string;
  tipo: string;
  telefone1: string;
  telefone2: string;
  telefone3: string;
};

const PrestadoresServicos = () => {
  const [tipoListCheck, setTipoListCheck] = useState([]);
  const [selecionados, setSelecionados] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [listaPrestadores, setListaPrestadores] = useState([]);

  const [id, setId] = useState(null);
  const [name, setName] = useState("");
  const [telefone1, setTelefone1] = useState("");
  const [telefone2, setTelefone2] = useState("");
  const [telefone3, setTelefone3] = useState("");

  const [itemDeleteSelected, setItemDeleteSelected] = useState(null);
  const [showModelDelete, setShowModelDelete] = useState(false);

  const getConfigItemsListCheck = async () => {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "tipos_prestadores_servicos"
    );
    try {
      let lista = JSON.parse(response);
      console.log("lista", lista);
      setTipoListCheck(lista);
    } catch (err) {
      return [];
    }
  };

  const handleCheckboxChange = (item) => {
    setSelecionados((prev) =>
      prev.includes(item) ? prev.filter((i) => i !== item) : [...prev, item]
    );
  };
  const columns = [
    { key: "nome", label: "Nome" },
    { key: "tipo", label: "Tipo" },
    { key: "telefone1", label: "Telefone 1" },
    { key: "telefone2", label: "Telefone 2" },
    { key: "telefone3", label: "Telefone 3" },
    { key: "actions", label: "Ações", className: "text-right" },
  ];

  const getListaPrestadores = async () => {
    setIsLoading(true);
    await GetData(null, "prestadoresServicos")
      .then((resultado: PrestadoresServicos[]) => {
        if (resultado) {
          setListaPrestadores(resultado);
        } else {
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleSave = async () => {
    if (name === "") {
      toast.error("Preencha o nome do prestador");
      return;
    }

    if (id !== null) {
      let payload = {
        id: id,
        nome: name,
        telefone1: telefone1,
        telefone2: telefone2,
        telefone3: telefone3,
        tipo: JSON.stringify(selecionados),
      };

      await putApi(payload, "prestadoresServicos")
        .then((resultado) => {
          if (resultado) {
            toast.success("Registro atualizado com sucesso!");
          } else {
            toast.error("Erro ao atualizar registro!");
          }
        })
        .catch((err) => {
          console.log("erro", err);
        })
        .finally(() => {
          limparForm();
          setIsLoading(false);
          getListaPrestadores();
        });

      return;
    }

    let payload = {
      nome: name,
      telefone1: telefone1,
      telefone2: telefone2,
      telefone3: telefone3,
      tipo: JSON.stringify(selecionados),
    };

    await postApi(payload, "prestadoresServicos")
      .then((resultado) => {
        if (resultado) {
          toast.success("Registro salvo com sucesso!");
          getListaPrestadores();
        } else {
          toast.error("Erro ao salvar registro!");
        }
      })
      .catch((err) => {
        console.log("erro", err);
      })
      .finally(() => {
        limparForm();
        setIsLoading(false);
        getListaPrestadores();
      });
  };

  const limparForm = () => {
    setId(null);
    setName("");
    setTelefone1("");
    setTelefone2("");
    setTelefone3("");
    setSelecionados([]);
  };

  const handleDeleteSubmit = async () => {
    setIsLoading(true);
    if (itemDeleteSelected === null) {
      return;
    }
    await deleteApiInline(itemDeleteSelected.id, "prestadoresServicos")
      .then((resultado) => {
        if (resultado) {
          toast.success("Registro excluído com sucesso!");
          getListaPrestadores();
        } else {
          toast.error("Erro ao excluir registro!");
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        getListaPrestadores();
        setIsLoading(false);
        setShowModelDelete(false);
      });
  };
  const handleDelete = async (item: PrestadoresServicos) => {
    if (item.id === null) {
      return;
    }
    setItemDeleteSelected(item);
    setShowModelDelete(true);
  };

  const handleEdit = async (item: PrestadoresServicos) => {
    if (item.id === null) {
      return;
    }
    setId(item.id);
    setName(item.nome);
    setTelefone1(item.telefone1);
    setTelefone2(item.telefone2);
    setTelefone3(item.telefone3);
    setSelecionados(JSON.parse(item.tipo));
  };

  const renderActions = (item: PrestadoresServicos) => {
    return (
      <td>
        <CButton
          color="warning"
          onClick={() => handleEdit(item)}
          className="mr-2"
          size="sm"
        >
          <i className="cil-pencil" />
        </CButton>
        <CButton
          color="danger"
          onClick={() => {
            handleDelete(item);
          }}
          size="sm"
        >
          <i className="cil-trash" />
        </CButton>
      </td>
    );
  };

  const renderTipo = (item: PrestadoresServicos) => {
    if (!item.tipo) {
      return <td></td>;
    }

    try {
      // Substitui aspas simples por aspas duplas
      const jsonCorrigido = item.tipo.replace(/'/g, '"');
      const array = JSON.parse(jsonCorrigido);

      return (
        <td>
          {array.map((tipo: string, index: number) => (
            <span className="badge badge-secondary mr-1" key={index}>
              {tipo}
              {index < array.length - 1 ? ", " : ""}
            </span>
          ))}
        </td>
      );
    } catch (error) {
      console.error("Erro ao converter tipo:", item.tipo, error);
      return <td>Erro</td>;
    }
  };

  const telefoneFormatado = (e: string) => {
    let value = e.replace(/\D/g, ""); // remove tudo que não for número

    if (value.length === 0) {
      return "";
    }

    if (value.length > 11) value = value.slice(0, 11);

    if (value.length > 10) {
      // 11 dígitos: celular com 9 dígitos no número
      value = value.replace(/^(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
    } else if (value.length > 6) {
      // 10 dígitos: telefone fixo com 8 dígitos no número
      value = value.replace(/^(\d{2})(\d{4})(\d{0,4})/, "($1) $2-$3");
    } else if (value.length > 2) {
      value = value.replace(/^(\d{2})(\d{0,5})/, "($1) $2");
    } else {
      value = value.replace(/^(\d*)/, "($1");
    }

    return value;
  };

  useEffect(() => {
    getConfigItemsListCheck();
    getListaPrestadores();
  }, []);

  return (
    <div>
      <h3>Cadastro de prestadores de serviços</h3>
      <CCard>
        <CCardBody>
          <CRow>
            <CCol md={3}>
              <CLabel>Nome</CLabel>
              <CInput
                type="text"
                name="nome"
                value={name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setName(e.target.value)
                }
              />
            </CCol>
            <CCol md={2}>
              <CLabel>Telefone 1</CLabel>
              <CInput
                type="text"
                name="telefone1"
                value={telefone1}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setTelefone1(telefoneFormatado(e.target.value))
                }
              />
            </CCol>
            <CCol md={2}>
              <CLabel>Telefone 2</CLabel>
              <CInput
                type="text"
                name="telefone2"
                value={telefone2}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setTelefone2(telefoneFormatado(e.target.value))
                }
              />
            </CCol>
            <CCol md={2}>
              <CLabel>Telefone 3</CLabel>
              <CInput
                type="text"
                name="telefone3"
                value={telefone3}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setTelefone3(telefoneFormatado(e.target.value))
                }
              />
            </CCol>
            <CCol
              md={2}
              className={"d-flex justify-content-end align-items-center"}
            >
              <CButton
                className="d-flex"
                color="primary"
                onClick={() => {
                  handleSave();
                }}
              >
                Salvar
              </CButton>
            </CCol>
          </CRow>
          <CRow>
            <CCol md={12} className="d-flex justify-content-start mt-2">
              {tipoListCheck?.map((item, index) => (
                <div key={index} className="form-check  mr-4">
                  <CInputCheckbox
                    id={`checkbox-${index}`}
                    checked={selecionados.includes(item)}
                    onChange={() => handleCheckboxChange(item)}
                    className="form-check-input"
                  />
                  <CLabel
                    htmlFor={`checkbox-${index}`}
                    className="form-check-label"
                  >
                    {item}
                  </CLabel>
                </div>
              ))}
            </CCol>
          </CRow>
        </CCardBody>
      </CCard>
      <CCard>
        <CCardBody>
          <CCardTitle>Prestadores Cadastrados</CCardTitle>
          <CRow>
            <CCol md={12} className="d-flex justify-content-start mt-2">
              <CDataTable
                items={listaPrestadores}
                fields={columns}
                hover
                striped
                loading={isLoading}
                scopedSlots={{
                  actions: (item) => renderActions(item),
                  tipo: (item) => renderTipo(item),
                }}
                noItemsViewSlot={
                  <p className="text-center">Nenhum registro encontrado</p>
                }
              />
            </CCol>
          </CRow>
        </CCardBody>
      </CCard>
      <ConfirmModal
        isOpen={showModelDelete}
        texto={
          "Deseja realmente excluir o registro selecionado? Esta ação não pode ser desfeita!"
        }
        onClose={(t) => {
          if (t) {
            handleDeleteSubmit();
          }
          setShowModelDelete(false);
        }}
      />
    </div>
  );
};

export default PrestadoresServicos;
