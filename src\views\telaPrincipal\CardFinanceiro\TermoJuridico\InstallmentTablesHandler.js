import {
  CCard,
  CNav,
  CNavItem,
  CNavLink,
  CTabContent,
  CTabPane,
  CTabs,
} from "@coreui/react";
import { useCallback, useEffect, useState } from "react";
import {
  formatCurrency,
  formatDate,
  formatThousands,
} from "src/reusable/helpers";
import LoadingComponent from "src/reusable/Loading";
import NewconTableInstallment from "src/views/negociacao/SimulacaoModais/components/NewconInstallmentTable";
import TableInstallmentTermo from "./TableInstallmentTermo";
import { postApi } from "src/reusable/functions";

const SELECTED_DATE = new Date();

const TableDatacobTermo = ({ tableData, handleTableDataChange }) => {
  const [checkedAll, setCheckedAll] = useState(
    tableData.every((x) => x.parcelaSelecionada)
  );
  const selectedContract = "";

  const handleSelectAll = (e) => {
    const newTableData = tableData.map((item) => {
      if (
        item.nrContrato?.replaceAll(" ", "") ===
        selectedContract?.replaceAll(" ", "")
      )
        item.parcelaSelecionada = e.target.checked;
      else if (selectedContract === "")
        item.parcelaSelecionada = e.target.checked;
      return item;
    });
    setCheckedAll(e.target.checked);
    handleTableDataChange(newTableData);
  };

  const calcTotalValue = (item) => {
    return (
      item.vlOriginal +
      item.vlHoOriginal +
      item.vlJurosOriginal +
      item.vlMultaOriginal +
      item.vlComPermanenciaOriginal +
      item.vlDespesasOriginal +
      item.vlNotificacaoOriginal +
      item.vlTarifaOriginal
    );
  };

  const HandleInstallmentDatacobChange = (input, item) => {
    const selec = tableData.map((x) => {
      if (x.idParcela === item.idParcela)
        x.parcelaSelecionada = input.target.checked;
      return x;
    });
    handleTableDataChange(selec);

    setCheckedAll(
      tableData
        .filter((x) =>
          selectedContract !== null &&
          selectedContract !== undefined &&
          selectedContract !== ""
            ? x.nrContrato?.replaceAll(" ", "") ===
              selectedContract?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };

  const COLUMNS = [
    {
      label: "",
    },
    {
      key: "nrContrato",
      defaultSort: "ascending",
      label: "Contrato",
      filter: true,
    },
    {
      key: "nrParcela",
      label: "Parcela",
      cellStyleCondicional: (item) => {
        if (item.atraso && item.atraso > 0) {
          return {
            backgroundColor: "red",
            color: "white",
            textAlign: "center",
          };
        }
        return {
          backgroundColor: "white",
          color: "black",
          textAlign: "center",
        };
      },
      formatter: (value) => String(value).padStart(3, "0"),
    },
    { key: "nrPlano", label: "Plano" },
    {
      key: "tipoParcela",
      label: "Tp. Parcela",
    },
    {
      key: "dtVencimento",
      label: "Vencimento",
      formatter: (value) => formatDate(value),
    },
    {
      key: "vlSaldo",
      label: "Valor Saldo",
      formatter: (value) => formatThousands(value),
    },
    {
      key: "vlAtualizado",
      label: "Valor Total",
      formatter: (value) => formatCurrency(value, false),
    },
  ];

  return (
    <TableInstallmentTermo
      columns={COLUMNS}
      selectAll={checkedAll}
      selectedDate={SELECTED_DATE}
      selectedContract={selectedContract}
      tableData={tableData}
      handleSelectAll={handleSelectAll}
      calcTotalValue={calcTotalValue}
      HandleInstallmentChange={HandleInstallmentDatacobChange}
    />
  );
};

const financiadoData = localStorage.getItem("financiadoData")
  ? JSON.parse(localStorage.getItem("financiadoData"))
  : null;

const TableNewconTermo = (contrato) => {
  const [tableDataNewcon, setTableDataNewcon] = useState([]);
  const [checkedAllNewcon, setCheckedAllNewcon] = useState(false);
  const selectedContratoNewcon = null;
  const [isLoadingNewcon, setLoadingNewcon] = useState(false);

  const getCalculoNewcon = useCallback(async () => {
    const data = [];

    const payload = {
      nrContratos: contrato.contrato,
      cpfCnpjDevedor: financiadoData.cpfCnpj,
    };
    const calculo = await postApi(payload, "getNewconParcelas");
    data.push(
      ...(calculo?.data?.envelope?.element?.element?.parcelasVincendas?.map(
        (x) => {
          x.vencida = false;
          return x;
        }
      ) ?? [])
    );

    setTableDataNewcon(
      data.map((x) => {
        x.parcelaSelecionada = false;
        return x;
      })
    );
  }, [contrato]);

  const HandleInstallmentNewconChange = (input, item) => {
    setTableDataNewcon(
      tableDataNewcon.map((x) => {
        if (
          x.nrContrato === item.nrContrato &&
          x.noParcela === item.noParcela &&
          x.noPlano === item.noPlano
        ) {
          x.parcelaSelecionada = input.target.checked;
        }
        return x;
      })
    );
    setCheckedAllNewcon(
      tableDataNewcon
        .filter((x) =>
          selectedContratoNewcon?.value !== null &&
          selectedContratoNewcon?.value !== undefined
            ? x?.nrContrato?.replaceAll(" ", "") ===
              selectedContratoNewcon?.value?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };

  const COLUMNS_NEWCON = [
    {
      key: "",
      label: "",
      formatterByObject: (item) => (
        <input
          type="checkbox"
          checked={item.parcelaSelecionada}
          onChange={(input) => HandleInstallmentNewconChange(input, item)}
        />
      ),
    },
    { key: "nrContrato", label: "Nr. Contrato" },
    {
      key: "noParcela",
      label: "No. Parcela",
      formatter: (value) => String(value).padStart(3, "0"),
    },
    {
      key: "noPlano",
      label: "No. Plano",
      formatter: (value) => String(value).padStart(3, "0"),
    },
    {
      key: "vlOriginal",
      label: "Vl. Original",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "vlSaldo",
      label: "Vl. Saldo",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "dtVencimento",
      label: "Dt. Vencimento",
      formatter: (value) => formatDate(value),
    },
  ];

  useEffect(() => {
    getCalculoNewcon();
  }, [contrato, getCalculoNewcon]);

  const handleSelectAllNewcon = (e) => {
    setTableDataNewcon(
      tableDataNewcon.map((x) => {
        if (
          selectedContratoNewcon?.value !== null &&
          selectedContratoNewcon?.value !== undefined &&
          x?.nrContrato?.replaceAll(" ", "") ===
            selectedContratoNewcon?.value?.replaceAll(" ", "")
        )
          x.parcelaSelecionada = e.target.checked;
        else if (
          selectedContratoNewcon?.value === null ||
          selectedContratoNewcon?.value === undefined
        )
          x.parcelaSelecionada = e.target.checked;
        return x;
      })
    );
    setCheckedAllNewcon(e.target.checked);
  };

  return (
    <>
      {isLoadingNewcon && <LoadingComponent />}
      {!isLoadingNewcon && (
        <NewconTableInstallment
          HandleInstallmentChange={HandleInstallmentNewconChange}
          columns={COLUMNS_NEWCON}
          handleSelectAll={handleSelectAllNewcon}
          selectAll={checkedAllNewcon}
          tableData={tableDataNewcon.filter((x) =>
            selectedContratoNewcon?.value !== null &&
            selectedContratoNewcon?.value !== undefined
              ? x?.nrContrato?.replaceAll(" ", "") ===
                selectedContratoNewcon?.value?.replaceAll(" ", "")
              : true
          )}
        />
      )}
    </>
  );
};

const TableInTabsTermo = ({ tableData, handleTableDataChange }) => {
  const [currentTab, setCurrentTab] = useState("DataCob");
  console.log("tableData", tableData);
  const tabs = [
    {
      id: 1,
      label: "DataCob",
      icon: "cil-spreadsheet",
      content: (
        <TableDatacobTermo
          tableData={tableData}
          handleTableDataChange={handleTableDataChange}
        />
      ),
    },
    {
      id: 2,
      label: "NewCon",
      icon: "cil-columns",
      content: <TableNewconTermo contrato={tableData[0]?.nrContrato} />,
    },
  ];

  const handleTabSelect = (tab) => {
    setCurrentTab(tab.label);
  };

  return (
    <div className="container-fluid px-0 pt-3">
      <CCard>
        <CTabs onSelect={handleTabSelect} activeTab={"DataCob"}>
          <CNav className="custom-nav">
            {tabs.map((tab) => (
              <CNavItem
                key={tab.id}
                className={currentTab === tab.label ? "" : "nonactive-tab"}
              >
                <CNavLink
                  data-tab={tab.label}
                  onClick={() => handleTabSelect(tab)}
                >
                  <i className={tab.icon} /> {tab.label}
                </CNavLink>
              </CNavItem>
            ))}
          </CNav>
          <CTabContent
            className="px-3 overflow-auto"
            style={{
              maxHeight: "230px",
              minHeight: "200px",
            }}
          >
            {tabs.map((tab) => (
              <CTabPane key={tab.id} data-tab={tab.label}>
                {tab.content}
              </CTabPane>
            ))}
          </CTabContent>
        </CTabs>
      </CCard>
    </div>
  );
};

export { TableDatacobTermo, TableNewconTermo, TableInTabsTermo };
