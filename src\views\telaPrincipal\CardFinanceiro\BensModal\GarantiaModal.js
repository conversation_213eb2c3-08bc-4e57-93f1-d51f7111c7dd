import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CDataTable,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CRow,
  CCard,
  CCol,
  CLabel,
} from "@coreui/react";
import { formatThousands, formatDate } from "src/reusable/helpers";
import LoadingComponent from "src/reusable/Loading";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import TableSelectItens from "src/reusable/TableSelectItens";
import DetalhesBensModal from "./DetalhesBensModal";

const GarantiaModal = ({ isOpen, onClose, idCota }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [faseData, setFaseData] = useState([]);
  const [assetsData, setAssetsData] = useState([]);

  const [showDetalhesBensModal, setShowDetalhesBensModal] = useState(false);
  const [dadosDetalhesBens, setDadosDetalhesBens] = useState(null);

  // const fields = [
  //   {
  //     key: "percIdealAdminstration",
  //     label: "Data do Reajuste",
  //     _style: { whiteSpace: "nowrap" },
  //   },
  //   {
  //     key: "percIdealCommonFund",
  //     label: "Data da Efetivação",
  //     _style: { whiteSpace: "nowrap" },
  //   },
  //   { key: "dueDate", label: "Taxas" },
  //   { key: "nameCodeFinanMovement", label: "Preço" },
  //   {
  //     key: "idCodeFinanMovement",
  //     label: "% Variação",
  //     _style: { whiteSpace: "nowrap" },
  //   },
  //   { key: "installmentNumber", label: "#" },
  // ];

  const faseFields = [
    {
      key: "date",
      label: "Data",
      formatter: (item) => formatDate(item),
    },
    {
      key: "occurence",
      label: "Ocorrência",
    },
    { key: "description", label: "Descrição" },
    {
      key: "conclusion",
      label: "Conclusão",
      formatter: (item) => formatDate(item),
    },
    {
      key: "accessMethodInc",
      label: "Forma Acesso Inc.",
    },
    {
      key: "accessMethodAlt",
      label: "Forma Acesso Alt.",
    },
  ];

  const assetsFields = [
    {
      key: "asset",
      label: "Bem",
    },
    {
      key: "situation",
      label: "Situação",
    },
    { key: "typePayment", label: "Tipo Pgto" },
    { key: "typeAquisition", label: "Tipo Aquisição" },
    {
      key: "paymentValue",
      label: "Valor do Pagto",
      formatter: (value) => formatThousands(value),
    },
    {
      key: "antecipationValue",
      label: "Valor Antecipação",
      formatter: (value) => formatThousands(value),
    },
    {
      key: "detalhes",
      label: "Garantia",
      formatterByObject: (item) => renderGarantia(item),
    },
  ];

  const renderGarantia = (item) => {
    return (
      <div>
        <CButton
          color="info"
          onClick={() => handleButtonDetalhesBens(item)}
          style={{ width: "100%" }}
        >
          <i className="cil-shield-alt" />
        </CButton>
      </div>
    );
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  function handleButtonDetalhesBens(item) {
    setDadosDetalhesBens(item.id);
    setShowDetalhesBensModal(true);
  }

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      GetData(`/${idCota}`, "getNewconBensGarantia")
        .then((data) => {
          if (data) {
            setData(data);
            setFaseData(data.contFase);
            setAssetsData(data.assets);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="xl"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Detalhes do Bem</h5>
      </CModalHeader>
      <CModalBody>
        {isLoading ? (
          <div>
            <LoadingComponent />
          </div>
        ) : (
          <>
            <CRow>
              <CCol>
                <CLabel className="mr-2">1ª Assembleia:</CLabel>{" "}
                {data?.firstAssembly}
              </CCol>
              <CCol>
                <CLabel className="mr-2">Encerramento previsto: </CLabel>
                {data?.expectedClosure
                  ? formatDate(data?.expectedClosure)
                  : "---"}
              </CCol>
              <CCol>
                <CLabel className="mr-2">Plano:</CLabel>{" "}
                {data?.plan ? data?.plan : "---"}
              </CCol>
            </CRow>
            <CRow>
              <CCol md="8">
                <CLabel className="mr-2">Bem:</CLabel>{" "}
                {data?.asset ? data?.asset : "---"}
              </CCol>
              <CCol>
                <CLabel className="mr-2">Chegada Cadastro: </CLabel>
                {data?.arrivalRegistration
                  ? formatDate(data?.arrivalRegistration)
                  : "---"}
              </CCol>
            </CRow>
            <CRow>
              <CCol md="8">
                <CLabel className="mr-2">Comtemplação:</CLabel>{" "}
                {data?.contemplation ? data?.contemplation : "---"}
              </CCol>
              <CCol>
                <CLabel className="mr-2">Entrega Documento: </CLabel>
                {data?.documentDelivery
                  ? formatDate(data?.documentDelivery)
                  : "---"}
              </CCol>
            </CRow>
            <CRow>
              <CCol md="8">
                <CLabel className="mr-2">Comt. Manual:</CLabel>{" "}
                {data?.contManual ? data?.contManual : "---"}
              </CCol>
              <CCol>
                <CLabel className="mr-2">Data Aprovação Cadastro:</CLabel>
                {data?.dateApprovalContract
                  ? formatDate(data?.dateApprovalContract)
                  : "---"}
              </CCol>
            </CRow>
            <CCard>
              <TableSelectItens
                data={faseData ? faseData : []}
                columns={faseFields}
                onSelectionChange={(_) => {}}
                defaultSelectedKeys={[]}
                selectable={false}
                heightParam="290px"
              />
            </CCard>
            <CCard>
              <TableSelectItens
                data={assetsData ? assetsData : []}
                columns={assetsFields}
                onSelectionChange={(_) => {}}
                defaultSelectedKeys={[]}
                selectable={false}
                heightParam="290px"
              />
            </CCard>
            <CRow>
              <CCol md="2" />
              <CCol>
                <CRow>
                  <CCol>
                    <CLabel className="mr-2">Bem:</CLabel>{" "}
                    {data?.asset ? data?.asset : "---"}
                  </CCol>
                  <CCol>
                    <CLabel className="mr-2">Chegada Cadastro: </CLabel>
                    {data?.arrivalRegistration
                      ? formatDate(data?.arrivalRegistration)
                      : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol>
                    <CLabel className="mr-2">Crédito Original:</CLabel>
                    {data?.originalCredit
                      ? formatThousands(data.originalCredit)
                      : "---"}
                  </CCol>
                  <CCol>
                    <CLabel className="mr-2">Crédito Corrigido: </CLabel>
                    {data?.correctedCredit
                      ? formatThousands(data.correctedCredit)
                      : "---"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol>
                    <CLabel className="mr-2">Rendimento:</CLabel>
                    {data?.revenue ? formatThousands(data.revenue) : "---"}
                  </CCol>
                  <CCol>
                    <CLabel className="mr-2">Pagamentos Realizados: </CLabel>
                    {data?.paymentMade
                      ? formatThousands(data.paymentMade)
                      : "0,00"}
                  </CCol>
                </CRow>
                <CRow>
                  <CCol>
                    <CLabel className="mr-2">Taxas Pendentes:</CLabel>
                    {data?.pendentTax
                      ? formatThousands(data.pendentTax)
                      : "---"}
                  </CCol>
                  <CCol>
                    <CLabel className="mr-2">Valor a Pagar: </CLabel>
                    {data?.valueToPay
                      ? formatThousands(data.valueToPay)
                      : "---"}
                  </CCol>
                </CRow>
              </CCol>
            </CRow>
          </>
        )}{" "}
        {showDetalhesBensModal && (
          <DetalhesBensModal
            isOpen={showDetalhesBensModal}
            onClose={() => setShowDetalhesBensModal(false)}
            IdBem={dadosDetalhesBens}
          />
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default GarantiaModal;
