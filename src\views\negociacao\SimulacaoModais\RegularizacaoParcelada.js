import React, { useEffect, useRef, useState } from "react";
import {
  CButton,
  CCard,
  CCardBody,
  CRow,
  CCol,
  CLabel,
  CInput,
  CTabs,
  CNav,
  CNavItem,
  CNavLink,
  CTabContent,
  CTabPane,
} from "@coreui/react";
import { GET_DATA, POST_DATA } from "src/api";
import {
  formatCurrency,
  formatDate,
  formatThousands,
} from "src/reusable/helpers";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import CardLoading from "src/reusable/CardLoading";
import Select from "react-select";
import SendEmailModal from "./SendEmailModal";
import { postApi } from "src/reusable/functions";
import TableInstallment from "../Parcial/TableInstallment";
import LoadingComponent from "src/reusable/Loading";
import NewconTableInstallment from "./components/NewconInstallmentTable";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useAuth } from "src/auth/AuthContext";

const SELECTED_DATE = new Date();

const COLUMNS = [
  {
    label: "",
  },
  {
    key: "numero_Contrato",
    defaultSort: "ascending",
    label: "Contrato",
    filter: true,
  },
  {
    key: "nr_Parcela",
    label: "Parcela",
    cellStyleCondicional: (item) => {
      if (item.atraso && item.atraso > 0) {
        return {
          backgroundColor: "red",
          color: "white",
          textAlign: "center",
        };
      }
      return {
        backgroundColor: "white",
        color: "black",
        textAlign: "center",
      };
    },
    formatter: (value) => String(value).padStart(3, "0"),
  },
  { key: "nr_Plano", label: "Plano" },
  {
    key: "nome_Tipo_Parcela",
    label: "Tp. Parcela",
  },
  {
    key: "dt_Vencimento",
    label: "Vencimento",
    formatter: (value) => formatDate(value),
  },
  {
    key: "vl_Saldo",
    label: "Valor Saldo",
    formatter: (value) => formatThousands(value),
  },
  {
    key: "vl_Saldo_Atualizado",
    label: "Valor Total",
    formatter: (value) => formatCurrency(value, false),
  },
  {
    key: "valorNegociado",
    label: "Valor Negociado",
    formatter: (value) => formatCurrency(value, false),
  },
  {
    key: "dt_Pgto",
    label: "Data Pagamento",
    defaultValue: SELECTED_DATE ? formatDate(SELECTED_DATE) : "---",
  },
  { key: "atraso", label: "Atraso", formatter: (value) => value.toString() },
  //Esses dois campos de desconto são provavelmente calculados aqui no front e carregados na tabela
  // {
  //   key: "desconto",
  //   label: "Desconto",
  //   formatter: (value) => formatCurrency(value, false),
  // },
  // {
  //   key: "percDesconto",
  //   label: "% de Desconto",
  //   formatter: (value) => formatCurrency(value, false),
  // },
];

const RegularizacaoParcelada = ({
  ocorrencia,
  onSave,
  onClose,
  cleanCalculoPost,
}) => {
  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const contratos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const clientData = localStorage.getItem("clientData")
    ? JSON.parse(localStorage.getItem("clientData"))
    : null;

  const parcelasAbertas =
    contratos === null || contratos === undefined
      ? []
      : contratos?.flatMap((item) =>
          item.parcelas.filter(
            (pItem) =>
              pItem.status === "A" &&
              !pItem.nr_Acordo /* && pItem.atraso > 0  ||
        (pItem.status === "A" && !pItem.nr_Acordo && pItem.atraso === 0 && pItem.nome_Tipo_Parcela === "DIF_PARCELAS")*/
          )
        );

  const { checkPermission } = useAuth();
  const permissaoAcordo = {
    modulo: "Acordos",
    submodulo: "Criar Acordos",
  };

  const [data, setData] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [isLoadingDatacob, setLoadingDatacob] = useState(true);
  const [isLoadingNewcon, setLoadingNewcon] = useState(false);
  const [vlHo, setVlHo] = useState(null);
  const [percHo, setPercHo] = useState(null);
  const [selectedOcorrencia, setSelectedOcorrencia] = useState(null);
  const [optionsOcorrencia, setOptionsOcorrencia] = useState([]);
  const [selectedPhone, setSelectedPhone] = useState(null);
  const [optionsPhone, setOptionsPhone] = useState([]);
  const [optionsEmail, setOptionsEmail] = useState([]);
  const [showSendEmailModal, setShowSendEmailModal] = useState(false);
  const [showCreateDealModal, setShowCreateDealModal] = useState(false);
  const [parcelamento, setParcelamento] = useState(1);
  const [entrada, setEntrada] = useState(0);
  const [negociador, setNegociador] = useState("");
  const [ativo, setAtivo] = useState("");
  const [statusProjuris, setStatusProjuris] = useState("");
  const [email, setEmail] = useState(null);
  const [primeiraParcela, setPrimeirParcela] = useState(new Date().toJSON());
  const [demaisParcelas, setDemaisParcelas] = useState(new Date().toJSON());
  const textData = useRef(null);

  const [selectedParcelas, setSelectedParcelas] = useState([]);
  const [selectedDifParcelas, setSelectedDifParcelas] = useState([]);
  const [checkedAll, setCheckedAll] = useState(false);

  const [selectedContract, setSelectedContract] = useState("");
  const [tableData, setTableData] = useState([]);

  const [valorMinimo, setValorMinimo] = useState(0);
  const [valid, setValid] = useState(false);

  async function getCalculo() {
    const pay = [];
    // setLoading(true);
    // const pay = {
    //   IdAgrupamento: financiadoData.id_Agrupamento,
    //   IdFinanciado: financiadoData.id_Financiado,
    //   IdParcelas: selectedParcelas,
    //   ValorHo: vlHo,
    //   percHo: percHo,
    //   IdParcelasDiff: selectedDifParcelas,
    // };
    // const result = await POST_DATA("Simulacoes/RegularizacaoParcelada", pay);
    // if (result.success === false) {
    //   toast.warning(result.message);
    // }
    // if (result.data) {
    //   setData(result.data);
    //   // postCalculo(result.data[0]);
    // } else {
    //   toast.warning(
    //     "Não encontrado parcelas em aberto para o contrato selecionado."
    //   );
    // }
    // setLoading(false);

    const parcelasDcSelecionados = tableData.filter(
      (x) => x.parcelaSelecionada
    );
    const parcelasNcSelecionados = tableDataNewcon.filter(
      (x) => x.parcelaSelecionada
    );
    const contratosSelecionados = [
      ...new Set([
        ...parcelasDcSelecionados.flatMap((x) =>
          x.nrContrato.replaceAll(" ", "")
        ),
        ...parcelasNcSelecionados.flatMap((x) =>
          x.nrContrato.replaceAll(" ", "")
        ),
      ]),
    ];

    for (const contrato of contratosSelecionados) {
      const parcDc = parcelasDcSelecionados.filter(
        (x) => x.nrContrato.replaceAll(" ", "") === contrato
      );
      const parcNc = parcelasNcSelecionados.filter(
        (x) => x.nrContrato.replaceAll(" ", "") === contrato
      );

      const parcDcVencidas = parcDc.filter((x) => x.atraso > 0);
      const parcNcVencidas = parcNc.filter((x) => x.vencida);

      const parcDcVincendo = parcDc.filter((x) => x.atraso === 0);
      const parcNcVincendo = parcNc.filter((x) => !x.vencida);

      const vlCustas =
        parcDcVencidas.reduce(
          (a, b) =>
            a +
            b.vlDespesasNegociado +
            b.vlNotificacaoNegociado +
            b.vlTarifaNegociado,
          0
        ) + parcNcVencidas.reduce((a, b) => a + b.vlTarifa, 0);

      const vlSaldoVencido =
        parcDcVencidas.reduce((a, b) => a + b.vlAtualizado, 0) +
        parcNcVencidas.reduce((a, b) => a + b.vlSaldo, 0);

      const vlSaldoVincendo =
        parcDcVincendo.reduce((a, b) => a + b.vlAtualizado, 0) +
        parcNcVincendo.reduce((a, b) => a + b.vlSaldo, 0);

      const valorHonorario =
        vlSaldoVincendo > 0
          ? vlHo > 0
            ? vlHo
            : vlSaldoVincendo * (percHo / 100)
          : 0;

      const percentualHonorario =
        vlSaldoVincendo > 0
          ? percHo > 0
            ? percHo
            : (vlHo / vlSaldoVincendo) * 100
          : 0;

      const vlTotal = vlSaldoVincendo + vlSaldoVencido + valorHonorario;
      const vlParcDiff = parcDcVincendo
        .filter((x) => x.nome_Tipo_Parcela === "DIF_PARCELAS")
        .reduce((a, b) => a + b.vlAtualizado, 0);

      pay.push({
        contrato: parcDc[0]?.nrContrato ?? parcNc[0]?.nrContrato,
        custas: vlCustas,
        idContrato: null,
        parcDiff: vlParcDiff,
        parcVencidasDc: parcDcVencidas.flatMap((x) => x.nrParcela).join(","),
        parcVencidasNc: parcNcVencidas.flatMap((x) => x.noParcela).join(","),
        parcVincendoDc: parcDcVincendo.flatMap((x) => x.nrParcela).join(","),
        parcVincendoNc: parcNcVincendo.flatMap((x) => x.noParcela).join(","),
        vlParcVincendoDc: parcDcVincendo[0]?.vlAtualizado ?? 0,
        vlParcVincendoNc: parcNcVincendo[0]?.vlSaldo ?? 0,
        vlHO: valorHonorario,
        vlVencido: vlSaldoVencido,
        vlVincendo: vlSaldoVincendo,
        percHO: percentualHonorario,
        vlTotal: vlTotal,
      });
    }

    const totalNeg = pay.reduce((a, b) => a + b.vlTotal, 0);
    setValid(totalNeg >= valorMinimo);
    setData(pay);
  }

  const buildText = (label, value = 0) => {
    if (
      label === "Saldo Vencido" ||
      label === "Parcela/Vincendo" ||
      label === "Valor H.O." ||
      label === "Custas" ||
      label === "Saldo à Vencer" ||
      label === "Saldo Total" ||
      label === "Valor H.O. à Vencer"
    ) {
      return `${label}: R$ ${formatThousands(value)}`;
    }
    if (label === "H.O. à Vencer") {
      return `${label}: ${value.toFixed(2)}%`;
    } else {
      return `${label}: ${value}`;
    }
  };

  const fieldMappings = [
    { label: "Contrato", field: "contrato" },
    { label: "Parcelas Vencidas DataCob", field: "parcVencidasDc" },
    { label: "Parcelas Vencidas NewCon", field: "parcVencidasNc" },
    { label: "Dif. Parcela", field: "parcDiff" },
    {
      label: "Custas",
      field: "custas",
    },
    { label: "Saldo Vencido", field: "vlVencido" },
    { label: "Parcelas à Vencer DataCob", field: "parcVincendoDc" },
    { label: "Parcelas à Vencer NewCon", field: "parcVincendoNc" },
    { label: "Qtde à Vencer", field: "qtdVincendo" },
    { label: "Parcela/Vincendo", field: "vlParcVincendo" },
    { label: "Valor H.O. à Vencer", field: "vlHO" },
    { label: "H.O. à Vencer", field: "percHO" },
    { label: "Saldo à Vencer", field: "vlVincendo" },
    { label: "Saldo Total", field: "vlTotal" },
  ];

  const renderTextLines = (item = null) => {
    if (item !== null) {
      return fieldMappings.map((mapping) =>
        buildText(mapping.label, item?.[mapping.field])
      );
    }
    return fieldMappings.map((mapping) =>
      buildText(mapping.label, data[0]?.[mapping.field])
    );
  };

  // const handleContratoChange = (selection) => {
  //   setSelectedContrato(selection);
  // };

  const handleCheckParcela = (e, idParcela, tipoParcela) => {
    if (e.target.checked) {
      setSelectedParcelas([...selectedParcelas, idParcela]);
      if (tipoParcela === "DIF_PARCELAS") {
        setSelectedDifParcelas([...selectedDifParcelas, idParcela]);
      }
    } else {
      setSelectedParcelas(
        selectedParcelas.filter((item) => item !== idParcela)
      );
      if (tipoParcela === "DIF_PARCELAS") {
        setSelectedDifParcelas(
          selectedDifParcelas.filter((item) => item !== idParcela)
        );
      }
      setCheckedAll(false);
    }
  };

  const handleSelectAll = (e) => {
    tableData.map((item) => {
      if (
        item.nrContrato?.replaceAll(" ", "") ===
        selectedContract?.replaceAll(" ", "")
      )
        item.parcelaSelecionada = e.target.checked;
      else if (selectedContract === "")
        item.parcelaSelecionada = e.target.checked;
      return item;
    });
    // setTableData(tableData);

    const vlMinimo = tableData
      .filter((x) => x.parcelaSelecionada)
      .reduce((a, b) => a + b.vlAtualizadoDescontoMax, 0);
    setValorMinimo(vlMinimo);
    const totalNeg = data.reduce((a, b) => a + b.vlTotal, 0);
    setValid(totalNeg >= vlMinimo);

    setCheckedAll(e.target.checked);
  };

  const handleValueChange = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    value = Number(input) / 100;
    if (input.length > 11) return;

    setPercHo(null);
    setVlHo(value);
  };

  const handlePercChange = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    value = Number(input) / 100;
    if (value > 100) value = 100;
    if (isNaN(value)) value = 0;

    setVlHo(null);
    setPercHo(value);
  };

  const handleParcelamentoChange = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    value = parseInt(input);
    if (isNaN(value)) value = 0;

    setParcelamento(value);
  };

  const handleEntradaChange = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    value = Number(input) / 100;
    if (input.length > 11) return;

    setEntrada(value);
  };

  async function getTiposOcorrencia() {
    setLoading(true);
    const tiposOcorrencia = await GET_DATA("Datacob/Ocorrencias");
    if (tiposOcorrencia !== null && tiposOcorrencia !== undefined) {
      const options = [
        ...tiposOcorrencia.map((item) => {
          return {
            label: item.cod_Ocorr_Sistema + " - " + item.descricao,
            value: item.id_Ocorrencia_Sistema,
            cod_Ocorr_Sistema: item.cod_Ocorr_Sistema,
          };
        }),
      ];
      setOptionsOcorrencia(options);
    }
    setLoading(false);
  }

  const handleCalculoSuccess = (response) => {
    const neg = response.negociacaoDto[0] ?? null;
    const parcelas = neg?.parcelas;
    if (parcelas === undefined || parcelas === null) return [];
    const obj = parcelas
      .filter((item) => {
        const parcAb = parcelasAbertas.find(
          (x) => x.id_Parcela === item.idParcela
        );
        return item.atraso > 0 || parcAb?.nome_Tipo_Parcela === "DIF_PARCELAS";
      })
      .map((item) => {
        const parcAb = parcelasAbertas.find(
          (x) => x.id_Parcela === item.idParcela
        );
        item.parcelaSelecionada = false;
        item.nome_Tipo_Parcela = parcAb?.nome_Tipo_Parcela;
        item.dt_Pgto = parcAb?.dt_Pgto;
        return item;
      });

    setTableData(obj);
  };

  const handleSaveOccurrence = async () => {
    if (selectedPhone === null || selectedOcorrencia === null) {
      toast.warning("Selecione o telefone e a ocorrência!");
      return;
    }
    toast.info("Enviando ocorrência...");
    const payload = {
      login: user?.username,
      id_Contrato: financiadoData?.id_Contrato,
      id_Ocorrencia_Sistema: selectedOcorrencia?.value,
      observacao: textData.current.innerText,
      telefones: [selectedPhone?.value],
      complemento: "",
      telefoneParaRetorno: selectedPhone?.value,
    };
    const ocorrencia = await POST_DATA("Datacob/historicoAdicionar", payload, false, true);
    if (ocorrencia.success) {
      toast.info("Ocorrência adicionada com sucesso");
    } else {
      toast.error("Erro ao enviar ocorrência!");
    }
  };

  const handleChangeSelectContract = (e) => {
    setSelectedContract(e.target.value);

    setCheckedAll(
      tableData
        .filter((x) =>
          e.target.value !== null &&
          e.target.value !== undefined &&
          e.target.value !== ""
            ? x.nrContrato?.replaceAll(" ", "") ===
              e.target.value?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };

  const HandleInstallmentDatacobChange = (input, item) => {
    const selec = tableData.map((x) => {
      if (x.idParcela === item.idParcela)
        x.parcelaSelecionada = input.target.checked;
      return x;
    });
    setTableData(selec);

    const vlMinimo = selec
      .filter((x) => x.parcelaSelecionada)
      .reduce((a, b) => a + b.vlAtualizadoDescontoMax, 0);
    setValorMinimo(vlMinimo);
    const totalNeg = data.reduce((a, b) => a + b.vlTotal, 0);
    setValid(totalNeg >= vlMinimo);

    setCheckedAll(
      tableData
        .filter((x) =>
          selectedContract !== null &&
          selectedContract !== undefined &&
          selectedContract !== ""
            ? x.nrContrato?.replaceAll(" ", "") ===
              selectedContract?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };

  const calcTotalValue = (item) => {
    return (
      item.vlOriginal +
      item.vlHoOriginal +
      item.vlJurosOriginal +
      item.vlMultaOriginal +
      item.vlComPermanenciaOriginal +
      item.vlDespesasOriginal +
      item.vlNotificacaoOriginal +
      item.vlTarifaOriginal
    );
  };

  const [currentTab, setCurrentTab] = useState("DataCob");

  const handleTabSelect = (tab) => {
    setCurrentTab(tab.label);
  };

  const [tableDataNewcon, setTableDataNewcon] = useState([]);
  const [checkedAllNewcon, setCheckedAllNewcon] = useState(false);
  const [selectedContratoNewcon, setSelectedContratoNewcon] = useState(null);

  const HandleInstallmentNewconChange = (input, item) => {
    setTableDataNewcon(
      tableDataNewcon.map((x) => {
        if (
          x.nrContrato === item.nrContrato &&
          x.noParcela === item.noParcela &&
          x.noPlano === item.noPlano
        ) {
          x.parcelaSelecionada = input.target.checked;
        }
        return x;
      })
    );
    setCheckedAllNewcon(
      tableDataNewcon
        .filter((x) =>
          selectedContratoNewcon?.value !== null &&
          selectedContratoNewcon?.value !== undefined
            ? x?.nrContrato?.replaceAll(" ", "") ===
              selectedContratoNewcon?.value?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };

  const handleSelectAllNewcon = (e) => {
    setTableDataNewcon(
      tableDataNewcon.map((x) => {
        if (
          selectedContratoNewcon?.value !== null &&
          selectedContratoNewcon?.value !== undefined &&
          x?.nrContrato?.replaceAll(" ", "") ===
            selectedContratoNewcon?.value?.replaceAll(" ", "")
        )
          x.parcelaSelecionada = e.target.checked;
        else if (
          selectedContratoNewcon?.value === null ||
          selectedContratoNewcon?.value === undefined
        )
          x.parcelaSelecionada = e.target.checked;
        return x;
      })
    );
    setCheckedAllNewcon(e.target.checked);
  };

  const COLUMNS_NEWCON = [
    {
      key: "",
      label: "",
      formatterByObject: (item) => (
        <input
          type="checkbox"
          checked={item.parcelaSelecionada}
          onChange={(input) => HandleInstallmentNewconChange(input, item)}
        />
      ),
    },
    { key: "nrContrato", label: "Nr. Contrato" },
    {
      key: "noParcela",
      label: "No. Parcela",
      formatter: (value) => String(value).padStart(3, "0"),
    },
    {
      key: "noPlano",
      label: "No. Plano",
      formatter: (value) => String(value).padStart(3, "0"),
    },
    {
      key: "vlOriginal",
      label: "Vl. Original",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "vlSaldo",
      label: "Vl. Saldo",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "dtVencimento",
      label: "Dt. Vencimento",
      formatter: (value) => formatDate(value),
    },
  ];

  const handleChangeSelectedContractNewcon = (item) => {
    setSelectedContratoNewcon(item);
    setCheckedAllNewcon(
      tableDataNewcon
        .filter((x) =>
          item?.value !== null && item?.value !== undefined
            ? x?.nrContrato?.replaceAll(" ", "") ===
              item?.value?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };

  const tabs = [
    {
      id: 1,
      label: "DataCob",
      icon: "cil-spreadsheet",
      content: isLoadingDatacob ? (
        <div className="mt-5">
          <LoadingComponent />
        </div>
      ) : (
        <TableInstallment
          columns={COLUMNS}
          selectAll={checkedAll}
          selectedDate={SELECTED_DATE}
          selectedContract={selectedContract}
          contratosAtivos={contratos}
          tableData={tableData}
          handleSelectAll={handleSelectAll}
          handleChangeSelectContract={handleChangeSelectContract}
          calcTotalValue={calcTotalValue}
          HandleInstallmentChange={HandleInstallmentDatacobChange}
        />
      ),
    },

    {
      id: 2,
      label: "NewCon",
      icon: "cil-columns",
      content: isLoadingNewcon ? (
        <div className="mt-5">
          <LoadingComponent />
        </div>
      ) : (
        <>
          <Select
            className="mt-3"
            placeholder="Contratos"
            options={[
              { label: "Contratos", value: null },
              ...contratos
                .filter((x) => x.contrato_Aberto === true)
                .map((x) => ({
                  label: x.numero_Contrato,
                  value: x.numero_Contrato,
                })),
            ]}
            value={selectedContratoNewcon}
            getOptionValue={(option) => option?.value}
            getOptionLabel={(option) => option?.label}
            onChange={handleChangeSelectedContractNewcon}
          />
          <NewconTableInstallment
            HandleInstallmentChange={HandleInstallmentNewconChange}
            columns={COLUMNS_NEWCON}
            handleSelectAll={handleSelectAllNewcon}
            selectAll={checkedAllNewcon}
            tableData={tableDataNewcon.filter((x) =>
              selectedContratoNewcon?.value !== null &&
              selectedContratoNewcon?.value !== undefined
                ? x?.nrContrato?.replaceAll(" ", "") ===
                  selectedContratoNewcon?.value?.replaceAll(" ", "")
                : true
            )}
          />
        </>
      ),
    },
  ];

  const emptyFunc = () => {};

  const getCalculoNewcon = async () => {
    const data = [];
    for (const contrato of contratos) {
      const payload = {
        nrContratos: contrato.numero_Contrato,
        cpfCnpjDevedor: financiadoData.cpfCnpj,
      };
      if (contrato.contrato_Aberto === true) {
        const calculo = await postApi(payload, "getNewconParcelas");
        data.push(
          ...(calculo?.data?.envelope?.element?.element?.parcelasVincendas?.map(
            (x) => {
              x.vencida = false;
              return x;
            }
          ) ?? [])
        );
      }
    }
    setTableDataNewcon(
      data.map((x) => {
        x.parcelaSelecionada = false;
        return x;
      })
    );
  };

  useEffect(() => {
    const asyncFunc = async () => {
      // busca Calculo Datacob
      cleanCalculoPost(
        [],
        0,
        0,
        new Date(),
        handleCalculoSuccess,
        emptyFunc,
        emptyFunc,
        () => {
          setLoadingDatacob(false);
        }
      );

      // busca Calculo NewCon
      getCalculoNewcon();
    };
    getTiposOcorrencia();
    const optPhone = clientData?.telefones.map((item) => {
      return {
        label: item.ddd + item.fone,
        value: item.ddd + item.fone,
      };
    });
    setOptionsPhone(optPhone);
    const optEmail = clientData?.emails?.map((item) => {
      return {
        label: item.endereco_Email,
        value: item.endereco_Email,
      };
    });
    setOptionsEmail(optEmail);
    asyncFunc();
  }, []);

  const calcDemaisParcelas = () => {
    if (entrada === 0 || parcelamento <= 1) return "";
    return (
      (data.reduce((a, b) => a + b.vlTotal, 0) - entrada) / (parcelamento - 1)
    );
  };

  const mountDealData = () => {
    return {
      calc: data,
      parcelamento: parcelamento ?? 0,
      entrada: entrada ?? 0,
      dataNegociacao: new Date(primeiraParcela),
      parcDc: tableData,
      parcNc: tableDataNewcon,
    };
  };

  const getIdsParcelas = () => {
    const ids = tableData
      ?.filter((x) => x.parcelaSelecionada)
      ?.map((x) => x.idParcela);
    const nwSel = tableDataNewcon?.filter((y) => y.parcelaSelecionada);
    if (nwSel?.length > 0) {
      const idsParcDcNw = [];
      nwSel.forEach((x) => {
        const parc = tableData.find(
          (y) =>
            y.nrContrato?.replaceAll(" ", "") ===
              x.nrContrato?.replaceAll(" ", "") && y.noParcela === x.nrParcela
        );
        if (parc !== undefined) {
          idsParcDcNw.push(parc?.idParcela);
        }
      });
      return [...new Set([...ids, ...idsParcDcNw])];
    }
    return ids;
  };

  const handleSaveDeal = async () => {
    const valorParcela = data.reduce((a, b) => a + b.vlTotal, 0) / parcelamento;
    const pay = {
      idContrato: financiadoData?.id_Agrupamento,
      valorEntrada: entrada,
      dataNegociacao: primeiraParcela,
      parcelas: getIdsParcelas(),
      qtdeParcelas: parcelamento,
      modalidadeNegociacao: 0,
      dataPagtoEntrada: primeiraParcela,
      valorParcela: valorParcela,
    };
    const response = await postApi(pay, "postConfirmarAcordo");
    if (
      response !== null &&
      response !== undefined &&
      response?.data?.idAcordo !== null &&
      response?.success === true
    ) {
      toast.success("Acordo criado com sucesso!");
    } else {
      const error =
        response?.message.indexOf("Retorno Api.") > -1
          ? "Erro CRM: " +
            response?.message.replace('Retorno Api. ["', "").replace('"]', "")
          : response?.message;
      toast.error(error);
    }
  };

  return (
    <div>
      <CRow>
        <CCol md="8">
          {/* //? Tabs Datacob e NewCon */}
          <div className="container-fluid px-0">
            <CCard>
              <CTabs onSelect={handleTabSelect} activeTab={"DataCob"}>
                <CNav className="custom-nav">
                  {tabs.map((tab) => (
                    <CNavItem
                      key={tab.id}
                      className={
                        currentTab === tab.label ? "" : "nonactive-tab"
                      }
                    >
                      <CNavLink
                        data-tab={tab.label}
                        onClick={() => handleTabSelect(tab)}
                      >
                        <i className={tab.icon} /> {tab.label}
                      </CNavLink>
                    </CNavItem>
                  ))}
                </CNav>
                <CTabContent
                  className="px-3 overflow-auto"
                  style={{
                    maxHeight: "230px",
                    minHeight: "200px",
                  }}
                >
                  {tabs.map((tab) => (
                    <CTabPane key={tab.id} data-tab={tab.label}>
                      {tab.content}
                    </CTabPane>
                  ))}
                </CTabContent>
              </CTabs>
            </CCard>
          </div>
          {/* //! Tabs Datacob e NewCon */}

          {/* //? Inputs de honorários e botão de calculo =============================================================================== */}
          <CRow className="mb-2 mt-2">
            <CCol>
              <CLabel clasName="text-nowrap">Valor H.O à Vencer</CLabel>
              <CInput
                onChange={handleValueChange}
                value={formatCurrency(vlHo ?? 0, false)}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Percentual H.O à Vencer</CLabel>
              <CInput
                onChange={handlePercChange}
                value={formatCurrency(percHo ?? 0, false)}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Parcelamento</CLabel>
              <CInput
                onChange={handleParcelamentoChange}
                value={parcelamento ?? 0}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Entrada</CLabel>
              <CInput
                onChange={handleEntradaChange}
                value={formatCurrency(entrada ?? 0, false)}
              />
            </CCol>
          </CRow>
          <CRow className="mb-2 mt-2">
            <CCol>
              <CLabel clasName="text-nowrap">Negociador</CLabel>
              <CInput
                onChange={(e) => setNegociador(e.target.value)}
                value={negociador}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Ativo</CLabel>
              <CInput
                onChange={(e) => setAtivo(e.target.value)}
                value={ativo}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Status Projuris</CLabel>
              <CInput
                onChange={(e) => setStatusProjuris(e.target.value)}
                value={statusProjuris}
              />
            </CCol>
          </CRow>
          <CRow className="mb-2 mt-2">
            <CCol>
              <CLabel clasName="text-nowrap">E-mail</CLabel>
              {/* <CInput
                onChange={(e) => setEmail(e.target.value)}
                value={email}
              /> */}
              <Select
                value={email}
                options={optionsEmail}
                onChange={(e) => setEmail(e)}
                placeholder={"Selecione"}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Data 1ª Parcela</CLabel>
              <ReactDatePicker
                selected={new Date(primeiraParcela)}
                onChange={(e) => setPrimeirParcela(e.toJSON())}
                className="form-control"
                dateFormat="dd/MM/yyyy"
                onKeyDown={(e) => e.preventDefault()}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Data das demais Parcelas</CLabel>
              <ReactDatePicker
                selected={new Date(demaisParcelas)}
                onChange={(e) => setDemaisParcelas(e.toJSON())}
                className="form-control"
                dateFormat="dd/MM/yyyy"
                onKeyDown={(e) => e.preventDefault()}
              />
            </CCol>
          </CRow>
          <CButton
            color="info"
            onClick={getCalculo}
            className="mb-2"
            block
            disabled={isLoading}
          >
            Calcular
          </CButton>
          {/* //! Inputs de honorários e botão de calculo =============================================================================== */}
        </CCol>
        <CCol md="4">
          <CCard className={"mb-2"}>
            <CCardBody>
              <strong>Valor mínimo: </strong>
              {formatCurrency(valorMinimo, false)}
            </CCardBody>
          </CCard>
          <CCard>
            {" "}
            {isLoading ? (
              <CardLoading />
            ) : (
              <>
                <CCardBody>
                  <div
                    className="d-flex flex-column"
                    style={{ maxHeight: "439px", overflowY: "auto" }}
                    ref={textData}
                  >
                    <div>
                      <div>
                        <strong>Negociador:</strong> {negociador}
                      </div>
                      <div>
                        <strong>Ativo:</strong> {ativo}
                      </div>
                      <div>
                        <strong>Status Projuris:</strong> {statusProjuris}
                      </div>
                      <div>
                        <strong>E-mail Cliente:</strong> {email?.value}
                      </div>
                      <div>
                        <strong>Data da 1ª Parcela:</strong>{" "}
                        {formatDate(primeiraParcela)}
                      </div>
                      <div>
                        <strong>Data das Demais Parcela:</strong>{" "}
                        {formatDate(demaisParcelas)}
                      </div>
                    </div>
                    <br />
                    <div>
                      <div>
                        <strong>
                          Total Negociação:{" "}
                          {formatCurrency(
                            data.reduce((a, b) => a + b.vlTotal, 0),
                            false
                          )}
                        </strong>
                      </div>
                      <div>
                        <strong>Parcelamento:</strong> {parcelamento}
                      </div>
                      <div>
                        <strong>Valor Parcela:</strong>{" "}
                        {formatCurrency(
                          entrada !== 0 || parcelamento === 0
                            ? ""
                            : data.reduce((a, b) => a + b.vlTotal, 0) /
                                parcelamento,
                          false
                        )}
                      </div>
                      <div>
                        <strong>Entrada:</strong>{" "}
                        {formatCurrency(entrada, false)}
                      </div>
                      <div>
                        <strong>
                          Demais Parcelas (
                          {parcelamento === 0 || entrada === 0
                            ? " "
                            : parcelamento - 1}
                          ):
                        </strong>{" "}
                        {formatCurrency(calcDemaisParcelas(), false)}
                      </div>
                    </div>
                    <br />
                    <div>
                      <strong>Detalhes Contratos</strong>
                    </div>
                    <br />
                    {data.map((item, index) => (
                      <>
                        <div key={index}>
                          {renderTextLines(item).map((line, index) => (
                            <div key={index}>{line}</div>
                          ))}
                        </div>
                        <br />
                      </>
                    ))}
                  </div>
                </CCardBody>
              </>
            )}
          </CCard>
        </CCol>
      </CRow>

      <CRow>
        <CCol md="6">
          <CLabel className={"mt-2"}>Selecione a ocorrência:</CLabel>
          <Select
            value={selectedOcorrencia}
            options={optionsOcorrencia}
            onChange={(e) => setSelectedOcorrencia(e)}
            placeholder={"Selecione"}
          />
        </CCol>
        <CCol md="6">
          <CLabel className={"mt-2"}>Selecione o telefone:</CLabel>
          <Select
            value={selectedPhone}
            options={optionsPhone}
            onChange={(e) => setSelectedPhone(e)}
            placeholder={"Selecione"}
          />
        </CCol>
      </CRow>
      <CRow className="mt-4 text-center">
        <CCol>
          <CButton
            className={"mr-2"}
            color="success"
            onClick={handleSaveOccurrence}
            disabled={isLoading || !valid}
          >
            Adicionar à ocorrência
          </CButton>
          <CButton
            className={"mr-2"}
            color="info"
            onClick={() => setShowSendEmailModal(true)}
            disabled={isLoading || !valid}
          >
            Enviar E-mail
          </CButton>
          <CButton
            color="info"
            onClick={handleSaveDeal}
            disabled={
              isLoading ||
              !checkPermission(
                permissaoAcordo.modulo,
                "Create",
                permissaoAcordo.submodulo
              ) ||
              !valid
            }
          >
            Criar Acordo
          </CButton>
        </CCol>
      </CRow>

      {showSendEmailModal && (
        <SendEmailModal
          show={showSendEmailModal}
          handleClose={() => setShowSendEmailModal(false)}
          msg={textData.current.innerText}
          em={email?.value}
        />
      )}
    </div>
  );
};

export default RegularizacaoParcelada;
