import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
} from "@coreui/react";
import { formatDate , formatThousands} from "src/reusable/helpers";

const VisualizarBoleto = ({ isOpen, onClose, dados }) => {

  const handleClose = () => {
    onClose();
  };

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="lg">
      <CModalHeader closeButton>Visualizar Boleto</CModalHeader>
      <CModalBody>
        <CRow>
          <CCol><span></span></CCol>
        </CRow>
      </CModalBody>
      <CModalFooter></CModalFooter>
    </CModal>
  );
};

export default VisualizarBoleto;
