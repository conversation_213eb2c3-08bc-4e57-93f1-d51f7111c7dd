import { CButton, CTooltip } from "@coreui/react";
import React, { useEffect, useState } from "react";

const TreeNode = ({
  item,
  onCheckboxChange,
  marginLeftText = "",
  subModulesItem = false,
}) => {
  const permissions = ["view", "create", "edit", "remove"];

  return (
    <>
      <div
        style={{
          display: "flex",
          padding: "7px",
          marginBottom: "10px",
          borderBottom: "1px solid #d8dbe0",
          backgroundColor: subModulesItem ? "#efefef" : "",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            width: "200px",
            flexGrow: "1",
            cursor:
              item.subModules && item.subModules.length > 0 ? "pointer" : "",
          }}
        >
          <div style={{ marginLeft: marginLeftText }}>{item.userName}</div>
        </div>
        {permissions.map((perm, index) => (
          <div className="pt-1" style={{ textAlign: "center", flexGrow: "4" }}>
            <input
              type="checkbox"
              style={{ width: "50px", textAlign: "center" }}
              key={index}
              checked={item[perm]}
              onClick={(e) => onCheckboxChange(item, perm, e.target.checked)}
            />
          </div>
        ))}
      </div>
    </>
  );
};

const TreeRole = ({ initialItems, onCheckboxChange }) => {
  const [items, setItems] = useState([]);

  useEffect(() => {
    setItems(initialItems);
  }, [initialItems]);

  const handleCheckboxChange = (item, permission, isChecked) => {
    const newItem = items.find((x) => x.id === item.id);
    newItem[permission] = isChecked;
    setItems([...items.filter((x) => x.id !== item.id), newItem]);
    onCheckboxChange(newItem);
  };

  const TreeHeader = () => {
    return (
      <div
        style={{
          display: "flex",
          padding: "10px",
          fontWeight: "bold",
          marginBottom: "10px",
          borderBottom: "2px solid #d8dbe0",
          borderTop: "1px solid #d8dbe0",
        }}
      >
        <div style={{ width: "200px", flexGrow: "1" }}>Usuário</div>
        <div style={{ textAlign: "center", flexGrow: "4" }}>Ver</div>
        <div style={{ textAlign: "center", flexGrow: "4" }}>Criar</div>
        <div style={{ textAlign: "center", flexGrow: "4" }}>Editar</div>
        <div style={{ textAlign: "center", flexGrow: "4" }}>Deletar</div>
      </div>
    );
  };

  return (
    <div>
      <TreeHeader />
      {items
        .sort((a, b) => a.userId - b.userId)
        .map((item) => (
          <TreeNode
            key={item.moduleId}
            item={item}
            onCheckboxChange={handleCheckboxChange}
          />
        ))}
      {items?.length < 1 && (
        <div style={{ textAlign: "center", marginTop: "10px" }}>
          Não há permissões definidas.
        </div>
      )}
    </div>
  );
};

export default TreeRole;
