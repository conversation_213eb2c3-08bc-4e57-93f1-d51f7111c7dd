import React from "react";
import { CCard, CCardBody, CLabel } from "@coreui/react";
import { useBBCContext } from "../pageContext/BBCContext.tsx";
import { formatCurrency, formatDate } from "src/reusable/helpers.js";

const labelStyle = {
  width: "200px", // Defina a largura desejada aqui
};

const CardCalcular = () => {
  const BBCContext = useBBCContext();
  return (
    <div>
      <CCard style={{ backgroundColor: "#153860", color: "white" }}>
        <CCardBody>
          <CLabel className="mr-2 h5">
            Resumo da Simulação{" "}
            {BBCContext.contratoNegociar != null
              ? BBCContext.contratoNegociar["numero_Contrato"]
              : ""}
          </CLabel>
          <hr className="m-1" />

          <div>
            <CLabel className="mr-2 mb-3">Dados do Contrato</CLabel>
          </div>

          <div>
            <CLabel style={{ color: "lightblue," }} className="mr-2">
              <div style={labelStyle}>Valor Principal: </div>
            </CLabel>
            {BBCContext.contratoNegociar != null
              ? formatCurrency(BBCContext.contratoNegociar.dadosBBC.valor)
              : formatCurrency(0.0)}
          </div>

          <div>
            <CLabel style={{ color: "lightblue," }} className="mr-2">
              <div style={labelStyle}>PMT: </div>
            </CLabel>
            {BBCContext.contratoNegociar != null
              ? formatCurrency(BBCContext.contratoNegociar.dadosBBC.pmt)
              : formatCurrency(0.0)}
          </div>
          <div>
            <CLabel style={{ color: "lightblue," }} className="mr-2">
              <div style={labelStyle}>Mora: </div>
            </CLabel>
            {BBCContext.contratoNegociar != null
              ? formatCurrency(BBCContext.contratoNegociar.dadosBBC.mora)
              : formatCurrency(0.0)}
          </div>
          <div>
            <CLabel style={{ color: "lightblue," }} className="mr-2">
              <div style={labelStyle}>Multa: </div>
            </CLabel>
            {BBCContext.contratoNegociar != null
              ? formatCurrency(BBCContext.contratoNegociar.dadosBBC.multa)
              : formatCurrency(0.0)}
          </div>
          <div>
            <CLabel style={{ color: "lightblue," }} className="mr-2">
              <div style={labelStyle}>IOF: </div>
            </CLabel>
            {BBCContext.contratoNegociar != null
              ? formatCurrency(BBCContext.contratoNegociar.dadosBBC.iof)
              : formatCurrency(0.0)}
          </div>
          <div>
            <CLabel style={{ color: "lightblue," }} className="mr-2">
              <div style={labelStyle}>Saldo Curva: </div>
            </CLabel>
            {BBCContext.contratoNegociar != null
              ? formatCurrency(BBCContext.contratoNegociar.dadosBBC.saldoCurva)
              : formatCurrency(0.0)}
          </div>

          {BBCContext?.contratoNegociar?.dadosBBC?.simulacao && (
            <>
              <hr className="m-1" />

              <div>
                <CLabel className="mr-2 mb-3">Simulação</CLabel>
              </div>
              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>Data 1º Vencimento: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.data1Vcto
                  ? formatDate(
                      BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                        ?.data1Vcto
                    )
                  : ""}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>Data Ult Vencimento: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.dataUltVcto
                  ? formatDate(
                      BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                        ?.dataUltVcto
                    )
                  : ""}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>Qtd Parcelas: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                  ?.qtdeParcelas != null
                  ? BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                      ?.qtdeParcelas
                  : ""}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>Valor Financiado: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                  ?.valorFinanciado != null
                  ? formatCurrency(
                      BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                        ?.valorFinanciado
                    )
                  : formatCurrency(0.0)}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>Valor Parcela: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                  ?.valorParcela != null
                  ? formatCurrency(
                      BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                        ?.valorParcela
                    )
                  : formatCurrency(0.0)}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>Valor Financiado: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                  ?.valorFinanciado != null
                  ? formatCurrency(
                      BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                        ?.valorFinanciado
                    )
                  : formatCurrency(0.0)}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>taxaClienteMes: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                  ?.taxaClienteMes != null
                  ? BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.taxaClienteMes.toFixed(
                      2
                    )
                  : ""}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>taxaClienteAno: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                  ?.taxaClienteAno != null
                  ? BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.taxaClienteAno.toFixed(
                      2
                    )
                  : ""}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>taxaApMes: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.taxaApMes !=
                null
                  ? BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.taxaApMes.toFixed(
                      2
                    )
                  : ""}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>taxaApAno: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.taxaApAno !=
                null
                  ? BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.taxaApAno.toFixed(
                      2
                    )
                  : ""}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>taxaCetMes: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                  ?.taxaCetMes != null
                  ? BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.taxaCetMes.toFixed(
                      2
                    )
                  : ""}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>taxaCetAno: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                  ?.taxaCetAno != null
                  ? BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.taxaCetAno.toFixed(
                      2
                    )
                  : ""}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>taxaNominalMes: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                  ?.taxaNominalMes != null
                  ? BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.taxaNominalMes.toFixed(
                      2
                    )
                  : ""}
              </div>

              <div>
                <CLabel style={{ color: "lightblue," }} className="mr-2">
                  <div style={labelStyle}>taxaNominalAno: </div>
                </CLabel>
                {BBCContext?.contratoNegociar?.dadosBBC?.simulacao
                  ?.taxaNominalAno != null
                  ? BBCContext?.contratoNegociar?.dadosBBC?.simulacao?.taxaNominalAno.toFixed(
                      2
                    )
                  : ""}
              </div>
            </>
          )}
        </CCardBody>
      </CCard>
    </div>
  );
};

export default CardCalcular;
