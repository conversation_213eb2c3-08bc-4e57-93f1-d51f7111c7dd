import { CCol, CInput, CLabel, CRow } from "@coreui/react";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";
import CardLoading from "src/reusable/CardLoading";
import Select from "react-select";

const getConfigGrupoCyfer = (payload, endpoint = "getConfigUnicaByKey") => {
  return new Promise((resolve, reject) => {
    try {
      const response = GET_DATA(
        getURI(endpoint),
        null,
        true,
        true,
        `/${payload}`
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};
const FormFunilSafra = forwardRef(
  ({ onParametersVerified, tabulacao, telefones, paramOccur }, ref) => {
    const [loading, setLoading] = useState(false);
    const [showForm, setShowForm] = useState(false);
    const [payload, setPayload] = useState({
      seguro: -1,
      garantia: -1,
      trabalho: -1,
      finalidade: -1,
      kilometragem: -1,
      redesocial: "",
      tabulacao: -1,
      contratoCurto: "",
      contratoLongo: "",
      idContratoDataCob: "",
      telefone: "",
    });
    const [defaultContrato, setDefaultContrato] = useState(null);
    const financiadoData = localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : {};
    const contratosAtivos = localStorage.getItem("contratosAtivos")
      ? JSON.parse(localStorage.getItem("contratosAtivos"))
      : [];

    useEffect(() => {
      if (tabulacao !== undefined && tabulacao !== null && tabulacao !== "") {
        handleTabulacoes(tabulacao.label);
      }
    }, [tabulacao]);

    useEffect(() => {
      if (
        telefones !== undefined &&
        telefones !== null &&
        telefones !== "" &&
        telefones.length > 0
      ) {
        const primeiroTelefone = telefones[0];
        handleTelefone(primeiroTelefone);
      }
    }, [telefones]);

    const validateFunilForm = () => {
      if (!showForm) return {};
      const newErrors = { ...initialErrors };
      newErrors.tabulacao = "";

      if (
        payload.tabulacao === -1 ||
        payload.tabulacao === undefined ||
        payload.tabulacao === ""
      )
        newErrors.tabulacao = "O campo de Tipo de Ocorrência é obrigatório";
      if (
        payload.telefone === -1 ||
        payload.telefone === undefined ||
        payload.telefone === ""
      ) {
        if (newErrors.tabulacao === "") {
          const ocr = payload.tabulacao.split(" ")[0];
          if (paramOccur.indexOf(ocr) === -1) {
            newErrors.telefone = "O campo de Telefone é obrigatório";
          }
        } else {
          newErrors.telefone = "O campo de Telefone é obrigatório";
        }
      }
      /*  if (payload.seguro === -1) newErrors.seguro = "Campo obrigatório";

     if (payload.garantia === -1) newErrors.garantia = "Campo obrigatório";

     if (payload.trabalho === -1) newErrors.trabalho = "Campo obrigatório";

     if (payload.finalidade === -1) newErrors.finalidade = "Campo obrigatório";

     if (payload.kilometragem === -1) newErrors.kilometragem = "Campo obrigatório";

     if (payload.redesocial === "") newErrors.redesocial = "Campo obrigatório";*/

      if (payload.contrato === "" || payload.contrato === undefined)
        newErrors.contrato = "Campo obrigatório";

      setErrors(newErrors);
      if (!Object.values(newErrors).every((error) => error === "")) return null;

      let contratos = handleContratosSafras(payload.contrato.numero_Contrato);

      payload.contratoCurto = contratos.contratoCurto;
      payload.contratoLongo = contratos.contratoLongo;
      payload.idContratoDataCob = payload.contrato.id_Contrato;
      return payload;
    };
    const verifyParameters = (param = "grupos_datacob_gvc_cyber_safra") => {
      setShowForm(false);
      if (
        financiadoData.id_Agrupamento !== null &&
        financiadoData.id_Agrupamento !== undefined &&
        financiadoData.id_Agrupamento !== ""
      ) {
        setLoading(true);
        getConfigGrupoCyfer(param)
          .then((data) => {
            const dataConfig = JSON.parse(data);
            if (Array.isArray(dataConfig) && dataConfig.length > 0)
              setShowForm(
                dataConfig.includes(financiadoData.id_Grupo.toString())
              );
          })
          .finally(() => {
            setLoading(false);
            onParametersVerified();
          });
      }
    };

    useImperativeHandle(ref, () => ({
      validateFunilForm,
      verifyParameters,
    }));

    useEffect(() => {
      if (contratosAtivos.length === 1) {
        const contrato = {
          label: contratosAtivos[0].numero_Contrato,
          value: contratosAtivos[0],
        };
        setDefaultContrato(contrato);
        handleValueChange("contrato", contrato.value);
      }
      verifyParameters();
    }, []);

    const options = [
      { label: "Não", value: 0 },
      { label: "Sim", value: 1 },
    ];
    const options2 = [
      { label: "Cliente", value: 1 },
      { label: "Terceiro", value: 2 },
    ];
    const options3 = [
      { label: "Particular", value: 0 },
      { label: "Trabalho", value: 1 },
    ];
    const optionsContrato = [
      // { label: "Selecione", value: 0 },
      ...contratosAtivos.map((item) => {
        return { label: item.numero_Contrato, value: item };
      }),
    ];

    const initialErrors = {
      seguro: "",
      garantia: "",
      trabalho: "",
      finalidade: "",
      kilometragem: "",
      redesocial: "",
      tabulacao: "",
      contrato: "",
      contratoCurto: "",
      contratoLongo: "",
      telefone: "",
    };
    const [errors, setErrors] = useState(initialErrors);

    const handleValueChange = (field, value) => {
      const newErrors = { ...errors };
      const newPayload = { ...payload };
      newErrors[field] = "";
      newPayload[field] = value;
      setErrors(newErrors);
      setPayload(newPayload);
    };

    const handleSeguro = (value) => handleValueChange("seguro", value);

    const handleGarantia = (value) => handleValueChange("garantia", value);

    const handleTrabalho = (value) => handleValueChange("trabalho", value);

    const handleFinalidade = (value) => handleValueChange("finalidade", value);
    const handleKilometragem = (value) => {
      const regex = /^\d+(\.\d+)?$/; // Esta regex valida números inteiros e decimais
      const newErrors = { ...errors };
      if (!regex.test(value)) {
        newErrors.kilometragem = "Por favor, insira um número válido.";
        setErrors(newErrors);
        return;
      }

      handleValueChange("kilometragem", value);
    };
    const handleRedeSocial = (value) => handleValueChange("redesocial", value);
    const handleTabulacoes = (value) => handleValueChange("tabulacao", value);
    const handleTelefone = (value) => handleValueChange("telefone", value);
    const handleContrato = (value) => handleValueChange("contrato", value);

    const handleContratosSafras = (contrato) => {
      let contratoCurto = "";
      let contratoLongo = "";

      if (contrato.length <= 9) {
        contratoCurto = contrato.padStart(9, "0");
        contratoLongo = (
          "01" +
          contratoCurto.substring(0, 3) +
          "00010" +
          contratoCurto.substring(3, 6)
        ).padStart(16, "0");
      } else if (contrato.length >= 16) {
        contratoLongo = contrato.substring(0, 15).padStart(16, "0");

        if (contrato.substring(0, 7) === "0000000") {
          contratoCurto = contrato.substring(7, 16).padStart(9, "0");
        } else if (contrato.substring(2, 7) === "11500") {
          contratoCurto = ("500" + contrato.substring(10, 16)).padStart(9, "0");
        } else if (contrato.substring(2, 7) === "00109") {
          contratoCurto = (
            contrato.substring(4, 7) + contrato.substring(10, 16)
          ).padStart(9, "0");
        } else {
          contratoCurto = (
            contrato.substring(3, 5) + contrato.substring(10, 16)
          ).padStart(9, "0");
        }
      }
      return { contratoCurto, contratoLongo };
    };

    return (
      <div>
        {loading && (
          <CardLoading
            Title="Validação Funil Safra"
            Msg="Buscando o grupo de clientes, aguarde..."
          />
        )}
        {showForm && (
          <CRow className="mt-2 mb-4">
            <CCol>
              <CLabel>
                Funil Safra
                {errors.tabulacao && (
                  <div className="text-danger">
                    {" "}
                    <br /> {errors.tabulacao}
                  </div>
                )}
                {errors.telefone && (
                  <div className="text-danger">
                    {" "}
                    <br /> {errors.telefone}
                  </div>
                )}
              </CLabel>
              <CRow>
                <CCol>
                  <CLabel>Contrato</CLabel> <br />
                  <Select
                    options={optionsContrato}
                    onChange={(e) => {
                      handleContrato(e.value);
                    }}
                    defaultValue={defaultContrato}
                    placeholder={"Selecione"}
                    isDisabled={defaultContrato != null}
                    className={errors.contrato ? "border-danger rounded" : ""}
                  />
                  {errors.contrato && (
                    <div className="text-danger">{errors.contrato}</div>
                  )}
                </CCol>
                <CCol>
                  <CLabel>Kilometragem</CLabel> <br />
                  <CInput
                    type="text"
                    onChange={(e) => {
                      handleKilometragem(e.target.value);
                    }}
                    placeholder={"Ex: 000000"}
                    className={
                      errors.kilometragem ? "border-danger rounded" : ""
                    }
                  />
                  {errors.kilometragem && (
                    <div className="text-danger">{errors.kilometragem}</div>
                  )}
                </CCol>
                <CCol>
                  <CLabel>Rede Social</CLabel> <br />
                  <CInput
                    type="text"
                    onChange={(e) => {
                      handleRedeSocial(e.target.value);
                    }}
                    placeholder={"Ex: @perfil_do_cliente"}
                    className={errors.redesocial ? "border-danger rounded" : ""}
                  />
                  {errors.redesocial && (
                    <div className="text-danger">{errors.redesocial}</div>
                  )}
                </CCol>
              </CRow>
              <CRow className={"mt-3"}>
                <CCol>
                  <CLabel>Seguro</CLabel> <br />
                  <Select
                    options={options}
                    onChange={(e) => {
                      handleSeguro(e.value);
                    }}
                    placeholder={"Selecione"}
                    className={errors.seguro ? "border-danger rounded" : ""}
                  />
                  {errors.seguro && (
                    <div className="text-danger">{errors.seguro}</div>
                  )}
                </CCol>
                <CCol>
                  <CLabel>Garantia</CLabel> <br />
                  <Select
                    options={options2}
                    onChange={(e) => {
                      handleGarantia(e.value);
                    }}
                    placeholder={"Selecione"}
                    className={errors.garantia ? "border-danger rounded" : ""}
                  />
                  {errors.garantia && (
                    <div className="text-danger">{errors.garantia}</div>
                  )}
                </CCol>

                <CCol>
                  <CLabel>Trabalho</CLabel> <br />
                  <Select
                    options={options}
                    onChange={(e) => {
                      handleTrabalho(e.value);
                    }}
                    placeholder={"Selecione"}
                    className={errors.trabalho ? "border-danger rounded" : ""}
                  />
                  {errors.trabalho && (
                    <div className="text-danger">{errors.trabalho}</div>
                  )}
                </CCol>
                <CCol>
                  <CLabel>Finalidade</CLabel> <br />
                  <Select
                    options={options3}
                    onChange={(e) => {
                      handleFinalidade(e.value);
                    }}
                    placeholder={"Selecione"}
                    className={errors.finalidade ? "border-danger rounded" : ""}
                  />
                  {errors.finalidade && (
                    <div className="text-danger">{errors.finalidade}</div>
                  )}
                </CCol>
              </CRow>
            </CCol>
          </CRow>
        )}
      </div>
    );
  }
);

export default FormFunilSafra;
