import React, { useEffect, useState } from "react";
import {
  CButton,
  CCol,
  CDataTable,
  CInput,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CRow,
} from "@coreui/react";
import CardLoading from "src/reusable/CardLoading";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { on } from "ws";
import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";
import {
  acionarLigacao,
  acionarPauseLigacao,
} from "src/config/telephonyFunctions";
import { toast } from "react-toastify";
type PrestadoresServicos = {
  id: number;
  nome: string;
  tipo: string;
  telefone1: string;
  telefone2: string;
  telefone3: string;
};
type Props = {
  isOpen: boolean;
  onClose: () => void;
};
const ModalPrestadosServicosCall = ({ isOpen, onClose }: Props) => {
  const { message } = useWebsocketTelefoniaContext();
  const [isLoading, setIsLoading] = useState(false);
  const [listaPrestadores, setListaPrestadores] = useState([]);
  const [listaFiltrada, setListaFiltrada] = useState([]);
  const [disableButtonCall, setDisableButtonCall] = useState(false);
  const [isLoadingConfirmar, setIsLoadingConfirmar] = useState(false);
  const [disableButtonConfirmar, setDisableButtonConfirmar] = useState(false);

  const [inputFiltro, setInputFiltro] = useState("");

  const handleFilterPrestadores = async () => {
    if (inputFiltro === "" || inputFiltro === null) {
      return;
    }
    // filtrar com base no nome, pegando de listaPrestadores e gerar uma nova lista, mantendo a lista antiga
    let listaFiltrada = [];
    listaPrestadores.forEach((prestador) => {
      if (prestador.nome.includes(inputFiltro)) {
        listaFiltrada.push(prestador);
      }
    });
    setListaFiltrada(listaFiltrada);
  };

  const renderActions = (item: PrestadoresServicos) => {
    return (
      <td>
        <CButton color="warning" onClick={() => {}} className="mr-2" size="sm">
          <i className="cil-pencil" />
        </CButton>
        <CButton color="danger" onClick={() => {}} size="sm">
          <i className="cil-trash" />
        </CButton>
      </td>
    );
  };
  const IconButton = ({
    icon,
    color,
    titulo,
    onClick,
    disabled = false,
    className = "",
  }) => {
    return (
      <CButton
        title={titulo}
        className={className}
        style={{
          border: "solid 1px",
          borderColor: color,
          color: color,
          padding: "2px 4px",
        }}
        onClick={onClick}
        disabled={disabled}
      >
        <i className={icon} />
      </CButton>
    );
  };
  async function handleCallResponse(callData, response) {
    while (callData.status === "Idle") {
      await new Promise((resolve) => setTimeout(resolve, 500));
    }
    setIsLoadingConfirmar(false);
    switch (response) {
      case 11:
        toast.warning("Usuário não está conectado à telefonia.");
        break;
      case 12:
        toast.warning(
          "Por favor, faça conexão com a telefonia antes de realizar chamadas."
        );
        break;
      case 13:
        toast.warning("Problemas realizando a chamada.");
        break;
      default:
        break;
    }
  }
  async function ligacaoManual(ddd, fone) {
    let callData = message;

    setDisableButtonCall(true);
    if (callData && callData.status === "Idle") {
      setIsLoadingConfirmar(true);
      localStorage.removeItem("pausaId");
      const response = await acionarLigacao(ddd, fone, message);
      await handleCallResponse(callData, response);
    } else if (callData && callData.status === "Pause") {
      setIsLoadingConfirmar(true);
      const response = await acionarPauseLigacao(ddd, fone, message);
      await handleCallResponse(callData, response);
    } else {
      if (
        callData &&
        (callData.status === "Talking" ||
          callData.status === "TalkingWithPause" ||
          callData.status === "TalkingWithEnding" ||
          callData.status === "TalkingWithManualCall" ||
          callData.status === "PersonalCall" ||
          callData.status === "ManualCall")
      ) {
        toast.warning("Já existe uma chamada em andamento.");
      } else if (callData && callData.status === "Error") {
        toast.warning(callData.message);
      } else
        toast.warning(
          "Por favor, verifique se está conectado à telefonia e com o status Livre."
        );
    }
  }
  const renderTelefoneCallButton = (numberphone) => {
    if (!numberphone) {
      return <td></td>;
    }
    const match = numberphone.match(/\((\d{2})\)\s*(\d{4,5}-\d{4})/);

    if (match) {
      const ddd = match[1]; // "43"
      const numero = match[2]; // "42134-2134"

      return (
        <td>
          <span>
            ({ddd}) {numero}
          </span>
          <IconButton
            className="ml-2"
            icon={"cil-phone"}
            color={"blue"}
            titulo={"Realizar Ligação"}
            onClick={() => ligacaoManual(ddd, numero.replace(/-/g, ""))}
            disabled={
              (disableButtonCall && message?.status !== "Idle") ||
              (message &&
                message?.status !== "Idle" &&
                message?.status !== "Pause")
            }
          />
        </td>
      );
    } else {
      console.log("Formato inválido");
      return (
        <td>
          <span>{numberphone}</span>
        </td>
      );
    }
  };

  const renderTipo = (item: PrestadoresServicos) => {
    if (!item.tipo) {
      return <td></td>;
    }

    try {
      // Substitui aspas simples por aspas duplas
      const jsonCorrigido = item.tipo.replace(/'/g, '"');
      const array = JSON.parse(jsonCorrigido);

      return (
        <td>
          {array.map((tipo: string, index: number) => (
            <span className="badge badge-secondary mr-1" key={index}>
              {tipo}
              {index < array.length - 1 ? ", " : ""}
            </span>
          ))}
        </td>
      );
    } catch (error) {
      console.error("Erro ao converter tipo:", item.tipo, error);
      return <td>Erro</td>;
    }
  };
  useEffect(() => {
    getListaPrestadores();
  }, []);

  const getListaPrestadores = async () => {
    setIsLoading(true);
    await GetData(null, "prestadoresServicos")
      .then((resultado: PrestadoresServicos[]) => {
        if (resultado) {
          setListaPrestadores(resultado);

          setListaFiltrada(resultado);
        } else {
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  return (
    <div>
      <CModal
        size="lg"
        className="custom-modal"
        show={isOpen}
        onClose={onClose}
        closeOnBackdrop={false}
      >
        <CModalHeader closeButton>
          <CModalTitle>Prestadores Cadastrados</CModalTitle>
        </CModalHeader>
        <CModalBody>
          {isLoading ? <CardLoading Title={""} Msg={""} /> : ""}
          {!isLoading ? (
            <CRow className={"p-2"}>
              <CCol>
                <CRow>
                  <CCol
                    className={"d-flex justify-content-end align-items-center"}
                  >
                    <CLabel className="mr-2">Perquisa</CLabel>
                    <CInput
                      className="mr-2"
                      value={inputFiltro}
                      onKeyUp={(e: React.KeyboardEvent<HTMLInputElement>) =>
                        e.key === "Enter" && handleFilterPrestadores()
                      }
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        setInputFiltro(e.target.value)
                      }
                    />
                    <CButton
                      color="success"
                      className=""
                      onClick={() => handleFilterPrestadores()}
                    >
                      Pesquisar
                    </CButton>
                  </CCol>
                </CRow>
                <CRow className={"mt-2"}>
                  <CCol>
                    <CDataTable
                      items={listaFiltrada}
                      fields={[
                        { key: "nome", label: "Nome" },
                        { key: "tipo", label: "Tipo" },
                        { key: "telefone1", label: "Telefone 1" },
                        { key: "telefone2", label: "Telefone 2" },
                        { key: "telefone3", label: "Telefone 3" },
                      ]}
                      hover
                      striped
                      loading={isLoading}
                      scopedSlots={{
                        actions: (item) => renderActions(item),
                        tipo: (item) => renderTipo(item),
                        telefone1: (item) =>
                          renderTelefoneCallButton(item.telefone1),
                        telefone2: (item) =>
                          renderTelefoneCallButton(item.telefone2),
                        telefone3: (item) =>
                          renderTelefoneCallButton(item.telefone3),
                      }}
                      noItemsViewSlot={
                        <p className="text-center">
                          Nenhum registro encontrado
                        </p>
                      }
                    />
                  </CCol>
                </CRow>
              </CCol>
            </CRow>
          ) : (
            ""
          )}
        </CModalBody>

        <CModalFooter>
          <CButton color="secondary" className="mr-2" onClick={() => onClose()}>
            Sair
          </CButton>
        </CModalFooter>
      </CModal>
    </div>
  );
};

export default ModalPrestadosServicosCall;
