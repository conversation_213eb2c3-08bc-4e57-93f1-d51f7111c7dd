/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect } from "react";
import { CButton, CCard, CCardBody, CCol, CRow } from "@coreui/react";
import DadosContratos from "src/views/telaPrincipal/CardContratos/Contratos";

/* Import da Tela */
import CardCalcular from "./partials/CardCalcular.tsx";
import CardAcordos from "./partials/CardAcordos.tsx";
import CardContratos from "./partials/CardContratos.tsx";
import CardLoading from "src/reusable/CardLoading";
import CardAvisos from "src/reusable/CardAvisos";

/* Import de utilidades */
import "react-datepicker/dist/react-datepicker.css";
import { useBBCContext } from "./pageContext/BBCContext.tsx";
import AlertaModal from "../AlertaModal.js";
import { useAuth } from "src/auth/AuthContext.js";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min.js";

const Negociar = () => {
  const BBCContext = useBBCContext();
  const {permissions, checkPermission} = useAuth()
  const history = useHistory();

  useEffect(()=>{
    if(
      checkPermission('Negociação', 'View', 'BBC') === false 
      && checkPermission('Negociação', 'Create', 'BBC') === false 
      && permissions?.isAdmin !== true
    ){
      history.push('/telaprincipal');
    }
  },[]);

  return (
    
      <div>
        <AlertaModal
          isOpen={BBCContext.alertModalShow}
          onClose={()=>{BBCContext.closeAlertModal(false)}}
          dados={BBCContext.alertModalMessage}
        />

        <CRow className="mb-2">
          <CCol>
            <h1>Negociar BBC</h1>
          </CCol>
        </CRow>
        {BBCContext.TitleAviso !== "" && BBCContext.MsgAviso !== "" ? (
          <div>
            <CardAvisos Title={BBCContext.TitleAviso} Msg={BBCContext.MsgAviso} />
            {BBCContext.financiadoData.id_Agrupamento == null || BBCContext.loading ? (
              ""
            ) : (
              <CButton
                color="success"
                disabled={BBCContext.financiadoData.id_Agrupamento == null || BBCContext.loading}
                // onClick={() => BBCContext.verifyParameters()}
              >
                <i className="cil-reload" />
              </CButton>
            )}
          </div>
        ) : BBCContext.loading && BBCContext.loadingAction === "VarifyParam" ? (
          <CardLoading
            Title="Validação do Parametros"
            Msg="Buscando o grupo de clientes, aguarde..."
          />
        ) : (
          <CRow>
            <CCol xs="8">
              <CCard>
                <CCardBody>
                  <DadosContratos selected={""} />
                </CCardBody>
              </CCard>
              <div>
                <CardAcordos />
              </div>
              <div>
                <CardContratos />
              </div>
            </CCol>
            <CCol xs="4">
              <CardCalcular/>
            </CCol>
          </CRow>
        )}
      </div>
  );
};

export default Negociar;
