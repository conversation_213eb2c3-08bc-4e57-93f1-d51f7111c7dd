/* Estilos para a modal de preview do documento */
.preview-modal .modal-dialog {
  max-width: 90vw;
  width: 90vw;
  height: 90vh;
  margin: 5vh auto;
}

.preview-modal .modal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-modal .modal-body {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #f8f9fa;
}

.preview-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #f8f9fa;
}

.preview-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #f8f9fa;
  flex-direction: column;
}

.preview-error-icon {
  font-size: 3rem;
  color: #f86c6b;
  margin-bottom: 1rem;
}

.preview-error-message {
  color: #721c24;
  font-size: 1.1rem;
  text-align: center;
  margin-bottom: 1rem;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
  .preview-modal .modal-dialog {
    max-width: 95vw;
    width: 95vw;
    height: 95vh;
    margin: 2.5vh auto;
  }
}

/* Melhorias para o botão de download */
.download-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.download-button i {
  font-size: 1rem;
}

/* Animação de loading */
.preview-loading .spinner-border {
  width: 3rem;
  height: 3rem;
}

.preview-loading-text {
  margin-top: 1rem;
  color: #6c757d;
  font-size: 1rem;
}
