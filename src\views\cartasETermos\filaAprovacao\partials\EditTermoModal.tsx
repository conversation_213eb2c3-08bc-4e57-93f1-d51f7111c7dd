import React, { useCallback, useEffect, useState } from "react";
import {
  CButton,
  CInput,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
} from "@coreui/react";
import { getApi, getApiInline, putApi } from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import {
  convertCurrencyToFloatDynamic,
  formatCurrency,
  formatDateGlobaltoSimplified,
} from "src/reusable/helpers";
import { toast } from "react-toastify";
import { ItemFilaAprovacaoType } from "../../types/ItemFilaAprovacaoType";

interface Props {
  isOpen: boolean;
  item: ItemFilaAprovacaoType | null;
  onClose: (updateData?: boolean) => void;
}

const EditTermoModal = ({ isOpen, onClose, item }: Props) => {
  const handleClose = () => {
    onClose();
  };

  const [loading, setLoading] = useState(false);
  const [tipoTermo, setTipoTermo] = useState([]);
  const [tipoTermoSelected, setTipoTermoSelected] = useState(null);

  // Form fields based on TermoJuridicoModal
  const [valorVencidas, setValorVencidas] = useState(0);
  const [valorVincendas, setValorVincendas] = useState(0);
  const [multaJuros, setMultaJuros] = useState(0);
  const [diferencaParcelas, setDiferencaParcelas] = useState(0);
  const [honorarios, setHonorarios] = useState(0);
  const [custas, setCustas] = useState(0);
  const [total, setTotal] = useState(0);
  const [qtdParcelas, setQtdParcelas] = useState(0);
  const [dataBase, setDataBase] = useState(new Date());
  const [valorAcordado, setValorAcordado] = useState(0);
  const [jurisdicaoAtual, setJurisdicaoAtual] = useState("");
  const [nrAtual, setNrAtual] = useState("");
  const [tipoAcao, setTipoAcao] = useState("");
  const [descricaoVeiculo, setDescricaoVeiculo] = useState("");
  const [grupoCotaContrato, setGrupoCotaContrato] = useState("");
  const [clientePrincipal, setClientePrincipal] = useState("");
  const [adversoPrincipal, setAdversoPrincipal] = useState("");
  const [nrParcelasVencidas, setNrParcelasVencidas] = useState("");
  const [nrParcelasVincendas, setNrParcelasVincendas] = useState("");

  const getTipoTermo = useCallback(async () => {
    try {
      const response = await getApi({}, "getTermoJuridico");
      if (response && response.length > 0) {
        setTipoTermo(response);
      } else {
        setTipoTermo([]);
      }
    } catch (error) {
      console.log(error);
    }
  }, []);

  const loadExistingTermData = useCallback(async () => {
    if (!item?.id) return;

    try {
      const response = await getApiInline(item.id, "getTermosInfos");
      if (response) {
        setJurisdicaoAtual(response.jurisdicaoAtual || "");
        setNrAtual(response.nrAtual || "");
        setClientePrincipal(response.cliente || "");
        setTipoAcao(response.tipoAcao || "");
        setAdversoPrincipal(response.adversoPrincipal || "");
        setGrupoCotaContrato(response.grupoCotaContrato || "");
        setNrParcelasVencidas(response.nrParcelasVencidas || "");
        setValorVencidas(response.valorParcelasVencidas || 0);
        setMultaJuros(response.multaJuros || 0);
        setCustas(response.custas || 0);
        setNrParcelasVincendas(response.nrParcelasVincendas || "");
        setValorVincendas(response.valorParcelasVincendas || 0);
        setHonorarios(response.honorarios || 0);
        setQtdParcelas(response.qtdParcelasAcordadas || 0);
        setValorAcordado(response.valorAcordado || 0);
        setDescricaoVeiculo(response.descricaoVeiculo || "");
        if (response.dataBase) {
          setDataBase(new Date(response.dataBase));
        }
      }
    } catch (error) {
      console.log("Could not load existing term data:", error);
      // This is expected if the endpoint doesn't exist yet
    }
  }, [item?.id]);

  const asyncLoadFunc = useCallback(async () => {
    setLoading(true);
    await Promise.all([getTipoTermo(), loadExistingTermData()]);
    setLoading(false);
  }, [getTipoTermo, loadExistingTermData]);

  useEffect(() => {
    asyncLoadFunc();
  }, [asyncLoadFunc]);

  // Calculate total when values change
  useEffect(() => {
    const calculatedTotal =
      valorVencidas +
      valorVincendas +
      multaJuros +
      diferencaParcelas +
      honorarios +
      custas;
    setTotal(calculatedTotal);
    setValorAcordado(calculatedTotal);
  }, [
    valorVencidas,
    valorVincendas,
    multaJuros,
    diferencaParcelas,
    honorarios,
    custas,
  ]);

  const handleSave = async () => {
    try {
      setLoading(true);

      const payload = {
        pedidoId: item?.id,
        jurisdicaoAtual: jurisdicaoAtual,
        nrAtual: nrAtual,
        clientePrincipal: clientePrincipal,
        tipoAcao: tipoAcao,
        adversoPrincipal: adversoPrincipal,
        grupoCotaContrato: grupoCotaContrato,
        nrParcelasVencidas: nrParcelasVencidas,
        valorParcelasVencidas: valorVencidas,
        multaJuros: multaJuros,
        custas: custas,
        nrParcelasVincendas: nrParcelasVincendas,
        valorParcelasVincendas: valorVincendas,
        honorarios: honorarios,
        total: total,
        dataBase: dataBase,
        qtdParcelasAcordadas: qtdParcelas,
        valorAcordado: valorAcordado,
        descricaoVeiculo: descricaoVeiculo,
      };

      const response = await putApi(payload, "putTermosInfos");

      if (response?.success === true) {
        toast.success("Termo atualizado com sucesso!");
        onClose(true);
      } else {
        toast.error("Erro ao atualizar termo!");
      }
    } catch (error) {
      console.error(error);
      toast.error("Erro ao atualizar termo!");
    } finally {
      setLoading(false);
    }
  };

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal modal-xxl"
      centered
    >
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Editar Termo Jurídico - {item?.nrContrato}</h5>
      </CModalHeader>
      {loading && (
        <CModalBody style={{ minHeight: "470px" }}>
          <CardLoading Title="Carregando" Msg="Aguarde..." />
        </CModalBody>
      )}
      {!loading && (
        <CModalBody>
          <div className="row mt-4">
            <div className="col-md-6">
              <label className="pt-1">Cliente Principal:</label>
              <CInput
                className="mr-2 ml-2"
                value={clientePrincipal}
                onChange={(e) => setClientePrincipal(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-6">
              <label className="pt-1">Adverso Principal:</label>
              <CInput
                className="mr-2 ml-2"
                value={adversoPrincipal}
                onChange={(e) => setAdversoPrincipal(e.currentTarget.value)}
              />
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-4">
              <label className="pt-1">Grupo/Cota/Contrato:</label>
              <CInput
                className="mr-2 ml-2"
                value={grupoCotaContrato}
                onChange={(e) => setGrupoCotaContrato(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-4">
              <label className="pt-1">Nº Parcelas Vencidas:</label>
              <CInput
                className="mr-2 ml-2"
                value={nrParcelasVencidas}
                onChange={(e) => setNrParcelasVencidas(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-4">
              <label className="pt-1">Nº Parcelas Vincendas:</label>
              <CInput
                className="mr-2 ml-2"
                value={nrParcelasVincendas}
                onChange={(e) => setNrParcelasVincendas(e.currentTarget.value)}
              />
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-3">
              <label className="pt-1">Valor Parcelas Vencidas:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(valorVencidas, false)}
                onChange={(e) =>
                  setValorVencidas(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Valor Parcelas Vincendas:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(valorVincendas, false)}
                onChange={(e) =>
                  setValorVincendas(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Honorários Advocatícios:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(honorarios, false)}
                onChange={(e) =>
                  setHonorarios(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Multa e Juros:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(multaJuros, false)}
                onChange={(e) =>
                  setMultaJuros(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-3">
              <label className="pt-1">Custas:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(custas, false)}
                onChange={(e) =>
                  setCustas(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Total:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(total, false)}
                readOnly
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Qtd Parcelas Acordadas:</label>
              <CInput
                className="mr-2 ml-2"
                value={qtdParcelas}
                onChange={(e) => setQtdParcelas(Number(e.currentTarget.value))}
                type="number"
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Valor Acordado:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(valorAcordado, false)}
                onChange={(e) =>
                  setValorAcordado(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-3">
              <label className="pt-1">Jurisdição Atual:</label>
              <CInput
                className="mr-2 ml-2"
                value={jurisdicaoAtual}
                onChange={(e) => setJurisdicaoAtual(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Número Atual:</label>
              <CInput
                className="mr-2 ml-2"
                value={nrAtual}
                onChange={(e) => setNrAtual(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Tipo Ação:</label>
              <CInput
                className="mr-2 ml-2"
                value={tipoAcao}
                onChange={(e) => setTipoAcao(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Descrição Veículo:</label>
              <CInput
                className="mr-2 ml-2"
                value={descricaoVeiculo}
                onChange={(e) => setDescricaoVeiculo(e.currentTarget.value)}
              />
            </div>
          </div>
        </CModalBody>
      )}
      <CModalFooter>
        <CButton
          color="success"
          className="mr-2"
          onClick={handleSave}
          disabled={loading}
        >
          Salvar Alterações
        </CButton>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Cancelar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default EditTermoModal;
