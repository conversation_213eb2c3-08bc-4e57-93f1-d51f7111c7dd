import React, { useState } from "react";

import "react-datepicker/dist/react-datepicker.css";

import {
  CButton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalTitle,
  CCol,
  CRow,
  CLabel,
  CModalFooter,
  CInput,
} from "@coreui/react";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import Select from "react-select";
import { isEmailValid } from "src/reusable/helpers";


const PostData = async (payload, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), payload, true, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const FormEnviarEmailModal = ({ isOpen, onClose, idAcordo, id }) => {
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");
  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");
  const [tipoEnvioOutro, setTipoEnvioOutro] = useState(false);

  const [payload, setPayload] = useState({
    nroAcordo: idAcordo.toString(),
    email: ""
  });

  const initialErrors = {
    nroAcordo: "",
    email: ""
  };

  const [errors, setErrors] = useState(initialErrors);

  const EnviarEmail = async () => {
    let ret = false;
    setTitleAvisoLoading("Enviando Email API Safra")
    setMsgAvisoLoading(`Enviando...`)
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(payload, "cyberSafraEnviarEmail")
      .then((data) => {
        if (data.success)
          if (data.message.includes("E-mail enviado com sucesso")) {
            setTitleAvisoLoading("E-mail Enviado");
            setMsgAvisoLoading("");
          } else {
            setMsgAvisoLoading("Falha ao enviar e-mail");
            setTitleAvisoLoading(data.message);
          }

      }).catch((err) => {
        setMsgAvisoLoading(`Erro na chamada das APIS`);
        setTitleAvisoLoading(`Erro na chamada API de Cyber Simulação de Acordos, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`);
      })
      .finally(() => {
        setTimeout(() => {
          setLoading(false);
          setLoadingAction("empty");
          onClose();
        }, 3000);
      });

    return ret;
  }

  const handleEnviar = () => {
    setErrors(initialErrors);
    const newErrors = { ...initialErrors };
    handlePayloadChange("nroAcordo", idAcordo.toString())
    if (payload.email === undefined || payload.email === null || payload.email === "")
      newErrors.email = "E-mail é Obrigatório";

    if(!isEmailValid(payload.email))
      newErrors.email = "E-mail inválido";

    if (payload.nroAcordo === undefined || payload.nroAcordo === null || payload.nroAcordo === "")
      newErrors.motivoCanc = "Id do Acordo não informado";

    setErrors(newErrors);
    if (!Object.values(newErrors).every(error => error === "")){
      return;
    }

    EnviarEmail();
  };


  let emailsCliente = localStorage.getItem("clientData") ? JSON.parse(localStorage.getItem("clientData")) : "";
  const optionsContrato = [
    ...emailsCliente.emails.map((item) => {
      return { label: item.endereco_Email, value: item.endereco_Email };
    }),
  ];

  const handlePayloadChange = (field, value) => {

    setErrors({ ...errors, [field]: "" });
    setPayload((prevPayload) => ({
      ...prevPayload,
      [field]: value
    }));
  };

  const handleEmail = (value) => {
    handlePayloadChange("email", value);
  }

  const handleTipoEnvio = (value) => {
    setTipoEnvioOutro(value);
  }

  return (
    <CModal
      className="custom-modal"
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
    >
      <CModalHeader closeButton>
        <CModalTitle>Ennviar e-mail Cliente</CModalTitle>
      </CModalHeader>
      <CModalBody>
        {loading && loadingAction === "VarifyParam" ?
          (<CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />) : ""
        }
        {!loading ? <CRow className={"p-2"}>
          <CCol>
            <CRow>
              <CCol>
                <div className={"form-check form-switch"}>
                  <input className={"form-check-input"} type="checkbox" role="switch" id="flexSwitchCheckDefault" onChange={(e) => { handleTipoEnvio(e.target.checked); }} />
                  <label className={"form-check-label"} htmlFor="flexSwitchCheckDefault">Usar outro e-mail</label>
                </div>
              </CCol>

            </CRow>
            <CRow className={"mt-3"}>
              {!tipoEnvioOutro ? <CCol>
                <CLabel>E-mail Cadastrado</CLabel> <br />
                <Select
                  options={optionsContrato}
                  onChange={(e) => { handleEmail(e.value); }}
                  placeholder={"Selecione"}
                  className={errors.email ? 'border-danger rounded' : ''}
                />
                {errors.email && <div className="text-danger">{errors.email}</div>}
              </CCol> : <CCol>
                <CLabel>E-mail</CLabel> <br />
                <CInput
                  type="email"
                  onChange={(e) => { handleEmail(e.target.value); }}
                  placeholder={"Ex: <EMAIL>"}
                  className={errors.email ? 'border-danger rounded' : ''}
                />
                {errors.email && <div className="text-danger">{errors.email}</div>}
              </CCol>}
            </CRow>
          </CCol>
        </CRow> : ""}

      </CModalBody>

      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={onClose}>
          Fechar
        </CButton>
        <CButton
          color="info"
          onClick={handleEnviar}
          disabled={loading}
        >
          Enviar e-mail
        </CButton>

      </CModalFooter>

    </CModal>);
}

export default FormEnviarEmailModal;
