import React, { useState, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { CButton } from "@coreui/react";

const FileDropzone = ({ onFilesAdded }) => {
  const [files, setFiles] = useState([]);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      const mappedFiles = acceptedFiles.map(file => Object.assign(file, {
        preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null
      }));
      setFiles([...files, ...mappedFiles]);
      onFilesAdded([...files, ...mappedFiles]);
    }
  });

  const removeFile = (fileToRemove) => {
    const newFiles = files.filter(file => file !== fileToRemove);
    setFiles(newFiles);
    onFilesAdded(newFiles);
  };

  useEffect(() => {
    return () => files.forEach(file => URL.revokeObjectURL(file.preview));
  }, [files]);

  return (
    <div>
      <div {...getRootProps()} style={{ border: '2px dashed gray', padding: '20px', textAlign: 'center' }}>
        <input {...getInputProps()} />
        <p>Arraste e solte alguns arquivos aqui, ou clique para selecionar arquivos</p>
      </div>
      <ul className="list-group list-group-flush">
        {files.map(file => (
          <li className="list-group-item" style={{ display: "flex", flexDirection: "row" }} key={file.path}>
            <div style={{ width: "80px" }}>
              {file.preview ? (
                <img src={file.preview} style={{ width: '50px', height: '50px' }} alt="Pré-visualização" />
              ) : (
                <span><i style={{ fontSize: '50px' }} className="cil-file"></i></span>
              )}
            </div>
            <div style={{ flexGrow: "2", display: "flex", alignItems: "center" }}>{file.path} - {file.size} bytes</div>
            <div style={{ display: "flex", alignItems: "center" }}>
              <CButton color="danger" onClick={() => removeFile(file)}>
                <i className="cil-trash"></i>
              </CButton>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FileDropzone;
