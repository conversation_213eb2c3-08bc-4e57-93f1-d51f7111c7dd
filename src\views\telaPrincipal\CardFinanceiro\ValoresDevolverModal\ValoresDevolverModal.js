import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CCard,
  CRow,
  CCol,
  CLabel,
} from "@coreui/react";

import {
  formatThousands,
  formatDate,
  formatDocument,
  formatDateTime,
} from "src/reusable/helpers";

import LoadingComponent from "src/reusable/Loading";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import DetalhesDevolverModal from "./DetalhesDevolverModal";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import TableSelectItens from "src/reusable/TableSelectItens";

const ValoresDevolverModal = ({ isOpen, onClose, idCota }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(null);

  const [showDetalhesModal, setShowDetalhesModal] = useState(false);
  const [detalhesDevolver, setDetalhesDevolver] = useState(null);

  const closureFields = [
    {
      key: "number",
      label: "Nº",
      formatterByObject: (item) => renderRowCell(item, item.number),
    },
    {
      key: "closure",
      label: "Encerramento",
      formatterByObject: (item) => renderRowCell(item, item.closure),
    },
    {
      key: "control",
      label: "Controle",
      formatterByObject: (item) => renderRowCell(item, item.control),
    },
    {
      key: "disposition",
      label: "Disposição(+)",
      formatterByObject: (item) => renderRowCellValue(item, item.disposition),
    },
    {
      key: "advance",
      label: "Antecipado(-)",
      formatterByObject: (item) => renderRowCellValue(item, item.advance),
    },
    {
      key: "vnp",
      label: "VNP(-)",
      formatterByObject: (item) => renderRowCellValue(item, item.vnp),
    },
    {
      key: "revenue",
      label: "Rendimento(+)",
      formatterByObject: (item) => renderRowCellValue(item, item.revenue),
    },
    {
      key: "payment",
      label: "Pagamento(-)",
      formatterByObject: (item) => renderRowCellValue(item, item.payment),
    },
    {
      key: "provIR",
      label: "Prov. I.R.(-)",
      style: { whiteSpace: "nowrap" },
      formatterByObject: (item) => renderRowCellValue(item, item.provIR),
    },
    {
      key: "bankTax",
      label: "Tar. Banco(-)",
      style: { whiteSpace: "nowrap" },
      formatterByObject: (item) => renderRowCellValue(item, item.bankTax),
    },
    {
      key: "cpmf",
      label: "CPMF(-)",
      formatterByObject: (item) => renderRowCellValue(item, item.cpmf),
    },
    {
      key: "refund",
      label: "Devolver(=)",
      formatterByObject: (item) => renderRowCellValue(item, item.refund),
    },
  ];

  const favoredsFields = [
    {
      key: "name",
      label: "Nome do Favorecido",
    },
    {
      key: "doc",
      label: "CPF/CNPJ",
      formatter: (item) => formatDocument(item),
    },
    {
      key: "value",
      label: "Valor",
      formatter: (item) => formatThousands(item),
    },
  ];

  const messagesFields = [
    {
      key: "message",
      label: "Mensagem",
    },
  ];

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleClose = () => {
    setShowDetalhesModal(false);
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      GetData(`/${idCota}`, "getNewconValorDevolver")
        .then((data) => {
          setData(data);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen]);

  const handleRowDoubleClick = (item) => {
    const detalhesData = {
      idCota: idCota,
      encerramento: item.closureDT,
    };
    setDetalhesDevolver(detalhesData);
    setShowDetalhesModal(true);
  };

  const renderRowCell = (item, cellValue) => {
    return (
      <div onDoubleClick={() => handleRowDoubleClick(item)}>
        {cellValue ?? "---"}
      </div>
    );
  };

  const renderRowCellValue = (item, cellValue) => {
    return (
      <div onDoubleClick={() => handleRowDoubleClick(item)}>
        {cellValue ? formatThousands(cellValue) : "0,00"}
      </div>
    );
  };

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="xl"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Consulta de Valores a Devolver</h5>
      </CModalHeader>
      <CModalBody>
        {isLoading ? (
          <div className="mt-2">
            <LoadingComponent />
          </div>
        ) : data == null || data === undefined || data.length === 0 ? (
          <NaoHaDadosTables />
        ) : (
          <>
            <CRow>
              <CCol>
                <CCol className="information-text">
                  Dê um duplo clique com o botão esquerdo do mouse para ver os
                  detalhes das informações.
                </CCol>
                <CCard>
                  <TableSelectItens
                    data={data.closure}
                    columns={closureFields}
                    onSelectionChange={(_) => {}}
                    defaultSelectedKeys={[]}
                    selectable={false}
                    rowDataClass={"double-clickable-table"}
                    heightParam="290px"
                  />
                </CCard>
              </CCol>
            </CRow>
            <CRow className="mt-2">
              <CCol>
                <CCard>
                  <TableSelectItens
                    data={data.favoreds}
                    columns={favoredsFields}
                    onSelectionChange={(_) => {}}
                    defaultSelectedKeys={[]}
                    selectable={false}
                    heightParam="290px"
                  />
                </CCard>
              </CCol>
            </CRow>
            <CRow>
              <CCol style={{ textAlign: "center" }}>
                <CLabel>Forma de Recebimento a ser Utilizada</CLabel>{" "}
              </CCol>
            </CRow>
            <CRow>
              <CCol md="5">
                <CLabel className="mr-2">Forma Recebimento</CLabel>
                <>{data?.receipt ? data.receipt : "---"}</>
              </CCol>
              <CCol md="3">
                <CLabel className="mr-2">Banco</CLabel>
                <>{data?.bank ? data.bank : "---"}</>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Agência</CLabel>
                <>{data?.agency ? data.agency : "---"}</>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Conta</CLabel>
                <>{data?.account ? data.account : "---"}</>
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CLabel className="mr-2">Data Remessa</CLabel>
                <>
                  {data?.shippingDate ? formatDate(data.shippingDate) : "---"}
                </>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Data Retorno Pagamento</CLabel>
                <>
                  {data?.returnPaymentDate
                    ? formatDate(data.returnPaymentDate)
                    : "---"}
                </>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Data Pagamento</CLabel>
                <>{data?.paymentDate ? formatDate(data.paymentDate) : "---"}</>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Data Estorno</CLabel>
                <>{data?.refundDate ? formatDate(data.refundDate) : "---"}</>
              </CCol>
            </CRow>
            <hr />
            <CRow>
              <CCol>
                <CLabel className="mr-2">Cota do Dia</CLabel>
                <>{data?.quotaDay ? data.quotaDay : "---"}</>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Saldo em Cotas</CLabel>
                <>{data?.quotaValue ? data.quotaValue : "---"}</>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Posição Em</CLabel>
                <>
                  {data?.positionIn ? formatDateTime(data.positionIn) : "---"}
                </>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Início Rendimento</CLabel>
                <>
                  {data?.beginRevenue ? formatDate(data.beginRevenue) : "---"}
                </>
              </CCol>
            </CRow>
            <hr />
            <CRow>
              <CCol>
                <CLabel className="mr-2">Sit. Cota</CLabel>
                <>{data?.quotaSituation ? data.quotaSituation : "---"}</>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Perc. Pago</CLabel>
                <>{data?.paidPerc ? data.paidPerc + "%" : "---"}</>
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CLabel className="mr-2">Bem</CLabel>
                <>{data?.asset ? data.asset : "---"}</>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Valor do Bem</CLabel>
                <>
                  {data?.assetValue ? formatThousands(data.assetValue) : "---"}
                </>
              </CCol>
            </CRow>{" "}
            <CRow>
              <CCol style={{ textAlign: "center" }}>
                <CLabel className="mr-2">Dados do Controle de Postagem</CLabel>
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CLabel className="mr-2">Data Envio</CLabel>
                <>{data?.sendDate ? formatDate(data.sendDate) : "---"}</>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Número A.R.</CLabel>
                <>{data?.arNumber ? data.arNumber : "---"}</>
              </CCol>
              <CCol>
                <CLabel className="mr-2">Enviado Em</CLabel>
                <>{data?.sendIn ? formatDate(data.sendIn) : "---"}</>
              </CCol>
            </CRow>
            <CCard className="mt-2">
              <TableSelectItens
                data={data.messages}
                columns={messagesFields}
                onSelectionChange={(_) => {}}
                defaultSelectedKeys={[]}
                selectable={false}
                heightParam="290px"
              />
            </CCard>
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
      <DetalhesDevolverModal
        isOpen={showDetalhesModal}
        onClose={() => setShowDetalhesModal(false)}
        payload={detalhesDevolver}
      />
    </CModal>
  );
};

export default ValoresDevolverModal;
