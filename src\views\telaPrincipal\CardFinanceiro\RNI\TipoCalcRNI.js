import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
} from "@coreui/react";

const TipoCalcRNI = ({ isOpen, onClose }) => {
  const [tipo, setTipo] = useState(null);

  const handleClose = (x = false) => {
    if (!x) onClose();
    else {
      if (tipo === null) {
        alert("Por favor, selecione um tipo de cálculo, ou clique em Sair.");
        return;
      }
      onClose(tipo);
    }
  };

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="lg"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Selecione o tipo de cálculo desejado:</h5>
      </CModalHeader>
      <CModalBody>
        <div className="d-flex justify-content-around">
          <div className="form-check">
            <input
              className="form-check-input"
              type="radio"
              name="flexRadioDefault"
              id="flexRadioDefault1"
              onChange={(e) => setTipo(1)}
            />
            <label className="form-check-label" htmlFor="flexRadioDefault1">
              Parcelamento
            </label>
          </div>
          <div className="form-check">
            <input
              className="form-check-input"
              type="radio"
              name="flexRadioDefault"
              id="flexRadioDefault2"
              onChange={(e) => setTipo(2)}
            />
            <label className="form-check-label" htmlFor="flexRadioDefault2">
              Parcelamento Desconto Principal
            </label>
          </div>
          <div className="form-check">
            <input
              className="form-check-input"
              type="radio"
              name="flexRadioDefault"
              id="flexRadioDefault3"
              onChange={(e) => setTipo(3)}
            />
            <label className="form-check-label" htmlFor="flexRadioDefault3">
              Parcelamento DB
            </label>
          </div>
        </div>
      </CModalBody>
      <CModalFooter className={"justify-content-center"}>
        <CButton
          color="success"
          className="mr-2"
          onClick={() => handleClose(true)}
        >
          Continuar
        </CButton>
        <CButton
          color="danger"
          className="mr-2"
          onClick={() => handleClose(false)}
        >
          Sair
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default TipoCalcRNI;
