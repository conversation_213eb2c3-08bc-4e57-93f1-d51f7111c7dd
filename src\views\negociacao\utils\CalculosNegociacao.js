const findMultasByFaixa = (parametroCalculo, atraso) => {
  // Achar parâmetros de calculo, para a faixa
  for (const key in parametroCalculo?.parametro?.faixasDeCalculo) {
    if (parametroCalculo.parametro.faixasDeCalculo.hasOwnProperty(key)) {
      const element = parametroCalculo.parametro.faixasDeCalculo[key];
      if (atraso >= element.dias_De_Calc && atraso <= element.dias_Ate_Calc) {
        return element;
      }
    }
  }
  return null;
};

const calculaAtrasoParcelasPorDataNegociaco = (parcelas, dataNegociacao) => {
  const retorno = parcelas.map((item) => {
    const dataParcela = new Date(item.dt_Vencimento);
    const dataNeg = new Date(dataNegociacao);
    const diffTime = Math.abs(dataNeg.getTime() - dataParcela.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) - 1;
    if (diffDays > 0) item.atraso = diffDays;

    if (dataParcela.getTime() > dataNeg.getTime()) item.atraso = 0;
    return item;
  });
  return retorno;
};

const calcularDescontoNoPrincipal = (parametroCalculo, p_parcela) => {
  if (!parametroCalculo) return p_parcela;
  const parcela = JSON.parse(JSON.stringify(p_parcela));
  const TipoPercDesconto = parametroCalculo.desc_Original ?? 0;
  const percDesconto =
    TipoPercDesconto > 0 ? parametroCalculo.desc_Original / 100 : 0;
  const tipoDeconto = parametroCalculo.desc_Sobre ?? "P";
  const obrigatorio = parametroCalculo.desc_Obrigatorio ?? false;
  const percVincenda = parametroCalculo.desconto_Original_Vincenda ?? 0;
  const valorOriginal = parcela.vl_Saldo;
  const valorAtualizado = parcela.vl_Saldo_Atualizado;
  const valorACalcular = tipoDeconto === "P" ? valorOriginal : valorAtualizado;
  const descMaximo = Math.round(valorACalcular * percDesconto * 100) / 100;
  const calculoDescontoAtualizado = obrigatorio
    ? valorACalcular - valorACalcular * percDesconto
    : valorOriginal;
  parcela.DescontoPrincipalMaximo = descMaximo;
  parcela.DescontoPrincipal = calculoDescontoAtualizado;
  return parcela;
};

const calcularJuros = (parametroCalculo, p_parcela) => {
  if (!parametroCalculo) return p_parcela;
  const parcela = JSON.parse(JSON.stringify(p_parcela));
  const usarTaxaContrato = parametroCalculo.juros_Usar_Tx_Contrato;
  const tipoJuros = parametroCalculo.juros_Md; //M: Mensal, D: Diario
  const tipoJurosSobre = parametroCalculo.juros_Sobre; //P: Principal, A: Atualizado
  const TipoPercJuros = usarTaxaContrato
    ? parcela.tx_Contrato ?? 0
    : parametroCalculo.perc_Juros ?? 0; //tipo do contrato ou do parametro
  const percJuros = TipoPercJuros / 100; //percutal a usar
  const diasIsencao = parametroCalculo.dias_Juros; //Calcular juros se o atraso for maior que esse parametro
  const percDesconto =
    parametroCalculo.perc_Desc_Juros > 0
      ? parametroCalculo.perc_Desc_Juros / 100
      : 0; //Percentual de desconto maximo permitido
  const tipoCalculo = parametroCalculo.tipo_Juros; //L: Linear, C: Composto
  const principal = parcela.vl_Saldo;
  const atualizado = parcela.vl_Saldo_Atualizado;
  const atraso = parcela.atraso;
  const valorACalcular = tipoJurosSobre === "P" ? principal : atualizado;

  let juros = 0;
  if (atraso > diasIsencao) {
    if (tipoJuros === "D")
      if (tipoCalculo === "L") juros = valorACalcular * percJuros * atraso;
      else
        juros =
          valorACalcular * Math.pow(1 + percJuros, atraso) - valorACalcular;

    if (tipoJuros === "M")
      if (tipoCalculo === "L")
        juros = valorACalcular * percJuros * (atraso / 30);
      else
        juros =
          valorACalcular * Math.pow(1 + percJuros, atraso / 30) -
          valorACalcular;
  }
  const descontoMaximo =
    Math.floor((Math.round(juros * 100) / 100) * percDesconto * 100) / 100;

  parcela.juros = Math.round(juros * 100) / 100;
  parcela.jurosMaxDesconto = descontoMaximo;
  parcela.percJuros = percJuros;
  return parcela;
};

function arredondarParaCima(numero) {
  var multiplicador = Math.pow(10, 2); // 2 casas decimais
  var numeroMultiplicado = numero * multiplicador;
  var parteDecimal = numeroMultiplicado - Math.floor(numeroMultiplicado);

  if (parteDecimal > 0.5) {
    // Arredonda para cima
    return Math.ceil(numeroMultiplicado) / multiplicador;
  } else {
    // Não é necessário arredondar
    return Math.floor(numeroMultiplicado) / multiplicador;
  }
}

const calcularMulta = (parametroCalculo, p_parcela) => {
  if (!parametroCalculo) return p_parcela;
  const parcela = JSON.parse(JSON.stringify(p_parcela));
  const usarTaxaContrato = parametroCalculo.multa_Usar_Tx_Contrato;
  const tipoPercMulta = usarTaxaContrato
    ? parcela.tx_Contrato ?? 0
    : parametroCalculo.perc_Multa ?? 0; //tipo do contrato ou do parametro
  const percMulta = tipoPercMulta > 0 ? tipoPercMulta / 100 : 0; //percutal a usar
  const diasIsencao = parametroCalculo.dias_Multa; //Calcular juros se o atraso for maior que esse parametro
  const percDesconto =
    parametroCalculo.perc_Desc_Multa > 0
      ? parametroCalculo.perc_Desc_Multa / 100
      : 0; //Percentual de desconto maximo permitido
  const tipoCalculo = parametroCalculo.multa_Sobre;
  const juros = parcela.juros ?? 0;
  const principal = parcela.vl_Saldo;
  const atualizado = parcela.vl_Saldo_Atualizado;
  const comissaoPerm = parcela.comissacaoPerm ?? 0;
  const atraso = parcela.atraso;
  let valorACalcular = principal;

  /*
      tipoCalculo
      0 - Principal
      1 - Atualizado
      2 - Principal + Juros
      3 - Atualizado + Juros
      4 - Principal + Juros + Comissao Permanencia
      5 - Atualizado + Juros + Comissao Permanencia
      6 - Principal + Comissao Permanencia
      7 - Atualizado + Comissao Permanencia
   */

  if (atraso > diasIsencao) {
    switch (tipoCalculo) {
      case "0":
        valorACalcular = principal;
        break;
      case "1":
        valorACalcular = atualizado;
        break;
      case "2":
        valorACalcular = principal + juros;
        break;
      case "3":
        valorACalcular = atualizado + juros;
        break;
      case "4":
        valorACalcular = principal + juros + comissaoPerm;
        break;
      case "5":
        valorACalcular = atualizado + juros + comissaoPerm;
        break;
      case "6":
        valorACalcular = principal + comissaoPerm;
        break;
      case "7":
        valorACalcular = atualizado + comissaoPerm;
        break;
      default:
        valorACalcular = 0;
        break;
    }
  } else if (atraso === 0) {
    valorACalcular = 0;
  }

  parcela.multa =
    Math.round(parseFloat(valorACalcular.toFixed(2)) * percMulta * 100) / 100;
  const descontoMaximo = parseFloat((parcela.multa * percDesconto).toFixed(2));
  //3,575 => 3,58 => 3,57
  parcela.multaMaxDesconto = descontoMaximo;
  parcela.percMulta = percMulta;

  return parcela;
};

const calcularHonorario = (parametroCalculo, p_parcela) => {
  if (!parametroCalculo) return p_parcela;
  const parcela = JSON.parse(JSON.stringify(p_parcela));
  const tipoPercHonorario = parametroCalculo.perc_Honor ?? 0;
  const percHonorario = tipoPercHonorario > 0 ? tipoPercHonorario / 100 : 0; //percutal a usar
  const diasIsencao = parametroCalculo.dias_Honor; //Calcular juros se o atraso for maior que esse parametro
  const percVincenda =
    parametroCalculo.perc_Honor_Vincenda > 0
      ? parametroCalculo.perc_Honor_Vincenda / 100
      : 0; //Percentual de desconto maximo permitido
  const percMinimoHonorario =
    parametroCalculo.perc_Desc_Hono > 0
      ? parametroCalculo.perc_Desc_Hono / 100
      : 0; //Percentual de desconto maximo permitido
  const juros = parcela.juros ?? 0;
  const multa = parcela.multa ?? 0;
  const principal = parcela.vl_Saldo;
  const atualizado = parcela.vl_Saldo_Atualizado;
  const comissaoPerm = parcela.comissacaoPerm ?? 0;
  const valorOriginal = parcela.vl_Original;
  const valorACalcular =
    Math.round((atualizado + juros + multa + comissaoPerm) * 100) / 100;
  const valorMinimoHonorario =
    Math.round(valorACalcular * percMinimoHonorario * 100) / 100;
  const valorHonorario =
    Math.round(valorACalcular * (percHonorario * 100)) / 100;
  let letPercHonorario = percHonorario;
  let letPercMinimoHonorario = percMinimoHonorario;
  if (parcela.atraso <= diasIsencao) {
    parcela.honorario = 0;
  } else if (parcela.atraso === 0) {
    letPercHonorario = 0;
    letPercMinimoHonorario = 0;
    parcela.honorario = 0;
  } else {
    parcela.honorario =
      valorHonorario < valorMinimoHonorario
        ? valorMinimoHonorario
        : valorHonorario;
  }

  parcela.perHonorario = letPercHonorario;
  parcela.perHonorarioMinimo = letPercMinimoHonorario;
  return parcela;
};

const calcularComissaoPermanencia = (parametroCalculo, p_parcela) => {
  if (!parametroCalculo) return p_parcela;
  const parcela = JSON.parse(JSON.stringify(p_parcela));
  const usarTaxaContrato = parametroCalculo.mora_Usar_Tx_Contrato;
  const tipoJuros = parametroCalculo.taxap_Md; //M: Mensal, D: Diario
  const TipoPercJuros = usarTaxaContrato
    ? parcela.tx_Contrato ?? 0
    : parametroCalculo.perc_Taxap ?? 0; //tipo do contrato ou do parametro
  const percJuros = TipoPercJuros / 100; //percutal a usar

  const diasIsencao = parametroCalculo.dias_Taxap; //Calcular juros se o atraso for maior que esse parametro
  const tipoCalculo = parametroCalculo.taxap_Sobre;
  const tipoCalculoL = parametroCalculo.tipo_Taxap; //L: Linear, C: Composto
  const percDesconto =
    parametroCalculo.perc_Desc_Taxap > 0
      ? parametroCalculo.perc_Desc_Taxap / 100
      : 0; //Percentual de desconto maximo permitido
  const juros = parcela.juros ?? 0;
  const principal = parcela.vl_Saldo;
  const atualizado = parcela.vl_Atualizado;
  const multa = parcela.multa ?? 0;
  const atraso = parcela.atraso;
  let valorACalcular = principal;
  let comissaoPerm = 0;

  if (atraso > diasIsencao) {
    /*
      tipoCalculo
      0 - Principal
      1 - Atualizado
      2 - Principal + Juros
      3 - Atualizado + Juros
      4 - Principal + Juros + Multa
      5 - Atualizado + Juros + Multa
      6 - Principal + Multa
      7 - Atualizado + Multa
   */
    switch (tipoCalculo) {
      case "0":
        valorACalcular = principal;
        break;
      case "1":
        valorACalcular = atualizado;
        break;
      case "2":
        valorACalcular = principal + juros;
        break;
      case "3":
        valorACalcular = atualizado + juros;
        break;
      case "4":
        valorACalcular = principal + juros + multa;
        break;
      case "5":
        valorACalcular = atualizado + juros + multa;
        break;
      case "6":
        valorACalcular = principal + multa;
        break;
      case "7":
        valorACalcular = atualizado + multa;
        break;
      default:
        valorACalcular = 0;
        break;
    }

    if (tipoJuros === "D")
      if (tipoCalculoL === "L")
        comissaoPerm = valorACalcular * percJuros * atraso;
      else
        comissaoPerm =
          valorACalcular * Math.pow(1 + percJuros, atraso) - valorACalcular;

    if (tipoJuros === "M")
      if (tipoCalculoL === "L")
        comissaoPerm = valorACalcular * percJuros * (atraso / 30);
      else
        comissaoPerm =
          valorACalcular * Math.pow(1 + percJuros, atraso / 30) -
          valorACalcular;
  }
  const descontoMaximo = Math.round(comissaoPerm * percDesconto * 100) / 100;
  parcela.comissacaoPerm = Math.round(comissaoPerm * 100) / 100;
  parcela.comissacaoPermMaxDesconto = descontoMaximo;
  parcela.percComissaoPerm = Math.round(percJuros * 100) / 100;
  return parcela;
};

const detalhesCalculoOriginal = (
  parametroCalculo,
  p_parcela,
  baseCalculo,
  dataPagamento
) => {
  const parcela = JSON.parse(JSON.stringify(p_parcela));
  const detalhesOriginal = {
    real: parcela.vl_Saldo,
    correcao: 0,
    atualizado: parcela.vl_Saldo,
    juros: parcela.juros,
    multa: parcela.multa,
    comissacaoPerm: parcela.comissacaoPerm ?? 0,
    subtotal:
      Math.round(
        (parcela.vl_Saldo +
          parcela.juros +
          parcela.multa +
          parcela.comissacaoPerm) *
          100
      ) / 100,
    honorarios: parcela.honorario,
    custa: parcela.vl_Custa,
    despesas: 0,
    notificacao: 0,
    tarifa: 0,
    iof: 0,
    baseCalculo: baseCalculo,
    dataPagameno: dataPagamento,
    total:
      Math.round(
        (parcela.vl_Saldo +
          parcela.juros +
          parcela.multa +
          parcela.comissacaoPerm +
          parcela.honorario +
          parcela.vl_Custa) *
          100
      ) / 100,
  };
  return detalhesOriginal;
};

const detalhesCalculoDesconto = (
  parametroCalculo,
  p_parcela,
  baseCalculo,
  dataPagamento
) => {
  const parcela = JSON.parse(JSON.stringify(p_parcela));
  const principalCalc = parcela.vl_Saldo - parcela.DescontoPrincipalMaximo;
  const jurosCalc = parcela.juros - parcela.jurosMaxDesconto;
  const multaCalc = parcela.multa - parcela.multaMaxDesconto;
  const comissacaoPermCalc =
    parcela.comissacaoPerm - parcela.comissacaoPermMaxDesconto;
  const subtotal = jurosCalc + multaCalc + comissacaoPermCalc + principalCalc;
  const honorioCalc =
    Math.round(
      (!parametroCalculo ||
      parcela.atraso === 0 ||
      parametroCalculo.dias_Honor >= parcela.atraso
        ? 0
        : parametroCalculo.perc_Honor / 100) *
        subtotal *
        100
    ) / 100;
  const totalDesconto =
    Math.round((subtotal + honorioCalc + parcela.vl_Custa) * 100) / 100;
  const detalhesDescontoMaximo = {
    real: principalCalc,
    correcao: 0,
    atualizado: principalCalc,
    percDescontoJuros: Math.round(parcela.percJuros * 100 * 100) / 100,
    percDescontoMulta: Math.round(parcela.percMulta * 100 * 100) / 100,
    juros: jurosCalc,
    multa: multaCalc,
    comissacaoPerm: comissacaoPermCalc,
    subtotal: subtotal,
    honorarios: honorioCalc,
    custa: parcela.vl_Custa,
    despesas: 0,
    notificacao: 0,
    tarifa: 0,
    iof: 0,
    baseCalculo: baseCalculo,
    dataPagameno: dataPagamento,
    total: totalDesconto,
  };
  return detalhesDescontoMaximo;
};

/* Parcelas = ParcelasContratoAtivo selecionadas para negociacao */
/* perDescontoRegua = Ao percentual de movimento da Regua 1 a 0 */
const calculaValoresParcelas = (
  parametroCalculo,
  parcelas,
  valorNegociadoCalculo,
  dataPagamento
) => {
  let parcelasCopia = JSON.parse(JSON.stringify(parcelas));
  const selecionadas = parcelasCopia.filter((item) => item.parcelaSelecionada);
  //const selecionadas = parcelasCopia;
  if (selecionadas.length === 0) return parcelasCopia;

  const ordernaAtrasoDesc = [...selecionadas].sort(
    (a, b) => b.atraso - a.atraso
  );
  const paramMaiorAtraso = findMultasByFaixa(
    parametroCalculo,
    ordernaAtrasoDesc[0].atraso
  );

  const ordernaAtraso = [...selecionadas].sort((a, b) => a.atraso - b.atraso);
  const paramMenorAtraso = findMultasByFaixa(
    parametroCalculo,
    ordernaAtraso[0].atraso
  );
  const parametrosUsar =
    parametroCalculo?.parametro?.base_Venc === "A"
      ? paramMaiorAtraso
      : paramMenorAtraso;

  const contratos = parcelasCopia.map((x) => x.numero_Contrato);
  const contratosUnicos = contratos.filter(
    (item, index) => contratos.indexOf(item) === index
  );

  const custasContratos = contratosUnicos.map((item) => {
    const parcCusta = parcelasCopia.find(
      (x) => x.numero_Contrato === item && x.vl_Custa > 0
    );
    return {
      contrato: item,
      vlCusta: parcCusta?.vl_Custa ?? 0,
      parc: parcCusta,
    };
  });

  parcelasCopia
    .filter((x) => !x.parcelaSelecionada)
    .forEach((x) => {
      x.vl_Custa = 0;
    });

  parcelasCopia.forEach((x) => {
    x.changeCusta = false;
  });

  custasContratos.forEach((x) => {
    if (x.parc === null || x.parc === undefined) return;
    const parc = parcelasCopia.find(
      (y) => y.numero_Contrato === x.contrato && y.parcelaSelecionada
    );
    if (parc?.nr_Parcela !== x.parc?.nr_Parcela) {
      if (parc === null || parc === undefined) {
        x.parc.vl_Custa = x.vlCusta;
      } else {
        parc.vl_Custa = x.vlCusta;
        parc.changeCusta = true;
      }
    }
  });

  const retorno = parcelasCopia.map((item) => {
    const paramPorVencimento = findMultasByFaixa(parametroCalculo, item.atraso);
    let paramHonorario =
      parametroCalculo?.parametro?.base_Venc_Honor === "V"
        ? paramPorVencimento
        : parametrosUsar;
    paramHonorario =
      parametroCalculo?.parametro?.base_Venc_Honor === "A"
        ? paramMaiorAtraso
        : paramHonorario;
    paramHonorario =
      parametroCalculo?.parametro?.base_Venc_Honor === "N"
        ? paramMenorAtraso
        : paramHonorario;
    const paramCalculo =
      parametroCalculo?.parametro?.base_Venc === "V"
        ? paramPorVencimento
        : parametrosUsar;

    if (
      item?.faixaDeCalculo !== null &&
      item?.faixaDeHonorario !== null &&
      (item?.faixaDeCalculo?.dias_Ate_Calc !== paramCalculo?.dias_Ate_Calc ||
        item?.faixaDeHonorario?.dias_Ate_Calc !== paramHonorario?.dias_Ate_Calc)
    ) {
      item.faixaNova = true;
    } else {
      item.faixaNova = false;
    }

    item.faixaDeCalculo = paramCalculo;
    item.faixaDeHonorario = paramHonorario;

    item.vl_Saldo = Math.round(item.vl_Saldo * 100) / 100;
    item.vl_Saldo_Atualizado = item.vl_Saldo;
    item = calcularDescontoNoPrincipal(paramCalculo, item);
    item = calcularJuros(paramCalculo, item);
    item = calcularComissaoPermanencia(paramCalculo, item);
    item = calcularMulta(paramCalculo, item);
    item = calcularHonorario(paramHonorario, item);
    item.vl_Saldo_Atualizado =
      Math.round(
        (item.vl_Saldo +
          item.juros +
          item.multa +
          item.comissacaoPerm +
          item.honorario +
          item.vl_Custa) *
          100
      ) / 100;
    item.vl_Original_atualizado = item.vl_Saldo_Atualizado;
    item.valorNegociado = item.vl_Saldo_Atualizado;
    item.detalhesCalculo = {
      detalhesOriginal: {},
      detalhesDescontoMaximo: {},
      detalhesNegociacao: {},
    };
    item.percDesconto = 0;
    item.desconto = 0;
    const principalCalc =
      Math.round((item.vl_Saldo - item.DescontoPrincipalMaximo) * 100) / 100;
    const jurosCalc = item.juros - item.jurosMaxDesconto;
    const multaCalc = item.multa - item.multaMaxDesconto;
    const comissacaoPermCalc =
      item.comissacaoPerm - item.comissacaoPermMaxDesconto;
    const subtotal = principalCalc + jurosCalc + multaCalc + comissacaoPermCalc;
    const honorioCalc =
      Math.round(
        (!paramHonorario ||
        item.atraso === 0 ||
        paramHonorario?.dias_Honor >= item.atraso
          ? 0
          : paramHonorario.perc_Honor / 100) *
          subtotal *
          100
      ) / 100;
    const totalDesconto =
      Math.round((subtotal + honorioCalc + item.vl_Custa) * 100) / 100;
    item.vl_Desc_Max = totalDesconto;
    item.detalhesCalculo.detalhesOriginal = detalhesCalculoOriginal(
      paramCalculo,
      item,
      selecionadas[0].dt_Vencimento,
      dataPagamento
    );
    item.detalhesCalculo.detalhesDescontoMaximo = detalhesCalculoDesconto(
      paramHonorario,
      item,
      selecionadas[0].dt_Vencimento,
      dataPagamento
    );
    item.detalhesCalculo.detalhesNegociacao =
      item?.detalhesCalculo.detalhesOriginal;
    item.detalhesCalculo.detalhesNegociacao.perHonorario =
      paramCalculo?.dias_Honor >= item.atraso
        ? 0
        : Math.round(item.perHonorario * 100 * 100) / 100;
    return item;
  });
  return retorno;
};

const calculaPerncetuaisDesconto = (
  parametroCalculo,
  parcelas,
  valorNegociadoCalculo
) => {
  const parcelasCopia = JSON.parse(JSON.stringify(parcelas));
  const selecionadas = parcelas.filter((item) => item.parcelaSelecionada);
  if (selecionadas.length === 0 || valorNegociadoCalculo <= 0)
    return parcelasCopia;
  const valorOriginal = selecionadas.reduce(
    (total, item) =>
      Math.round((total + item.vl_Original_atualizado) * 100) / 100,
    0
  );
  const valorDescMaximo = selecionadas.reduce(
    (total, item) => Math.round((total + item.vl_Desc_Max) * 100) / 100,
    0
  );
  const descontoMaximo =
    Math.round((valorOriginal - valorDescMaximo) * 100) / 100;
  const desconto =
    Math.round((valorOriginal - valorNegociadoCalculo) * 100) / 100;
  const perc =
    desconto === 0 || descontoMaximo === 0
      ? 0
      : Math.round((desconto / descontoMaximo) * 100 * 100) / 100 / 100;
  const retorno = parcelasCopia.map((item) => {
    if (item.parcelaSelecionada) {
      const detalhes = { ...item?.detalhesCalculo };
      const juroDesc = item.jurosMaxDesconto * perc;
      const multaDesc = item.multaMaxDesconto * perc;
      const comissacaoDesc = item.comissacaoPermMaxDesconto * perc ?? 0;
      const totalDesc = Math.round((juroDesc + multaDesc) * 100) / 100;
      detalhes.detalhesNegociacao.juros =
        detalhes.detalhesNegociacao.juros - juroDesc;
      detalhes.detalhesNegociacao.multa =
        detalhes.detalhesNegociacao.multa - multaDesc;
      detalhes.detalhesNegociacao.comissacaoPerm =
        detalhes.detalhesNegociacao.comissacaoPerm - comissacaoDesc;
      detalhes.detalhesNegociacao.percDescontoJuros =
        Math.round((juroDesc / item.jurosMaxDesconto) * 100 * 100) / 100;
      detalhes.detalhesNegociacao.percDescontoMulta =
        Math.round((multaDesc / item.multaMaxDesconto) * 100 * 100) / 100;
      detalhes.detalhesNegociacao.percDescontoComissaoPerm =
        Math.round(
          (comissacaoDesc / item.comissacaoPermDescMaximo) * 100 * 100
        ) / 100;
      const subtotalneg = detalhes.detalhesNegociacao.subtotal - totalDesc;
      detalhes.detalhesNegociacao.subtotal = subtotalneg;
      const honorarioNegociado =
        Math.round(
          subtotalneg * (detalhes.detalhesNegociacao.perHonorario / 100) * 100
        ) / 100;
      detalhes.detalhesNegociacao.honorarios = honorarioNegociado;
      const totalNegociacao =
        Math.round((subtotalneg + honorarioNegociado + item.vl_Custa) * 100) /
        100;
      detalhes.detalhesNegociacao.total = totalNegociacao;
      const jurosMulta = Math.round((item.juros + item.multa) * 100) / 100;
      item.percDesconto =
        jurosMulta === 0 || totalDesc === 0
          ? 0
          : Math.round((totalDesc / jurosMulta) * 100 * 100) / 100;
      //item.valorNegociado = totalNegociacao;
      item.desconto = totalDesc;
    }
    return item;
  });

  return retorno;
};

const calculaValoresDetelhes = (dados) => {
  const realOriginal = dados ? dados.reduce((total, item) => Math.round((total + item.vlOriginal) * 100) / 100, 0) : 0;
  const atualizadoOriginal = dados ? dados.reduce((total, item) => Math.round((total + item.vlSaldo) * 100) / 100, 0) : 0;
  const correcaoOriginal = atualizadoOriginal - realOriginal; 
  const jurosOriginal = dados ? dados.reduce((total, item) => Math.round((total + item.vlJurosOriginal) * 100) / 100, 0) : 0;
  const multaOriginal = dados ? dados.reduce((total, item) => Math.round((total + item.vlMultaOriginal) * 100) / 100, 0) : 0;
  const comissaoOriginal = dados ? dados.reduce((total, item) => Math.round((total + item.vlComPermanenciaOriginal) * 100) / 100, 0) : 0;
  const subTotalOriginal = dados ? atualizadoOriginal + jurosOriginal + multaOriginal + correcaoOriginal : 0;
  const honorariosOriginal = dados ? dados.reduce((total, item) => Math.round((total + item.vlHoOriginal) * 100) / 100, 0) : 0;
  const custasOriginal = dados ? dados.reduce((total, item) => Math.round((total + item.vlDespesasOriginal) * 100) / 100, 0) : 0;
  const notificacaoOriginal = dados ? dados.reduce((total, item) => Math.round((total + item.vlNotificacaoOriginal) * 100) / 100, 0) : 0;
  const tarifaOriginal = dados ? dados.reduce((total, item) => Math.round((total + item.vlTarifaOriginal) * 100) / 100, 0) : 0;
  const iofOriginal = dados ? 0 : 0;
  const totalOriginal = dados ? subTotalOriginal + honorariosOriginal + custasOriginal + notificacaoOriginal + tarifaOriginal + iofOriginal : 0;


  const correcaoDescMax = dados ? 0: 0;
  const jurosDescMax = dados ? dados.reduce((total, item) => Math.round((total + item.vlJurosMaxDesc) * 100) / 100, 0) : 0;
  const multaDescMax = dados ? dados.reduce((total, item) => Math.round((total + item.vlMultaMaxDesc) * 100) / 100, 0) : 0;
  const comissaoDescMax = dados ? dados.reduce((total, item) => Math.round((total + item.vlComPermanenciaMaxDesc) * 100) / 100, 0) : 0;
  const honorariosDescMax = dados ? dados.reduce((total, item) => Math.round((total + item.vlHoMaxDesc) * 100) / 100, 0) : 0;
  const custasDescMax = dados ? dados.reduce((total, item) => Math.round((total + item.vlDespesasMaxDesc) * 100) / 100, 0) : 0;
  const notificacaoDescMax = dados ? dados.reduce((total, item) => Math.round((total + item.vlNotificacaoMaxDesc) * 100) / 100, 0) : 0;
  const tarifaDescMax = dados ? dados.reduce((total, item) => Math.round((total + item.vlTarifaMaxDesc) * 100) / 100, 0) : 0;
  const iofDescMax = dados ? 0 : 0;
  const totalDescMax = dados ? dados.reduce((total, item) => Math.round((total + item.vlAtualizadoDescontoMax) * 100) / 100, 0) : 0;
  const subTotalDescMax = dados ? totalDescMax - honorariosDescMax - custasDescMax - notificacaoDescMax - tarifaDescMax - iofDescMax : 0;
  const realDescMax = dados ? subTotalDescMax - jurosDescMax - multaDescMax - comissaoDescMax : 0;
  const atualizadoDescMax = dados ? realDescMax + correcaoDescMax : 0;
  
  const correcaoNegociacao = dados ? 0 : 0;
  const honorariosNegociacao = dados ? dados.reduce((total, item) => Math.round((total + item.vlHoNegociado) * 100) / 100, 0) : 0;
  const jurosNegociacao = dados ? dados.reduce((total, item) => Math.round((total + item.vlJurosNegociado) * 100) / 100, 0) : 0;
  const multaNegociacao = dados ? dados.reduce((total, item) => Math.round((total + item.vlMultaNegociado) * 100) / 100, 0) : 0;
  const comissaoNegociacao = dados ? dados.reduce((total, item) => Math.round((total + item.vlComPermanenciaNegociado) * 100) / 100, 0) : 0;
  const custasNegociacao = dados ? dados.reduce((total, item) => Math.round((total + item.vlDespesasNegociado) * 100) / 100, 0) : 0;
  const notificacaoNegociacao = dados ? dados.reduce((total, item) => Math.round((total + item.vlNotificacaoNegociado) * 100) / 100, 0) : 0;
  const tarifaNegociacao = dados ? dados.reduce((total, item) => Math.round((total + item.vlTarifaNegociado) * 100) / 100, 0) : 0;
  const iofNegociacao = dados ? 0 : 0;
  const totalNegociacao = dados ? dados.reduce((total, item) => Math.round((total + item.vlAtualizado) * 100) / 100, 0) : 0;
  const subTotalNegociacao = dados ? totalNegociacao - honorariosNegociacao - custasNegociacao - notificacaoNegociacao - tarifaNegociacao - iofNegociacao : 0;
  const realNegociacao = dados ? subTotalNegociacao - jurosNegociacao - multaNegociacao - comissaoNegociacao : 0;
  const atualizadoNegociacao = dados ? realNegociacao + correcaoNegociacao : 0;

  const percDescMax = realDescMax === 0 || realOriginal === 0 ? 0 : ((realDescMax / realOriginal) - 1) * 100 * -1 < 0 ? 0 : ((realDescMax / realOriginal) - 1) * 100 * -1;
  const percNegociacao = realNegociacao === 0 || realOriginal === 0 ? 0 : ((realNegociacao / realOriginal) - 1) * 100 * -1 < 0 ? 0 : ((realNegociacao / realOriginal) - 1) * 100 * -1;

  const percCorrecaoDescMax = correcaoDescMax === 0 || correcaoOriginal === 0 ? 0 : ((correcaoDescMax / correcaoOriginal) - 1) * 100 * -1 < 0 ? 0 : ((correcaoDescMax / correcaoOriginal) - 1) * 100 * -1;
  const percCorrecaoNegociacao = correcaoNegociacao === 0 || correcaoOriginal === 0 ? 0 : ((correcaoNegociacao / correcaoOriginal) - 1) * 100 * -1 < 0 ? 0 : ((correcaoNegociacao / correcaoOriginal) - 1) * 100 * -1;

  const percAtualizadoDescMax = atualizadoDescMax === 0 || atualizadoOriginal === 0 ? 0 : ((atualizadoDescMax / atualizadoOriginal) - 1) * 100 * -1 < 0 ? 0 : ((atualizadoDescMax / atualizadoOriginal) - 1) * 100 * -1;
  const percAtualizadoNegociacao = atualizadoNegociacao === 0 || atualizadoOriginal === 0 ? 0 : ((atualizadoNegociacao / atualizadoOriginal) - 1) * 100 * -1 < 0 ? 0 : ((atualizadoNegociacao / atualizadoOriginal) - 1) * 100 * -1;

  const percJurosDescMax = jurosDescMax === 0 || jurosOriginal === 0 ? 0 : ((jurosDescMax / jurosOriginal) - 1) * 100 * -1 < 0 ? 0 : ((jurosDescMax / jurosOriginal) - 1) * 100 * -1;
  const percJurosNegociacao = jurosNegociacao === 0 || jurosOriginal === 0 ? 0 : ((jurosNegociacao / jurosOriginal) - 1) * 100 * -1 < 0 ? 0 : ((jurosNegociacao / jurosOriginal) - 1) * 100 * -1;

  const percMultaDescMax = multaDescMax === 0 || multaOriginal === 0 ? 0 : ((multaDescMax / multaOriginal) - 1) * 100 * -1 < 0 ? 0 : ((multaDescMax / multaOriginal) - 1) * 100 * -1;
  const percMultaNegociacao = multaNegociacao === 0 || multaOriginal === 0 ? 0 : ((multaNegociacao / multaOriginal) - 1) * 100 * -1 < 0 ? 0 : ((multaNegociacao / multaOriginal) - 1) * 100 * -1;

  const percComissaoDescMax = comissaoDescMax === 0 || comissaoOriginal === 0 ? 0 : ((comissaoDescMax / comissaoOriginal) - 1) * 100 * -1 < 0 ? 0 : ((comissaoDescMax / comissaoOriginal) - 1) * 100 * -1;
  const percComissaoNegociacao = comissaoNegociacao === 0 || comissaoOriginal === 0 ? 0 : (((comissaoNegociacao / comissaoOriginal) - 1) * 100 * -1 < 0 ? 0 : ((comissaoNegociacao / comissaoOriginal) - 1) * 100 * - 100) -1;

  const percSubTotalDescMax = subTotalDescMax === 0 || subTotalOriginal === 0 ? 0 : ((subTotalDescMax / subTotalOriginal) - 1) * 100 * -1 < 0 ? 0 : ((subTotalDescMax / subTotalOriginal) - 1) * 100 * -1;
  const percSubTotalNegociacao = subTotalNegociacao === 0 || subTotalOriginal === 0 ? 0 : ((subTotalNegociacao / subTotalOriginal) - 1) * 100 * -1 < 0 ? 0 : ((subTotalNegociacao / subTotalOriginal) - 1) * 100 * -1;

  
  const percHonorariosDescMax = honorariosDescMax === 0 || honorariosOriginal === 0 ? 0 : ((honorariosDescMax / honorariosOriginal) - 1) * 100 * -1 < 0 ? 0 : ((honorariosDescMax / honorariosOriginal) - 1) * 100 * -1;
  const percHonorariosNegociacao = honorariosNegociacao === 0 || subTotalNegociacao === 0 ? 0 : parseFloat(((honorariosNegociacao*100)/subTotalNegociacao).toFixed(2));

  const percCustasDescMax = custasDescMax === 0 || custasOriginal === 0 ? 0 : ((custasDescMax / custasOriginal) - 1) * 100 * -1 < 0 ? 0 : ((custasDescMax / custasOriginal) - 1) * 100 * -1;
  const percCustasNegociacao = custasNegociacao === 0 || custasOriginal === 0 ? 0 : ((custasNegociacao / custasOriginal) - 1) * 100 * -1 < 0 ? 0 : ((custasNegociacao / custasOriginal) - 1) * 100 * -1;

  const percNotificacaoDescMax = notificacaoDescMax === 0 || notificacaoOriginal === 0 ? 0 : ((notificacaoDescMax / notificacaoOriginal) - 1) * 100 * -1 < 0 ? 0 : ((notificacaoDescMax / notificacaoOriginal) - 1) * 100 * -1;
  const percNotificacaoNegociacao = notificacaoNegociacao === 0 || notificacaoOriginal === 0 ? 0 : ((notificacaoNegociacao / notificacaoOriginal) - 1) * 100 * -1 < 0 ? 0 : ((notificacaoNegociacao / notificacaoOriginal) - 1) * 100 * -1;

  const percTarifaDescMax = tarifaDescMax === 0 || tarifaOriginal === 0 ? 0 : ((tarifaDescMax / tarifaOriginal) - 1) * 100 * -1 < 0 ? 0 : ((tarifaDescMax / tarifaOriginal) - 1) * 100 * -1;
  const percTarifaNegociacao = tarifaNegociacao === 0 || tarifaOriginal === 0 ? 0 : ((tarifaNegociacao / tarifaOriginal) - 1) * 100 * -1 < 0 ? 0 : ((tarifaNegociacao / tarifaOriginal) - 1) * 100 * -1;

  const percIofDescMax = iofDescMax === 0 || iofOriginal === 0 ? 0 : ((iofDescMax / iofOriginal) - 1) * 100 * -1 < 0 ? 0 : ((iofDescMax / iofOriginal) - 1) * 100 * -1;
  const percIofNegociacao = iofNegociacao === 0 || iofOriginal === 0 ? 0 : ((iofNegociacao / iofOriginal) - 1) * 100 * -1 < 0 ? 0 : ((iofNegociacao / iofOriginal) - 1) * 100 * -1;

  const percTotalDescMax = totalDescMax === 0 || totalOriginal === 0 ? 0 : ((totalDescMax / totalOriginal) - 1) * 100 * -1 < 0 ? 0 : ((totalDescMax / totalOriginal) - 1) * 100 * -1;
  const percTotalNegociacao = totalNegociacao === 0 || totalOriginal === 0 ? 0 : ((totalNegociacao / totalOriginal) - 1) * 100 * -1 < 0 ? 0 : ((totalNegociacao / totalOriginal) - 1) * 100 * -1;

  return {
    realOriginal,
    atualizadoOriginal,
    correcaoOriginal,
    jurosOriginal,
    multaOriginal,
    comissaoOriginal,
    subTotalOriginal,
    honorariosOriginal,
    custasOriginal,
    notificacaoOriginal,
    tarifaOriginal,
    iofOriginal,
    totalOriginal,
    realDescMax,
    atualizadoDescMax,
    correcaoDescMax,
    jurosDescMax,
    multaDescMax,
    comissaoDescMax,
    subTotalDescMax,
    honorariosDescMax,
    custasDescMax,
    notificacaoDescMax,
    tarifaDescMax,
    iofDescMax,
    totalDescMax,
    realNegociacao,
    atualizadoNegociacao,
    correcaoNegociacao,
    jurosNegociacao,
    multaNegociacao,
    comissaoNegociacao,
    subTotalNegociacao,
    honorariosNegociacao,
    custasNegociacao,
    notificacaoNegociacao,
    tarifaNegociacao,
    iofNegociacao,
    totalNegociacao,
    percDescMax,
    percNegociacao,
    percCorrecaoDescMax,
    percCorrecaoNegociacao,
    percAtualizadoDescMax,
    percAtualizadoNegociacao,
    percJurosDescMax,
    percJurosNegociacao,
    percMultaDescMax,
    percMultaNegociacao,
    percComissaoDescMax,
    percComissaoNegociacao,
    percSubTotalDescMax,
    percSubTotalNegociacao,
    percHonorariosDescMax,
    percHonorariosNegociacao,
    percCustasDescMax,
    percCustasNegociacao,
    percNotificacaoDescMax,
    percNotificacaoNegociacao,
    percTarifaDescMax,
    percTarifaNegociacao,
    percIofDescMax,
    percIofNegociacao,
    percTotalDescMax,
    percTotalNegociacao,
  }
}

export {
  calculaAtrasoParcelasPorDataNegociaco,
  calculaValoresParcelas,
  calculaPerncetuaisDesconto,
  calculaValoresDetelhes
};
