import React from "react";
import { formatCurrency } from 'src/reusable/helpers';

const TableContratos = ({ dataTable, contratoIndex, onContratoClick}) => {  
  return (
    <div className="table-responsive">
      <table className='table'>
        <thead>
          <tr>
              <th>Contrato</th>
              <th>Valor</th>
              <th>PMT</th>
              <th><PERSON>ra</th>
              <th>Multa</th>
              <th>IOF</th>
              <th>Saldo Curva</th>
          </tr>
        </thead>
        <tbody>
          {dataTable.map((item, index) => {
            return (
              <tr key={index}
              onClick={() => onContratoClick(item)}
              className={contratoIndex != null && contratoIndex.numero_Contrato === item.numero_Contrato ? 'bg-success' : ''}
              style={{cursor: 'pointer'}}
              >
                <td>{item.numero_Contrato}</td>
                <td>{formatCurrency(item.dadosBBC.valor)}</td>
                <td>{formatCurrency(item.dadosBBC.pmt)}</td>
                <td>{formatCurrency(item.dadosBBC.mora)}</td>
                <td>{formatCurrency(item.dadosBBC.multa)}</td>
                <td>{formatCurrency(item.dadosBBC.iof)}</td>
                <td>{formatCurrency(item.dadosBBC.saldoCurva)}</td>
              </tr>
            )
          })}
        </tbody>
      </table>
    </div>
  );
};

export default TableContratos;
