import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";

export const updateConnection = async ({ crm, userProfile }) => {  
  const payload = {
    activeConnection: crm.datacobNumber,
    userId: userProfile.id,
  };
  try {
    const data = await postActiveConnection(payload, "postUserConnection");
    if (data) {
      const newActiveConnection = {
        ...userProfile,
        activeConnection: crm.datacobNumber,
      };
      localStorage.setItem("user", JSON.stringify(newActiveConnection));
    }
  } catch (err) {
    console.error("Error updating connection:", err);
    throw new Error("Failed to update connection.");
  }
};

const postActiveConnection = async (payload, endpoint) => {
  try {
    const response = await POST_DATA(getURI(endpoint), payload, true);
    return response;
  } catch (error) {
    console.error("Error in postActiveConnection:", error);
    throw error;
  }
};
