import React from 'react';
import {
  <PERSON>ard,
  <PERSON>ardBody,
  C<PERSON>abel,
} from "@coreui/react";
import { formatCurrency } from 'src/reusable/helpers';

const labelStyle = {
  width: "200px", // Defina a largura desejada aqui
};

const CardCalcular = ({ contratoNegociar }) => {
  return (
    <div>
      <CCard style={{ backgroundColor: "#153860", color: "white" }}>
        <CCardBody>
          <div>
            <CLabel className="mr-2">
              Resumo do Contrato {contratoNegociar != null ? contratoNegociar.dadosSafra.contrato : ""}
            </CLabel>
          </div>
          <div>
            <CLabel style={{ color: "lightblue," }} className="mr-2" >
              <div style={labelStyle} >Valor Principal:  </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrPrincipal) : formatCurrency(0.00)}
          </div>
          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Devedor:  </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrDevedor) : formatCurrency(0.00)}
          </div>
          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Principal Descapitalizado:  </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrPrincDesc) : formatCurrency(0.00)}
          </div>
          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Juros CDI:  </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrJurosCDI) : formatCurrency(0.00)}
          </div>
          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Juros:  </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrJuros) : formatCurrency(0.00)}
          </div>
          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Multa:  </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrMulta) : formatCurrency(0.00)}
          </div>
          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Tarifa: </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrTarifa) : formatCurrency(0.00)}
          </div>

          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Custas: </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrCustas) : formatCurrency(0.00)}
          </div>

          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Alvará: </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrAlvara) : formatCurrency(0.00)}
          </div>

          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Juros TX: </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrJurTxContr) : formatCurrency(0.00)}
          </div>

          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Dívida: </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrDevedorEncargos) : formatCurrency(0.00)}
          </div>

          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Honorários: </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.vlrHonorario) : formatCurrency(0.00)}
          </div>

          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Entrada: </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.parcelasOriginais[0].vlrPaOrig) : formatCurrency(0.00)}
          </div>

          <div>
            <CLabel style={{ color: "lightblue" }} className="mr-2" >
              <div style={labelStyle} >Valor Entrada + Honorários: </div>
            </CLabel>
            {contratoNegociar != null ? formatCurrency(contratoNegociar.dadosSafra.parcelasOriginais[0].vlrPaOrig + contratoNegociar.dadosSafra.vlrHonorario) : formatCurrency(0.00)}
          </div>

        </CCardBody>
      </CCard>
    </div>
  );
};

export default CardCalcular;
