import React, { useState, useEffect } from "react";
import {
  CDataTable,
  CSelect,
  CButton,
  CCardBody,
  CCol,
  CRow,
  <PERSON>ardFooter,
  CBadge,
} from "@coreui/react";

import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { formatThousands, formatDate } from "src/reusable/helpers";
import CardLoading from "src/reusable/CardLoading";
import Select from "react-select";
import TableSelectItens from "src/reusable/TableSelectItens";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";

import { useAuth } from "src/auth/AuthContext";
import ValoresDevolverModal from "./ValoresDevolverModal/ValoresDevolverModal";
import DebitoEmContaModal from "./DebitoEmContaModal/DebitoEmContaModal";
import DetalheContratoModal from "./DetalheContratoModal/DetalheContratoModal";

const DetalhesContrato = ({ selected }) => {
  const financiadoData = JSON.parse(localStorage.getItem("financiadoData"))
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const dadosNewcon = localStorage.getItem("newcon")
    ? JSON.parse(localStorage.getItem("newcon"))
    : [];

  const [tableData, setTableData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFase, setSelectedFase] = useState("");
  const [selectedTaxa, setSelectedTaxa] = useState("");
  const [faseOptions, setFaseOptions] = useState([]);
  const [taxaOptions, setTaxaOptions] = useState([]);
  const [showModalDetalhe, setShowModalDetalhe] = useState(false);
  const [dadosModalDetalhe, setDadosModalDetalhe] = useState(null);
  // const [detalhesContrato, setDetalhesContrato] = useState([]);

  // Handle status filter change
  const handleFaseChange = (selectedOption) => {
    setSelectedFase(selectedOption.value);
  };

  // Handle number filter change
  const handleTaxaChange = (selectedOption) => {
    setSelectedTaxa(selectedOption.value);
  };

  const updateView = () => {
    const payload = {
      IdFinanciado: financiadoData.id_Financiado,
      idGrupo: financiadoData.id_Grupo,
    };
    setIsLoading(true);
    getDetalhesContrato(payload, "getdetcontratosdatacob")
      .then((data) => {
        if (data) {
          setTableData(data);
          setFiltersOptions(data);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const getDetalhesContrato = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const setFiltersOptions = (data) => {
    const uniqueFases = [...new Set(tableData.map((item) => item.fase))];
    const fases = [
      { value: "", label: "Fase" }, // "All" option
      ...uniqueFases.map((fase) => ({
        value: fase,
        label: fase,
      })),
    ];
    setFaseOptions(fases);

    const uniqueTaxas = [...new Set(tableData.map((item) => item.tx_Contrato))];
    const taxas = [
      { value: "", label: "Taxa" }, // "All" option
      ...uniqueTaxas.map((tx_Contrato) => ({
        value: tx_Contrato,
        label: tx_Contrato,
      })),
    ];
    setTaxaOptions(taxas);
  };

  const tableColumns = [
    { key: "fase", label: "Fase", formatter: (item) => renderFaseBadge(item) },
    {
      key: "nmSituacaoCobranca",
      label: "Status",
      formatter: (item) => renderCell(item),
    },
    {
      key: "numero_Contrato",
      label: "Contrato",
      formatter: (item) => renderCell(item),
    },
    {
      key: "vl_Contr",
      label:
        financiadoData?.id_Grupo === 4 ? "Saldo Contábil" : "Valor contrato", //Checando se é Safra
      style: { whiteSpace: "nowrap" },
      formatter: (item) => renderCellValue(item),
    },
    // {
    //   key: "saldo_Cont",
    //   label: "Saldo Contábil",
    //   style: { whiteSpace: "nowrap" },
    //   formatterByObject: (item) => renderCellSaldoContábil(item.vl_Contr),
    // },
    {
      key: "tx_Contrato",
      label: "Taxa contrato",
      style: { whiteSpace: "nowrap" },
      formatter: (item) => renderCellValue(item),
    },
    { key: "agencia", label: "Agência", formatter: (item) => renderCell(item) },
    {
      key: "padrao",
      label: "Vl Parcela Original BTG",
      formatter: (item) =>
        item?.nome === "Vl Parcela Original BTG" ? item?.valor : "---",
    },
    { key: "nome_Loja", label: "Loja", formatter: (item) => renderCell(item) },
    {
      key: "plataforma",
      label: "Plataforma",
      formatter: (item) => renderCell(item),
    },
    {
      key: "vl_Risco",
      label: "Vl. Risco",
      style: { whiteSpace: "nowrap" },
      formatter: (item) => renderCellValue(item),
    },
    { key: "atraso", label: "Atraso", formatter: (item) => renderCell(item) },
    { key: "regua", label: "Regua", formatter: (item) => renderCell(item) },
    {
      key: "dt_adesao",
      label: "Data Adesão",
      style: { whiteSpace: "nowrap" },
      formatter: (item) => renderCellDate(item),
    },
    {
      key: "data_Atraso",
      label: "Data de encerramento",
      style: { whiteSpace: "nowrap" },
      formatter: (item) => renderCellDate(item),
    },
  ];

  const renderCell = (item) => {
    return <div style={{ whiteSpace: "nowrap" }}>{item ?? "---"}</div>;
  };
  const renderFaseBadge = (item) => {
    return (
      <div style={{ whiteSpace: "nowrap" }}>
        {item ? <CBadge color="info">{item}</CBadge> : "---"}
      </div>
    );
  };

  const renderCellValue = (item) => {
    return (
      <div style={{ whiteSpace: "nowrap" }}>
        {item ? formatThousands(item) : "0,00"}
      </div>
    );
  };
  const renderCellSaldoContábil = (item) => {
    return (
      <div style={{ whiteSpace: "nowrap" }}>
        {item ? formatThousands(item) : "0,00"}
      </div>
    );
  };

  const renderCellDate = (item) => {
    return (
      <div style={{ whiteSpace: "nowrap" }}>
        {item ? formatDate(item) : "---"}
      </div>
    );
  };

  useEffect(() => {
    if (tableData) {
      const filteredData = tableData.filter((item) => {
        const matchesFase = !selectedFase || item.fase === selectedFase;
        const matchesTaxa =
          !selectedTaxa ||
          item.tx_Contrato === selectedTaxa ||
          (selectedTaxa === "" && item.tx_Contrato);

        // return matchesFase;
        return matchesFase && matchesTaxa;
      });
      setTableData(filteredData);
    }
  }, [selectedFase, selectedTaxa]);

  useEffect(() => {
    if (financiadoData && selected === true) {
      updateView();
      if (cnscCotas && cnscCotas.idCota > 0) {
        setHabilitarConsulta(true);
      } else setHabilitarConsulta(false);
    }
  }, [selected]);

  /* Botões da Consultas e Buscas */
  const { checkPermission, inforPermissions } = useAuth();

  const permissaoTelaPrincipalConsultaseBuscasCasultaValoresADevolver = {
    modulo: "Tela Principal",
    submodulo: "Consultas e Buscas - Consulta de Valores à Devolver",
  };

  const permissaoTelaPrincipalConsultaseBuscasCDebitoEmConta = {
    modulo: "Tela Principal",
    submodulo: "Consultas e Buscas - Debito em Conta",
  };

  const [showValoresDevolverModal, setShowValoresDevolverModal] =
    useState(false);
  const [showDebitoEmContaModal, setShowDebitoEmContaModal] = useState(false);
  const [habilitarConsulta, setHabilitarConsulta] = useState(false);

  const cnscCotas =
    localStorage.getItem("cnscCotas") &&
      localStorage.getItem("cnscCotas") !== "undefined"
      ? JSON.parse(localStorage.getItem("cnscCotas")) ?? []
      : [];

  function modalContratoPadrao(item) {
    setShowModalDetalhe(true);
    setDadosModalDetalhe(item.padrao);
  }

  return (
    <>
      {isLoading ? (
        <CardLoading />
      ) : (
        <>
          {tableData ? (
            <>
              <CCardBody>
                <CRow className="mb-2">
                  <CCol md="4" className="d-flex px-0">
                    <div className="flex-grow-1">
                      <Select
                        className="mr-2"
                        placeholder={"Fase"}
                        options={faseOptions}
                        value={faseOptions.find(
                          (option) => option.value === selectedFase
                        )}
                        onChange={handleFaseChange}
                      />
                    </div>
                    <div className="flex-grow-1">
                      <Select
                        options={taxaOptions}
                        placeholder={"Taxa"}
                        value={taxaOptions.find(
                          (option) => option.value === selectedTaxa
                        )}
                        onChange={handleTaxaChange}
                      />
                    </div>
                  </CCol>
                  <CCol
                    className="px-0"
                    style={{
                      textAlign: "end",
                    }}
                  >
                    <CButton
                      className="mr-2"
                      color="info"
                      onClick={() => setShowValoresDevolverModal(true)}
                      title={
                        inforPermissions(
                          permissaoTelaPrincipalConsultaseBuscasCasultaValoresADevolver
                        ).view
                      }
                      disabled={
                        !checkPermission(
                          permissaoTelaPrincipalConsultaseBuscasCasultaValoresADevolver.modulo,
                          "View",
                          permissaoTelaPrincipalConsultaseBuscasCasultaValoresADevolver.submodulo
                        ) || !habilitarConsulta
                      }
                    >
                      Consulta de Valores a Devolver
                    </CButton>
                    <CButton
                      color="info"
                      onClick={() => setShowDebitoEmContaModal(true)}
                      title={
                        inforPermissions(
                          permissaoTelaPrincipalConsultaseBuscasCDebitoEmConta
                        ).view
                      }
                      disabled={
                        !checkPermission(
                          permissaoTelaPrincipalConsultaseBuscasCDebitoEmConta.modulo,
                          "View",
                          permissaoTelaPrincipalConsultaseBuscasCDebitoEmConta.submodulo
                        ) || !habilitarConsulta
                      }
                    >
                      Débito Em Conta
                    </CButton>
                  </CCol>
                </CRow>
                <CRow>
                  <TableSelectItens
                    data={tableData}
                    columns={tableColumns}
                    onSelectionChange={(_) => {}}
                    defaultSelectedKeys={[]}
                    selectable={false}
                    heightParam="250px"
                    onDoubleClick={modalContratoPadrao}
                  />
                </CRow>
              </CCardBody>
              <ValoresDevolverModal
                isOpen={showValoresDevolverModal}
                onClose={() => setShowValoresDevolverModal(false)}
                idCota={cnscCotas.idCota}
              />
              <DebitoEmContaModal
                isOpen={showDebitoEmContaModal}
                onClose={() => setShowDebitoEmContaModal(false)}
                idCota={cnscCotas.idCota}
              />
              {showModalDetalhe && (
                <DetalheContratoModal
                  isOpen={showModalDetalhe}
                  onClose={() => setShowModalDetalhe(false)}
                  dados={dadosModalDetalhe}
                />
              )}
            </>
          ) : (
            <NaoHaDadosTables />
          )}
        </>
      )}
    </>
  );
};

export default DetalhesContrato;
