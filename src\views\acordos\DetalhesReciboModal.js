import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CCard,
  CButton,
  CDataTable,
} from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";

const DetalhesReciboModal = ({ isOpen, onClose, dados }) => {
  const [detalhesCalculos, setDetalhesCalculos] = useState(null);
  const [tableDataL, setTableDataL] = useState(null);
  const [tableDataR, setTableDataR] = useState(null);

  const handleClose = () => {
    onClose("DetalhesBoleto");
  };

  useEffect(() => {
    if (isOpen) {
      setTableDataL([
        {
          label: "Número",
          value: dados.nr_Recibo,
        },
        {
          label: "Sobre",
          value: dados.recibo_Sobre,
        },
        {
          label: "Bol Obrigatório",
          value: dados.recibo_Boleto_Obrigatorio,
        },
        {
          label: "Somente Baixa",
          value: dados.forma_Pagamento_Boleto,
        },
        {
          label: "Modelo",
          value: dados.id_Deposito_Identificado,
        },
        {
          label: "Data de Emissão",
          value: dados.dt_Emiss ? formatDate(dados.dt_Emiss) : "",
        },
        {
          label: "Data Cálculo",
          value: dados.dt_Calculo ? formatDate(dados.dt_Calculo) : "",
        },
        {
          label: "Data Prestação",
          value: dados.dt_Prestc ? formatDate(dados.dt_Prestc) : "",
        },
        {
          label: "Data Pagamento",
          value: dados.dt_Pagto ? formatDate(dados.dt_Pagto) : "",
        },
        {
          label: "Empresa",
          value: dados.razao,
        },
        {
          label: "Forma Pagto Bol",
          value: dados.forma_Pagamento_Boleto,
        },
        {
          label: "Dep Identif",
          value: dados.id_Deposito_Identificado,
        },
      ]);

      setTableDataR([
        {
          label: "Tipo Pagto",
          value: dados.tipo_Pagto,
        },
        {
          label: "Vl Pago",
          value: dados.vl_Pago,
        },
        {
          label: "Tx Bancária",
          value: dados.vl_Tx_Banc,
        },
        {
          label: "Status",
          value: dados.status,
        },
        {
          label: "Fase",
          value: dados.fase,
        },
        {
          label: "Boleto",
          value: dados.nr_Boleto,
        },
        {
          label: "Cheque",
          value: dados.id_Cheque,
        },
        {
          label: "Cheque Terceiro",
          value: dados.cheque_Terceiro,
        },
        {
          label: "Origem Bol",
          value: dados.origem_Boleto,
        },
        {
          label: "Motivo Canc",
          value: dados.descricao_Motivo_Cancel,
        },
        {
          label: "Observação",
          value: dados.observacao,
        },
      ]);
    }
  }, [isOpen]);

  const renderStatus = (value) => {
    switch (value) {
      case "P":
        return "Pago";
      case "D":
        return "Devolvido";
      case "A":
        return "Acordo";
      case "C":
        return "Cancelado";
      case "Ab":
        return "Aberto";
      default:
        break;
    }
  };

  const renderBoletoSobre = (value) => {
    switch (value) {
      case "P":
        return "Parcela";
      case "D":
        return "Devolvido";
      case "A":
        return "Acordo";
      case "Ab":
        return "Aberto";
      default:
        break;
    }
  };

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="xl">
      <CModalBody>
        {tableDataL && (
          <>
            <CRow className="mb-2">
              <CCol style={{ textAlign: "center" }}>Detalhes do Recibo</CCol>
            </CRow>
            <CRow>
              <CCol className="pr-1">
                <CCard>
                  <table className="table table-hover calculo">
                    <tbody>
                      {tableDataL.map((row) => (
                        <tr key={row.label}>
                          <td>{row.label}</td>
                          <td>
                            {row.label === "Status"
                              ? renderStatus(row.value)
                              : row.value}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </CCard>
              </CCol>
              <CCol className="pl-1">
                <CCard>
                  <table className="table table-hover calculo">
                    <tbody>
                      {tableDataR.map((row) => (
                        <tr key={row.label}>
                          <td>{row.label}</td>
                          <td>
                            {row.label === "Boleto Sobre"
                              ? renderBoletoSobre(row.value)
                              : row.value}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </CCard>
              </CCol>
            </CRow>
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={handleClose}>
          Sair
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default DetalhesReciboModal;
