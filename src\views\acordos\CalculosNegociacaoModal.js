import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CButton,
  CDataTable,
} from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";
import { GET_DATA } from "src/api";

const CalculosNegociacaoModal = ({ isOpen, onClose, dados }) => {
  const [detalhesCalculos, setDetalhesCalculos] = useState(null);
  const [tableData, setTableData] = useState(null);

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (isOpen && dados) {
      // setDetalhesCalculos(dados);
      const upperTableData = [
        {
          label: "(Valores)",
          Original: "(R$)",
          Desconto_Perc: "(%)",
          Desconto_Real: "(R$)",
          Negociacao_Perc: "(%)",
          Negociacao_Real: "(R$)",
        },
        {
          label: "Real",
          Original: formatThousands(dados.real.original),
          Desconto_Perc: formatThousands(dados.real.descontoPerc),
          Desconto_Real: formatThousands(dados.real.desconto),
          Negociacao_Perc: formatThousands(dados.real.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.real.negociacao),
        },
        {
          label: "Correção",
          Original: formatThousands(dados.correcao.original),
          Desconto_Perc: formatThousands(dados.correcao.descontoPerc),
          Desconto_Real: formatThousands(dados.correcao.desconto),
          Negociacao_Perc: formatThousands(dados.correcao.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.correcao.negociacao),
        },
        {
          label: "Atualizado",
          Original: formatThousands(dados.atualizado.original),
          Desconto_Perc: formatThousands(dados.atualizado.descontoPerc),
          Desconto_Real: formatThousands(dados.atualizado.desconto),
          Negociacao_Perc: formatThousands(dados.atualizado.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.atualizado.negociacao),
        },
        {
          label: "Juros",
          Original: formatThousands(dados.juros.original),
          Desconto_Perc: formatThousands(dados.juros.descontoPerc),
          Desconto_Real: formatThousands(dados.juros.desconto),
          Negociacao_Perc: formatThousands(dados.juros.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.juros.negociacao),
        },
        {
          label: "Multa",
          Original: formatThousands(dados.multa.original),
          Desconto_Perc: formatThousands(dados.multa.descontoPerc),
          Desconto_Real: formatThousands(dados.multa.desconto),
          Negociacao_Perc: formatThousands(dados.multa.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.multa.negociacao),
        },
        {
          label: "Comissão de Perm.",
          Original: formatThousands(dados.comissao.original),
          Desconto_Perc: formatThousands(dados.comissao.descontoPerc),
          Desconto_Real: formatThousands(dados.comissao.desconto),
          Negociacao_Perc: formatThousands(dados.comissao.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.comissao.negociacao),
        },
        {
          label: "Subtotal",
          Original: formatThousands(dados.subTotal.original),
          Desconto_Perc: formatThousands(dados.subTotal.descontoPerc),
          Desconto_Real: formatThousands(dados.subTotal.desconto),
          Negociacao_Perc: formatThousands(dados.subTotal.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.subTotal.negociacao),
        },
      ];
      const descAutorizado =
        dados.descontoAutorizado !== 0
          ? {
              label: "Desconto autorizado",
              Original: "--",
              Desconto_Perc: "--",
              Desconto_Real: "--",
              Negociacao_Perc: "--",
              Negociacao_Real: formatThousands(dados.descontoAutorizado),
            }
          : {};

      const bottomTableData = [
        {
          label: "Honorários",
          Original: formatThousands(dados.honorario.original),
          Desconto_Perc: formatThousands(dados.honorario.descontoPerc),
          Desconto_Real: formatThousands(dados.honorario.desconto),
          Negociacao_Perc: formatThousands(dados.honorario.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.honorario.negociacao),
        },
        {
          label: "Despesas",
          Original: formatThousands(dados.despesa.original),
          Desconto_Perc: formatThousands(dados.despesa.descontoPerc),
          Desconto_Real: formatThousands(dados.despesa.desconto),
          Negociacao_Perc: formatThousands(dados.despesa.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.despesa.negociacao),
        },
        {
          label: "Notificação",
          Original: formatThousands(dados.notificacao.original),
          Desconto_Perc: formatThousands(dados.notificacao.descontoPerc),
          Desconto_Real: formatThousands(dados.notificacao.desconto),
          Negociacao_Perc: formatThousands(dados.notificacao.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.notificacao.negociacao),
        },
        {
          label: "Tarifa",
          Original: formatThousands(dados.tarifa.original),
          Desconto_Perc: formatThousands(dados.tarifa.descontoPerc),
          Desconto_Real: formatThousands(dados.tarifa.desconto),
          Negociacao_Perc: formatThousands(dados.tarifa.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.tarifa.negociacao),
        },
        {
          label: "IOF",
          Original: formatThousands(dados.iof.original),
          Desconto_Perc: formatThousands(dados.iof.descontoPerc),
          Desconto_Real: formatThousands(dados.iof.desconto),
          Negociacao_Perc: formatThousands(dados.iof.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.iof.negociacao),
        },
        {
          label: "Total",
          Original: formatThousands(dados.total.original),
          Desconto_Perc: formatThousands(dados.total.descontoPerc),
          Desconto_Real: formatThousands(dados.total.desconto),
          Negociacao_Perc: formatThousands(dados.total.negociacaoPerc),
          Negociacao_Real: formatThousands(dados.total.negociacao),
        },
      ];
      setTableData([...upperTableData, descAutorizado, ...bottomTableData]);
    }
  }, [isOpen]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="lg">
      <CModalHeader closeButton>Detalhes do Calculo</CModalHeader>
      <CModalBody>
        {tableData && (
          <table className="table table-hover calculo">
            <thead>
              <tr>
                <th></th>
                <th>Cálculo Original</th>
                <th colSpan={2}>Desconto Máximo</th>
                <th colSpan={2}>Negociação</th>
              </tr>
            </thead>
            <tbody>
              {tableData.map((row) => (
                <tr
                  key={row.label}
                  className={
                    row.label === "(Valores)"
                      ? "valores-row"
                      : row.label === "Total"
                      ? "total-row"
                      : row.label === "Subtotal"
                      ? "subtotal-row"
                      : row.label === "Desconto autorizado"
                      ? "desconto-autorizado-row"
                      : "detalhes-row"
                  }
                >
                  <td>{row.label}</td>
                  <td className="column-detalhes-1">{row.Original}</td>
                  <td className="column-detalhes-2">{row.Desconto_Perc}</td>
                  <td className="column-detalhes-1">{row.Desconto_Real}</td>
                  <td className="column-detalhes-2">{row.Negociacao_Perc}</td>
                  <td className="column-detalhes-1">{row.Negociacao_Real}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default CalculosNegociacaoModal;
