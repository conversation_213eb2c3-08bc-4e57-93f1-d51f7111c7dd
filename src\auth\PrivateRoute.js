import React, { useEffect, useState } from "react";
import { Route } from "react-router-dom";
import { useAuth } from "src/auth/AuthContext";

const PrivateRoute = ({ component: Component, ...rest }) => {
  const { checkToken } = useAuth();
  const [authorized, setAuthorized] = useState(false);

  useEffect(() => {
    let isMounted = true;

    const verify = async () => {
      const valid = await checkToken();
      if (isMounted) { 
        setAuthorized(valid);
      }
    };
    verify();
    return () => { isMounted = false; };
  }, [checkToken]);

  return authorized && (
    <Route
      {...rest}
      render={(props) =>
          <Component {...props} />
      }
    />
  );
};

export default PrivateRoute;