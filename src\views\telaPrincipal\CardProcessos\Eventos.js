import React, { useState, useEffect } from "react";
import { CCardBody } from "@coreui/react";
import { formatDate } from "src/reusable/helpers";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";
import LoadingComponent from "src/reusable/Loading";
import TableSelectItens from "src/reusable/TableSelectItens";

const Eventos = ({ pasta, selected, idProcesso }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [truncatedItems, setTruncatedItems] = useState({});
  const [first, setFirst] = useState(true);

  const payload = {
    IdProcess: idProcesso || null,
  };

  const updateView = (_) => {
    if (payload.IdProcess !== null) {
      setIsLoading(true);
      setFirst(false);
      getEventos(payload, "geteventosprojuris")
        .then((data) => {
          if (data) {
            // data.forEach((item) => {
            //   item.insertDate = formatDate(item.insertDate);
            //   item.dtComprov = formatDate(item.dtComprov);
            //   item.dtDespesa = formatDate(item.dtDespesa);
            //   item.StatusAutomacao = item.processado
            //     ? "Processado"
            //     : "Não Processado";
            //   item.StatusProcesso = item.sucesso ? "Inserido" : "Falha";
            //   item.MsgAutomacao = item.msgErro ? item.msgErro : "";
            // });
            setTableData(data);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  };

  const getEventos = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const tableColumns = [
    {
      key: "evento",
      label: "Evento",
      formatterByObject: (item) => renderEvento(item),
    },
    { key: "proximo", label: "Próximo" },
    { key: "realizado", label: "Realizado" },
    { key: "atual", label: "Atual" },
    { key: "prazo" },
    { key: "documento" },
    {
      key: "data_Cadastro",
      label: "Data Cadastro",
      formatter: (value) => formatDate(value),
    },
    { key: "data", formatter: (value) => formatDate(value) },
    { key: "hora", defaultValue: "---" },
    {
      key: "data_Da_Realizacao",
      label: "Data da Realização",
      formatter: (value) => formatDate(value),
    },
    {
      key: "advogados_Resposanveis",
      className: "nowrap-cell",
    },
    {
      key: "fase",
      className: "nowrap-cell",
    },
    {
      key: "cadastrado_Por",
      className: "nowrap-cell",
    },
  ];

  // const renderBadge = (status) => {
  //   switch (status) {
  //     case true:
  //       return (
  //         <td>
  //           <CBadge
  //             shape="pill"
  //             color="success"
  //             className="justify-content-center"
  //           >
  //             <i className="cil-check-alt" />
  //           </CBadge>
  //         </td>
  //       );
  //     case false:
  //       return <td></td>;
  //     default:
  //       break;
  //   }
  // };

  const toggleTextExpansion = (itemId) => {
    setTruncatedItems((prevState) => ({
      ...prevState,
      [itemId]: !prevState[itemId],
    }));
  };

  useEffect(() => {
    if (selected === true && pasta && first) {
      updateView();
    }
  }, [selected]);

  useEffect(() => {
    setFirst(true);
  }, [pasta]);

  const renderEvento = (item) => {
    return (
      <div
        className={
          truncatedItems[item.created_at] ? "expand-text" : "truncate-text"
        }
        onClick={() => toggleTextExpansion(item.created_at)}
        style={{ width: "650px" }}
      >
        {truncatedItems[item.created_at]
          ? item.evento
          : item.evento.length > 200
          ? item.evento.slice(0, 200) + " ..."
          : item.evento}
      </div>
    );
  };

  return (
    <CCardBody>
      {isLoading ? (
        <LoadingComponent
          text={
            "Aguarde! Buscando dados no Projuris! Isso pode demorar um pouco."
          }
        />
      ) : (
        <div>
          <TableSelectItens
            data={tableData}
            columns={tableColumns}
            onSelectionChange={(_) => {}}
            defaultSelectedKeys={[]}
            selectable={false}
            heightParam="290px"
          />
        </div>
      )}
    </CCardBody>
  );
};

export default Eventos;
