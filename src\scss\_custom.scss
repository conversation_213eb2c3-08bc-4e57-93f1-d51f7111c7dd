// Here you can add other styles

$tealColor: #27b7c7;
$darkColor: #153860;
$darker: #1f2937;
$grayColor: rgb(150, 150, 150);

.custom-header-1 {
  color: #ffffff;
}

.custom-sidebar {
  // color:#000000 ;
  background-color: whitesmoke;
  .nav-title {
    //CSidebarNavTitle
    color: $grayColor;
    font-weight: normal;
  }
  .nav-link {
    //CSidebarNavItem
    font-size: 14px;
    font-weight: bold;
  }
  .nav-group-items {
    //CSidebarNavDropdown
    .nav-link {
      //CSidebarNavItem
      font-size: 12px; // Set the desired font size for the menu items inside the group
      color: $grayColor;
    }
  }
}

// .sidebar-toggler {
//   top: 0; /* Change the top value to your desired position */
// }

.subtle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  background-color: rgba(255, 255, 255, 0.1);
  border: 0px;
  border-radius: 5px;
  // border-color: #ccc;
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.25);
    color: rgba(255, 255, 255, 0.8);
  }
}

// .destaque-red {
//   display: inline-flex;
//   align-items: center;
//   justify-content: center;
//   padding: 5px;
//   background-color: rgba(255, 90, 90, 0.5);
//   border: 1px;
//   border-radius: 5px;
//   border-color: rgba(255, 255, 255, 0.8);
//   color: rgba(255, 255, 255);
//   text-decoration: none;
//   transition: background-color 0.3s ease;

//   &:hover {
//     background-color: rgba(255, 50, 50, 0.7);
//     color: rgba(255, 255, 255);
//   }
// }

.green-tint {
  color: rgba(0, 255, 89, 0.6);
  background-color: rgba(0, 255, 89, 0.25);
}

.red-tint {
  color: rgba(255, 90, 90, 0.6);
  background-color: rgba(255, 90, 90, 0.25);
}

.orange-tint {
  color: rgb(190 64 0);
  background-color: rgb(255 105 0 / 38%);
}

.gray-tint-icon {
  color: #6b7280;
  background-color: #d1d5db;
  padding: 2px;
  border-radius: 3px;
}

.separator {
  position: relative;
  // top: 50%;
  right: 0;
  // transform: translateY(50%);
  // height: 80%;
  border-right: 2px solid rgba(70, 170, 170, 0.6);
  border-radius: 2px;
}

.subtitle {
  color: $grayColor;
  // font-size: small;
  font-weight: normal;
}

.sticky-header {
  thead th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa; /* set your desired background color */
    z-index: 1;
  }
}

.custom-table {
  .empty-cell {
    border: none;
  }

  .ctable-head th {
    border-radius: 0.25rem;
  }

  .ctable-data-cell {
    border-radius: 0.25rem;
  }
  input[type="checkbox"] {
    /* Hide the native checkbox */
    // display: none;
    appearance: none;

    /* Custom checkbox styles */
    // &::before {
    // content: "";
    // display: inline-block;
    width: 16px;
    height: 16px;
    border: 1px solid #007a7e;
    border-radius: 3px;
    background-color: #fff;
    position: relative;
    // }

    /* Checked checkbox styles */
    &:checked {
      background-color: rgb(100, 211, 211);
      border-color: $tealColor;
    }

    /* Checkmark symbol */
    // &:checked::before {
    //   content: "";
    //   position: absolute;
    //   top: 4px;
    //   left: 4px;
    //   width: 8px;
    //   height: 8px;
    //   border-radius: 50%;
    //   background-color: #fff;
    // }
  }
}

.fixed-select {
  min-width: 200px; /* Set the minimum width here */
}

.tabs-table {
  th {
    white-space: nowrap;
    text-align: center;
    padding: 6px 10px;
  }
  td {
    white-space: nowrap;
  }
}

.tabs-cards {
  // padding: 0%;
  margin-bottom: 1rem;
}

.custom-modal {
  h5 {
    color: $tealColor;
    font-size: medium;
  }
  label {
    font-weight: bold;
    font-size: small;
  }
  select {
    font-size: small;
    padding: 4px;
  }
  textarea {
    font-size: small;
    padding: 2px 8px;
  }
  button {
    font-size: small;
    font-weight: 600;
  }
  // background-color: whitesmoke;
}

.custom-modal-2 {
  h5 {
    color: $tealColor;
    font-size: medium;
  }
  h3 {
    color: $darker;
  }
  label {
    color: $darker;
    font-weight: bold;
    font-size: small;
  }
  select {
    font-size: small;
    padding: 4px;
  }
  textarea {
    font-size: small;
    padding: 2px 8px;
  }
  button {
    font-size: small;
    font-weight: 600;
  }
  .react-datepicker-wrapper input {
    height: 39px !important;
  }
  // background-color: whitesmoke;
}

.saldo-devedor {
  h5 {
    color: $tealColor;
    font-size: medium;
  }
  label {
    color: #6b7280;
    font-size: medium;
  }
  span {
    color: #153860;
    font-size: large;
    font-weight: 600;
  }
}

.saldo-p {
  font-weight: bold;
  // font-size: small;
}

.expanded-table {
  tr {
    border: 0;
  }
  td {
    text-align: center;
    padding: 4px 0;
    user-select: none;
    // border-left: 1px solid $grayColor;
    // border-right: 1px solid $grayColor;
    // border-top: 0;
    // border-bottom: 0;
  }
  &:hover {
    background-color: #ebebeb;
  }
}

.pressed {
  background-color: #d5d5d5; /* Change to the desired darker background color */
  font-weight: bold;
  &:hover {
    background-color: #d5d5d5;
  }
}

.double-clickable-table {
  tr {
    border: 0;
  }
  td {
    padding: 12px;
  }
  .td div {
    padding: 0%;
  }
  &:hover {
    background-color: #ebebeb;
    cursor: default;
  }
}

.custom-nav {
  // border-bottom: 2px solid lightgray;
  .nav-link {
    color: $tealColor;
    font-weight: bold;
    border-bottom: solid $tealColor;
  }
}

.nonactive-tab {
  .nav-link {
    color: $darkColor;
    font-weight: normal;
    border-bottom: solid lightgray;
  }
}

.cotasatraso-tab {
  .nav-link {
    color: crimson;
    font-weight: normal;
    border-bottom: solid lightgray;
    // border-bottom: solid crimson ;
  }
  // background-color: lightgray;
}

.nowrap-cell {
  white-space: nowrap; //prevents line breaks within the cell
  overflow: hidden; //hides any content that exceeds the cell width
  text-overflow: ellipsis; //adds an ellipsis (...) to indicate when the content is truncated.
}

.selected-row {
  background-color: lightgray;
  border-radius: 4px;
}

.selected-message {
  background-color: lightgray;
}

.button-link {
  background: none;
  border: none;
  color: #007bff;
  text-decoration: underline;
  cursor: pointer;
}

.button-link:hover {
  text-decoration: none;
}

.calculo {
  th {
    font-weight: bold;
    font-style: italic;
    border: 0;
  }
  td {
    padding-top: 1px;
    padding-bottom: 1px;
    border: 0;
  }
}

.valores-row {
  color: lightblue;
}
.total-row {
  font-size: 16px;
  font-weight: bolder;
  color: red;
}
.subtotal-row {
  font-size: 16px;
  font-weight: bolder;
  color: blue;
}
.desconto-autorizado-row {
  font-size: 13px;
  color: red;
}
.detalhes-row {
  font-size: 13px;
}

.column-detalhes-1 {
  text-align: end;
  padding-right: 12px;
  border-right: gray solid 2px !important;
}
.column-detalhes-2 {
  text-align: center;
  padding-right: 12px;
}

.fade-border {
  position: relative;
  // width: 200px;
  // height: 100px;
  // background: linear-gradient(to right, rgba(0, 0, 0, 0), black, rgba(0, 0, 0, 0));
}

.fade-border:before,
.fade-border:after {
  content: "";
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0),
    black,
    rgba(0, 0, 0, 0)
  );
}

.fade-border:before {
  left: 0;
}

.fade-border:after {
  right: 0;
}

.table-container {
  overflow: auto;
  max-height: 250px; /* Defina a altura máxima desejada */
}

.table.tabs-table {
  width: 100%;
  border-collapse: collapse;
}

.table.tabs-table thead th {
  background-color: #f8f9fa;
  position: sticky;
  top: -3px;
}

.table.tabs-table tbody td {
  // background-color: #ffffff;
}

.colored-card-green {
  background-color: #bbf7d0;
  height: 180px;
  color: #16653499;
}
.colored-card-red {
  background-color: #fecaca;
  color: #7f1d1d99;
  height: 180px;
}
.colored-card-yellow {
  background-color: #fde68a;
  color: #92400e99;
  height: 180px;
}
.colored-card-gray {
  background-color: #f3f4f6;
  color: #6b7280;
  height: 180px;
}

.title-pg {
  color: #166534;
  font-weight: bold;
}
.title-py {
  color: #92400e;
  font-weight: bold;
}
.title-pr {
  color: #7f1d1d;
  font-weight: bold;
}
.title-pw {
  color: #030712;
  font-weight: bold;
}

.truncate-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expand-text {
  min-width: 400px; /* Set the maximum width */
  white-space: normal;
  overflow: visible;
  text-overflow: unset;
}

.scrollable-tabs {
  overflow-x: auto;
  white-space: nowrap;
}

.tab-item {
  padding: 10px 20px;
  margin-right: 10px;
  border: 1px solid #ccc;
}

// .dropdown-menu {
//   display: none;
//   position: absolute;
//   top: 100%;
//   // left: 0;
//   background-color: #fff;
//   border: 1px solid #ccc;
//   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
//   z-index: 100; /* Ensure it's above other elements */
// }

// .dropdown-menu.active {
//   display: block;
// }
.info-panel {
  position: absolute;
  // top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 100;
  color: $darker;
  margin-top: 18px;
  min-width: 210px;
  font-size: small;

  p {
    padding: 0 1em;
    margin: 0;
  }
}
@media (max-width: 878px) {
  .info-panel {
    top: 50%; /* Adjust the top position for smaller screens */
    left: -100%;
    transform: translate(-50%, 0); /* Center the panel */
    margin-top: 18px;
    min-width: 210px;
    width: fit-content;
  }
}

.info-table {
  margin: 0;
  width: max-content;
  th {
    font-weight: bold;
    font-style: italic;
    border: 0;
  }
  //tr ?
  td {
    padding-top: 1px;
    padding-bottom: 1px;
    border: 0;
  }
}

.info-status {
  background-color: rgb(255, 212, 84);
}

.information-text {
  display: flex;
  justify-content: end;
  align-items: center;
  font-size: smaller;
  font-weight: bold;
  color: gray;
}

.close {
  font-size: x-large;
}

.btn-aba {
  border-radius: 0.5rem;
  border: 1px solid $tealColor;
  color: $tealColor;
  margin-top: 4px;
  width: 90%;
}

.detalhes-contrato-tr:hover {
  background-color: #e1e1e1;
}

.header-icon-lg {
  font-size: 20px;
  width: 100% !important;
  height: 100% !important;
}

.header-icon-red {
  color: rgb(255, 255, 255);
  background-color: rgb(255, 90, 90, 0.9);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  padding-right: 5px;
  padding-left: 5px;
  border: 0px;
  border-radius: 5px;
  text-decoration: none;
  transition: background-color 0.3s ease;

  &:hover {
    color: rgba(255, 255, 255);
    background-color: rgb(255, 90, 90);
  }
}

.header-icon-orange {
  color: rgb(255, 255, 255);
  background-color: rgb(255, 105, 0, 0.9);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  padding-right: 5px;
  padding-left: 5px;
  border: 0px;
  border-radius: 5px;
  text-decoration: none;
  transition: background-color 0.3s ease;

  &:hover {
    color: rgba(255, 255, 255);
    background-color: rgb(255, 105, 0);
  }
}

.header-icon-blue {
  color: rgb(255, 255, 255);
  background-color: rgb(0, 124, 255, 0.9);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  padding-right: 5px;
  padding-left: 5px;
  border: 0px;
  border-radius: 5px;
  text-decoration: none;
  transition: background-color 0.3s ease;

  &:hover {
    color: rgba(255, 255, 255);
    background-color: rgb(0, 124, 255);
  }
}

.inline-flex-gap-right {
  display: flex;
  gap: 1rem;
  text-wrap: nowrap;
  justify-content: flex-end;
}

.inline-flex-gap-right input[type="text"] {
  width: 50%;
}

.inline-flex-gap-right label {
  margin-top: 7px;
}

.inline-flex-gap-left {
  display: flex;
  gap: 1rem;
  text-wrap: nowrap;
  justify-content: flex-start;
}

.inline-flex-gap-left input[type="text"] {
  width: 50%;
}

.inline-flex-gap-left label {
  margin-top: 7px;
}

.inline-flex-gap-left input[type="checkbox"] {
  margin-top: 7px;
}

.custas-alert {
  width: 100%;
  height: 50%;
  margin: 5px;
  padding: 10px;
  border-radius: 3px;
  box-shadow: 1px 1px 1px 1px #d0d0d0;
}

.btn-error-udpate {
  display: inline-flex;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  justify-content: center;
  align-items: center;
  border-radius: 0.375rem;
  border-width: 1px;
  height: 2.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  white-space: nowrap;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

  &:hover {
    color: #fff;
    background-color: #000;
  }
}

.remove-focus-color {
  outline: none;
}

.pointer {
  cursor: pointer;
}

.pointer label {
  cursor: pointer;
}

.btn-custom-outline-green {
  border: 1px solid green;
  color: green;
  background-color: transparent;
  border-radius: 4px;
  transition: 0.3s;
}

.btn-custom-outline-green:hover {
  background-color: green;
  color: white;
}

.modal-xxl .modal-dialog {
  max-width: 1500px;
}

@media (max-width: 1200px) {
  .modal-xxl .modal-dialog {
    max-width: 1000px;
  }
}
@media (max-width: 992px) {
  .modal-xxl .modal-dialog {
    max-width: 800px;
  }
}
@media (max-width: 768px) {
  .modal-xxl .modal-dialog {
    max-width: 600px;
  }
}

/* Estilos para a modal de preview do documento */
.preview-modal .modal-dialog {
  max-width: 90vw;
  width: 90vw;
  height: 90vh;
  margin: 5vh auto;
}

.preview-modal .modal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-modal .modal-body {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #f8f9fa;
}

.preview-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #f8f9fa;
}

.preview-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #f8f9fa;
  flex-direction: column;
}

.preview-error-icon {
  font-size: 3rem;
  color: #f86c6b;
  margin-bottom: 1rem;
}

.preview-error-message {
  color: #721c24;
  font-size: 1.1rem;
  text-align: center;
  margin-bottom: 1rem;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
  .preview-modal .modal-dialog {
    max-width: 95vw;
    width: 95vw;
    height: 95vh;
    margin: 2.5vh auto;
  }
}

/* Melhorias para o botão de download */
.download-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.download-button i {
  font-size: 1rem;
}

/* Animação de loading */
.preview-loading .spinner-border {
  width: 3rem;
  height: 3rem;
}

.preview-loading-text {
  margin-top: 1rem;
  color: #6c757d;
  font-size: 1rem;
}
