# CoreUI React version

## Intro 
This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app)

It uses Sass (with .scss). The styles are loaded at the template level with `node-sass-chokidar` css preprocessor

Dependencies are handled by **npm**.

## Usage
`npm i` - to install dependencies

## Sctipts 
`npm start` for developing (it runs webpack-dev-server)  
`npm run build` to run a dev build  

## See also
[Create-React-App](CRA.md)
[Readme](./README.md)
