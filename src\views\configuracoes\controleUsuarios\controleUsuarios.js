import React, { useEffect, useState } from "react";
import {
  <PERSON>ow,
  <PERSON>ol,
  CCardBody,
  CButton,
  CLabel,
  <PERSON>ard,
  <PERSON>orm,
  <PERSON>ard<PERSON>ooter,
  CCardHeader,
  CInput,
} from "@coreui/react";
import "react-toastify/dist/ReactToastify.css";
import CardLoading from "src/reusable/CardLoading";
import { getApi } from "src/reusable/functions.js";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { GET_DATA } from "src/api.js";
import { getURI } from "src/config/apiConfig.js";
import Select from "react-select";
import { toast } from "react-toastify";
import ControleUsuariosTable from "./controleUsuariosTable.js";
import LoadingComponent from "src/reusable/Loading.js";
import ControleUsuariosEmailModal from "./controleUsuariosEmailModal.js";
import { formatCurrency, postManager } from "src/reusable/helpers.js";

const today = new Date();

const ControleUsuarios = () => {
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;
  const [loading, setLoading] = useState(false);
  const [tickets, setTickets] = useState([]);
  const [selectDateBegin, setSelectDateBegin] = useState(today);
  const [selectDateEnd, setSelectDateEnd] = useState(today);
  const [selectCrm, setSelectCrm] = useState({
    datacobName: user?.activeConnection,
    datacobNumber: user?.activeConnection,
  });
  const [selectVencimento, setSelectVencimento] = useState(null);

  const [statusBol, setStatusBol] = useState(null);

  const [nrBoleto, setNrBoleto] = useState(null);

  const [usersCrm, setUsersCrm] = useState([]);
  const [loadingUsersCrm, setLoadingUsersCrm] = useState(false);
  const [selectedUserCrm, setSelectedUserCrm] = useState(null);

  const [groupsCrm, setGroupsCrm] = useState([]);
  const [loadingGroupsCrm, setLoadingGroupsCrm] = useState(false);
  const [selectedGroupCrm, setSelectedGroupCrm] = useState(null);

  const [loadingExport, setLoadingExport] = useState(false);
  const [showModalEmail, setShowModalEmail] = useState(false);

  const [isAllowed, setIsAllowed] = useState(false);
  const token = localStorage.getItem("token");

  const getTicketsData = async () => {
    setLoading(true);
    try {
      const usersProcessed = await Promise.all(
        usersCrm.map(async (item) => ({
          ...item,
          login: await postManager(token, item.login, 2),
        }))
      );
      const userLoggedCrm = usersProcessed?.find(
        (item) => item.login?.trim() === user?.username?.trim()
      );
      const idLogged = !isAllowed
        ? userLoggedCrm?.id_Usuario
        : selectedUserCrm?.id_Usuario;

      const url = isAllowed
        ? getURI("getControleUsuario") +
          `${
            selectedUserCrm?.id_Usuario !== null &&
            selectedUserCrm?.id_Usuario !== undefined
              ? "/" + selectedUserCrm.id_Usuario
              : ""
          }`
        : getURI("getControleUsuario") + `/${idLogged}`;
      const result = await GET_DATA(
        url,
        {
          ...{
            crm: selectCrm?.datacobNumber,
            dtInicio: selectDateBegin.toISOString().substring(0, 10),
            dtFim: selectDateEnd.toISOString().substring(0, 10),
          },
          ...(selectVencimento !== null &&
            selectVencimento !== undefined && {
              dtVencimento: selectVencimento?.toISOString()?.substring(0, 10),
            }),
          ...(statusBol !== null &&
            statusBol !== undefined && { status: statusBol?.value }),
          ...(selectedGroupCrm?.id_Grupo !== null &&
            selectedGroupCrm?.id_Grupo !== undefined && {
              idGrupo: selectedGroupCrm?.id_Grupo,
            }),
          ...(nrBoleto !== null &&
            nrBoleto !== undefined && { nr_Boleto: nrBoleto }),
        },
        true
      );
      if (result.length > 0) {
        setTickets(result);
      } else {
        setTickets([]);
      }
    } catch (error) {
      setTickets([]);
      console.log(error);
    }
    setLoading(false);
  };

  const getUsersTu = async () => {
    setLoadingUsersCrm(true);
    try {
      const res = await getApi(
        {
          activeConnection: selectCrm?.datacobNumber,
        },
        "getDatacobUsers"
      );
      setUsersCrm(
        res === null
          ? []
          : [
              {
                id_Usuario: null,
                nome: "Todos",
              },
              ...res,
            ]
      );
      setSelectedUserCrm(null);
    } catch (e) {
      setUsersCrm([]);
      setSelectedUserCrm(null);
      console.error(e);
    }
    setLoadingUsersCrm(false);
  };

  const getGroupsCRM = async () => {
    setLoadingGroupsCrm(true);
    try {
      const res = await getApi(
        {
          activeConnection: selectCrm?.datacobNumber,
        },
        "getDatacobGroups"
      );
      setGroupsCrm(
        res === null
          ? []
          : [
              {
                id_Grupo: null,
                descricao: "Todos",
              },
              ...res,
            ]
      );
      setSelectedGroupCrm(null);
    } catch (e) {
      setGroupsCrm([]);
      setSelectedGroupCrm(null);
      console.error(e);
    }
    setLoadingGroupsCrm(false);
  };

  useEffect(() => {
    getUsersTu();
    getGroupsCRM();
  }, [selectCrm]);

  async function getAllowedRoles() {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "controle_usuario_funcoes"
    );
    try {
      const arrayRoles = JSON.parse(response)?.find(
        (x) => x === user?.role?.id
      );
      if (arrayRoles !== undefined) setIsAllowed(true);
      else setIsAllowed(false);
    } catch (err) {
      setIsAllowed(false);
    }
  }

  useEffect(() => {
    getAllowedRoles();
  }, []);

  const handleCloseModals = () => {
    setShowModalEmail(false);
  };

  const handleSelectCrm = (e) => {
    setSelectCrm(e);
  };

  const handleStatusBol = (e) => {
    setStatusBol(e);
  };

  const handleExportExcel = async (e) => {
    if (tickets?.length === 0) {
      toast.error("Nenhum Registro para Exportar!");
      return;
    }
    setLoadingExport(true);
    try {
      const url = getURI("postControleUsuarioExcel");
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
        body: JSON.stringify(tickets),
      });

      // Cria uma URL para o Blob
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "dados.xlsx";
        document.body.appendChild(link); // Necessário para o Firefox
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url); // Limpa o objeto URL
      } else {
        toast.warning("Nenhum resultado encontrado!");
      }
    } catch (error) {
      console.log(error);
    }
    setLoadingExport(false);
  };

  const statusObj = [
    { value: "A", label: "Aberto" },
    { value: "C", label: "Cancelado" },
    { value: "P", label: "Pago" },
  ];

  return (
    <div>
      <h3>Controle de Boletos por Usuário:</h3>
      <p style={{ color: "gray" }}></p>
      <div>
        <CCard>
          <CCardBody>
            <CRow>
              <CCol>
                <CCardBody className="py-1">
                  <CRow>
                    <CCol md={2}>
                      <CLabel>Data Inicio</CLabel> <br />
                      <ReactDatePicker
                        selected={selectDateBegin}
                        onChange={(e) => setSelectDateBegin(e)}
                        className="form-control w-100"
                        dateFormat="dd/MM/yyyy"
                        onKeyDown={(e) => e.preventDefault()}
                      />
                    </CCol>
                    <CCol md={2}>
                      <CLabel>Data Fim</CLabel> <br />
                      <ReactDatePicker
                        selected={selectDateEnd}
                        onChange={(e) => setSelectDateEnd(e)}
                        className="form-control"
                        dateFormat="dd/MM/yyyy"
                        onKeyDown={(e) => e.preventDefault()}
                      />
                    </CCol>
                    {user?.datacobs?.length > 1 && (
                      <CCol md={2} style={{ color: "black" }}>
                        <CLabel>CRM</CLabel> <br />
                        <Select
                          options={user.datacobs}
                          onChange={handleSelectCrm}
                          getOptionLabel={(opt) => opt.datacobNumber}
                          getOptionValue={(opt) => opt.datacobNumber}
                          placeholder={"Selecione"}
                          value={selectCrm}
                          height="500px"
                        />
                      </CCol>
                    )}
                    <CCol md={2} style={{ color: "black" }}>
                      {loadingUsersCrm ? (
                        <div style={{ color: "black" }}>
                          <LoadingComponent />
                        </div>
                      ) : (
                        <>
                          <CLabel>Carteira</CLabel> <br />
                          <Select
                            options={groupsCrm}
                            value={selectedGroupCrm}
                            getOptionLabel={(opt) => opt.descricao}
                            getOptionValue={(opt) => opt.id_Grupo}
                            onChange={(e) => setSelectedGroupCrm(e)}
                            placeholder={"Selecione"}
                          />
                        </>
                      )}
                    </CCol>
                    {isAllowed === true && (
                      <CCol md={3} style={{ color: "black" }}>
                        {loadingUsersCrm ? (
                          <div style={{ color: "black" }}>
                            <CardLoading />
                          </div>
                        ) : (
                          <>
                            <CLabel>Usuário</CLabel> <br />
                            <Select
                              options={usersCrm}
                              value={selectedUserCrm}
                              getOptionLabel={(opt) => opt.nome}
                              getOptionValue={(opt) => opt.id_Usuario}
                              onChange={(e) => setSelectedUserCrm(e)}
                              placeholder={"Selecione"}
                            />
                          </>
                        )}
                      </CCol>
                    )}
                    <CCol md={2} style={{ color: "black" }}>
                      <CLabel>Status</CLabel> <br />
                      <Select
                        options={statusObj}
                        onChange={handleStatusBol}
                        placeholder={"Selecione"}
                        value={statusBol}
                        height="500px"
                      />
                    </CCol>
                    <CCol md={2}>
                      <CLabel>Vencimento</CLabel> <br />
                      <ReactDatePicker
                        selected={selectVencimento}
                        onChange={(e) => setSelectVencimento(e)}
                        className="form-control"
                        dateFormat="dd/MM/yyyy"
                        onKeyDown={(e) => e.preventDefault()}
                      />
                    </CCol>
                    <CCol md={2}>
                      <CLabel>Nr. Boleto</CLabel> <br />
                      <CInput
                        value={nrBoleto}
                        onChange={(e) => setNrBoleto(e.target.value)}
                        className="form-control"
                      />
                    </CCol>
                    <CCol md={2}>
                      <CButton
                        color="primary"
                        style={{ marginTop: "31px" }}
                        onClick={getTicketsData}
                        disabled={loadingUsersCrm}
                      >
                        {loadingUsersCrm && <LoadingComponent />}
                        {!loadingUsersCrm && "Buscar"}
                      </CButton>
                    </CCol>
                  </CRow>
                  <br />
                </CCardBody>
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
        <CCard>
          <CCardBody>
            <CForm>
              <CRow style={{ justifyContent: "right" }}>
                <CCard style={{ width: "200px" }}>
                  <CCardHeader
                    style={{
                      textAlign: "center",
                      fontWeight: "bold",
                      fontSize: "17px",
                      padding: "2px",
                    }}
                  >
                    Total Valor Pago
                  </CCardHeader>
                  <CCardBody
                    style={{
                      textAlign: "center",
                      fontSize: "16px",
                      padding: "2px",
                    }}
                  >
                    {formatCurrency(
                      tickets
                        .filter(
                          (x) => x.statusBoleto?.toUpperCase() !== "CANCELADO"
                        )
                        .reduce((acum, ticket) => acum + ticket.valorCaixa, 0)
                    )}
                  </CCardBody>
                </CCard>
                <CCard className={"mr-4"} style={{ width: "200px" }}>
                  <CCardHeader
                    style={{
                      textAlign: "center",
                      fontWeight: "bold",
                      fontSize: "17px",
                      padding: "2px",
                    }}
                  >
                    Valor Total Honorário
                  </CCardHeader>
                  <CCardBody
                    style={{
                      textAlign: "center",
                      fontSize: "16px",
                      padding: "2px",
                    }}
                  >
                    {formatCurrency(
                      tickets
                        .filter(
                          (x) => x.statusBoleto?.toUpperCase() !== "CANCELADO"
                        )
                        .reduce((acum, ticket) => acum + ticket.honorario, 0)
                    )}
                  </CCardBody>
                </CCard>
              </CRow>
              <CRow>
                <CCol xs>
                  <CRow>
                    {loading ? (
                      <div className="text-center" style={{ width: "100%" }}>
                        <CardLoading />
                      </div>
                    ) : (
                      <CCol>
                        <ControleUsuariosTable
                          tickets={tickets}
                          getTicketsData={getTicketsData}
                        />
                      </CCol>
                    )}
                  </CRow>
                </CCol>
              </CRow>
            </CForm>
          </CCardBody>
          <CCardFooter className={"text-right"}>
            <CButton color="primary" onClick={handleExportExcel}>
              {!loadingExport && "Exportar para Excel"}
              {loadingExport && <LoadingComponent />}
            </CButton>
            <CButton
              color="info"
              className={"ml-2"}
              onClick={() => {
                if (tickets.length === 0) {
                  toast.error("Nenhum registro para Enviar por E-mail!");
                  return;
                }
                setShowModalEmail(true);
              }}
            >
              Enviar por E-mail
            </CButton>
          </CCardFooter>
        </CCard>
      </div>

      {showModalEmail && (
        <ControleUsuariosEmailModal
          isOpen={showModalEmail}
          onClose={handleCloseModals}
          data={tickets}
        />
      )}
    </div>
  );
};

export default ControleUsuarios;
