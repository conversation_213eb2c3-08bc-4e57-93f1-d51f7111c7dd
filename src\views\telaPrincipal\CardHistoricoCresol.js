import { useEffect, useState } from "react";
import { <PERSON>ard, CCardBody, CBadge, CCardHeader } from "@coreui/react";
import { GET_DATA, POST_DATA } from "src/api";

import TableSelectItens from "src/reusable/TableSelectItens";
import CardLoading from "src/reusable/CardLoading";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import { getURI } from "src/config/apiConfig";

// Histórico Cresol
const CardHistoricoCresol = () => {
  const [tableData, setTableData] = useState([]);

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const getHistoricoCresol = async () => {
    const numeroContrato = financiadoData.numero_Contrato;
    if (!numeroContrato) return;
    await GET_DATA(
      getURI("getHistoricoCresol"),
      { numeroContrato: numeroContrato },
      true
    ).then((result) => {
      setTableData(result);
    });
  };

  useEffect(() => {
    getHistoricoCresol();
  }, []);

  const [isLoading, setIsLoading] = useState(false);
  //HistoricoCresol
  const tableColumns = [
    {
      key: "tipoRegistro",
      label: "Tipo Registro",
    },
    { key: "grupo", label: "Grupo" },
    { key: "numeroContrato", label: "Número do Contrato" },
    {
      key: "dataAcionamento",
      label: "Data do Acionamento",
    },
    { key: "horaAcionamento", label: "Hora do Acionamento" },
    { key: "sequenciaComentario", label: "Sequência do Comentário" },
    { key: "codigoAcao", label: "Código da Ação" },
    { key: "codigoResultado", label: "Código do Resultado" },
    { key: "codigoComplemento", label: "Código do Complemento" },
    { key: "codigoEmpresa", label: "Código da Empresa" },
    { key: "gestorAtividade", label: "Gestor da Atividade" },
    { key: "comentario", label: "Comentário" },
    { key: "assessoria", label: "Assessoria" },
  ];

  return (
    <>
      <CCard>
        <CCardHeader style={{ display: "flex", alignItems: "center" }}>
          Histórico Cresol
        </CCardHeader>
        <CCardBody className="mt-2">
          {isLoading ? (
            <CardLoading msg={"Atualizando..."} />
          ) : (
            <TableSelectItens
              data={tableData}
              columns={tableColumns}
              onSelectionChange={(_) => {}}
              defaultSelectedKeys={[]}
              selectable={false}
              heightParam="290px"
            />
          )}
        </CCardBody>

        {/* ) : (
      <CCardBody>
        <NaoHaDadosTables />
      </CCardBody>
      )} */}
      </CCard>
    </>
  );
};
export default CardHistoricoCresol;
