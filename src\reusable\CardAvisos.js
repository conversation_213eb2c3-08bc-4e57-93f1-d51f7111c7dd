import React, { useState, useEffect } from "react";
import {
  CCard,
  CCardBody
} from "@coreui/react";

const CardAvisos = ({Title,Msg}) => {

  return (
    <div>
      <CCard style={{border: "none"}}>
        <CCardBody>
          <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            {Title && Title !=="" ? <h5>{Title}</h5>:""}
            {Msg && Msg !=="" ? <h6>{Msg}</h6>:""}
          </div>
        </CCardBody>
      </CCard>
    </div>
  );
};

export default CardAvisos;
