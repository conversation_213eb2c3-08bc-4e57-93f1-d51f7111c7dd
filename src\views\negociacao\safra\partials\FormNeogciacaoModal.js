import React, { useEffect, useState } from "react";
import ReactDatePicker from "react-datepicker";
import ptBR from "date-fns/locale/pt-BR";

import "react-datepicker/dist/react-datepicker.css";

import {
  CButton,
  CInput,
  CModal,
  CModalBody,
  CModalHeader,
  CModalTitle,
  CCol,
  CRow,
  CLabel,
  CModalFooter,
} from "@coreui/react";
import Select from "react-select";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardCalcular from "./CardCalcular";
import CardLoading from "src/reusable/CardLoading";
import FormularioSimulacao from "./FormularioSimulacao";

const PostData = async (payload, endpoint = "cyberSafraSimularSaldo") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), payload, true, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const FormNeogciacaoModal = ({ isOpen, onClose, contrato }) => {
  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const parcelasAbertas = contratosAtivos?.flatMap((item) =>
    item.parcelas.filter(
      (pItem) =>
        pItem.status === "A" && !pItem.nr_Acordo /* && pItem.atraso > 0  ||
        (pItem.status === "A" && !pItem.nr_Acordo && pItem.atraso === 0 && pItem.nome_Tipo_Parcela === "DIF_PARCELAS")*/
    )
  );
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");

  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");

  let financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";
  const [dataEntrada, setDataEntrada] = useState("");
  const [contratoOriginal, setContratoOriginal] = useState(null);
  const [contratoSimulado, setContratoSimulado] = useState(null);

  const [contratoNegociado, setContratoNegociado] = useState(null);

  const [valorMultaOriginal, setValorMultaOriginal] = useState(
    contrato.dadosSafra.vlrMulta
  );
  const [valorJurosOriginal, setValorJurosOriginal] = useState(
    contrato.dadosSafra.vlrJuros
  );
  const [valorDisagioOriginal, setValorDisagioOriginal] = useState(
    contrato.dadosSafra.vlrPrincDesc
  );
  const [valorPrincipalOriginal, setValorPrincipalOriginal] = useState(
    contrato.dadosSafra.vlrPrincipal
  );
  const [valorCustasOriginal, setValorCustasOriginal] = useState(
    contrato.dadosSafra.vlrCustas
  );

  const [primeiraParcela, setPrimeiraParcela] = useState(null);

  const [payload, setPayload] = useState({
    cdTipoAcordo: "",
    vlrNegociado: 36554.72,
    vlrNegociadoSemHonor: 36554.72,
    vlrNegociadoComHonor: 36687.33,
    vlrHonorarios: 132.61,
    vlrEntrada: 1458.75,
    diasCarencia: 0,
    qtdParcela: 0,
    periodicidade: "MONTHLY",
    dtPagamentoInicial: "",
    cpfCnpj: "09875012670",
    parcAlvara: "N",
    parcBalao: "N",
    parcelasBalao: [],
    idContrato: null,
    idAcordo: 0,
    dtParcelaEntrada: "",
    dtPagamentoEntrada: null,
    parcelaEntrada: 3,
    valorPmtOriginal: 1326.14,
    valorMultaOriginal: 132.6,
    valorJurosOriginal: 2407.64,
    valorMultaEntrada: 26.52,
    valorJurosEntrada: 0.0,
    chaveCyber: "1620030162004390000000001",
    encargos: 0,
    iof: 0,
    tarifas: 0,
    vlrCustasOriginal: 0.0,
    vlrCustasNegociado: 0.0,
  });

  const initialErrors = {
    cdTipoAcordo: "",
    vlrNegociado: "",
    vlrNegociadoSemHonor: "",
    vlrNegociadoComHonor: "",
    vlrHonorarios: "",
    vlrEntrada: "",
    diasCarencia: "",
    qtdParcela: "",
    periodicidade: "",
    dtPagamentoInicial: "",
    cpfCnpj: "",
    parcAlvara: "",
    parcBalao: "",
    parcelasBalao: "",
    idContrato: "",
    idAcordo: "",
    dtParcelaEntrada: "",
    dtPagamentoEntrada: "",
    parcelaEntrada: "",
    valorPmtOriginal: "",
    valorMultaOriginal: "",
    valorJurosOriginal: "",
    valorMultaEntrada: "",
    valorJurosEntrada: "",
    chaveCyber: "",
    encargos: "",
    iof: "",
    tarifas: "",
    vlrCustasOriginal: "",
    vlrCustasNegociado: "",
  };

  const [errors, setErrors] = useState(initialErrors);
  const [tipoNegociacao, setTipoNegociacao] = useState(null);

  const [descVlPrincipal, setDescVlPrincipal] = useState(null);
  const [descVlDivida, setDescVlDivida] = useState(null);
  const [descVlMulta, setDescVlMulta] = useState(null);
  const [descVlJuros, setDescVlJuros] = useState(null);
  const [descVlCustas, setDescVlCustas] = useState(null);

  React.useEffect(() => {
    var originalContrato = { ...contrato };
    var negociadoContrato = { ...contrato };
    setContratoOriginal(originalContrato);
    setContratoNegociado(negociadoContrato);
    console.warn("Contrato Original", originalContrato);
    console.warn("Contrato Negociado", negociadoContrato);
    if (contratosAtivos != null) {
      const parcelas = JSON.parse(JSON.stringify(parcelasAbertas));
      const ordenarParcelas = parcelas.sort((a, b) => {
        // Verifica se a.nr_Parcela ou b.nr_Parcela é igual a 0
        if (a.nr_Parcela === 0) return 1; // Move 'a' para o final se a.nr_Parcela é 0
        if (b.nr_Parcela === 0) return -1; // Move 'b' para o final se b.nr_Parcela é 0

        // Caso contrário, ordena normalmente
        return a.nr_Parcela - b.nr_Parcela;
      });
      const ordenarContrato = ordenarParcelas.sort(
        (a, b) => a.numero_Contrato - b.numero_Contrato
      );
      // const parcela = ordenarContrato[0];
      const parcelaContrato = ordenarContrato.filter(
        (item) => item.numero_Contrato === contrato.numero_Contrato
      )[0];
      setPrimeiraParcela(parcelaContrato);
    }
  }, []);

  const optionsContrato = [
    ...contrato.elegivel.map((item) => {
      return { label: item.dscTipo, value: item };
    }),
  ];

  const RealiarSimulacao = async () => {
    let ret = false;
    setTitleAvisoLoading("Enviando Simulação Safra");
    setMsgAvisoLoading(`Enviando dados para Simulação...`);
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(payload, "cyberSafraSimularSaldo")
      .then((data) => {
        if (
          data.success &&
          data.message === "Simulação de acordo obtida com Sucesso."
        ) {
          setContratoSimulado(contratoNegociado);
          contratoNegociado.dadosSafra.simulacao = data.data;
          contratoNegociado.dadosSafra.tipoAcordo = tipoNegociacao;
          contratoNegociado.dadosFinanciado = financiadoData;
          setContratoSimulado(contratoNegociado);
        } else {
          setTitleAvisoLoading("Falha Simulação Safra");
          setMsgAvisoLoading(`Simulação não gerada ${data.message}`);
        }
      })
      .catch((err) => {
        setTitleAvisoLoading(`Erro na chamada das APIS`);
        setMsgAvisoLoading(
          `Erro na chamada API de Cyber Simulação de Acordos, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`
        );
      })
      .finally(() => {
        setTimeout(() => {
          setLoading(false);
          setLoadingAction("empty");
          setMsgAvisoLoading("");
          setTitleAvisoLoading("");
        }, 3000);
      });

    return ret;
  };

  const handleSelectTipoNegociacao = (value) => {
    const newContrato = { ...contratoOriginal };
    const negContrato = { ...contratoNegociado };

    setTipoNegociacao(value);
    const vlrHonorario =
      newContrato.dadosSafra.parcelasOriginais[
        primeiraParcela.nr_Parcela && primeiraParcela.nr_Parcela > 0
          ? primeiraParcela.nr_Parcela - 1
          : 0
      ].vlrPaPrinc * (value.percHonorario > 0 ? value.percHonorario / 100 : 0);
    negContrato.dadosSafra.vlrHonorario = parseFloat(vlrHonorario.toFixed(2));
    setContratoNegociado(negContrato);
    handlePayloadChange("cdTipoAcordo", value.cdTipo);
  };

  const handleSimular = () => {
    setErrors(initialErrors);
    const newErrors = { ...initialErrors };
    payload.cdTipoAcordo = tipoNegociacao.cdTipo;
    payload.vlrNegociado = parseFloat(
      (
        contratoNegociado.dadosSafra.vlrPrincipal +
        contratoNegociado.dadosSafra.vlrMulta +
        contratoNegociado.dadosSafra.vlrJuros +
        contratoNegociado.dadosSafra.vlrPrincDesc +
        contratoNegociado.dadosSafra.vlrCustas
      ).toFixed(2)
    );
    payload.vlrNegociadoSemHonor = payload.vlrNegociado;
    payload.vlrNegociadoComHonor = parseFloat(
      (
        payload.vlrNegociado + contratoNegociado.dadosSafra.vlrHonorario
      ).toFixed(2)
    );
    payload.vlrHonorarios = contratoNegociado.dadosSafra.vlrHonorario;
    payload.vlrEntrada = parseFloat(
      (
        contratoNegociado.dadosSafra.parcelasOriginais[
          primeiraParcela.nr_Parcela && primeiraParcela.nr_Parcela > 0
            ? primeiraParcela.nr_Parcela - 1
            : 0
        ].vlrPaPrinc + contratoNegociado.dadosSafra.vlrHonorario
      ).toFixed(2)
    );
    payload.periodicidade = "MONTHLY";
    payload.cpfCnpj = financiadoData.cpfCnpj;
    payload.parcelaEntrada =
      contratoNegociado.dadosSafra.parcelasOriginais[
        primeiraParcela.nr_Parcela && primeiraParcela.nr_Parcela > 0
          ? primeiraParcela.nr_Parcela - 1
          : 0
      ].nrParcela;
    payload.valorPmtOriginal =
      contratoNegociado.dadosSafra.parcelasOriginais[
        primeiraParcela.nr_Parcela && primeiraParcela.nr_Parcela > 0
          ? primeiraParcela.nr_Parcela - 1
          : 0
      ].vlrPaPrinc;
    payload.valorMultaOriginal = contratoNegociado.dadosSafra.vlrMulta;
    payload.valorJurosOriginal = contratoNegociado.dadosSafra.vlrJuros;
    payload.valorMultaEntrada =
      contratoNegociado.dadosSafra.parcelasOriginais[
        primeiraParcela.nr_Parcela && primeiraParcela.nr_Parcela > 0
          ? primeiraParcela.nr_Parcela - 1
          : 0
      ].vlrPaMulta;
    payload.valorJurosEntrada =
      contratoNegociado.dadosSafra.parcelasOriginais[
        primeiraParcela.nr_Parcela && primeiraParcela.nr_Parcela > 0
          ? primeiraParcela.nr_Parcela - 1
          : 0
      ].vlrPaMora;
    payload.chaveCyber = contratoNegociado.dadosSafra.contrato;
    payload.encargos = 0;
    payload.iof = 0;
    payload.tarifas = contratoNegociado.dadosSafra.vlrTarifa;
    payload.vlrCustasOriginal = valorCustasOriginal;
    payload.vlrCustasNegociado = contratoNegociado.dadosSafra.vlrCustas;

    const vPayload = { ...payload };
    const vQtdParcela = parseInt(vPayload.qtdParcela);

    if (
      vQtdParcela === undefined ||
      vQtdParcela === null ||
      vQtdParcela <= 0 ||
      vQtdParcela > tipoNegociacao.qtdMaxPagamentos ||
      !Number.isInteger(vQtdParcela)
    )
      newErrors.qtdParcela = "Quantidade de Parcelas Inválida";

    if (
      payload.dtPagamentoInicial === undefined ||
      payload.dtPagamentoInicial === null ||
      payload.dtPagamentoInicial === ""
    )
      newErrors.dtPagamentoInicial = "Data de Entrada Inválida";

    if (
      tipoNegociacao.cdTipo === undefined ||
      tipoNegociacao.cdTipo === null ||
      tipoNegociacao.cdTipo === ""
    )
      newErrors.cdTipoAcordo = "Tipo de Acordo Inválido";

    setErrors(newErrors);
    if (!Object.values(newErrors).every((error) => error === "")) return;
    RealiarSimulacao();
  };

  const handlePayloadChange = (key, value) => {
    setPayload((prevPayload) => ({
      ...prevPayload,
      [key]: value,
    }));
  };

  const handleDatesChange = (date) => {
    handlePayloadChange("dtPagamentoInicial", date.toISOString());
    handlePayloadChange("dtParcelaEntrada", date.toISOString());
    setDataEntrada(date);
  };

  const handleParcelaChange = (value) => {
    value > 0 && value <= tipoNegociacao.qtdMaxPagamentos
      ? handlePayloadChange("qtdParcela", value)
      : handlePayloadChange("qtdParcela", 0);
  };

  const handleValorPrincipalChange = (value) => {
    contratoNegociado.dadosSafra.vlrPrincipal = valorPrincipalOriginal;
    setContratoNegociado({ ...contratoNegociado });
    if (value >= 0 && value <= searchTipoDescont("dmamtdlq")) {
      const vlrNewPrincipal = parseFloat(
        (
          valorPrincipalOriginal -
          valorPrincipalOriginal * (value / 100)
        ).toFixed(2)
      );
      contratoNegociado.dadosSafra.vlrPrincipal = vlrNewPrincipal; // Atualize o valor no contrato original
      setContratoNegociado({ ...contratoNegociado }); // Atualize o estado contratoNegociado
      setDescVlPrincipal(value);
    } else {
      setDescVlPrincipal(0);
    }
  };

  const handleDisagioChange = (value) => {
    contratoNegociado.dadosSafra.vlrPrincDesc = valorDisagioOriginal;
    setContratoNegociado({ ...contratoNegociado });
    if (value >= 0 && value <= searchTipoDescont("dmpayoff")) {
      const vlrNewDisagio = parseFloat(
        (valorDisagioOriginal - valorDisagioOriginal * (value / 100)).toFixed(2)
      );
      contratoNegociado.dadosSafra.vlrPrincDesc = vlrNewDisagio; // Atualize o valor no contrato original
      setContratoNegociado({ ...contratoNegociado }); // Atualize o estado contratoNegociado
      setDescVlDivida(value);
    } else {
      setDescVlDivida(0);
    }
  };

  const handleMultaChange = (value) => {
    contratoNegociado.dadosSafra.vlrMulta = valorMultaOriginal;
    setContratoNegociado({ ...contratoNegociado });
    if (value >= 0 && value <= searchTipoDescont("u3multcon")) {
      const vlrNewMulta = parseFloat(
        (valorMultaOriginal - valorMultaOriginal * (value / 100)).toFixed(2)
      );
      contratoNegociado.dadosSafra.vlrMulta = vlrNewMulta; // Atualize o valor no contrato original
      setContratoNegociado({ ...contratoNegociado }); // Atualize o estado contratoNegociado
      setDescVlMulta(value);
    } else {
      setDescVlMulta(0);
    }
  };

  const handleJurosChange = (value) => {
    contratoNegociado.dadosSafra.vlrJuros = valorJurosOriginal;
    setContratoNegociado({ ...contratoNegociado });
    if (value >= 0 && value <= searchTipoDescont("u3vljuratra")) {
      const vlrNewJuros = parseFloat(
        (valorJurosOriginal - valorJurosOriginal * (value / 100)).toFixed(2)
      );
      contratoNegociado.dadosSafra.vlrJuros = vlrNewJuros; // Atualize o valor no contrato original
      setContratoNegociado({ ...contratoNegociado }); // Atualize o estado contratoNegociado
      setDescVlJuros(value);
    } else {
      setDescVlJuros(0);
    }
  };

  const handleCustasChange = (value) => {
    contratoNegociado.dadosSafra.vlrCustas = valorCustasOriginal;
    setContratoNegociado({ ...contratoNegociado });
    if (value >= 0 && value <= searchTipoDescont("U3VLCUSTAS")) {
      const vlrNewCustasNegociado = parseFloat(
        (valorCustasOriginal - valorCustasOriginal * (value / 100)).toFixed(2)
      );
      contratoNegociado.dadosSafra.vlrCustas = vlrNewCustasNegociado; // Atualize o valor no contrato original
      setContratoNegociado({ ...contratoNegociado }); // Atualize o estado contratoNegociado
      setDescVlCustas(value);
    } else {
      setDescVlCustas(0);
    }
  };

  const searchTipoDescont = (campo) => {
    if (
      tipoNegociacao != null &&
      Array.isArray(tipoNegociacao.camposDesconto)
    ) {
      const item = tipoNegociacao.camposDesconto.find(
        (item) => item.campo.toLowerCase() === campo.toLowerCase()
      );
      if (item) {
        return item.maxDescontoNivel1;
      }
    }

    return 0;
  };

  return (
    <CModal
      className="custom-modal"
      size="xl"
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
    >
      <CModalHeader closeButton>
        <CModalTitle>Negociação</CModalTitle>
      </CModalHeader>
      <CModalBody>
        {loading && loadingAction === "VarifyParam" ? (
          <CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />
        ) : (
          ""
        )}
        {contratoSimulado != null && !loading ? (
          <FormularioSimulacao
            onClickNovaSimulacao={() => setContratoSimulado(null)}
            onFinshAcordo={onClose}
            contrato={contratoSimulado}
          />
        ) : (
          ""
        )}
        {contratoSimulado == null && !loading ? (
          <CRow>
            <CCol xs="8">
              <CRow>
                <CCol>
                  <CLabel>Tipo de Negociação</CLabel>
                  <Select
                    options={optionsContrato}
                    onChange={(e) => {
                      handleSelectTipoNegociacao(e.value);
                    }}
                    placeholder={"Selecione"}
                    className={
                      errors.cdTipoAcordo ? "border-danger rounded" : ""
                    }
                  />
                  {errors.cdTipoAcordo && (
                    <div className="text-danger">{errors.cdTipoAcordo}</div>
                  )}
                </CCol>
              </CRow>
              {tipoNegociacao != null ? (
                <CRow className={"mt-2"}>
                  <CCol>
                    <CLabel>Data Entrada</CLabel>
                    <div>
                      <ReactDatePicker
                        selected={dataEntrada}
                        onChange={(date) => handleDatesChange(date)}
                        dateFormat="dd/MM/yyyy"
                        placeholder="dd/MM/yyyy"
                        minDate={new Date()}
                        maxDate={
                          new Date(
                            new Date().getTime() +
                              tipoNegociacao.maxDiasPagamentoIni *
                                24 *
                                60 *
                                60 *
                                1000
                          )
                        }
                        locale={ptBR}
                        className={`form-control input ${
                          errors.dtPagamentoInicial
                            ? "border-danger rounded"
                            : ""
                        }`}
                      />
                    </div>
                    {errors.dtPagamentoInicial && (
                      <div className="text-danger">
                        {errors.dtPagamentoInicial}
                      </div>
                    )}
                  </CCol>
                </CRow>
              ) : (
                ""
              )}
              {tipoNegociacao != null ? (
                <CRow className={"mt-2"}>
                  <CCol>
                    <CLabel>
                      Quantidade Parcelas (Max Permitido{" "}
                      {tipoNegociacao.qtdMaxPagamentos})
                    </CLabel>
                    <CInput
                      type="number"
                      precision="2"
                      onChange={(e) => {
                        handleParcelaChange(e.target.value);
                      }}
                      placeholder="Qtd"
                      className={
                        errors.qtdParcela ? "border-danger rounded" : ""
                      }
                      value={payload.qtdParcela !== 0 ? payload.qtdParcela : ""}
                    />
                    {errors.qtdParcela && (
                      <div className="text-danger">{errors.qtdParcela}</div>
                    )}
                  </CCol>
                </CRow>
              ) : (
                ""
              )}

              {searchTipoDescont("dmamtdlq") > 0 && tipoNegociacao != null ? (
                <CRow className={"mt-2"}>
                  <CCol>
                    <CLabel>
                      Valor Principal (Max Permitido{" "}
                      {searchTipoDescont("dmamtdlq")}%)
                    </CLabel>
                    <CInput
                      type="number"
                      precision="2"
                      onChange={(e) => {
                        handleValorPrincipalChange(e.target.value);
                      }}
                      placeholder="% de Desconto"
                      className={
                        errors.vlrPrincipal ? "border-danger rounded" : ""
                      }
                      value={descVlPrincipal !== 0 ? descVlPrincipal : ""}
                    />
                    {errors.vlrPrincipal && (
                      <div className="text-danger">{errors.vlrPrincipal}</div>
                    )}
                  </CCol>
                </CRow>
              ) : (
                ""
              )}

              {searchTipoDescont("dmpayoff") > 0 && tipoNegociacao != null ? (
                <CRow className={"mt-2"}>
                  <CCol>
                    <CLabel>
                      Valor Principal Divida (Max Permitido{" "}
                      {searchTipoDescont("dmpayoff")}%)
                    </CLabel>
                    <CInput
                      type="number"
                      precision="2"
                      onChange={(e) => {
                        handleDisagioChange(e.target.value);
                      }}
                      placeholder="% de Desconto"
                      className={
                        errors.vlrPrincDesc ? "border-danger rounded" : ""
                      }
                      value={descVlDivida !== 0 ? descVlDivida : ""}
                    />
                    {errors.vlrPrincDesc && (
                      <div className="text-danger">{errors.vlrPrincDesc}</div>
                    )}
                  </CCol>
                </CRow>
              ) : (
                ""
              )}
              {searchTipoDescont("u3multcon") > 0 && tipoNegociacao != null ? (
                <CRow className={"mt-2"}>
                  <CCol>
                    <CLabel>
                      Valor Multa (Max Permitido{" "}
                      {searchTipoDescont("u3multcon")}%)
                    </CLabel>
                    <CInput
                      type="number"
                      precision="2"
                      onChange={(e) => {
                        handleMultaChange(e.target.value);
                      }}
                      placeholder="% de Desconto"
                      className={errors.vlrMulta ? "border-danger rounded" : ""}
                      value={descVlMulta !== 0 ? descVlMulta : ""}
                    />
                    {errors.vlrMulta && (
                      <div className="text-danger">{errors.vlrMulta}</div>
                    )}
                  </CCol>
                </CRow>
              ) : (
                ""
              )}
              {searchTipoDescont("u3vljuratra") > 0 &&
              tipoNegociacao != null ? (
                <CRow className={"mt-2"}>
                  <CCol>
                    <CLabel>
                      Valor Juros (Max Permitido{" "}
                      {searchTipoDescont("u3vljuratra")}%)
                    </CLabel>
                    <CInput
                      type="number"
                      precision="2"
                      onChange={(e) => {
                        handleJurosChange(e.target.value);
                      }}
                      placeholder="% de Desconto"
                      className={errors.vlrJuros ? "border-danger rounded" : ""}
                      value={descVlJuros !== 0 ? descVlJuros : ""}
                    />
                    {errors.vlrJuros && (
                      <div className="text-danger">{errors.vlrJuros}</div>
                    )}
                  </CCol>
                </CRow>
              ) : (
                ""
              )}
              {searchTipoDescont("U3VLCUSTAS") > 0 && tipoNegociacao != null ? (
                <CRow className={"mt-2"}>
                  <CCol>
                    <CLabel>
                      Valor Custas (Max Permitido{" "}
                      {searchTipoDescont("U3VLCUSTAS")}%)
                    </CLabel>
                    <CInput
                      type="number"
                      precision="2"
                      onChange={(e) => {
                        handleCustasChange(e.target.value);
                      }}
                      placeholder="% de Desconto"
                      className={
                        errors.vlrCustasNegociado ? "border-danger rounded" : ""
                      }
                      value={descVlCustas !== 0 ? descVlCustas : ""}
                    />
                    {errors.vlrCustasNegociado && (
                      <div className="text-danger">
                        {errors.vlrCustasNegociado}
                      </div>
                    )}
                  </CCol>
                </CRow>
              ) : (
                ""
              )}
            </CCol>
            <CCol xs="4">
              <CardCalcular contratoNegociar={contratoNegociado} />
            </CCol>
          </CRow>
        ) : (
          ""
        )}
      </CModalBody>
      {contratoSimulado != null ? (
        ""
      ) : (
        <CModalFooter>
          <CButton
            color="secondary"
            className="mr-2"
            onClick={onClose}
            disabled={loading}
          >
            Cancelar
          </CButton>
          {tipoNegociacao != null ? (
            <CButton color="info" onClick={handleSimular} disabled={loading}>
              Simular Negociação
            </CButton>
          ) : (
            ""
          )}
        </CModalFooter>
      )}
    </CModal>
  );
};

export default FormNeogciacaoModal;
