import React, { useState, useEffect } from "react";
import {
    CButton,
    CModal,
    CModalHeader,
    CModalBody,
    CDataTable,
    CModalFooter,
  } from "@coreui/react";
  import LoadingComponent from "./Loading";
import NaoHaDadosTables from "./NaoHaDadosTables";
import { useMyContext } from "./DataContext";
import { GET_FINANCIADO} from "./functions";
import { useHistory, useLocation } from "react-router-dom/cjs/react-router-dom.min";
import { useAuth } from "src/auth/AuthContext";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { formatDocument } from "./helpers";
import { toast } from "react-toastify";

type Props = {
    isOpen: boolean;
    onClose: () => void;
    data_filter: any;
}
  
const MudarClientePorTelefoneModal = ({isOpen, onClose, data_filter}: Props) => {
    const {user,checkGroup} = useAuth();
    const { updateData, updateCustas, updateCustasProjuris } = useMyContext();

    const [tableVisible, setTableVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const [tableContratosDataFilter, setTableContratosDataFilter] = useState([]);

    const history = useHistory();
    const location = useLocation();

  const isHomePage = location.pathname === "/telaprincipal";

    const handleClick = async (item) => {
        setTableVisible(false);
        setIsLoading(true);
        const financiado = await GET_FINANCIADO(item);
        // await updateConnection(item.coddatacob);
        setIsLoading(false);
        updateData(financiado);
        updateCustas(null);
        updateCustasProjuris(null);
        if (!isHomePage) {
          history.push("/telaprincipal");
        }
        onClose();
      };

      // const updateConnection = async (coddatacob) => {
      //   const payload = {
      //     activeConnection: coddatacob,
      //     userId: user.id,
      //   };
      //   await postActiveConnection(payload, "postUserConnection")
      //     .then((data) => {
      //       if (data) {
      //         const newActiveConnection = {
      //           ...user,
      //           activeConnection: coddatacob,
      //         };
      //         localStorage.setItem("user", JSON.stringify(newActiveConnection));
      //       }
      //     })
      //     .catch((err) => {
      //       console.log(err);
      //     });
      // };

      // const postActiveConnection = async (payload, endpoint) => {
      //   return new Promise(async (resolve, reject) => {
      //     try {
      //       const response = await POST_DATA(getURI(endpoint), payload, true);
      //       resolve(response);
      //     } catch (error) {
      //       reject(error);
      //     }
      //   });
      // };

      const renderCell = (item) => {
        return <div style={{ whiteSpace: "nowrap" }}>{item}</div>;
      };
      const tableFieldsDataFilter = [
        { key: "grupo", label: "Grupo", formatter: (value) => renderCell(value) },
        {
          key: "cliente",
          label: "Cliente",
          formatter: (value) => renderCell(value),
        },
        {
          key: "cpfCnpj",
          label: "CPF/CNPJ",
          formatter: (value) => formatDocument(value),
        },
        {
          key: "nome",
          label: "Nome",
          formatterByObject: (item) => renderBotaoNome(item),
        },
        // { key: "status", label: "Status Contrato" },
        {
          key: "numero_Contrato",
          label: "Contrato",
          formatter: (value) => renderCell(value),
        },
      ];

      const renderBotaoNome = (item) => {
        if (item.isPermitted) {
          return (
            <CButton
              onClick={() => buscarFinanciado(item)}
              className="flat px-2 pt-0 pb-1"
              style={{ color: "blue", whiteSpace: "nowrap" }}
            >
              {item.financiado}
            </CButton>
          );
        } else
          return (
            <span
              className="px-2 pt-0 pb-1"
              style={{ color: "grey", whiteSpace: "nowrap" }}
            >
              {item.financiado}
            </span>
          );
      };

      const buscarFinanciado = async (item) => {
        const payload = {
          ActiveConnection: item.coddatacob,
          agrupamentoId: item.idAgrupamento,
        };
        setIsLoading(true);
        // await updateConnection(item.coddatacob);
        await GetData(payload, "getDadosContratoLigacao")
          .then((resultado) => {
            if (resultado) {
              localStorage.setItem("financiadoData", JSON.stringify(resultado[0])); //Atualizando o financiadoData no localStorage
              updateData(resultado[0]); //Atualizando o financiadoData na telaPrincipal
            } else {
              //Se apareceu o contrato e teve permissão de clicar, nunca deveria falhar aqui
              toast.error("Erro: Dados não encontrados");
            }
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setIsLoading(false);
          });
      };

      const GetData = async (payload, endpoint = "") => {
        return new Promise(async (resolve, reject) => {
          try {
            const response = await GET_DATA(getURI(endpoint), payload, true);
            resolve(response);
          } catch (error) {
            reject(error);
          }
        });
      };

    useEffect(() => { 
      if(isOpen){
        setIsLoading(true)
        setTableContratosDataFilter(data_filter);
        setTableVisible(true);
        setIsLoading(false)
      }
      
    }, [isOpen]);

    return (
        <CModal 
          show={isOpen}
          onClose={onClose}
          size="xl"

          style={{color: "#333"}}
        >
            <CModalHeader color="danger" closeButton>Troca de Clientes</CModalHeader>

            {isLoading ? (
              <CModalBody>
                {" "}
                <div className="d-flex justify-content-center">
                  <LoadingComponent />
                </div>{" "}
              </CModalBody>
            ) : null}
           
            {tableVisible  && (
              <CModalBody>
                {tableContratosDataFilter == null ||
                tableContratosDataFilter === undefined ||
                tableContratosDataFilter.length === 0 ? (
                  <NaoHaDadosTables />
                ) : (
                <CDataTable
                  items={tableContratosDataFilter}
                  fields={tableFieldsDataFilter}
                  hover
                  striped
                  //bordered
                  size="lg"
                  itemsPerPage={10}
                  pagination
                  scopedSlots={{
                    numero_Contrato: (item) =>
                      item.numero_Contrato ? (
                        <td className="nowrap-cell">{item.numero_Contrato}</td>
                      ) : (
                        <td></td>
                      ),
                    nome: (item) => (
                      <td>
                        {" "}
                        <CButton
                          onClick={() => handleClick(item)}
                          className="flat px-2 pt-0 pb-1"
                          style={{
                            color: checkGroup(item.id_Grupo) ? "blue" : "black",
                            
                          }}
                          disabled={!checkGroup(item.id_Grupo)}
                          title={
                            !checkGroup(item.id_Grupo)
                              ? "Sem permissão para acessar o Grupo"
                              : ""
                          }
                        >
                          <span>{item.nome}</span>
                        </CButton>
                      </td>
                    ),
                    cpfCnpj: (item) => (
                      <td className="nowrap-cell">
                        {formatDocument(item.cpfCnpj)}
                      </td>
                    ),
                  }}
                />
              )}
            {/* <CModalFooter>
              <div className="d-flex justify-content-end">
                <CButton color="primary" onClick={()=> null}>
                  Nova pesquisa
                </CButton>
              </div>
            </CModalFooter> */}
          </CModalBody>
        )}
        </CModal>
    )
}

export default MudarClientePorTelefoneModal;
