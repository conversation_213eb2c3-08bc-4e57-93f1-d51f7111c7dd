import { CButton } from "@coreui/react";
import React, { useState } from "react";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import { formatCurrency, formatDate } from "src/reusable/helpers";
import { ApiResponse } from "src/types/common";

const PostData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), payload, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(
        getURI(endpoint),
        null,
        true,
        true,
        payload
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const CardParcelasAcordo = ({ dataParcelas }) => {
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");

  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");

  // const DownloadBoletos = async (contratosSelecionados) => {
  //   if (contratosSelecionados != null) {
  //     setLoading(true);
  //     setLoadingAction("VarifyParam");
  //     await GetData(`/${contratosSelecionados.id}/${contratosSelecionados.idAcordo}`, "cyberSafraObterBase64Boleto")
  //       .then((data: null | Blob | MediaSource) => {
  //         if (data !== undefined && data !== null) {
  //           const url = URL.createObjectURL(data);
  //           window.open(url, '_blank');
  //           setTitleAvisoLoading("Dados do Boleto Aberto Safra Gerado");
  //           setMsgAvisoLoading("Caso não abra uma segunda janela com o Boleto, verifique os Bloqueios de Pop-ups do navegador");
  //         } else {
  //           setTitleAvisoLoading("Não há boletos em aberto");
  //           setMsgAvisoLoading("");
  //         }
  //       }).catch((err) => {
  //         setTitleAvisoLoading("Não há boletos em aberto");
  //         setMsgAvisoLoading("");
  //       })
  //       .finally(() => {

  //         setTimeout(() => {
  //           setLoading(false);
  //           setLoadingAction("empty");
  //           setMsgAvisoLoading("");
  //           setTitleAvisoLoading("");
  //         }, 3000);
  //       });
  //   }
  // }

  // const gerearBoleto = async (item) => {
  //   setLoading(true);
  //   setLoadingAction("VarifyParam");
  //   setTitleAvisoLoading(`Solicitando boleto ao Safra`);
  //   setMsgAvisoLoading(`...`);
  //   await PostData(
  //     {
  //       idAcordo: dataParcelas.idAcordo.toString(),
  //       numeroParcela: item.numeroParcela.toString()
  //     }
  //     , "cyberSafraGerarBoleto")
  //     .then((data: ApiResponse<unknown>) => {
  //       if (data.success) {
  //         setTitleAvisoLoading(`Boleto Gerado com Sucesso`);
  //         setMsgAvisoLoading(`Boleto gerado com sucesso, clique no botão de download.`);
  //         DownloadBoletos(item);
  //       } else {
  //         setMsgAvisoLoading(`Boleto não Gerado`);
  //         setTitleAvisoLoading(data.message);
  //         setTimeout(() => {
  //           setLoading(false);
  //           setLoadingAction("empty");
  //           setMsgAvisoLoading("");
  //           setTitleAvisoLoading("");
  //         }, 3000);
  //       }
  //     })
  //     .catch((err) => {
  //       setTitleAvisoLoading(`Erro na chamada das APIS`);
  //       setMsgAvisoLoading(`Erro na chamada API de Gerar Boleto, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`);
  //     })
  // };

  // const limparAvisos = () => {
  //   setTimeout(() => {
  //     setLoading(false);
  //     setLoadingAction("empty");
  //     setMsgAvisoLoading("");
  //     setTitleAvisoLoading("");
  //   }, 3000);
  // }

  return (
    <div>
      {loading && loadingAction === "VarifyParam" ? (
        <CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />
      ) : (
        <div className="table-responsive">
          <table className="table">
            <thead>
              <tr>
                <th className="center-column">Parcela</th>
                <th>Data Vencimento</th>
                <th>Valor Parcela</th>
                <th>Valor Tarifa</th>
                {/* <th className="center-column">Ações</th> */}
              </tr>
            </thead>
            <tbody>
              {dataParcelas?.agreementSimulation?.installmentFlow?.map(
                (item, index) => {
                  return (
                    <tr key={index}>
                      <td style={{ textAlign: "center" }}>{item.nrParcela}</td>
                      <td>{formatDate(item.vencimento)}</td>
                      <td>{formatCurrency(item.valorParcela)}</td>
                      <td>{formatCurrency(item.valorTarifa)}</td>

                      {/* <td style={{ textAlign: 'center' }}>
                    {dataParcelas.status !== "Cancelado" ? <CButton
                      color="success"
                      onClick={() => gerearBoleto(item)}
                    ><i className="cil-file" /></CButton> : ""}
  
                  </td> */}
                    </tr>
                  );
                }
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default CardParcelasAcordo;
