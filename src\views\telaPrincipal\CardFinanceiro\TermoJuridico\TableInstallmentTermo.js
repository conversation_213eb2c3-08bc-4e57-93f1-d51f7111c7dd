import { formatDate, formatThousands } from "src/reusable/helpers";

const TableInstallmentTermo = ({
  columns,
  selectAll,
  selectedContract,
  selectedDate,
  contratosAtivos,
  tableData,
  handleSelectAll,
  handleChangeSelectContract,
  HandleInstallmentChange,
  calcTotalValue,
  handleCheckVencidas,
  parcelasVencidasChecked,
  showContractFilter = false,
}) => {
  const totalParcelasSelecionadas = (tableData) => {
    return tableData.filter((x) => x.parcelaSelecionada).length;
  };

  return (
    <div className="table-responsive" style={{ maxHeight: "300px" }}>
      <table className="table">
        <thead>
          <tr>
            {columns.map((column, key) => (
              <th key={key}>
                {column.label !== "" ? (
                  <div>{column.label}</div>
                ) : (
                  <div>
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={(e) => handleSelectAll(e)}
                    />
                  </div>
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          <tr>
            <td></td>
            {showContractFilter && (
              <td>
                <select
                  onChange={(e) => handleChangeSelectContract(e)}
                  value={selectedContract}
                >
                  <option value={""}>Todos</option>
                  {contratosAtivos
                    ?.filter((x) => {
                      return x.parcelas.find((y) => y.status === "A");
                    })
                    .map((item, key) => (
                      <option key={key}>{item.numero_Contrato}</option>
                    ))}
                </select>
              </td>
            )}
            <td>
              {handleCheckVencidas && (
                <div className="d-flex justify-content-start">
                  <input
                    type="checkbox"
                    className="mr-2"
                    checked={parcelasVencidasChecked}
                    onChange={(e) => handleCheckVencidas(e)}
                  />
                  Parcelas Vencidas
                </div>
              )}
            </td>
            <td colSpan={columns.length - 1}>
              <div className="d-flex justify-content-start">
                <strong>
                  Total de parcelas selecionadas:{" "}
                  {totalParcelasSelecionadas(tableData)}
                </strong>
              </div>
            </td>
          </tr>
          {tableData.map((item, key) => (
            <tr key={key}>
              {columns.map((column, key) => (
                <td key={key}>
                  {column?.formatter && column?.formatter(item[column.key])}

                  {column?.formatterByObject && column?.formatterByObject(item)}

                  {!column?.formatter &&
                    !column?.formatterByObject &&
                    item[column.key]}

                  {key === 0 && (
                    <input
                      type="checkbox"
                      checked={item.parcelaSelecionada}
                      onChange={(input) => HandleInstallmentChange(input, item)}
                    />
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TableInstallmentTermo;
