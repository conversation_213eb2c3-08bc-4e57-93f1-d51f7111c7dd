import { useEffect, useState } from "react";
import { CCard, CCardBody, CCol, CForm, CLabel, CRow, CFormGroup, CTooltip, CButton } from "@coreui/react";

import Select from "react-select";

import CardLoading from "src/reusable/CardLoading";
import { formatCurrency, formatDocument } from "src/reusable/helpers";
import TableSelectItens from "src/reusable/TableSelectItens";
import { getURI } from "src/config/apiConfig";
import { useAuth } from "src/auth/AuthContext";
import * as XLSX from "xlsx";

const Campaigns = () => {
  const token = localStorage.getItem("token");
  const [campaigns, setCampaigns] = useState([]);
  const [campaignSelected, setCampaignSelected] = useState([]);
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const { checkPermission } = useAuth();
  const permissao = {
    modulo: "Campanhas Safra",
  };

  const columns = [
    {
      key: "financedName",
      label: "Nome do Devedor",
    },
    {
      key: "document",
      label: "CPF/CNPJ",
      formatterByObject: (item) => formatDocument(item?.financedDocument ?? ""),
    },
    {
      key: "contract",
      label: "Contrato",
    },
    {
      key: "installmentAmount",
      label: "QTD de parcelas",
      formatterByObject: (item) => formatCurrency(item?.installmentAmount ?? 0, false),
    },
    {
      key: "installmentTotalValue",
      label: "Valor total das Parcelas",
      formatterByObject: (item) => formatCurrency(item?.installmentTotalValue, true),
    },
    {
      key: "statusSafra",
      label: "Acordo Safra",
      formatterByObject: (item) => formatAcordoSafra(item),
    },
    {
      key: "statusCrm",
      label: "Acordo Datacob",
      formatterByObject: (item) => formatAcordoDataCob(item),
    },
  ];

  const formatAcordoSafra = (item) => {
    return (
      <CTooltip content={item?.safraMessage}>
        <i
          className={`${iconStatusSafra(item)} m-3 p-3`}
          style={{ fontSize: "1.5rem" }}
        ></i>
      </CTooltip>);
  }

  const formatAcordoDataCob = (item) => {
    return (
      <CTooltip content={item?.datacobMessage}>
        <i
          className={`${iconStatusDatacob(
            item
          )} m-3 p-3`}
          style={{ fontSize: "1.5rem" }}
        ></i>
      </CTooltip>);
  }

  async function getCampaignList() {
    setIsLoading(true);
    const response = await fetch(`${getURI()}/SafraCampaign`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    if (response.ok) {
      const data = await response.json();
      setCampaigns(data.data);
    }
    setIsLoading(false);
  }

  const handleCampaignChange = async (campaign) => {
    setCampaignSelected(campaign);

    const listItems = campaign.itens.map((item) => {
      return item;
    });
    setData(listItems);
  };

  useEffect(() => {
    getCampaignList();
  }, []);

  const iconStatusSafra = (item) => {
    if (item?.safraStatus) return "cil-check-circle text-success";

    if (item?.safraMessage === "Aguardando Execução")
      return "cil-warning text-warning";

    return "cil-x-circle text-danger";
  };

  const iconStatusDatacob = (item) => {
    if (item?.datacobStatus) return "cil-check-circle text-success";

    if (item?.datacobMessage === "Aguardando Integração")
      return "cil-warning text-warning";

    return "cil-x-circle text-danger";
  };

  const exportToExcel = () => {
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
    XLSX.writeFile(workbook, "Campanhas_Selecionadas.xlsx");
  };

  return (
    <CCard>
      <CCardBody>
        {checkPermission(permissao.modulo, "View") ? (
          <>
            <CCol>
              {isLoading ? (
                <CardLoading />
              ) : (
                <>
                  <CRow>
                    <CCol md={8}>
                      <CForm>
                        <CFormGroup
                          style={{ display: "flex", alignItems: "center" }}
                        >
                          <CLabel className="mr-2">
                            Selecione um Campanha:
                          </CLabel>
                          <div className="flex-grow-1">
                            <Select
                              options={campaigns}
                              value={campaignSelected}
                              onChange={handleCampaignChange}
                              className="fixed-select"
                              placeholder="Selecione"
                              getOptionValue={(option) => option.id}
                              getOptionLabel={(option) => option.name}
                            />
                          </div>
                        </CFormGroup>
                      </CForm>
                    </CCol>
                    <CCol md={4}>
                      <CButton color="success" className="pl-4 pr-4" onClick={exportToExcel}>
                        Exportar Excel
                      </CButton>
                    </CCol>
                  </CRow>
                  <CRow>
                    <CCol>
                      <TableSelectItens
                        data={data}
                        columns={columns}
                        onSelectionChange={(_) => { }}
                        defaultSelectedKeys={[]}
                        selectable={false}
                        heightParam="100%"
                      />                      
                    </CCol>
                  </CRow>
                </>
              )}
            </CCol>
          </>
        ) : (
          "Usuário não possui permissão de visualização de campanhas."
        )}
      </CCardBody>
    </CCard>
  );
};

export default Campaigns;
