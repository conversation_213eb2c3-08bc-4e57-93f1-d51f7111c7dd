import React, { useState, useEffect } from "react";
import { formatCurrency,formatDate } from 'src/reusable/helpers';
/* {
    "nrParcela": 3,
    "vlrPaOrig": 1326.14,
    "vlrPaPrinc": 1326.14,
    "vlrPaMulta": 26.52,
    "vlrPaMora": 4.85,
    "vlrPaAtual": 2291.07,
    "vlrPaDtVenc": "2023-04-08"
} */
const CardParcelas = ({dataParcelas}) => {
  return (
    <div className="table-responsive">
      <table className='table'>
        <thead>
          <tr>
              <th>Parcela</th>
              <th>Data Vencimento</th>
              <th>Valor Parcela Original</th>
              <th>Valor <PERSON> Principal</th>
              <th>Valor Parcela <PERSON>lta</th>
              <th>Valor Parcela Mora</th>
              <th>Valor Parcela Atual</th>
          </tr>
        </thead>
        <tbody>
          {dataParcelas.map((item, index) => (
            <tr key={index}>
              <td>{item.nrParcela}</td>
              <td>{formatDate(item.vlrPaDtVenc)}</td>
              <td>{formatCurrency(item.vlrPaOrig)}</td>
              <td>{formatCurrency(item.vlrPaPrinc)}</td>
              <td>{formatCurrency(item.vlrPaMulta)}</td>
              <td>{formatCurrency(item.vlrPaMora)}</td>
              <td>{formatCurrency(item.vlrPaAtual)}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default CardParcelas;
