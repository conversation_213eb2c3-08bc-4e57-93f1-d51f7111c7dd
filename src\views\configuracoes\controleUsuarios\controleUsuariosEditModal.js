import React, { useEffect, useState } from "react";
import {
  CModal<PERSON>ooter,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CButton,
  CFormGroup,
  CLabel,
  CInput,
} from "@coreui/react";
import CardLoading from "src/reusable/CardLoading";
import { getApi, postApi } from "src/reusable/functions";
import { toast } from "react-toastify";
import Select from "react-select";
import { useAuth } from "src/auth/AuthContext";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";
import { postManager } from "src/reusable/helpers";

const ControleUsuariosEditModal = ({ isOpen, onClose, dataEdit = null }) => {
  const { user, authToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [bp, setBp] = useState(dataEdit?.bp);
  const [observacao, setObservacao] = useState(dataEdit?.observacao);
  const [idNegociacao, setIdNegociacao] = useState(dataEdit?.idNegociacao);

  const [selectCrm, setSelectCrm] = useState({
    datacobName: user?.activeConnection,
    datacobNumber: user?.activeConnection,
  });
  const [usersCrm, setUsersCrm] = useState([]);
  const [loadingUsersCrm, setLoadingUsersCrm] = useState(false);
  const [selectedUserCrm, setSelectedUserCrm] = useState(null);
  const [showInputDonoBoleto, setShowInputDonoBoleto] = useState(false);

  const handleObsevation = (e) => {
    if (e.target.value.length > 1000) return;
    setObservacao(e.target.value);
  };
  const handleBp = (e) => {
    const value = parseInt(e.target.value.replace(/[^0-9]/g, ""));
    setBp(value);
  };

  const getConfigUserShowDonoBoleto = async () => {
    try {
      const response = await GET_DATA(
        getURI("getConfigByKey"),
        null,
        true,
        true,
        "users_show_input_dono_boleto_controle_usuarios"
      );
      if (response !== "") {
        let listconfig = JSON.parse(response);
        return listconfig;
      }
    } catch (error) {
      return [];
    }
  };
  const mostrarInputDonoBoleto = async () => {
    const config = await getConfigUserShowDonoBoleto();

    if (user !== null) {
      if (config?.includes(user.username) || user?.username === "Admin") {
        setShowInputDonoBoleto(true);
      } else {
        setShowInputDonoBoleto(false);
      }
    }
  };

  useEffect(() => {
    mostrarInputDonoBoleto();
  }, [user]);

  const handleEditar = async () => {
    setLoading(true);
    const payload = {
      bP: bp,
      obs: observacao,
      ticketId: dataEdit?.idBoleto,
      userCrmId: dataEdit?.idUsuario,
      ticketNumber: dataEdit?.numeroBoleto,
      idNegotiation: idNegociacao,
      userTicketOwner: selectedUserCrm?.id_Usuario,
      crmUserBoletoOwner: selectedUserCrm?.crm,
      userUpdated: user?.id,
    };
    try {
      const res = await postApi(payload, "postControleUsuario");
      if (res?.success) {
        toast.success("Dados Editados com Sucesso!");
        onClose();
      } else {
        toast.error("Erro ao Editar!");
      }
    } catch (error) {
      console.log(error);
      toast.error("Erro ao Editar!");
    }
    setLoading(false);
  };
  const getUsersTu = async () => {
    setLoadingUsersCrm(true);

    const usersCrms = user?.datacobs || [];
    let listaUsers = [];

    try {
      // Aguarda todas as requisições assincronas
      const responses = await Promise.all(
        usersCrms.map(async (item) => {
          try {
            let res = await getApi(
              {
                activeConnection: item?.datacobName,
              },
              "getDatacobUsers"
            );
            return res
              .filter((x) => x.ativo === true)
              .map((x) => ({
                ...x,
                crm: item?.datacobName,
              }));
          } catch (error) {
            console.error(error);
            return [];
          }
        })
      );
      // Flatten do array de arrays
      listaUsers = responses.flat();
      const usersProcessed = await Promise.all(
        listaUsers.map(async (item) => {
          return {
            ...item,
            login: await postManager(authToken, item.login, 2),
          };
        })
      );

      // Ordena após todas as requisições
      const listaUserSort = [...usersProcessed].sort((a, b) =>
        a.nome.localeCompare(b.nome)
      );

      setUsersCrm(listaUserSort);
    } catch (e) {
      console.error(e);
    } finally {
      setLoadingUsersCrm(false);
    }
  };
  useEffect(() => {
    getUsersTu();
  }, [selectCrm]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>
        <CModalTitle>Edição</CModalTitle>
      </CModalHeader>
      {loading && (
        <CModalBody style={{ minHeight: "470px" }}>
          <CardLoading />{" "}
        </CModalBody>
      )}
      {!loading && (
        <CModalBody>
          <CFormGroup>
            <CLabel>BP </CLabel>
            <CInput value={bp} type="text" onChange={handleBp} />
            <CLabel>Observação</CLabel>
            <textarea
              rows={4}
              placeholder="Observação possuí um limite de 1000 caracteres"
              className="form-control"
              value={observacao}
              onChange={handleObsevation}
            ></textarea>
            <div style={{ display: showInputDonoBoleto ? "block" : "none" }}>
              {loadingUsersCrm ? (
                <div style={{ color: "black" }}>
                  <CardLoading />
                </div>
              ) : (
                <>
                  <CLabel>Mudar Dono do Boleto</CLabel> <br />
                  <Select
                    options={usersCrm}
                    value={selectedUserCrm}
                    getOptionLabel={(opt) =>
                      opt.nome + " (" + opt.login + ")" + " - " + opt.crm
                    }
                    getOptionValue={(opt) => opt.id_Usuario}
                    onChange={(e) => setSelectedUserCrm(e)}
                    placeholder={"Selecione"}
                  />
                </>
              )}
            </div>
          </CFormGroup>
        </CModalBody>
      )}
      <CModalFooter>
        <CButton disabled={loading} color="danger" onClick={onClose}>
          Cancelar
        </CButton>
        <CButton disabled={loading} color="info" onClick={handleEditar}>
          Salvar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default ControleUsuariosEditModal;
