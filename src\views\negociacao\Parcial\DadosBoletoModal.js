import {
  CButton,
  CCol,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CRow,
  CCard,
  CCardHeader,
  CCardBody,
  CLabel,
} from "@coreui/react";
import React from "react";
import {
  formatDate,
  formatThousands,
  formatDocument,
  leftPad,
} from "src/reusable/helpers";

const DadosBoletoModal = ({ isOpen, onClose, dados, parcelas }) => {
  const handleClose = () => {
    onClose();
  };

  const financaidoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const clientData = localStorage.getItem("clientData")
    ? JSON.parse(localStorage.getItem("clientData"))
    : null;

  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const getEndereco = () => {
    if (clientData?.enderecos === null || clientData?.enderecos.length === 0) {
      return null;
    }
    const end = clientData?.enderecos.find((item) => item.tipo_Status === 2);
    if (end) {
      const compl =
        end?.complemento !== null && end?.complemento !== ""
          ? end?.complemento + " - "
          : "";

      return (
        end?.logradouro +
        ", " +
        end?.numero +
        " - " +
        compl +
        end?.bairro +
        " - " +
        end?.cep +
        " - " +
        end?.cidade +
        ", " +
        end?.uf
      );
    }
    const endAtivo = clientData?.enderecos.find(
      (item) => item.tipo_Status === 1
    );
    if (endAtivo) {
      const comple =
        endAtivo?.complemento !== null && endAtivo?.complemento !== ""
          ? endAtivo?.complemento + " - "
          : "";
      return (
        endAtivo?.logradouro +
        ", " +
        endAtivo?.numero +
        " - " +
        comple +
        endAtivo?.bairro +
        " " +
        endAtivo?.cep +
        " - " +
        endAtivo?.cidade +
        ", " +
        endAtivo?.uf
      );
    }
    return null;
  };

  const getParcelas = () => {
    if (parcelas === null || parcelas.length === 0 || contratosAtivos === null)
      return null;

    let parcelasAtivas = [];
    contratosAtivos.map((item) =>
      item.parcelas.map((parcela) => {
        parcelasAtivas.push(parcela);
      })
    );

    parcelasAtivas = parcelasAtivas.filter(
      (item) => parcelas.indexOf(item.id_Parcela) > -1
    );

    return parcelasAtivas;
  };

  const dadosParcela = getParcelas();

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="lg">
      <CModalHeader>Dados do Boleto</CModalHeader>
      <CModalBody>
        <CRow>
          <CCol>
            <CCard>
              <CCardHeader>Valores</CCardHeader>
              <CCardBody>
                <CRow>
                  <CCol>
                    <CLabel>Valor Atualizado</CLabel> <br />
                    <CLabel>R$ {formatThousands(dados.vlNegociado)}</CLabel>
                  </CCol>
                  <CCol>
                    <CLabel>Taxa Bancária</CLabel> <br />
                    <CLabel>0,00</CLabel>
                  </CCol>
                </CRow>
                <CRow>
                  <CCol>
                    <CLabel>Total do Boleto</CLabel> <br />
                    <CLabel>R$ {formatThousands(dados.vlNegociado)}</CLabel>
                  </CCol>
                  <CCol>
                    <CLabel>Vencimento</CLabel> <br />
                    <CLabel>{formatDate(dados.dtNegociacao)}</CLabel>
                  </CCol>
                </CRow>
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CCard>
              <CCardHeader>Pagador</CCardHeader>
              <CCardBody>
                <CRow>
                  <CCol>
                    <CLabel>Nome:</CLabel> <br />
                    <CLabel>CPF / CNPJ:</CLabel>
                  </CCol>
                  <CCol>
                    <CLabel>{financaidoData?.nome}</CLabel> <br />
                    <CLabel>{formatDocument(financaidoData?.cpfCnpj)}</CLabel>
                  </CCol>
                </CRow>
                <CRow>
                  <CCol>
                    <CLabel>Endereço:</CLabel> <br />
                  </CCol>
                  <CCol>
                    <CLabel>{getEndereco()}</CLabel> <br />
                  </CCol>
                </CRow>
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CCard>
              <CCardHeader>Parcelas</CCardHeader>
              <CCardBody>
                <CRow>
                  <CCol>
                    <CLabel>Nr Contrato</CLabel> <br />
                    {dadosParcela.map((item) => (
                      <>
                        <CLabel key={item.id_Parcela}>
                          {item.numero_Contrato}
                        </CLabel>{" "}
                        <br />{" "}
                      </>
                    ))}
                  </CCol>
                  <CCol>
                    <CLabel>Nr Parcela</CLabel> <br />
                    {dadosParcela.map((item) => (
                      <>
                        <CLabel key={item.id_Parcela}>
                          {leftPad(item.nr_Parcela, 3, "0")}
                        </CLabel>{" "}
                        <br />{" "}
                      </>
                    ))}
                  </CCol>
                  <CCol>
                    <CLabel>Vencimento</CLabel> <br />
                    {dadosParcela.map((item) => (
                      <>
                        <CLabel key={item.id_Parcela}>
                          {formatDate(item.dt_Vencimento)}
                        </CLabel>{" "}
                        <br />{" "}
                      </>
                    ))}
                  </CCol>
                </CRow>
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CRow>
          <CCol className="d-flex justify-content-end">
            <CButton
              color="secondary"
              className="mr-2"
              onClick={() => handleClose()}
            >
              Fechar
            </CButton>
          </CCol>
        </CRow>
      </CModalFooter>
    </CModal>
  );
};

export default DadosBoletoModal;
