import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CFormGroup,
  CInput,
  CDataTable,
  CCard,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CCardBody,
  CLabel,
  CRow,
  CCol,
  CCardFooter,
} from "@coreui/react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import {
  formatThousands,
  formatDate,
  formatDateTime,
} from "src/reusable/helpers";
import CardLoading from "src/reusable/CardLoading";
import TableSelectItens from "src/reusable/TableSelectItens";

const AgendaModal = ({ isOpen, onClose, dados }) => {
  const [data, setData] = useState(null);
  const [mockData, setMockData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [resultMessage, setResultMessage] = useState("");

  const fields = [
    { key: "nM_Evento", label: "Evento", formatter: (item) => item ?? "---" },
    {
      key: "dh_Inclusao",
      label: "Inclusão",
      formatter: (item) => (item ? formatDateTime(item) : "---"),
    },
    {
      key: "dh_Encerramento",
      label: "Encerramento",
      formatter: (item) => (item ? formatDateTime(item) : "---"),
    },
    { key: "nM_Motivo", label: "Motivo", formatter: (item) => item ?? "---" },
    { key: "usuario", label: "Usuário" },
    {
      key: "nm_Ocorrencia",
      label: "Ocorrência",
      formatter: (item) => item ?? "---",
    },
    { key: "ocorrencia", label: "Obs.", formatter: (item) => item ?? "---" },
  ];

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      updateView();
    }
  }, [isOpen]);

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const updateView = () => {
    const Protocol = dados.idProtocol;
    setIsLoading(true);
    GetData(`/${Protocol}`, "postNewconAgendaProtocol")
      .then((data) => {
        if (data) {
          setData(data);
        } else {
          setResultMessage(
            "Nenhuma ocorrência encontrada para esse protocolo."
          );
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="xl"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Agenda - Protocolo</h5>
      </CModalHeader>
      {isLoading ? (
        <CardLoading />
      ) : (
        <CModalBody>
          {data ? (
            <TableSelectItens
              data={data}
              columns={fields}
              onSelectionChange={(_) => {}}
              defaultSelectedKeys={[]}
              selectable={false}
              heightParam="290px"
            />
          ) : (
            <div style={{ textAlign: "center", margin: "24 0" }}>
              {resultMessage}
            </div>
          )}
        </CModalBody>
      )}
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default AgendaModal;
