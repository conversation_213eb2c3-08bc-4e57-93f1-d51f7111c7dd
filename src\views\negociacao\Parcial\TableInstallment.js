import { CInputCheckbox } from "@coreui/react";
import { useEffect } from "react";
import { formatDate, formatThousands } from "src/reusable/helpers";

const TableInstallment = ({
  columns,
  selectAll,
  selectedContract,
  selectedDate,
  contratosAtivos,
  tableData,
  handleSelectAll,
  handleChangeSelectContract,
  HandleInstallmentChange,
  calcTotalValue,
  handleCheckVencidas,
  parcelasVencidasChecked,
  showContractFilter = false
}) => {
  
  const totalParcelasSelecionadas = (tableData) => {
    return tableData.filter((x => x.parcelaSelecionada)).length;
  } 

  return (
    <div className="table-responsive">
      <table className="table">
        <thead>
          <tr>
            {columns.map((column, key) => (
              <th key={key}>
                {column.label !== "" ? (
                  <div>{column.label}</div>
                ) : (
                  <div>
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={(e) => handleSelectAll(e)}
                    />
                  </div>
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          <tr>
            <td></td>
            {showContractFilter && (
              <td>
                  <select
                    onChange={(e) => handleChangeSelectContract(e)}
                    value={selectedContract}
                  >
                    <option value={""}>Todos</option>
                    {contratosAtivos
                      ?.filter((x) => {
                        return x.parcelas.find((y) => y.status === "A");
                      })
                      .map((item, key) => (
                        <option key={key}>{item.numero_Contrato}</option>
                      ))}
                  </select>
              </td>
            )}
            <td>
              {handleCheckVencidas && (
                <div className="d-flex justify-content-start">
                  <input
                    type="checkbox"
                    className="mr-2"
                    checked={parcelasVencidasChecked}
                    onChange={(e) => handleCheckVencidas(e)}
                  />
                  Parcelas Vencidas
                </div>
              )}
            </td>
            <td colSpan={columns.length - 1}>
              <div className="d-flex justify-content-start">
                <strong>
                  Total de parcelas selecionadas:{" "}
                  {totalParcelasSelecionadas(tableData)}
                </strong>
              </div>
            </td>
          </tr>
          {tableData
            .filter((x) => {
              if (selectedContract === "") return true;
              return (
                x.nrContrato.replaceAll(" ", "") ===
                selectedContract.replaceAll(" ", "")
              );
            })
            .map((item, key) => (
              <tr key={key}>
                <td>
                  <input
                    type="checkbox"
                    checked={item.parcelaSelecionada}
                    onChange={(input) => HandleInstallmentChange(input, item)}
                  />
                </td>
                <td style={{ textWrap: "nowrap" }}>{item.nrContrato}</td>
                <td>{item.nrParcela}</td>
                <td>{item.nrPlano}</td>
                <td>{item.nome_Tipo_Parcela}</td>
                <td>{formatDate(item.dtVencimento)}</td>
                <td>{formatThousands(item.vlSaldo)}</td>
                <td>{formatThousands(calcTotalValue(item), false)}</td>
                <td>{formatThousands(item.vlAtualizado, false)}</td>
                <td>
                  {item.dt_Pgto ?? selectedDate
                    ? formatDate(selectedDate)
                    : "---"}
                </td>
                <td>{item.atraso}</td>
              </tr>
            ))}
        </tbody>
      </table>
    </div>
  );
};

export default TableInstallment;
