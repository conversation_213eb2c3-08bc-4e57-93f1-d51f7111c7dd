import React, { useEffect, useState } from "react";
import { formatCurrency } from "src/reusable/helpers";
import "src/scss/negociacaoRegua.css";

const NegociacaoBar = ({
  vlNegociacao = 100.0,
  vlMinimoNegociacao = 50.0,
  onValueChange,
  atualizarValor = 0,
}) => {
  const [discount, setDiscount] = useState(100); // Inicia sem desconto
  const [valorComDesconto, setValorComDesconto] = useState(vlNegociacao);
  const [valorDescontoAutorizado, setValorDescontoAutorizado] = useState(
    vlNegociacao - vlMinimoNegociacao
  );

  const handleChange = (valor, notifica = true) => {
    const newDiscount = parseFloat(valor);
    setDiscount(newDiscount);
    // Calcula o novo valor com o desconto aplicado, mas não inferior ao mínimo
    let novoValorComDesconto = Math.max(
      0,
      vlNegociacao - (vlNegociacao * (newDiscount / 100) - vlNegociacao) * -1
    );
    if (valorDescontoAutorizado > 0)
      novoValorComDesconto = Math.max(
        0,
        vlNegociacao -
          (valorDescontoAutorizado * (newDiscount / 100) -
            valorDescontoAutorizado) *
            -1
      );

    if (notifica) {
      setValorComDesconto(novoValorComDesconto);
      onValueChange(novoValorComDesconto);
    } else setValorComDesconto(atualizarValor);
  };
  useEffect(() => {
    if (atualizarValor > 0) {
      if (atualizarValor < vlMinimoNegociacao) {
        const newNewDiscount = Math.round(
          (atualizarValor / vlMinimoNegociacao) * 100
        );
        handleChange(newNewDiscount, false);
      } else {
        const diff = atualizarValor - vlMinimoNegociacao;
        const newDiscount = Math.round(
          (diff / (vlNegociacao - vlMinimoNegociacao)) * 100
        );
        if (isNaN(newDiscount)) {
          handleChange(100, false);
        } else {
          handleChange(newDiscount, false);
        }
      }
    }
  }, [atualizarValor]);

  useEffect(() => {
    // setDiscount(100); // Inicializa o desconto como 0%
    setValorComDesconto(atualizarValor); // Inicializa o valor com desconto
    setValorDescontoAutorizado(vlNegociacao - vlMinimoNegociacao); // Inicializa o valor de desconto autorizado
  }, [vlNegociacao, vlMinimoNegociacao]);

  // Posição do balão do valor negociado
  const negociadoBubblePosition = {
    left: `calc(${discount > 100 ? 100 : discount}% - 50px)`, // Move conforme o desconto é aplicado
  };
  const positionBubbleMinimum = valorDescontoAutorizado > 0 ? 1.5 : 100;
  // const positionBubbleMinimum =
  //   valorDescontoAutorizado > 0
  //     ? 100 - (vlMinimoNegociacao / vlNegociacao) * 100
  //     : 100;

  // Posição fixa para o balão do valor mínimo
  const minimoBubblePosition = {
    left: `calc(${positionBubbleMinimum}%)`, // Posiciona de acordo com o valor mínimo
  };

  // Estilo condicional para maxValueBubble
  const maxValueBubbleStyle =
    valorComDesconto < vlMinimoNegociacao
      ? {
          ...negociadoBubblePosition,
          background: "linear-gradient(to right, #FF6347, #FF0000)", // Gradiente vermelho
          color: "white", // Cor do texto branco
        }
      : negociadoBubblePosition;

  return (
    <div className={"sliderContainer"}>
      <div
        className={"filledBefore"}
        style={{ width: `${positionBubbleMinimum}%` }}
      />{" "}
      {/* Preenche de acordo com o desconto */}
      <div className={"filledFull"} style={{ width: "100%" }} />
      <input
        id="regua"
        type="range"
        min={0}
        max={100}
        value={discount}
        onChange={(e) => handleChange(e.target.value)}
        className={"slider"}
      />
      <div className={"maxValueBubble"} style={maxValueBubbleStyle}>
        {formatCurrency(valorComDesconto)}
      </div>
      <div className={"valueBubble"} style={minimoBubblePosition}>
        {formatCurrency(vlMinimoNegociacao)}
      </div>
    </div>
  );
};

export default NegociacaoBar;
