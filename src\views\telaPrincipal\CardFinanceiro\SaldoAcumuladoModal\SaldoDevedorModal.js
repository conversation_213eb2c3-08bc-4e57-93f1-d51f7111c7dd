import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CCard,
  CModalHeader,
  CModalBody,
  CCardBody,
  CLabel,
  CRow,
  CCol,
  CNav,
  CNavItem,
  CNavLink,
  CTabContent,
  CTabPane,
  CTabs,
  CModalFooter,
} from "@coreui/react";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";
import { formatThousands } from "src/reusable/helpers";
import CardLoading from "src/reusable/CardLoading";

const SaldoDevedorModal = ({ isOpen, onClose, idCota }) => {
  const [dados, setDados] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [headerTableData, setHeaderTableData] = useState(null);
  const [tableData, setTableData] = useState(null);
  const [data, setData] = useState(null);
  const [firstRowData, setFirstRowData] = useState([]);
  const [secondRowData, setSecondRowData] = useState([]);
  const [thirdRowData, setThirdRowData] = useState([]);
  const [fourthRowData, setFourthRowData] = useState([]);

  const tabs = [
    {
      id: 1,
      label: "Valores Pagos",
      icon: "cil-calendar-check",
      content: (
        <CRow>
          <CCol md="5">
            {firstRowData &&
              firstRowData.map((row) => (
                <CRow>
                  <CCol>
                    <CLabel>{row.label}</CLabel>
                  </CCol>
                  <CCol>
                    <span>{row.value ? row.value : "---"}</span>
                  </CCol>
                </CRow>
              ))}
          </CCol>
          <CCol>
            {secondRowData &&
              secondRowData.map((row) => (
                <>
                  <CRow>
                    <CCol md="5">
                      <CLabel>{row.label}</CLabel>{" "}
                    </CCol>
                    <CCol>
                      {" "}
                      <span>{row.value ? row.value : "---"}</span>
                    </CCol>
                  </CRow>
                </>
              ))}
          </CCol>
        </CRow>
      ),
    },
    {
      id: 2,
      label: "Valores a Pagar",
      icon: "cil-dollar",
      content: (
        <>
          <CRow>
            <CCol md="5">
              {thirdRowData &&
                thirdRowData.map((row) => (
                  <CRow>
                    <CCol>
                      <CLabel>{row.label}</CLabel>
                    </CCol>
                    <CCol>
                      {" "}
                      <span>{row.value ? row.value : "---"}</span>
                    </CCol>
                  </CRow>
                ))}
            </CCol>
            <CCol>
              {fourthRowData &&
                fourthRowData.map((row) => (
                  <>
                    <CRow>
                      <CCol md="5">
                        <CLabel>{row.label}</CLabel>{" "}
                      </CCol>
                      <CCol>
                        {" "}
                        <span>{row.value ? row.value : "---"}</span>
                      </CCol>
                    </CRow>
                  </>
                ))}
            </CCol>
          </CRow>
        </>
      ),
    },
  ];

  const [currentTab, setCurrentTab] = useState(tabs[0].label);

  const handleTabSelect = (tab) => {
    setCurrentTab(tab.label);
  };

  const handleClose = () => {
    onClose();
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      GetData(`/${idCota}`, "getNewconSaldoDevedor")
        .then((result) => {
          const modalData = [
            {
              differencePerc: result.differencePerc,
              idealDuePerc: result.idealDuePerc,
              idealPaidPerc: result.idealPaidPerc,
              monthlyPerc: result.monthlyPerc,
            },
          ];

          // const modalData = result.map((x) => ({
          //   differencePerc: x.differencePerc,
          //   idealDuePerc: x.idealDuePerc,
          //   idealPaidPerc: x.idealPaidPerc,
          //   monthlyPerc: x.monthlyPerc,
          // }));

          setData(modalData[0]);
          setHeaderTableData(result);
          if (result) {
            const data = result.data;
            setFirstRowData([
              {
                label: "Fundo Comum",
                value:
                  formatThousands(result.paidAmounts.commonFund) +
                  " - " +
                  result.paidAmounts.commonFundPerc +
                  "%",
              },
              {
                label: "Fundo Reserva",
                value:
                  formatThousands(result.paidAmounts.reserveFund) +
                  " - " +
                  result.paidAmounts.reserveFundPerc +
                  "%",
              },
              {
                label: "Taxa de Administração",
                value:
                  formatThousands(result.paidAmounts.administrationFee) +
                  " - " +
                  result.paidAmounts.administrationFeePerc +
                  "%",
              },
              {
                label: "Adesão",
                value:
                  formatThousands(result.paidAmounts.accession) +
                  " - " +
                  result.paidAmounts.accessionPerc +
                  "%",
              },
              {
                label: "Seguros",
                value: formatThousands(result.paidAmounts.insurance),
              },
              {
                label: "Multa",
                value: formatThousands(result.paidAmounts.penalty),
              },
              {
                label: "Juros",
                value: formatThousands(result.paidAmounts.fees),
              },
            ]);
            setSecondRowData([
              {
                label: "Reajuste Saldo Caixa",
                value:
                  formatThousands(result.paidAmounts.cashBalanceAdjustment) +
                  " - " +
                  result.paidAmounts.cashBalanceAdjustmentPerc +
                  "%",
              },
              {
                label: "Fundo Reserva",
                value:
                  formatThousands(
                    result.paidAmounts.administrationFeeCashBalanceAdjustment
                  ) +
                  " - " +
                  result.paidAmounts
                    .administrationFeeCashBalanceAdjustmentPerc +
                  "%",
              },
              {
                label: "TOTAL",
                value: formatThousands(result.paidAmounts.total),
              },
              {
                label: "Outros Valores",
                value: formatThousands(result.paidAmounts.other),
              },
              {
                label: "Impostos",
                value: formatThousands(result.paidAmounts.tax),
              },
              {
                label: "Parcelas Rateadas/Pagas",
                value:
                  result.paidAmounts.proratedInstallments +
                  " - " +
                  formatThousands(result.paidAmounts.paidInstallments),
              },
            ]);
            setThirdRowData([
              {
                label: "Fundo Comum",
                value:
                  formatThousands(result.payableAmounts.commonFund) +
                  " - " +
                  result.payableAmounts.commonFundPerc +
                  "%",
              },
              {
                label: "Fundo Reserva",
                value:
                  formatThousands(result.payableAmounts.reserveFund) +
                  " - " +
                  result.payableAmounts.reserveFundPerc +
                  "%",
              },
              {
                label: "Taxa de Administração",
                value:
                  formatThousands(result.payableAmounts.administrationFee) +
                  " - " +
                  result.payableAmounts.administrationFeePerc +
                  "%",
              },
              {
                label: "Adesão",
                value:
                  formatThousands(result.payableAmounts.accession) +
                  " - " +
                  result.payableAmounts.accessionPerc +
                  "%",
              },
              {
                label: "Seguros",
                value:
                  formatThousands(result.payableAmounts.insurance) +
                  " - " +
                  result.payableAmounts.insurancePerc +
                  "%",
              },
              {
                label: "Multa",
                value:
                  formatThousands(result.payableAmounts.penalty) +
                  " - " +
                  result.payableAmounts.penaltyPerc +
                  "%",
              },
              {
                label: "Juros",
                value:
                  formatThousands(result.payableAmounts.fees) +
                  " - " +
                  result.payableAmounts.feesPerc +
                  "%",
              },
            ]);
            setFourthRowData([
              {
                label: "Diferença de Parcela",
                value:
                  formatThousands(result.payableAmounts.installmentDifference) +
                  " - " +
                  result.payableAmounts.installmentDifferencePerc +
                  "%",
              },
              {
                label: "Outros Valores",
                value:
                  formatThousands(result.payableAmounts.other) +
                  " - " +
                  result.payableAmounts.otherPerc +
                  "%",
              },

              {
                label: "TOTAL",
                value: formatThousands(result.payableAmounts.total),
              },
              {
                label: "Parcelas",
                value:
                  result.payableAmounts.installments +
                  " - " +
                  formatThousands(result.payableAmounts.installmentsValue),
              },
            ]);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="xl"
      className="saldo-devedor"
    >
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Saldo Devedor</h5>
      </CModalHeader>
      {isLoading ? (
        <CardLoading />
      ) : (
        <CModalBody>
          {headerTableData && (
            <CRow style={{ alignItems: "end" }}>
              <CCol style={{ paddingBottom: "24px" }}>
                <p className="saldo-p"></p>
                <p className="saldo-p">% Normal</p>
                <p className="saldo-p">% Antecipado</p>
                <p className="saldo-p">TOTAL:</p>
              </CCol>
              <CCol className="px-1">
                <CCard className="colored-card-green">
                  <CCardBody>
                    <p className="title-pg">Pago</p>
                    <p>{headerTableData.normal.paid ?? "0,00"}</p>
                    <p>{headerTableData.advance.paid ?? "0,00"}</p>
                    <p>{headerTableData.total.paid ?? "0,00"}</p>
                  </CCardBody>
                </CCard>
              </CCol>
              <CCol className="px-1">
                <CCard className="colored-card-red">
                  <CCardBody>
                    <p className="title-pr">Atraso</p>
                    <p>{headerTableData.normal.delay ?? "0,00"}</p>
                    <p>{headerTableData.advance.delay ?? "0,00"}</p>
                    <p>{headerTableData.total.delay ?? "0,00"}</p>
                  </CCardBody>
                </CCard>
              </CCol>
              <CCol className="px-1">
                <CCard className="colored-card-yellow">
                  <CCardBody>
                    <p className="title-py">A cobrar</p>
                    <p>{headerTableData.normal.collect ?? "0,00"}</p>
                    <p>{headerTableData.advance.collect ?? "0,00"}</p>
                    <p>{headerTableData.total.collect ?? "0,00"}</p>
                  </CCardBody>
                </CCard>
              </CCol>
              <CCol className="px-1">
                <CCard className="colored-card-gray">
                  <CCardBody>
                    <p className="title-pw">Total</p>
                    <p>{headerTableData.normal.total ?? "0,00"}</p>
                    <p>{headerTableData.advance.total ?? "0,00"}</p>
                    <p>{headerTableData.total.total ?? "0,00"}</p>
                  </CCardBody>
                </CCard>
              </CCol>
              <CCol></CCol>
              <CCol style={{ paddingBottom: "24px" }}>
                <p className="saldo-p">% Mensal</p>
                <p className="saldo-p">% Ideal Pago</p>
                <p className="saldo-p">% Diferença</p>
                <p className="saldo-p">% Ideal Devido</p>
              </CCol>
              <CCol>
                <CCard className="colored-card-gray">
                  <CCardBody>
                    <p>{headerTableData.monthlyPerc ?? "0,00"}</p>
                    <p>{headerTableData.idealPaidPerc ?? "0,00"}</p>
                    <p>{headerTableData.differencePerc ?? "0,00"}</p>
                    <p>{headerTableData.idealDuePerc ?? "0,00"}</p>
                  </CCardBody>
                </CCard>
              </CCol>
            </CRow>
          )}

          <CTabs onSelect={handleTabSelect} activeTab={tabs[0].label}>
            <CNav className="custom-nav">
              {tabs.map((tab) => (
                <CNavItem
                  key={tab.id}
                  className={currentTab === tab.label ? "" : "nonactive-tab"}
                >
                  <CNavLink
                    data-tab={tab.label}
                    onClick={() => handleTabSelect(tab)}
                  >
                    <i className={tab.icon} /> {tab.label}
                  </CNavLink>
                </CNavItem>
              ))}
            </CNav>
            <CTabContent className="px-3 overflow-auto">
              {tabs.map((tab) => (
                <CTabPane key={tab.id} data-tab={tab.label}>
                  {tab.content}
                </CTabPane>
              ))}
            </CTabContent>
          </CTabs>
          <CModalFooter>
            <CButton color="secondary" className="mr-2" onClick={handleClose}>
              Fechar
            </CButton>
          </CModalFooter>
        </CModalBody>
      )}
    </CModal>
  );
};

export default SaldoDevedorModal;
