import { <PERSON>ard<PERSON><PERSON>le, <PERSON>ol, CLabel, CRow } from "@coreui/react";
import React from "react";
import { formatCurrency, formatDate } from "src/reusable/helpers";
import CardCalcular from "./CardCalcular.tsx";
import TableParcelasSimulacao from "./TableParcelasSimulacao.tsx";
import { FinanciadoData, useBBCContext } from "../pageContext/BBCContext.tsx";

const DadosDaSimulacaoSimulacao = () => {
  const BBCContext = useBBCContext();
  const dadosFinanciado: FinanciadoData = BBCContext.financiadoData;
  const simulacao = BBCContext.contratoNegociar.dadosBBC.simulacao;

  const entrada = formatCurrency(simulacao["fluxoParcelas"][0]?.valorParcela);
  const dataEntrada = formatDate(simulacao["data1Vcto"]);
  const financiado = dadosFinanciado.nome + " - " + dadosFinanciado.cpfCnpj;
  //const contrato = simulacao.dadosSafra.contrato;
  const valorParcelas = formatCurrency(simulacao["valorParcela"]);
  const qtdParcelas = simulacao["qtdeParcelas"];

  return (
    <div>
      <CRow>
        <CCol xs="7">
          <CRow>
            <CCol xs="6" sm="12">
              <CLabel style={{ color: "gray" }}>Financiado</CLabel> <br />
              <CLabel>
                <strong>{financiado}</strong>
              </CLabel>
            </CCol>
          </CRow>
          <CRow>
            <CCol xs="6" sm="3">
              <CLabel style={{ color: "gray" }}>Entrada</CLabel> <br />
              <CLabel>
                <strong>{entrada}</strong>
              </CLabel>
            </CCol>
            <CCol xs="6" sm="3">
              <CLabel style={{ color: "gray" }}>Data da entrada</CLabel> <br />
              <CLabel>
                <strong>{dataEntrada}</strong>
              </CLabel>
            </CCol>
            <CCol xs="6" sm="3">
              <CLabel style={{ color: "gray" }}>Parcelamento</CLabel> <br />
              <CLabel>
                <strong>{qtdParcelas}</strong>
              </CLabel>
            </CCol>
            <CCol xs="6" sm="3">
              <CLabel style={{ color: "gray" }}>Valor das Parcelas</CLabel>{" "}
              <br />
              <CLabel>
                <strong>{valorParcelas}</strong>
              </CLabel>
            </CCol>
          </CRow>

          <CRow>
            <CCol>
              <CCardTitle style={{ fontSize: "1.2rem", marginTop: "15px" }}>
                <strong>Parcelamento do Acordo</strong>
              </CCardTitle>

              <TableParcelasSimulacao dataTable={simulacao["fluxoParcelas"]} />
            </CCol>
          </CRow>
        </CCol>
        <CCol xs="5">
          {/* <CardCalcular contratoNegociar={simulacao} /> */}
          <CardCalcular />
        </CCol>
      </CRow>
    </div>
  );
};

export default DadosDaSimulacaoSimulacao;
