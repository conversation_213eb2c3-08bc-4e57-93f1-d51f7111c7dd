import React, { useState, useEffect } from "react";
import {
  <PERSON>ard,
  CCardBody,
  CCol,
  CDataTable,
  CRow,
  CButton,
  CCardHeader,
  CInput,
  CTabContent,
  CTabPane,
  CNavLink,
  CTabs,
  CNavItem,
  CNav,
  CLabel,
  CInputCheckbox,
  CTooltip,
  CBadge,
} from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";
import { GET_DATA } from "src/api";
import Select from "react-select";

const NegociacaoParcelada = () => {
  const negociacoes = localStorage.getItem("negociacoes")
    ? JSON.parse(localStorage.getItem("negociacoes"))
    : "";

  const financiadoData = useState(localStorage.getItem("financiadoData") ? JSON.parse(localStorage.getItem("financiadoData")) : null);
  const [acordosData, setAcordosData] = useState(null);
  const [parcelasData, setParcelasData] = useState(null);

  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState("");

  const [idParcela, setIdParcela] = useState(null);
  const [idBoleto, setIdBoleto] = useState(null);

  const [tableBoletos, setTableBoletos] = useState([]);
  const [tableRecibos, setTableRecibos] = useState(null);
  const [tableNegociacoes, setTableNegociacoes] = useState(null);

  const [showModalDetalhesBoleto, setShowModalDetalhesBoleto] = useState(false);
  const [showModalDetalhesRecibo, setShowModalDetalhesRecibo] = useState(false);
  const [showModalCancelarBoleto, setShowModalCancelarBoleto] = useState(false);
  const [detalhesBoleto, setDetalhesBoleto] = useState(null);
  const [detalhesRecibo, setDetalhesRecibo] = useState(null);

  // Options for the status filter select
  // const statusOptions = [
  //   { value: "", label: "Todos" },
  //   { value: "P", label: "Pago" },
  //   { value: "A", label: "Aberto" },
  //   { value: "C", label: "Cancelada" },
  // ];

  // Handle status filter change
  const handleStatusChange = (selectedOption) => {
    setSelectedStatus(selectedOption.value);
  };

  //   const negociacoesAvalista = JSON.parse(localStorage.getItem("avalistasTerceiros"))
  //     ? JSON.parse(localStorage.getItem("avalistasTerceiros"))
  //     : null;

  // const acordosColumns = [
  //   { key: "id_Negociacao", label: "ID" },
  //   { key: "status" },
  //   { key: "origem" },
  //   { key: "qtde_Parcela", label: "Qtd. Parecelas" },
  //   { key: "vl_Total_Original", label: "Valor Original" },
  //   { key: "vl_Saldo_Acordo", label: "Valor Saldo" },
  //   { key: "situacao" , label:"Situação" },
  //   { key: "dt_Liberacao", label: "Data Liberação" },
  //   { key: "detalhes" },
  //   { key: "nr_Acordo_Banco", label: "Nr. Acordo Banco" },
  //   { key: "negociacao", label: "Negociação" },
  // ];

  const parcelasColumns = [
    { key: "nr_Parcela", label: "Parcela" },
    { key: "nr_Plano", label: "Plano" },
    { key: "dt_Vencimento", label: "Dt. Vencimento" },
    { key: "vl_Saldo", label: "Valor Saldo" },
    { key: "vl_Atualizado", label: "Valor Atualizado" },
    { key: "vl_Pago", label: "Valor Pago" },
    { key: "dt_Pagamento", label: "Dt. Pagamento." },
    { key: "atraso", label: "Atraso" },
    { key: "dt_Atualizacao", label: "Dt. Atualização" },
    { key: "dt_Venc_Boleto", label: "Dt. Vencimento Boleto" },
    { key: "linha_Digitavel", label: "Linha Digitável" },
    { key: "nr_Boleto", label: "Nosso Número" },
    { key: "url_Boleto", label: "URL Boleto" },
    { key: "vl_Honor", label: "Valor Honorário" },
  ];

  async function getParcelas(id_Negociacao) {
    const data = { IdNegociacao: id_Negociacao, numeroContrato: financiadoData.numero_Contrato };
    const parcelas = await GET_DATA("Datacob/Negociacoes/Parcelas", data);
    setParcelasData(parcelas);
    // return parcelas;
  }

  // const handleClick = async (item) => {
  //   const parcelas = await getParcelas(item.id_Acordo);
  //   setParcelasData(parcelas);
  //   setSelectedRow(item.id_Acordo);
  // };

  const renderBadge = (status) => {
    switch (status) {
      case "P":
        return (
          <td>
            <CBadge color="success">Pago</CBadge>
          </td>
        );
      case "A":
        return (
          <td>
            <CBadge color="info">Aberto</CBadge>
          </td>
        );
      case "C":
        return (
          <td>
            <CBadge color="danger">Cancelado</CBadge>
          </td>
        );
      default:
        break;
    }
  };

  const rowClassName = (item) => {
    return item.id_Negociacao === selectedRow ? "selected-row" : "";
  };

  async function getDadosParcelas(id_Negociacao) {
    const data = { IdNegociacao: id_Negociacao, numeroContrato: financiadoData.numero_Contrato };
    const boletos = await GET_DATA("Datacob/Negociacoes/Parcelas/Boleto", data);
    const recibos = await GET_DATA("Datacob/Negociacoes/Parcelas/Recibo", data);

    setTableBoletos(boletos);
    if (!recibos) {
      setTableRecibos([]);
    } else {
      setTableRecibos(recibos);
    }
    // setIdBoleto(boletos[0].id_Boleto);
    setCurrentTab("Boletos");
    return;
  }

  // async function getParcelas() {
  //   const data = { IdAcordo: negociacoes[0].id_Acordo };
  //   const parcelas = await GET_DATA("Datacob/Acordos/Parcelas", data);
  //   setParcelasData(parcelas);
  // }

  const renderStatus = (status) => {
    switch (status) {
      case "P":
        return <td>Pago</td>;
      case "A":
        return <td>Aberto</td>;
      case "C":
        return <td>Cancelado</td>;
      default:
        break;
    }
  };

  // useEffect(() => {
  //   if (acordos) {
  //     const filteredData = acordos.filter((item) => {
  //       const matchesStatus = !selectedStatus || item.status === selectedStatus;
  //       return matchesStatus;
  //     });
  //     setAcordosData(filteredData);
  //   }
  // }, [selectedStatus]);

  const boletosFields = [
    { key: "detalhes" },
    { key: "visualizar" },
    { key: "nr_Boleto" },
    { key: "dt_Venc", label: "Vencimento" },
    {
      key: "vl_Boleto",
      label: "Valor Boleto",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "status" },
    { key: "boleto_Emitido" },
    {
      key: "dt_Pago",
      label: "Data Pagamento",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "vl_Pago", label: "Valor Pago", _style: { whiteSpace: "nowrap" } },
    { key: "dt_Inco" },
    { key: "codigo" },
  ];

  const recibosFields = [
    { key: "detalhes" },
    { key: "nr_Recibo" },
    { key: "nr_boleto" },
    { key: "recibo_Sobre" },
    { key: "status" },
    { key: "vl_Pago", label: "Valor Pago", _style: { whiteSpace: "nowrap" } },
    {
      key: "dt_Emiss",
      label: "Data Emissão",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dt_Pagto",
      label: "Data Pagamento",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "descricao_Motivo_Cancel", label: "Motivo Cancelamento" },
  ];

  const negociacaoFields = [
    { key: "status" },
    { key: "liberado" },
    { key: "descricao" },
    {
      key: "dt_Negociacao",
      label: "Data Negociação",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "vl_Negociacao",
      label: "Valor Negociação",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "negociacao_Enviada" },
    {
      key: "dt_Cadastro_Negociacao",
      label: "Data Inclusão",
      _style: { whiteSpace: "nowrap" },
    },
  ];

  const tabs = [
    {
      id: 1,
      label: "Boletos",
      icon: "cil-barcode",
      content: (
        <div>
          <CDataTable
            items={tableBoletos}
            fields={boletosFields}
            hover
            responsive
            scopedSlots={{
              detalhes: (item) => (
                <td>
                  <CButton
                    className="button-link py-0 px-0"
                  // onClick={() => handleDetalhesBoleto(item)}
                  >
                    Detalhes
                  </CButton>
                </td>
              ),
              visualizar: (item) => (
                <td>
                  <CButton
                    className="button-link py-0 px-0"
                  // onClick={() => handleVisualizarBoleto(item)}
                  >
                    Visualizar
                  </CButton>
                </td>
              ),
              nr_Boleto: (item) => (
                <td className="nowrap-cell">{item.nr_Boleto}</td>
              ),
              dt_Venc: (item) =>
                item.dt_Venc ? (
                  <td>{formatDate(item.dt_Venc)}</td>
                ) : (
                  <td>---</td>
                ),
              dt_Pago: (item) =>
                item.dt_Pago ? (
                  <td>{formatDate(item.dt_Pago)}</td>
                ) : (
                  <td>---</td>
                ),
              dt_Inco: (item) =>
                item.dt_Inco ? (
                  <td>{formatDate(item.dt_Inco)}</td>
                ) : (
                  <td>---</td>
                ),
              vl_Boleto: (item) =>
                item.vl_Boleto ? (
                  <td>{formatThousands(item.vl_Boleto)}</td>
                ) : (
                  <td>---</td>
                ),
              vl_Pago: (item) =>
                item.vl_Pago ? (
                  <td>{formatThousands(item.vl_Pago)}</td>
                ) : (
                  <td>---</td>
                ),
              status: (item) => renderStatus(item.status),
              boleto_Emitido: (item) =>
                item.boleto_Emitido ? (
                  <td style={{ textAlign: "center" }}>
                    <i className="cil-check-circle" />
                  </td>
                ) : (
                  <td>X</td>
                ),
              codigo: (item) =>
                item.codigo ? <td>{item.vl_Boleto}</td> : <td>---</td>,
            }}
          />
        </div>
      ),
    },
    {
      id: 2,
      label: "Recibos",
      icon: "cil-notes",
      content: (
        <div>
          <CDataTable
            items={tableRecibos}
            fields={recibosFields}
            hover
            responsive
            scopedSlots={{
              detalhes: (item) => (
                <td>
                  <CButton
                    className="button-link py-0 px-0"
                  // onClick={() => handleDetalhesRecibo(item)}
                  >
                    Detalhes
                  </CButton>
                </td>
              ),
              nr_Boleto: (item) => (
                <td className="nowrap-cell">{item.nr_Boleto}</td>
              ),
              status: (item) => renderStatus(item.status),
              // recibo_Sobre: (item) => renderReciboSobre(item.recibo_Sobre),
              dt_Emiss: (item) =>
                item.dt_Emiss ? (
                  <td>{formatDate(item.dt_Emiss)}</td>
                ) : (
                  <td>---</td>
                ),
              dt_Pagto: (item) =>
                item.dt_Pagto ? (
                  <td>{formatDate(item.dt_Pagto)}</td>
                ) : (
                  <td>---</td>
                ),
              descricao_Motivo_Cancel: (item) =>
                item.descricao_Motivo_Cancel ? (
                  <td>{item.descricao_Motivo_Cancel}</td>
                ) : (
                  <td>---</td>
                ),
            }}
          />
        </div>
      ),
    },
    {
      id: 3,
      label: "Negociações",
      icon: "cil-dollar",
      content: (
        <div>
          <CDataTable
            items={tableNegociacoes}
            fields={negociacaoFields}
            hover
            responsive
            scopedSlots={{
              status: (item) => renderStatus(item.status),
              liberado: (item) =>
                item.liberado ? (
                  <td style={{ textAlign: "center" }}>
                    <i className="cil-check-circle" />
                  </td>
                ) : (
                  <td>X</td>
                ),
              descricao: (item) => (
                <td className="nowrap-cell">{item.descricao}</td>
              ),
              negociacao_Enviada: (item) =>
                item.negociacao_Enviada ? (
                  <td style={{ textAlign: "center" }}>
                    <i className="cil-check-circle" />
                  </td>
                ) : (
                  <td style={{ textAlign: "center" }}>X</td>
                ),
              dt_Negociacao: (item) =>
                item.dt_Negociacao ? (
                  <td>{formatDate(item.dt_Negociacao)}</td>
                ) : (
                  <td>---</td>
                ),
              dt_Cadastro_Negociacao: (item) =>
                item.dt_Cadastro_Negociacao ? (
                  <td>{formatDate(item.dt_Cadastro_Negociacao)}</td>
                ) : (
                  <td>---</td>
                ),
              vl_Negociacao: (item) =>
                item.vl_Negociacao ? (
                  <td>{formatThousands(item.vl_Negociacao)}</td>
                ) : (
                  <td>---</td>
                ),
            }}
          />
        </div>
      ),
    },
  ];

  const [currentTab, setCurrentTab] = useState("Boletos");

  useEffect(() => {
    if (negociacoes) {
      getParcelas(negociacoes[0].id_Negociacao);
      // setParcelasData(negociacoes);
      setIdParcela(negociacoes[0].id_Negociacao);
      getDadosParcelas(negociacoes[0].id_Negociacao);
    }
  }, []);

  // useEffect(() => {
  //   if (negociacoes) {
  //     getParcelas(negociacoes[0].id_Acordo);
  //   }
  // }, []);

  // useEffect(() => {
  //   if (idParcela) {
  //     setParcelasData(idParcela);
  //   }
  // }, [idParcela]);

  return (
    <div>
      <CRow className="mx-3 mb-2">
        <h1>Negociação Parcelada</h1>
      </CRow>
      <div className="container-fluid">
        <CCard>
          <CCardBody>
            <div style={{ maxHeight: "500px", overflowX: "auto" }}>
              <div style={{ width: "fit-content", overflowY: "auto" }}>
                <CDataTable
                  items={parcelasData}
                  fields={parcelasColumns}
                  scopedSlots={{
                    dt_Vencimento: (item) =>
                      item.dt_Vencimento ? (
                        <td>{formatDate(item.dt_Vencimento)}</td>
                      ) : (
                        <td>---</td>
                      ),
                    dt_Pagamento: (item) =>
                      item.dt_Pagamento ? (
                        <td>{formatDate(item.dt_Pagamento)}</td>
                      ) : (
                        <td>---</td>
                      ),
                    dt_Venc_Boleto: (item) =>
                      item.dt_Venc_Boleto ? (
                        <td>
                          <CButton
                            className="button-link py-0 px-0"
                            // onClick={() => handleDetalhesBoleto(item)}
                            disabled
                          >
                            {formatDate(item.dt_Venc_Boleto)}
                          </CButton>
                        </td>
                      ) : (
                        <td>---</td>
                      ),
                    dt_Atualizacao: (item) =>
                      item.dt_Atualizacao ? (
                        <td>{formatDate(item.dt_Atualizacao)}</td>
                      ) : (
                        <td>---</td>
                      ),
                    vl_Saldo: (item) =>
                      item.vl_Saldo ? (
                        <td>{formatThousands(item.vl_Saldo)}</td>
                      ) : (
                        <td>0,00</td>
                      ),
                    vl_Atualizado: (item) =>
                      item.vl_Atualizado ? (
                        <td>{formatThousands(item.vl_Atualizado)}</td>
                      ) : (
                        <td>0,00</td>
                      ),
                    vl_Pago: (item) =>
                      item.vl_Pago ? (
                        <td>{formatThousands(item.vl_Pago)}</td>
                      ) : (
                        <td>0,00</td>
                      ),
                    nr_Boleto: (item) =>
                      item.nr_Boleto ? <td>{item.nr_Boleto}</td> : <td>---</td>,
                    url_Boleto: (item) => (
                      <td>
                        <CButton
                          className="button-link nowrap-cell"
                          // onClick={() => handleVisualizarBoleto(item)}
                          disabled
                        >
                          {" "}
                          Visualizar Boleto
                        </CButton>
                      </td>
                    ),
                    linha_Digitavel: (item) => (
                      <td className="nowrap-cell">{item.linha_Digitavel}</td>
                    ),
                  }}
                />
              </div>
            </div>
            <CCard className="mt-4">
              <CTabs activeTab={currentTab}>
                <CRow>
                  <CCol md="1">
                    <CNav variant="tabs" className="flex-column">
                      {tabs.map((tab) => (
                        <CNavItem key={tab.id}>
                          <CTooltip content={tab.label}>
                            <CNavLink
                              data-tab={tab.label}
                              onClick={() => setCurrentTab(tab.label)}
                            >
                              <i className={tab.icon} />
                            </CNavLink>
                          </CTooltip>
                        </CNavItem>
                      ))}
                    </CNav>
                  </CCol>
                  <CCol style={{ maxHeight: "250px", overflowX: "auto" }}>
                    <div style={{ width: "fit-content", overflowY: "auto" }}>
                      <CTabContent className="overflow-auto">
                        {tabs.map((tab) => (
                          <CTabPane key={tab.id} data-tab={tab.label}>
                            {tab.content}
                          </CTabPane>
                        ))}
                      </CTabContent>
                    </div>
                  </CCol>
                </CRow>
              </CTabs>
            </CCard>
          </CCardBody>
        </CCard>
      </div>
    </div>
  );
};

export default NegociacaoParcelada;
