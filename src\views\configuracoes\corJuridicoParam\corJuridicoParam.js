import {
  CButton,
  CCard,
  CCardBody,
  CCol,
  CForm,
  CFormGroup,
  CInput,
  CLabel,
  CRow,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { DELETE_DATA, GET_DATA, POST_DATA, PUT_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import ConfirmModal from "src/reusable/ConfirmModal";
import TableSelectItens from "src/reusable/TableSelectItens";
import { HexColorPicker } from "react-colorful";

const CorJuridicoParam = () => {
  const [data, setData] = useState([]);
  const [name, setName] = useState("");
  const [colorHexa, setColorHexa] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [insertMode, setInsertMode] = useState(true);
  const [guid, setGuid] = useState();

  const columns = [
    {
      key: "name",
      label: "Nome",
      defaultSort: "ascending",
    },
    {
      key: "colorHexa",
      label: "Cor",
      formatter: (item) => (
        <div
          style={{
            backgroundColor: item,
            width: "70px",
            height: "30px",
            border: "1px solid gray",
            borderRadius: "5px",
            padding: "3px",
          }}
        >
          {item}
        </div>
      ),
    },
    {
      key: "actions",
      label: "Ações",
      formatterByObject: (item) => renderActionButton(item),
    },
  ];

  const renderActionButton = (item) => (
    <>
      <CButton
        color="info"
        onClick={() => handleConfirmUpdate(item)}
        className="mr-2"
        // title={inforPermissions(permissao).edit}
        // disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
      >
        <i className="cil-pencil" />
      </CButton>
      <CButton
        color="danger"
        onClick={() => handleConfirmDelete(item)}
        className="mr-2"
        // title={inforPermissions(permissao).edit}
        // disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
      >
        <i className="cil-trash" />
      </CButton>
    </>
  );

  const handleSave = async () => {
    setIsLoading(true);
    if (insertMode) {
      const data = {
        name: name,
        colorHash: colorHexa,
      };
      const insertSuccess = await POST_DATA(
        getURI("restCorJuridicoParam"),
        data,
        true
      );
      if (insertSuccess.success) {
        toast.success("Cadastro realizado com sucesso!");
        await getLista();
        clearInputs();
      } else {
        alert(insertSuccess.message);
      }
    } else {
      const data = {
        name: name,
        colorHexa: colorHexa,
        id: guid,
      };
      const insertSuccess = await PUT_DATA(
        getURI("restCorJuridicoParam"),
        data,
        true
      );
      if (insertSuccess.success) {
        toast.success("Alteração realizada com sucesso!");
        await getLista();
        clearInputs();
        setInsertMode(true);
        setGuid(null);
      } else {
        alert(insertSuccess.message);
      }
    }
    setIsLoading(false);
  };

  const handleModalClose = (confirmation) => {
    setShowConfirmModal(false);
    handleDelete(confirmation);
  };

  const handleConfirmDelete = (item) => {
    setSelectedItem(item);
    setShowConfirmModal(true);
  };
  const handleConfirmUpdate = (item) => {
    setGuid(item.id);
    setName(item.name);
    setColorHexa(item.colorHexa);
    setInsertMode(false);
  };

  const handleDelete = async (confirmation) => {
    setIsLoading(true);
    if (confirmation) {
      const deleteSuccess = await DELETE_DATA(
        getURI("restCorJuridicoParam") + "/" + selectedItem.id,
        null,
        true
      );

      if (deleteSuccess.success) {
        toast.success("Exclusão realizada com sucesso!");
        await getLista();
        clearInputs();
      } else {
        alert(deleteSuccess.message);
      }
    }
    setIsLoading(false);
  };

  const clearInputs = () => {
    setName("");
    setColorHexa("");
  };

  async function getLista() {
    setIsLoading(true);
    const lista = await GET_DATA(getURI("restCorJuridicoParam"), null, true);

    if (lista) {
      setData(lista);
    }
    setIsLoading(false);
    return;
  }

  useEffect(() => {
    const awaitFunc = async () => {
      await getLista();
    };
    awaitFunc();
  }, []);

  return (
    <div>
      <h3>Cadastro de cores indicativas do jurídico:</h3>
      <p style={{ color: "gray" }}></p>
      <div>
        <CCard>
          <CCardBody>
            <CForm>
              <CRow>
                <CCol xs>
                  <CRow>
                    <CCol>
                      <h5 style={{ color: "gray" }}>
                        {insertMode ? "Cadastro" : "Alteração"}:
                      </h5>
                    </CCol>
                  </CRow>
                  <CFormGroup className="row gx-3 gy-2">
                    <CCol xs={8}>
                      <CRow>
                        <CCol>
                          <CLabel>Nome</CLabel>
                          <CInput
                            value={name}
                            min={0}
                            onChange={(event) => {
                              setName(event.target.value);
                            }}
                          />
                        </CCol>

                        <CCol>
                          <CLabel>Cor</CLabel>
                          <CInput
                            value={colorHexa}
                            onChange={(e) => setColorHexa(e.target.value)}
                          />
                          <HexColorPicker
                            color={colorHexa}
                            onChange={(e) =>
                              setColorHexa(e === "#NaNNaNNaN" ? "#ffffff" : e)
                            }
                            style={{ width: "100%", marginTop: "15px" }}
                          />
                        </CCol>
                      </CRow>
                    </CCol>
                    <CCol xs="auto" style={{ paddingTop: "28px" }}>
                      <CButton
                        color="success"
                        onClick={handleSave}
                        className="mr-2"
                      >
                        {insertMode ? "Incluir" : "Alterar"}
                      </CButton>
                      <CButton
                        color="light"
                        onClick={() => {
                          setInsertMode(true);
                          setGuid(null);
                          clearInputs();
                        }}
                        className="mr-2"
                      >
                        Cancelar
                      </CButton>
                    </CCol>
                  </CFormGroup>
                </CCol>
                <CCol xs>
                  <CRow>
                    <CCol>
                      <h5 style={{ color: "gray" }}>
                        Lista de cores já inclusas:
                      </h5>
                    </CCol>
                  </CRow>
                  <CRow>
                    {isLoading ? (
                      <CardLoading />
                    ) : (
                      <CCol>
                        <TableSelectItens
                          data={data}
                          columns={columns}
                          onSelectionChange={(_) => {}}
                          defaultSelectedKeys={[]}
                          selectable={false}
                          heightParam="600px"
                        />
                        <ConfirmModal
                          isOpen={showConfirmModal}
                          onClose={handleModalClose}
                          texto={"Tem certeza que deseja excluir esse item?"}
                        />
                      </CCol>
                    )}
                  </CRow>
                </CCol>
              </CRow>
            </CForm>
          </CCardBody>
        </CCard>
      </div>
    </div>
  );
};

export default CorJuridicoParam;
