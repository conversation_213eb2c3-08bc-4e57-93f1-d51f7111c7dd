import React, { Component } from "react";
import { HashRouter, Route, Switch } from "react-router-dom";
import "./scss/style.scss";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { AuthProvider } from "./auth/AuthContext";
import PrivateRoute from "./auth/PrivateRoute";
import { ContextProvider } from "src/reusable/DataContext";
import { WebsocketTelefoniaProvider } from "./websocketProvider/websocketTelefoniaProvider";

const loading = (
  <div className="pt-3 text-center">
    <div className="sk-spinner sk-spinner-pulse"></div>
  </div>
);

// Containers
const TheLayout = React.lazy(() => import("./containers/TheLayout"));
const SoftphoneOlos = React.lazy(() => import("./views/telefonia/SoftphoneOlos"));
// Pages
const Login = React.lazy(() => import("./views/pages/login/Login"));
const Register = React.lazy(() => import("./views/pages/register/Register"));
const Page404 = React.lazy(() => import("./views/pages/page404/Page404"));
const Page500 = React.lazy(() => import("./views/pages/page500/Page500"));

class App extends Component {
  render() {
    return (
      <AuthProvider>
        <ContextProvider>
          <WebsocketTelefoniaProvider>
            <HashRouter>
              <React.Suspense fallback={loading}>
                <Switch>
                  <Route
                    exact
                    path="/login"
                    name="Login Page"
                    render={(props) => <Login {...props} />}
                  />
                  <Route
                    exact
                    path="/404"
                    name="Page 404"
                    render={(props) => <Page404 {...props} />}
                  />
                  <Route
                    exact
                    path="/500"
                    name="Page 500"
                    render={(props) => <Page500 {...props} />}
                  />
                  <PrivateRoute
                    path="/telefonia/softphone/olos"
                    name="SoftphoneOlos"
                    component={(props) => <SoftphoneOlos {...props} />}
                  />
                  <PrivateRoute
                    path="/"
                    name="Home"
                    component={(props) => <TheLayout {...props} />}
                  />
                </Switch>
                <ToastContainer
                  position="top-right"
                  autoClose={5000}
                  hideProgressBar={false}
                  newestOnTop={false}
                  closeOnClick
                  rtl={false}
                  pauseOnFocusLoss
                  draggable
                  pauseOnHover
                />
              </React.Suspense>
            </HashRouter>
          </WebsocketTelefoniaProvider>
        </ContextProvider>
      </AuthProvider>
    );
  }
}

export default App;
