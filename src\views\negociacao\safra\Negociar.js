import React, { useState, useEffect } from "react";
import {
  <PERSON>utton,
  CCard,
  CCardBody,
  CCol,
  CRow,
} from "@coreui/react";
import DadosContratos from "src/views/telaPrincipal/CardContratos/Contratos";

/* Import da Tela */
import CardCalcular from "./partials/CardCalcular";
import CardAcordos from "./partials/CardAcordos";
import CardContratos from "./partials/CardContratos";
import CardLoading from "src/reusable/CardLoading";
import CardAvisos from "src/reusable/CardAvisos";

/* Import de utilidades */
import "react-datepicker/dist/react-datepicker.css";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { useMyContext } from "src/reusable/DataContext";
import { liberarNegociacao } from "./../../../containers/AprovacaoJuridica";

const getConfigGrupoCyfer = async (payload, endpoint = "getConfigUnicaByKey") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(getURI(endpoint), null, true, true, `/${payload}`);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const Negociar = () => {
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");
  const [MsgAviso, setMsgAviso] = useState("");
  const [TitleAviso, setTitleAviso] = useState("");
  const [contratoNegociar, setContratoNegociar] = useState(null);
  let financiadoData = localStorage.getItem("financiadoData") ? JSON.parse(localStorage.getItem("financiadoData")) : "";
  const verifyParameters = (param = "grupos_datacob_gvc_cyber_safra") => {
    setTitleAviso("");
    setMsgAviso(``);
    if (financiadoData.id_Agrupamento !== null && financiadoData.id_Agrupamento !== undefined && financiadoData.id_Agrupamento !== "") {
      setLoading(true);
      setLoadingAction("VarifyParam");
      getConfigGrupoCyfer(param)
        .then((data) => {
          const dataConfig = JSON.parse(data);
          if (Array.isArray(dataConfig) && dataConfig.length > 0) {
            if (!dataConfig.includes(financiadoData.id_Grupo.toString())) {
              setTitleAviso("Grupo não autorizado");
              setMsgAviso(`Grupo de clientes {${financiadoData.id_Grupo}} não autorizado para esse tipo negociação, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`);
            }

          } else {
            setTitleAviso("Erro na busca Parâmetros de grupo");
            setMsgAviso(`Parâmetros de grupo não encontrado (${param}), Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`);
          }

        }).catch((err) => {
          setTitleAviso(`Erro na chamada das APIS`);
          setMsgAviso(`Erro na chamada API de Config parametro (${param}), Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`);
        })
        .finally(() => {
          setLoading(false);
          setLoadingAction("empty");
        });
    } else {
      setTitleAviso("Nenhum contrato selecionado");
      setMsgAviso("Não foi possivel identificar um contrato para o cliente, realize uma nova busca e tente novamente.");
    }
  }
  const [comunicacaoState, setComunicacaoState] = useState({});
  const history = useHistory();
  const context = useMyContext();
  useEffect(async () => {
    if (!await liberarNegociacao(context.data)) {
      history.push('/telaprincipal');
    }

    if (TitleAviso !== "" && MsgAviso !== "")
      verifyParameters("grupos_datacob_rodobens_cyber_safra");

  }, []);

  const handleNegociaClick = (contrato) => {
    setContratoNegociar(contrato);
  };

  return (
    <div>
      <CRow className="mb-2">
        <CCol>
          <h1>Negociar Cyber Safra</h1>
        </CCol>
      </CRow>
      {TitleAviso !== "" && MsgAviso !== "" ?
        (
          <div>
            <CardAvisos
              Title={TitleAviso}
              Msg={MsgAviso}
            />
            {financiadoData.id_Agrupamento == null || loading ? "" : <CButton
              color="success"
              disabled={financiadoData.id_Agrupamento == null || loading}
              onClick={() => verifyParameters()}
            ><i className="cil-reload" /></CButton>}

          </div>


        ) : (loading && loadingAction === "VarifyParam" ?
          (<CardLoading
            Title="Validação do Parametros"
            Msg="Buscando o grupo de clientes, aguarde..."
          />) : (
            <CRow>
              <CCol xs="8">
                <CCard>
                  <CCardBody>
                    <DadosContratos />
                  </CCardBody>
                </CCard>
                <div>
                  <CardAcordos comunicacaoState={comunicacaoState} />
                </div>
                <div>
                  <CardContratos onNegociarClick={handleNegociaClick} buscarAcordo={setComunicacaoState} />
                </div>
              </CCol>
              <CCol xs="4">
                <CardCalcular contratoNegociar={contratoNegociar} />
              </CCol>
            </CRow>
          ))
      }

    </div>
  );
};

export default Negociar;
