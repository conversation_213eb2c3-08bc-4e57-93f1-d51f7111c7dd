import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>utt<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@coreui/react";
import { MountURI } from "src/api";
import { GET_FINANCIADO, GET_ClientData } from "../reusable/functions";
import { useMyContext } from "../reusable/DataContext";
import CardLoading from "src/reusable/CardLoading";
import { updateConnection } from "src/config/updateConnection";
import { getURI } from "src/config/apiConfig";
import { useHistory, useLocation } from "react-router-dom";
import { toast } from "react-toastify";

const TheHeaderMailing = ({ mailings }) => {
  const [index, setIndex] = useState(0);
  const [indexMailing, setIndexMailing] = useState(0);
  const [mailingContract, setmailingContract] = useState(null);
  const [firstRecord, setfirstRecord] = useState(true);
  const [isLoadContract, setIsLoadContract] = useState(false);
  const [financiado, setFinanciado] = useState(null);
  const { updateData, updateCustas, updateCustasProjuris } = useMyContext();

  const history = useHistory();
  const location = useLocation();
  const isHomePage = location.pathname === "/telaprincipal";
  const userProfile = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : "";
  const token = localStorage.getItem("token");

  const nextRecord = () => {
    setIndex((prevIndex) => {
      const nextIndex = (prevIndex + 1) % mailings.length;
      setIndexMailing(0);
      return nextIndex;
    });
  };

  const nextRecordMailing = async () => {
    const existLaunch = await existsHistoryUser(financiado);
    if (existLaunch) {
      setIndexMailing((prevIndexMailing) => {
        const nextIndexMailing = prevIndexMailing + 1;
        if (nextIndexMailing >= mailings[index].mailingContracts.length) {
          nextRecord();
          return 0;
        }
        return nextIndexMailing;
      });
      await updateMailingContract(mailingContract, "Completed");
    } else {
      toast.warning("Cadastro do histórico pendente!");
    }
  };

  async function existsHistoryUser(next) {
    const currentDay = new Date().toISOString().substring(0, 10);
    const data = {
      Id_Agrupamento: next.id_Agrupamento,
      numeroContrato: next.numero_Contrato,
    };
    let history = [];
    const response = await fetch(MountURI("Datacob/HistoricoResumo", data), {
      headers: { Authorization: `Bearer ${token}` },
    });
    if (response.ok) {
      const responseData = (await response.json()).data || [];
      const usuarioLogado = localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : "";
      history =
        responseData.filter(
          (item) =>
            item.dt_ocorr.substring(0, 10) === currentDay &&
            item.nome === usuarioLogado.name
        ) || [];
      return history.length > 0;
    }
  }

  async function GetContract(item, mailing) {
    let data = { ActiveConnection: mailing.crm };

    if (item?.contractId) {
      data = { ...data, contratoId: item.contractId };
    }

    const url = MountURI("Datacob/BuscaDadosFinanciados", data);

    try {
      const response = await fetch(url, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        return data.data;
      } else {
        console.error("Erro:", response.statusText);
      }
    } catch (error) {
      console.error("Erro buscando usuários:", error);
    }
  }

  const updateMailing = async (mailing) => {
    try {
      if (mailing.status === "Create") {
        const url = `${getURI()}/mailing`;
        mailing.status = "Started";

        await fetch(url, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            ...mailing,
            id: mailing.id,
          }),
        });
      }
    } catch (error) {
      console.error("Erro editando o Mailing:", error);
    }
  };

  const updateMailingContract = async (mailingcontract, status) => {
    try {
      const url = `${getURI()}/mailingcontract`;
      mailingcontract.status = status;

      await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...mailingcontract,
          id: mailingcontract.id,
        }),
      });
    } catch (error) {
      console.error("Erro editando o Mailing:", error);
    }
  };

  const handleLoadCliente = async (item) => {
    setIsLoadContract(true);
    updateConnection({
      crm: { datacobNumber: item.mailing.crm },
      userProfile,
    }).then(
      async () => {
        if (item?.contract && item.contract) {
          GET_FINANCIADO(item?.contract).then(
            async (financiado) => {
              await GET_ClientData(financiado);
              setFinanciado(financiado);
              updateData(financiado);
              updateCustas(null);
              updateCustasProjuris(null);
              if (!isHomePage) {
                history.push("/telaprincipal");
              }
              await updateMailing(item.mailing);
              await updateMailingContract(item, "Started");
              setmailingContract(item);
              setIsLoadContract(false);
            },
            () => {
              setIsLoadContract(false);
              toast.warning("Falha ao carregar dados do financiado!");
            }
          );
        } else {
          setIsLoadContract(false);
          toast.warning("Falha ao carregar dados do contrato! Não localizado!");
        }
      },
      () => {
        setIsLoadContract(false);
        toast.warning("Falha ao carregar dados do contrato!");
      }
    );
  };

  useEffect(async () => {
    if ((mailings?.length ?? 0) > 0) {
      GetContract(
        mailings[index]?.mailingContracts[indexMailing],
        mailings[index]
      ).then(async (contract) => {
        setmailingContract({
          ...mailings[index]?.mailingContracts[indexMailing],
          mailing: mailings[index],
          contract: contract[0],
        });
        setfirstRecord(
          mailings[index]?.mailingContracts[indexMailing]?.status === "Create"
        );
        handleLoadCliente({
          ...mailings[index]?.mailingContracts[indexMailing],
          mailing: mailings[index],
          contract: contract[0],
        });
      });
    }
  }, [mailings, index, indexMailing]);

  return (
    <>
      {isLoadContract ? (
        <CardLoading Title="Carregando contrato ..." />
      ) : (
        <CHeader
          colorScheme="dark"
          className="custom-header-1"
          style={{
            width: "100%",
            height: "1%",
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
            zIndex: 1,
          }}
        >
          <CLabel
            htmlFor="name"
            style={{ marginRight: "2%", fontSize: "17px" }}
          >
            <strong>Mailing:</strong> {mailings[index]?.name || "N/A"}
          </CLabel>
          <CLabel
            htmlFor="name"
            style={{ marginRight: "2%", fontSize: "17px" }}
          >
            <strong>Contrato:</strong>{" "}
            {mailingContract?.contract?.numero_Contrato || "N/A"} -{" "}
            {mailingContract?.contract?.nome || "N/A"}
          </CLabel>
          <CButton
            color="light"
            onClick={nextRecordMailing}
            style={{ marginRight: "1%", height: "30px", fontSize: "12px" }}
          >
            <i className="cil-arrow-thick-to-right"></i>
          </CButton>
        </CHeader>
      )}
    </>
  );
};

export default TheHeaderMailing;
