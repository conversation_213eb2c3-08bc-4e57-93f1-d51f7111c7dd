import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useAuth } from "src/auth/AuthContext";
import { useMyContext } from "src/reusable/DataContext";
import {
  CCreateElement,
  CSidebar,
  CSidebarBrand,
  CSidebarNav,
  CSidebarNavDivider,
  CSidebarNavTitle,
  CSidebarMinimizer,
  CSidebarNavDropdown,
  CSidebarNavItem,
} from "@coreui/react";

import CIcon from "@coreui/icons-react";

// sidebar nav config
import navigation from "./_nav";
import {
  liberarNegociacao,
  showMenuAprovacaoJuridica,
  verificaAnaliseRestricaoPendente,
} from "./AprovacaoJuridica";

const TheSidebar = () => {
  const { data, contratos } = useMyContext();
  const [showMenuAprovacaoJurica, setShowMenuAprovacaoJurica] = useState(false);
  const [liberarMenuNegociacao, setLiberarMenuNegociacao] = useState(false);
  const { permissions, checkPermission } = useAuth();
  const dispatch = useDispatch();
  const show = useSelector((state) => state.sidebarShow);
  const cloneNAV = JSON.parse(JSON.stringify(navigation));
  const filteredItems = cloneNAV.filter((item) => {
    if (item.module === "Aprovação Jurídica") {
      const showmenuaprov = showMenuAprovacaoJurica;
      if (!showmenuaprov) {
        return false;
      }
      return true;
    }
    if (item.module === "Negociação") {
      const temParcelaAberta = contratos?.some((contrato) =>
        contrato.parcelas.some((parcela) => parcela.status === "A")
      );
      item._children = item._children?.filter((subitem) => {
        if (subitem.submodule === "Simular" && !temParcelaAberta) {
          return false;
        }
        return subitem;
      });
      if (!liberarMenuNegociacao) {
        return false;
      }
      return true;
    }
    if (item.module === "Lista Login Usuario") {
      const isAdminis = JSON.parse(localStorage.getItem("user"))?.isAdmin;
      if (!isAdminis) {
        return false;
      }
    }
    if (permissions?.isAdmin) return true;

    if (item?._tag === "CSidebarNavTitle") {
      return (
        checkPermission("Campanhas Safra", "View") ||
        checkPermission("Permissão de Campanhas", "View")
      );
    }

    if (!item.module || !checkPermission(item.module, "View")) return false;

    if (!item._children) return true;
    if (item._children) {
      item._children = item._children.filter((subitem) => {
        if (
          subitem.module &&
          subitem.submodule &&
          checkPermission(subitem.module, "View") &&
          checkPermission(subitem.module, "View", subitem.submodule)
        )
          return subitem;
        return false;
      });

      if (item.module === "Negociação") {
        if (!liberarMenuNegociacao) {
          return false;
        }
        return true;
      }

      return true;
    }
    return false;
  });

  useEffect(() => {
    const asyncFunc = async () => {
      const mostra = await showMenuAprovacaoJuridica();

      const libera = await liberarNegociacao(data);
      setLiberarMenuNegociacao(libera);
      setShowMenuAprovacaoJurica(mostra);
    };
    asyncFunc();
  }, []);
  const [key, setKey] = useState(0);
  const forceRender = () => {
    setKey((prevKey) => prevKey + 1);
  };
  useEffect(() => {
    const asyncFunc = async () => {
      const libera = await liberarNegociacao(data);
      setLiberarMenuNegociacao(libera);
      forceRender();
    };
    asyncFunc();
  }, [data]);

  return (
    <CSidebar
      show={show}
      onShowChange={(val) => dispatch({ type: "set", sidebarShow: val })}
      colorScheme="light"
      className="sidebar"
      key={key}
    >
      <CSidebarBrand
        className="d-md-down-none"
        style={{ height: "200px" }}
        to="/telaprincipal"
      >
        <CIcon
          className="c-sidebar-brand-full"
          name="logo-gvc"
          style={{ marginLeft: "40px" }}
          height={100}
        />
        <CIcon
          className="c-sidebar-brand-minimized"
          name="sygnet"
          height={35}
        />
      </CSidebarBrand>
      <CSidebarNav>
        <CCreateElement
          items={filteredItems}
          // items={navigation}
          components={{
            CSidebarNavDivider,
            CSidebarNavDropdown,
            CSidebarNavItem,
            CSidebarNavTitle,
          }}
        />
      </CSidebarNav>
      <CSidebarMinimizer className="c-d-md-down-none" />
    </CSidebar>
  );
};

export default React.memo(TheSidebar);
