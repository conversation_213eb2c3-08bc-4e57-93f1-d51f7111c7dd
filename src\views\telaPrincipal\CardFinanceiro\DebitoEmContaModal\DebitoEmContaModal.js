import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CRow,
  CCol,
  CLabel,
  CCard,
  CCardBody,
} from "@coreui/react";
import {
  formatThousands,
  formatDate,
  formatDateTime,
} from "src/reusable/helpers";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import TableSelectItens from "src/reusable/TableSelectItens";
import LoadingComponent from "src/reusable/Loading";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";

const DebitoEmContaModal = ({ isOpen, onClose, idCota }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(null);
  const [selectedAccount, setSelectedAccount] = useState(null);

  // const mockData = {
  //   accounts: [
  //     {
  //       bank: "string",
  //       agency: "string",
  //       nrAgency: "string",
  //       digit: "string",
  //       cp: "string",
  //       op: "string",
  //       account: "string",
  //       digitAccount: "string",
  //       dayDebit: "string",
  //       typeAccount: "string",
  //       favored: "string",
  //       favoredDoc: "string",
  //       inclusion: "string",
  //       alteration: "string",
  //       validity: [
  //         {
  //           begin: "2023-11-22T19:52:40.085Z",
  //           end: "2023-11-22T19:52:40.085Z",
  //           alteration: "2023-11-22T19:52:40.085Z",
  //           userAlteration: "string",
  //         },
  //       ],
  //     },
  //   ],
  // };

  const accountsFields = [
    {
      key: "bank",
      label: "Banco",
      formatterByObject: (item) => renderCell(item, item.bank),
    },
    {
      key: "agency",
      label: "Nome Agência",
    },
    { key: "nrAgency", label: "Nr. Agência" },
    {
      key: "digit",
      label: "Dígito",
    },
    {
      key: "cp",
      label: "C/P",
    },
    {
      key: "op",
      label: "Op",
    },
    {
      key: "account",
      label: "Conta",
    },
    {
      key: "digitAccount",
      label: "Dígito",
    },
  ];

  const validityFields = [
    {
      key: "begin",
      label: "Início da Vigência",
      formatter: (item) => formatDate(item),
    },
    {
      key: "end",
      label: "Final da Vigência",
      formatter: (item) => formatDate(item),
    },
    {
      key: "alteration",
      label: "Data Alteração Vigência",
      formatter: (item) => formatDate(item),
    },
    {
      key: "userAlteration",
      label: "Usuário Alteração da Vigência",
    },
  ];

  const renderCell = (item, cellValue) => {
    return <div onClick={() => setSelectedAccount(item)}>{cellValue}</div>;
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      GetData(`/${idCota}`, "getNewconDebitoConta")
        .then((data) => {
          if (data && data.accounts.length > 0) {
            setData(data.accounts);
            setSelectedAccount(data.accounts[0]);
          } else {
            setData(null);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="lg"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Débito em Conta</h5>
      </CModalHeader>
      <CModalBody>
        {isLoading ? (
          <div className="mt-2">
            <LoadingComponent />
          </div>
        ) : data == null || data === undefined || data.length === 0 ? (
          <NaoHaDadosTables />
        ) : (
          <>
            <CRow>
              <TableSelectItens
                data={data}
                columns={accountsFields}
                onSelectionChange={(_) => {}}
                defaultSelectedKeys={[]}
                selectable={false}
                heightParam="290px"
              />
            </CRow>
            <hr />
            <CRow>
              <CCol>
                <CLabel className="mr-2">Dia Débito</CLabel>
                {selectedAccount?.dayDebit ?? "---"}
              </CCol>
              <CCol>
                <CLabel className="mr-2">Tipo da Conta</CLabel>
                {selectedAccount?.typeAccount ?? "---"}
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CLabel className="mr-2">Favorecido</CLabel>
                {selectedAccount?.favored ?? "---"}
              </CCol>
              <CCol>{selectedAccount?.favoredDoc ?? "---"}</CCol>
            </CRow>
            <CRow>
              <CCol>
                <CLabel className="mr-2">Inclusão</CLabel>
                {selectedAccount?.inclusion ?? "---"}
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CLabel className="mr-2">Alteração</CLabel>
                {selectedAccount?.alteration ?? "---"}
              </CCol>
            </CRow>
            <hr />
            <CRow>
              {selectedAccount?.validity == null ||
              selectedAccount?.validity === undefined ||
              selectedAccount?.validity.length === 0 ? (
                <CCol style={{ textAlign: "center" }}>
                  <div>Não encontrado dados de vigência para essa conta.</div>
                </CCol>
              ) : (
                <TableSelectItens
                  data={selectedAccount?.validity}
                  columns={validityFields}
                  onSelectionChange={(_) => {}}
                  defaultSelectedKeys={[]}
                  selectable={false}
                  heightParam="290px"
                />
              )}
            </CRow>
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default DebitoEmContaModal;
