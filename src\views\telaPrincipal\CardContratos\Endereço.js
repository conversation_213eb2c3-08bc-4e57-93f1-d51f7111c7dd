import React, { useState, useEffect } from "react";
import {
  CRow,
  CCol,
  CLabel,
  CCardBody,
  CButton,
  CInputCheckbox,
  CBadge,
} from "@coreui/react";
import SelecionarModal from "./SelecionarModal";
import AdicionarEndereco from "./OutrosModals/AdicionarEndereco";
import EditarEndereco from "./OutrosModals/EditarEndereco";
import { formatDate } from "src/reusable/helpers";
import { GET_DATA, POST_DATA } from "src/api";
import { getDadosFinanciado, getTiposEndereco } from "src/reusable/functions";
import { useMyContext } from "src/reusable/DataContext";
import { useAuth } from "src/auth/AuthContext";
import { toast } from "react-toastify";

const Endereco = ({ item, backFunc, handleAdicionarEndereco, tipoEndereco }) => {
  const { checkPermission, inforPermissions } = useAuth();

  const permissao = {
    modulo: "Tela Principal",
    submodulo: "Endereço",
  };

  const [financiadoData, setFinanciadoData] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );
  const [showModalEditar, setShowModalEditar] = useState(false);

  const [end, setEnd] = useState(null);
  
  useEffect(() => {
    setEnd(item);
  }, [item]);

  const handleCloseEditModal = () => {
    setShowModalEditar(false);
  };

  const renderStatus = (value) => {
    switch (value) {
      case 0:
        return <CBadge color="danger">Inativo</CBadge>;
      case 1:
        return <CBadge color="success">Ativo</CBadge>;
      case 2:
        return <CBadge color="info">Efetivo</CBadge>;
      default:
        break;
    }
  };

  const renderTipoEndereco = (value) => {
    switch (value) {
      case 0:
        return <div>Residencial</div>;
      case 1:
        return <div>Comercial</div>;
      case 2:
        return <div>Outros</div>;
      case 3:
        return <div>Pesquisado</div>;
      case 4:
        return <div>Imovel</div>;
      case 5:
        return <div>Cobranca</div>;
      default:
        break;
    }
  };

  return (
    <CCardBody>
      <CRow>
        <CCol md="5">
          <CLabel>Logradouro</CLabel>
          <div>
            <strong> {end ? end.logradouro : "---"} </strong>
          </div>
        </CCol>
        <CCol md="3">
          <CLabel>Bairro</CLabel>
          <div>
            <strong> {end ? end.bairro : "---"} </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel>Número</CLabel>
          <div>
            <strong> {end ? end.numero : "---"} </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel>Complemento</CLabel>
          <div>
            <strong> {end ? end.complemento : "---"} </strong>
          </div>
        </CCol>
      </CRow>
      <CRow>
        <CCol md="2">
          <CLabel>CEP</CLabel>
          <div>
            <strong> {end ? end.cep : "---"} </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel>Cidade</CLabel>
          <div>
            <strong> {end ? end.cidade : "---"} </strong>
          </div>
        </CCol>
        <CCol md="1">
          <CLabel>UF</CLabel>
          <div>
            <strong> {end ? end.uf : "---"} </strong>
          </div>
        </CCol>
        <CCol md="3">
          <CLabel>Avalista/Sócio/Terceiro</CLabel>
          <div>
            <strong> {end ? end.id_Avalista : "---"} </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel>Data de Inclusão</CLabel>
          <div>
            <strong>{end ? formatDate(end.dtInclusao) : "---"}</strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel>Tipo</CLabel>
          <div>
            <strong>
              {" "}
              {end ? renderTipoEndereco(end.tipo_Endereco) : "---"}
            </strong>
          </div>
        </CCol>
      </CRow>
      <CRow>
        <CCol md="2">
          <CLabel>Status</CLabel>
          <div>{end ? renderStatus(end.tipo_Status) : "---"}</div>
        </CCol>
        <CCol md="2">
          <CLabel>Enviar Carta</CLabel>
          <div>
            <CInputCheckbox
              className="mx-0"
              name="carta"
              checked={end ? end.enviarCarta === 1 : false}
              readOnly
              disabled
            />
          </div>
        </CCol>
        <CCol md="4"></CCol>
        <CCol
          md="4"
          style={{
            display: "flex",
            alignItems: "flex-end",
            justifyContent: "end",
          }}
        >
          {/* <CButton
            color="secondary"
            onClick={() => setShowModalSelecionar(true)}
            size="sm"
            className="mr-2"
            disabled={!financiadoData}
          >
            Selecionar endereço
          </CButton> */}
          <CButton
            color="warning"
            onClick={() => setShowModalEditar(true)}
            size="sm"
            className="mr-2"
            title={
              !financiadoData
                ? "Nenhum financiado Selecionado"
                : inforPermissions(permissao).edit
            }
            disabled={
              !financiadoData ||
              !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
            }
          >
            Editar
          </CButton>
          {/* <CButton
            color="secondary"
            onClick={() => setShowModalAdicionar(true)}
            size="sm"
            className="mr-2"
            title={
              !financiadoData
                ? "Nenhum financiado Selecionado"
                : inforPermissions(permissao).create
            }
            disabled={
              !financiadoData ||
              !checkPermission(permissao.modulo, "Create", permissao.submodulo)
            }
          >
            Adicionar
          </CButton> */}
          <CButton color="secondary" onClick={backFunc} size="sm">
            Voltar
          </CButton>
        </CCol>
        {showModalEditar && (
          <EditarEndereco
            isOpen={showModalEditar}
            onClose={handleCloseEditModal}
            editEndereco={end}
            onEditEndereco={handleAdicionarEndereco}
            tipoEndereco={tipoEndereco}
          />
        )}
      </CRow>
    </CCardBody>
  );
};

export default Endereco;
