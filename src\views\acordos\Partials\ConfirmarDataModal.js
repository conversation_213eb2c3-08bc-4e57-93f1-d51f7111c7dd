import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CButton,
} from "@coreui/react";
import { formatDate } from "src/reusable/helpers";

const ConfirmarDataModal = ({ isOpen, onClose, onSubmit, datas }) => {
  const [confirmarDataMessage, setConfirmarDataMessage] = useState(false);
  const [proxDiaUtil, setProxDiaUtil] = useState("");
  const [permitirData, setPermitirData] = useState(true);

  const handleClose = () => {
    onClose();
  };

  const handleSetDate = () => {
    onSubmit(datas.proximoDiaUtil);
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      setProxDiaUtil(formatDate(datas.proximoDiaUtil));
      if (datas.proximoDiaUtil > datas.diaLimite) {
        setPermitirData(false);
        setConfirmarDataMessage(true);
      } else {
        setConfirmarDataMessage(false);
        setPermitirData(true);
      }
    }
  }, [isOpen]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="lg">
      <CModalBody>
        <CRow>
          <CCol
            className="px-2 my-2"
            style={{ textAlign: "center", fontSize: "larger" }}
          >
            <div>
              A data <strong>{formatDate(datas?.diaSelecionado)}</strong> não é
              um dia útil.
            </div>
            {confirmarDataMessage && (
              <div className="mt-1">
                O próximo dia útil ultrapassa o prazo para a quebra de acordo, a
                data limite é: <strong>{formatDate(datas?.diaLimite)}.</strong>
              </div>
            )}
            <div className="mt-1">Deseja:</div>
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={handleClose}>
          Cancelar
        </CButton>
        {permitirData && (
          <CButton color="primary" onClick={handleSetDate}>
            Definir próximo dia útil ({proxDiaUtil})
          </CButton>
        )}
        <CButton color="warning" onClick={handleClose}>
          Manter data informada.
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default ConfirmarDataModal;
