import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CCol,
  CRow,
} from "@coreui/react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
// import { formatThousands, formatDate } from "src/reusable/helpers";

const PontoVendaModal = ({ isOpen, onClose, dados }) => {
  const [tableData, setTableData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleClose = () => {
    onClose();
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const updateView = async () => {
    setIsLoading(true);

    GetData(`/${dados}`, "getNewConEstruturaPonto")
      .then((data) => {
        if (data) {
          setTableData([
            {
              label: "Ponto de Venda",
              value: data.cod.padStart(6, "0") + " - " + data.name,
            },
            {
              label: "E-mail",
              value: data.email,
            },
            {
              label: "Telefone",
              value: `(${data.ddd}) ${data.phone}`,
            },
          ]);
        } else {
          setTableData(null);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    if (isOpen) {
      updateView();
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal"
      size="lg"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Ponto de Venda</h5>
      </CModalHeader>
      {isLoading ? (
        <div className="mt-2">
          <CardLoading />
        </div>
      ) : tableData == null ||
        tableData === undefined ||
        tableData.length == 0 ? (
        <NaoHaDadosTables />
      ) : (
        <CModalBody>
          {tableData && (
            <>
              <table className="table table-hover calculo">
                <tbody>
                  {tableData.map((row) => (
                    <CRow key={row.label}>
                      <CCol md="6" style={{ textAlign: "end" }}>
                        <strong>{row.label}:</strong>
                      </CCol>
                      <CCol md="6">{row.value ? row.value : "---"}</CCol>
                    </CRow>
                  ))}
                </tbody>
              </table>
            </>
          )}
        </CModalBody>
      )}
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default PontoVendaModal;
