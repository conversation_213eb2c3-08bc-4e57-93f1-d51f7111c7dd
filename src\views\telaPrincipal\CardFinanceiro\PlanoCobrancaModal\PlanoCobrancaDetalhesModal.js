import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CFormGroup,
  CInput,
  CDataTable,
  CCard,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CCardBody,
  CLabel,
  CRow,
  CCol,
  CCardFooter,
  CCardHeader,
} from "@coreui/react";
import { GET_DATA } from "src/api";
import { formatThousands, formatDate } from "src/reusable/helpers";
import TableSelectItens from "src/reusable/TableSelectItens";

const PlanoCobrancaDetalhesModal = ({ isOpen, onClose, dados }) => {
  const [tableData, setTableData] = useState(null);

  const fields = [
    {
      key: "number",
      label: "Parcela",
      formatter: (value) => formatThousands(value ?? 0),
      totalizer: "text",
      defaultValue: "Total",
    },
    {
      key: "commonFund",
      label: "Fdo. Comum",
      formatter: (value) => formatThousands(value ?? 0),
      totalizer: "sum",
      defaultValue: 0,
    },
    {
      key: "admTax",
      label: "Tx. Adm",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "resFund",
      label: "Fdo. Res",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "accession",
      label: "Adesão",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "insurance",
      label: "Seguro",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "sn",
      label: "(SN)",
    },
    {
      key: "other",
      label: "Outros",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "paidValue",
      label: "Valor a pagar",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "occurrence",
      label: "Ocorrência",
    },
    {
      key: "modality",
      label: "Modalidade",
    },
  ];

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      setTableData([
        {
          label: "Fdo. Comum",
          value: dados.commonFund,
        },
        {
          label: "Adesão",
          value: dados.accession,
        },
        {
          label: "Tx. Adm",
          value: dados.admTax,
        },
        {
          label: "Fdo. Res",
          value: dados.resFund,
        },
        {
          label: "Seguros",
          value: dados.insurance,
        },
        {
          label: "Outros",
          value: dados.other,
        },
      ]);
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="xl"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Detalhes Plano de Cobrança</h5>
      </CModalHeader>
      <CModalBody>
        {dados && (
          <>
            <CRow>
              <CCol style={{ textAlign: "center" }}>
                <CCard>
                  <CCardHeader>Dados do Plano de Venda</CCardHeader>
                  <CCardBody>
                    <CLabel>Produto - </CLabel> {dados.codProduct}{" "}
                    {dados.product} <br />
                    <CLabel>Subroduto - </CLabel> {dados.codSubProduct}{" "}
                    {dados.subProduct} <br />
                    <CLabel>Prazo - </CLabel> {dados.time} meses <br />
                  </CCardBody>
                </CCard>
              </CCol>
              <CCol>
                <CCard>
                  <CCardHeader>Percentual Ideal</CCardHeader>
                  {tableData && (
                    <CCardBody>
                      <table>
                        <th></th>
                        {tableData.map((row) => (
                          <th
                            key={row.label}
                            style={{ padding: "4px", textAlign: "center" }}
                          >
                            {row.label}
                          </th>
                        ))}
                        <tr>
                          <td>% Ideal</td>
                          {tableData.map((row) => (
                            <td key={row.label} style={{ textAlign: "center" }}>
                              {row.value ? formatThousands(row.value) : "0,00"}
                            </td>
                          ))}
                        </tr>
                        <tr>
                          <td>% Cont</td>
                          <td style={{ textAlign: "center" }}>
                            {formatThousands(dados.percCont)}
                          </td>
                          <td style={{ textAlign: "end" }} colSpan={4}>
                            Parcela integrada a parcela do mês:
                          </td>
                          <td style={{ textAlign: "center" }}>
                            {dados.integratedInstallments}
                          </td>
                        </tr>
                      </table>
                    </CCardBody>
                  )}
                </CCard>
              </CCol>
            </CRow>
            <CCard className="pb-1">
              <TableSelectItens
                data={dados.installments}
                columns={fields}
                onSelectionChange={(_) => {}}
                defaultSelectedKeys={[]}
                selectable={false}
                heightParam="270px"
              />
            </CCard>
            <CCard style={{width:"70%"}}>
              <table className="table table-hover">
                <th style={{ padding: "4px" }}></th>
                <th style={{ padding: "4px" }}>Fdo. Comum</th>
                <th style={{ padding: "4px" }}>Tx. Adm</th>
                <th style={{ padding: "4px" }}>Fdo. Res</th>
                <th style={{ padding: "4px" }}>Adesão</th>
                <th style={{ padding: "4px" }}>Seguro</th>
                <th style={{ padding: "4px" }}>Outros</th>
                <th style={{ padding: "4px" }}>Valor a Pagar</th>
                <tr>
                  <td>Subtotal</td>
                  <td>{formatThousands(dados.subTotals.subCommonFund)}</td>
                  <td>{formatThousands(dados.subTotals.subAdmTax)}</td>
                  <td>{formatThousands(dados.subTotals.subResFund)}</td>
                  <td colSpan={3}></td>
                  <td>{formatThousands(dados.subTotals.paidValue)}</td>
                </tr>
                <tr>
                  <td>Total</td> <td colSpan={3}></td>
                  <td>{formatThousands(dados.totals.totalAccession)}</td>
                  <td>{formatThousands(dados.totals.totalInsurance)}</td>
                  <td>{formatThousands(dados.totals.totalOthers)}</td>
                  <td>{formatThousands(dados.totals.paidValue)}</td>
                </tr>
                <tr>
                  <td>% Cont</td>
                  <td colSpan={5}></td>
                  <td>{formatThousands(dados.totals.percCont)}</td>
                  <td></td>
                </tr>
                <tr>
                  <td>% Pagar</td>{" "}
                  <td>{formatThousands(dados.percPagar.percCommonFunc)}</td>
                  <td>{formatThousands(dados.percPagar.percAdmTax)}</td>
                  <td>{formatThousands(dados.percPagar.percResFund)}</td>
                  <td>{formatThousands(dados.percPagar.percAcsession)}</td>
                  <td colSpan={3}></td>
                </tr>
                <tr></tr>
              </table>
            </CCard>
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default PlanoCobrancaDetalhesModal;
