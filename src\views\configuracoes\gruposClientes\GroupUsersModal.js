import { useState } from "react";
import { CTooltip, CBadge, CModal, CModalBody, CModalHeader, CModalTitle } from "@coreui/react";
import CIcon from "@coreui/icons-react";

const GroupUsersModal = ({ group }) => {
  const [showGroupUsersModal, setShowGroupUsersModal] = useState(false);

  const toggleGroupUsersModal = () => {
    setShowGroupUsersModal(!showGroupUsersModal);
  };



  return (
    <div className="d-flex align-items-center mt-3" onClick={toggleGroupUsersModal}>
      {group?.users?.length === 0 && (
        <CTooltip key={"00"} content={"Sem usuários no grupo"} placement="top">
          <CIcon
            name="cil-user"
            alt={"Sem usuários no grupo"}
            className="rounded-circle me-2"
            width="30"
            height="30"
          />
        </CTooltip>
      )}
      {group?.users?.slice(0, 3).map((user, index) => (
        <CTooltip key={`${user.id}-${user.name}` || index} content={user.name} placement="top">
          <CIcon
            name="cil-user"
            alt={user.name}
            className="rounded-circle me-2"
            width="30"
            height="30"
          />
        </CTooltip>
      ))}
      {(group?.users?.length > 3 || group?.users?.length === 0) && (
        <CBadge color="primary">+{group.users.length - 3 < 0 ? 0 : group?.users?.length - 3}</CBadge>
      )}
      <CModal show={showGroupUsersModal && group?.users?.length > 0} onClose={() => toggleGroupUsersModal} closeOnBackdrop={false}>
      <CModalHeader closeButton>
        <CModalTitle>Usuários do grupo</CModalTitle>
      </CModalHeader>
        <CModalBody>
          <ul className="list-group list-group-flush">
            {group?.users?.map((user, index) => (
              <li key={user.id || index} className="list-group-item">{user.name}</li>
            ))}
          </ul>
        </CModalBody>
      </CModal>
    </div>
  );
};

export default GroupUsersModal;
