import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CButton,
  CForm,
  CFormGroup,
  CLabel,
  CInput,
  CSelect,
  CCard,
  CRow,
  CCol,
  CCardBody,
  CDataTable,
  CCardHeader,
  CInputGroup,
  CInputGroupAppend,
  CInputRadio,
  CInputGroupText,
  CInputCheckbox,
} from "@coreui/react";
import { formatDate } from "src/reusable/helpers";

const DetalhesAcordoModal = ({ isOpen, onClose, dados }) => {

  const tableColumns = [
    { key: "descricao" },
    { key: "descricao_Cancelamento" },
    { key: "dt_Acordo", label: "Data Acordo" },
    { key: "dt_Aprovacao_Proposta" },
    { key: "dt_Cancel" },
    { key: "dt_Confirmacao" },
    { key: "dt_Envio" },
    { key: "dt_Liberacao" },
    { key: "dt_Rejeicao" },
    { key: "email_Bol" },
    { key: "enviado" },
    { key: "id_Acordo" },
    { key: "id_Negociacao" },
    { key: "nr_Acordo" },
    { key: "nr_Acordo_Banco" },
    { key: "perc_Desconto" },
    { key: "perc_Financ" },
    { key: "qtde_Parcela" },
    { key: "status" },
    { key: "tipo_Envio" },
    { key: "vl_Acordo" },
    { key: "vl_Base_Acordo" },
    { key: "vl_Base_Multa" },
    { key: "vl_Entrada" },
    { key: "vl_Iof" },
    { key: "vl_Saldo_Acordo" },
    { key: "vl_Total_Original" },
  ];

  const handleClose = () => {
    onClose();
  };

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="xl">
      <CModalHeader closeButton>Detalhes do Calculo</CModalHeader>
      <CModalBody>
        <CRow>
          <CCol>
            <CDataTable
              items={dados}
              fields={tableColumns}
              // scopedSlots={{
              //   dt_Venc: (item) => <td>{formatDate(item.dt_Venc)}</td>,
              // }}
            />
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter></CModalFooter>
    </CModal>
  );
};

export default DetalhesAcordoModal;
