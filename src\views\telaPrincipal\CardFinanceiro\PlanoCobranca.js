import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>abel,
  CSelect,
  CDataTable,
  CRow,
  CBadge,
  CCol,
  CButton,
} from "@coreui/react";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";

import { formatThousands } from "src/reusable/helpers";
import LoadingComponent from "src/reusable/Loading";
import PlanoCobrancaDetalhesModal from "./PlanoCobrancaModal/PlanoCobrancaDetalhesModal";
import { GET_DATA } from "src/api";
import TableSelectItens from "src/reusable/TableSelectItens";
import { useAuth } from "src/auth/AuthContext";

const PlanoCobranca = ({ selected }) => {
  const { checkPermission,inforPermissions } = useAuth();
  const permissaoTelaPrincipalPlanodeCobranca = {
    modulo: "Tela Principal",
    submodulo: "Plano de Cobrança",
  }
  const permissaoTelaPrincipalPlanodeCobrancaDetalhesdoPlano = {
    modulo: "Tela Principal",
    submodulo: "Plano de Cobrança - Detalhes do Plano",
  }
  const [isLoading, setIsLoading] = useState(false);
  const [planoData, setPlanoData] = useState(null);
  const [installmentsData, setInstallmentsData] = useState(null);
  const [showDetalhesModal, setShowDetalhesModal] = useState(false);

  const [expandedRows, setExpandedRows] = useState([]);
  const [pressedRow, setPressedRow] = useState(null);
  const [selectedParcela, setSelectedParcela] = useState(null);

  const financiado = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : [];

  const cnscCotas =
    localStorage.getItem("cnscCotas") &&
      localStorage.getItem("cnscCotas") !== "undefined"
      ? JSON.parse(localStorage.getItem("cnscCotas")) ?? []
      : [];

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const updateView = async () => {
    setIsLoading(true);
    await GetData(`/${cnscCotas?.idCota}`, "getNewconPlanoCobrança")
      .then((data) => {
        if (data) {
          setPlanoData(data);
          setInstallmentsData(data.installments);
        } else {
          setPlanoData(null);
          setInstallmentsData(null);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    if (selected === true) {
      updateView();
    }
  }, [selected]);

  const fields = [
    {
      key: "number",
      label: "Parcela",
    },
    {
      key: "commonFund",
      label: "Fdo. Comum",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "admTax",
      label: "Tx. Adm",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "resFund",
      label: "Fdo. Res",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "accession",
      label: "Adesão",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "insurance",
      label: "Seguro",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "sn",
      label: "(SN)",
    },
    {
      key: "other",
      label: "Outros",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "paidValue",
      label: "Valor a pagar",
      formatter: (value) => formatThousands(value ?? 0),
    },
    {
      key: "occurrence",
      label: "Ocorrência",
    },
    {
      key: "modality",
      label: "Modalidade",
    },
  ];

  return (
    <>
      {isLoading ? (
        <LoadingComponent />
      ) : (
        <>
          {planoData ? (
            <>
              <CRow className="my-2">
                <CCol md='9' className="my-2">
                  <strong className="mr-1">Produto: </strong>{" "}
                  <span
                    style={{
                      padding: "2px",
                      border: "1px black solid",
                      borderRadius: "6px",
                      marginRight: "8px",
                    }}
                  >
                    {planoData?.codProduct} - {planoData?.product}{" "}
                  </span>
                  <strong className="mr-1">Subproduto: </strong>{" "}
                  <span
                    style={{
                      padding: "2px",
                      border: "1px black solid",
                      borderRadius: "6px",
                    }}
                  >
                    {planoData?.codSubProduct} - {planoData?.subProduct}{" "}
                  </span>
                </CCol>
                <CCol style={{ textAlign: "end" }}>
                  <CButton
                    color="info"
                    className="py-2"
                    onClick={() => setShowDetalhesModal(true)}
                    title={inforPermissions(permissaoTelaPrincipalPlanodeCobrancaDetalhesdoPlano).view}
                    disabled={!planoData || !checkPermission(permissaoTelaPrincipalPlanodeCobrancaDetalhesdoPlano.modulo,"View",permissaoTelaPrincipalPlanodeCobrancaDetalhesdoPlano.submodulo)}
                  >
                    Detalhes do Plano
                  </CButton>
                </CCol>
              </CRow>
              <TableSelectItens
                data={installmentsData}
                columns={fields}
                onSelectionChange={(_) => {}}
                defaultSelectedKeys={[]}
                selectable={false}
                heightParam="270px"
              />
            </>
          ) : (
            <CRow style={{ textAlign: "center" }}>
              <CCol>
                <div>Dados do plano de cobrança não encontrados.</div>
              </CCol>
            </CRow>
          )}
        </>
      )}

      <PlanoCobrancaDetalhesModal
        isOpen={showDetalhesModal}
        onClose={() => setShowDetalhesModal(false)}
        dados={planoData}
      />
    </>
  );
};

export default PlanoCobranca;
