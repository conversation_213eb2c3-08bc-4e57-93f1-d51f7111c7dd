import React, { useEffect, useState } from "react";
import { CButton } from "@coreui/react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { useMyContext } from "src/reusable/DataContext";
import CardBoletoStatus from "src/views/negociacao/Parcial/CardBoletoStatus";

const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(
        getURI(endpoint),
        null,
        true,
        true,
        payload
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};
const BtnBoletoManual = () => {
  const { data } = useMyContext();
  const [bol, setBol] = useState(null);
  const [showBoletoStatusModal, setShowBoletoStatusModal] = useState(false);
  useEffect(() => {
    const id = data?.id_Agrupamento;
    if (id !== null) {
      GetData(id, "getJokerTicketListByGrouping").then((response) => {
        if (response.length > 0) {
          setBol(response[0]);
        } else {
          setBol(null);
        }
      });
      const searchStatus = async () => {
        GetData(id, "getJokerTicketListByGrouping").then((response) => {
          if (response.length > 0) {
            setBol(response[0]);
          } else {
            setBol(null);
          }
        });
      };

      const intervalId = setInterval(searchStatus, 600000);
      return () => clearInterval(intervalId);
    }
  }, [data]);

  const renderLabel = (status) => {
    if (status === null || status === undefined) return "Boleto Manual";

    return "Status Boleto Manual: " + status;
  };

  const renderColor = (status) => {
    if (status === "Falha") return "danger";
    if (status === "Aguardando") return "warning";
    if (status === "Em Andamento") return "warning";
    if (status === "Concluído") return "success";
    return "primary";
  };

  return (
    <>
      <CButton
        hidden={bol === null || bol === undefined}
        color={renderColor(bol?.statusAutomation)}
        block
        className="text-white mt-2"
        onClick={() => setShowBoletoStatusModal(true)}
      >
        {renderLabel(bol?.statusAutomation)}
      </CButton>
      {showBoletoStatusModal && (
        <CardBoletoStatus
          onClose={() => {
            setShowBoletoStatusModal(false);
          }}
        />
      )}
    </>
  );
};

export default BtnBoletoManual;
