import { useState } from "react";
import Select from "react-select";
import { CButton, CModal, CModalBody, CModalHeader, CModalFooter, CFormGroup, CForm, CRow, CInputCheckbox, CCol, CLabel, CInput } from "@coreui/react";

const AdicionarEmail = ({ isOpen, onClose, onSave, tipoEmail }) => {

  const [email, setEmail] = useState({ enderecoEmail: "", tipoEmail: 18, status: 0, malaDireta: false, });
  const [isValid, setIsValid] = useState(true);
  const optionsEmails = tipoEmail ? [...tipoEmail.map((item) => { return { label: item.descricao, value: item.idTipo }; }),] : [];

  const listaStatus = [
    { value: 0, label: "Inativo" },
    { value: 1, label: "Ativo" },
    { value: 2, label: "Efetivo" },
  ];

  const handleTipoChange = (selectedOption) => {
    setEmail((prevState) => ({
      ...prevState,
      tipoEmail: selectedOption.value,
    }));
  };

  const handleStatusChange = (selectedOption) => {
    setEmail((prevState) => ({
      ...prevState,
      status: selectedOption.value,
    }));
  };

  const handleCheckbox = (target) => {
    const { name, checked } = target;
    setEmail((prevState) => ({
      ...prevState,
      [name]: checked,
    }));
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setEmail((prevState) => ({
      ...prevState,
      [name]: value.trim(),
    }));
  };

  const handleValidationBlur = (e) => {
    const inputValue = email.enderecoEmail.trim();
    const emailRegex = /^[^\s@`´,^]+@[^\s@`´,^]+\.[^\s@`´,^]+$/;
    setIsValid(emailRegex.test(inputValue));
  };

  function resetModal() {
    setEmail({
      enderecoEmail: "",
      tipoEmail: 18,
      status: 0,
      malaDireta: false,
    });
  }

  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };

  const handleSave = (event) => {
    event.preventDefault();

    const requiredFields = [{ name: "enderecoEmail", displayName: "Email" }];

    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = email[field.name];
      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      setIsValid(false);
      return;
    }

    onSave(email);
    resetModal();
    onClose();
  };

  return (
    <CModal show={isOpen} size="lg" onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Adicionar Email</CModalHeader>
      <CModalBody>
        <CForm>
          <CFormGroup>
            <CRow>
              <CCol>
                <CLabel>Email</CLabel>
                <CInput
                  name="enderecoEmail"
                  type="text"
                  value={email.enderecoEmail}
                  // value={enderecoEmail}
                  onChange={handleInputChange}
                  onBlur={handleValidationBlur}
                  style={{ borderColor: isValid ? "initial" : "red" }}
                />{" "}
                {!isValid && <p style={{ color: "red" }}>Email inválido</p>}
              </CCol>
            </CRow>
          </CFormGroup>
          <CFormGroup>
            <CRow>
              <CCol md="6">
                <CLabel>Status</CLabel>
                <Select
                  name="status"
                  value={listaStatus.find(
                    (option) => option.value === email.status
                  )}
                  onChange={handleStatusChange}
                  options={listaStatus}
                />
              </CCol>
              <CCol md="4">
                <CLabel>Tipo</CLabel>
                <Select
                  name="tipoEmail"
                  placeholder="Selecione"
                  value={optionsEmails.find(
                    (option) => option.value === email.tipoEmail
                  )}
                  onChange={handleTipoChange}
                  options={optionsEmails}
                />
              </CCol>
              <CCol md="2">
                <CLabel>Mala Direta</CLabel>
                <div style={{ padding: "6px 0 0 32px" }}>
                  <CInputCheckbox
                    className="mx-0"
                    name="malaDireta"
                    size="xl"
                    onChange={(e) => handleCheckbox(e.target)}
                    checked={email.malaDireta}
                  />
                </div>
              </CCol>
            </CRow>
          </CFormGroup>
          <CFormGroup>
            <CRow></CRow>
          </CFormGroup>
        </CForm>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Cancelar
        </CButton>
        <CButton color="primary" onClick={handleSave} disabled={!isValid}>
          Salvar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default AdicionarEmail;
