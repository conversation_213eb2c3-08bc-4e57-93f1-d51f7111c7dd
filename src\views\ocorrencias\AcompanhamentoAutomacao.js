import {
  CButton,
  CCol,
  CRow,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
} from "@coreui/react";
import React, { useState, useEffect } from "react";
import { GET_DATA, POST_DATA } from "src/api";
import TableSelectItens from "src/reusable/TableSelectItens";
import { formatDateTime } from "src/reusable/helpers";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import CardLoading from "src/reusable/CardLoading";
import { toast } from "react-toastify";
import LoadingComponent from "src/reusable/Loading";

const AcompanhamentoAutomacao = () => {
  const [dataAutomation, setDataAutomation] = useState([]);
  const [isLoadingAutomation, setLoadingAutomation] = useState(false);
  const [modalObs, setModalObs] = useState(false);
  const [obsFull, setObsFull] = useState("");
  const [isResendLoading, setIsResendLoading] = useState(false);

  const updateViewAutomation = async (_) => {
    if (financiadoData?.id_Agrupamento !== null) {
      setLoadingAutomation(true);
      const novoOcorrencias = await getOcorrenciasAutomacao(
        financiadoData?.id_Agrupamento
      );
      setDataAutomation(novoOcorrencias);
      setLoadingAutomation(false);
    }
  };

  useEffect(() => {
    const controller = new AbortController();
    updateViewAutomation();
    return () => {
      controller.abort();
    };
  }, []);

  const [financiadoData] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  function formatObs(item) {
    return (
      <>
        <span>{item.substring(0, 50)} </span>
        {item.length > 50 && (
          <button
            className="btn btn-secondary btn-sm"
            onClick={() => {
              setObsFull(item);
              setModalObs(true);
            }}
          >
            <i className="cil-plus" />
          </button>
        )}
      </>
    );
  }

  function buttonActions(item) {
    let render = false;
    let td = dataAutomation.find((x) => {
      return x.id === item;
    });
    if (
      td.statusOccurrence === "Falha Execução" ||
      (td.statusOccurrence !== "Ocorrência Inserida" &&
        td.statusOccurrence !== null)
    )
      render = true;

    return (
      <>
        {render && (
          <button
            className="btn btn-warning"
            disabled={isResendLoading}
            onClick={() => reenvioOccurrence(item)}
          >
            {!isResendLoading ? "Reenviar" : <LoadingComponent size="sm" />}
          </button>
        )}
      </>
    );
  }

  const fieldsAutomation = [
    {
      key: "createdAt",
      label: "Data",
      formatter: (value) => formatDateTime(value),
    },
    { key: "occurrenceSystem", label: "Ocorrência" },
    { key: "user", label: "Usuário" },
    { key: "obs", label: "Observação", formatter: (value) => formatObs(value) },
    {
      key: "phones",
      label: "Telefones",
      formatter: (item) => item.join(", "),
    },
    { key: "statusAutomation", label: "Status Autom." },
    { key: "statusOccurrence", label: "Status Ocorrência" },
    {
      key: "id",
      label: " ",
      formatter: (value) => buttonActions(value),
    },
  ];

  async function getOcorrenciasAutomacao(Id_Agrupamento) {
    // const data = { Id_Agrupamento: Id_Agrupamento };
    let uri_manager = "GVCManager/SaveOccurrence/List/" + Id_Agrupamento;
    const automation = await GET_DATA(uri_manager);
    if (automation) {
      const filteredAutomation = automation;
      return filteredAutomation;
    } else return null;
  }

  const reenvioOccurrence = async (id) => {
    setIsResendLoading(true);
    const data = {
      Id: id,
    };
    const ocorrencia = await POST_DATA(
      "GVCManager/SaveOccurrence/ResendOccurrence",
      data
    );

    if (ocorrencia.data === true) {
      toast.success("Reenvio realizado com sucesso!");
    } else {
      toast.error("Erro ao reenviar.");
    }
    setIsResendLoading(false);
    updateViewAutomation();
  };

  return isLoadingAutomation ? (
    <CardLoading />
  ) : (
    <>
      <CCol md="12" sm="12" className="d-flex justify-content-end">
        <CButton
          className={"ml-3"}
          color="success"
          disabled={financiadoData?.Id_Agrupamento === null}
          onClick={() => updateViewAutomation()}
          title=""
        >
          <i className="cil-reload" />
        </CButton>
      </CCol>
      {dataAutomation && dataAutomation?.length > 0 ? (
        <CRow className="my-2">
          <TableSelectItens
            data={dataAutomation}
            columns={fieldsAutomation}
            onSelectionChange={(_) => {}}
            defaultSelectedKeys={[]}
            selectable={false}
            heightParam="520px"
          />
        </CRow>
      ) : (
        <NaoHaDadosTables />
      )}
      <CModal show={modalObs} onClose={setModalObs}>
        <CModalHeader closeButton>
          <CModalTitle>Observação</CModalTitle>
        </CModalHeader>
        <CModalBody>{obsFull}</CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setModalObs(false)}>
            Fechar
          </CButton>
        </CModalFooter>
      </CModal>
    </>
  );
};

export default AcompanhamentoAutomacao;
