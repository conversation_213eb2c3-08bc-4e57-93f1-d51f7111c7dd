import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ow, <PERSON><PERSON><PERSON>, <PERSON>utton } from "@coreui/react";
import webSocketDocService from "src/config/webSocketDocService";
import { GET_EXTRA, getCNSCotas, getNewconDados } from "src/reusable/functions";
import ChangeCrmModal from "src/reusable/ChangeCrmModal";

import TabFinanceiro from "./TabFinanceiro";
import CardContratos from "./TabContratos";
import CardTelefone from "./CardTelefone";
import CardHistorico from "./CardHistorico";
import CardEmails from "./CardEmails";
import MudarClienteModal from "src/reusable/MudarClienteModal";
import { useAuth } from "src/auth/AuthContext";
// import RamalModal from "../tactium/RamalModal";
import { useMyContext } from "src/reusable/DataContext";
import { getURI } from "src/config/apiConfig";
import { GET_DATA, POST_DATA } from "src/api";
import LoadingComponent from "src/reusable/Loading";
import EditUserCrmAuthModal from "src/views/configuracoes/gerenciarUsuarios/Modal/EditUserCrmAuthModal.tsx";
import { toast } from "react-toastify";
import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";
import TicketChartModal from "src/containers/TheHeaderContent/TicketChartModal";
import CIcon from "@coreui/icons-react";
import CardHistoricoCresol from "./CardHistoricoCresol";
import AudioCapture from "src/containers/TranscriptionsView/AudioCapture";

const TelaPrincipal = () => {
  const { data, appSettings, updateAppSettings } = useMyContext();
  const { checkPermission, inforPermissions } = useAuth();

  //const [listaRestricoes, setListaRestricoes] = useState([]);
  const [dadosRestricoesFinanciado, setDadosRestricoesFinanciado] = useState(
    []
  );
  const [isLoading, setIsLoading] = useState(false);
  const [temAnaliseResticaoPendente, setTemAnaliseResticaoPendente] =
    useState(false);
  const [temAnaliseResticaoAprovada, setTemAnaliseResticaoAprovada] =
    useState(false);

  const [mudarClienteModal, setMudarClienteModal] = useState(false);
  const [changeCrmModal, setChangeCrmModal] = useState(false);
  const [pasta, setPasta] = useState(null);
  const [atualizadoEm, setAtualizadoEm] = useState(null);
  const [chosenCrm, setChosenCrm] = useState(null);

  const [wrapMoment, setWrapMoment] = useState(false);
  const [editUserCrmAuthModal, setEditUserCrmAuthModal] = useState(false);

  const { status } = useWebsocketTelefoniaContext();

  const permissaoTelaPrincipalTelefone = {
    modulo: "Tela Principal",
    submodulo: "Telefone",
  };

  const permissaoTelaPrincipalEmail = {
    modulo: "Tela Principal",
    submodulo: "Email",
  };
  /*
  const permissaoJuridico = {
    modulo: "Jurídico CRM",
    submodulo: "Jurídico",
  };
  */
  const permissaoHistoricosERegistros = {
    modulo: "Histórico e registros",
    submodulo: null,
  };

  const visibleTabIds = [1, 2, 3, 4, 5, 6, 7, 8, 9]; // Tabs do CardFinanceiro visiveís para o usuário

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  useEffect(() => {
    setPasta(null);
    webSocketDocService.initialize();

    if (!financiadoData) {
      setChangeCrmModal(true);
      // setMudarClienteModal(true);
      if (user?.datacobs.length === 1 && user?.isAdmin === false) {
        toast.info("Autenticando CRM.");
        postLoginDatacob()
          .then((data) => {
            if (data.success === false) {
              toast.error("Não foi possível autenticar CRM!");
              setEditUserCrmAuthModal(true);
            }
          })
          .catch((err) => {
            toast.error("Não foi possível autenticar CRM!");
            setEditUserCrmAuthModal(true);
          })
          .finally(() => { });
      }
    }

    if (financiadoData) {
      GET_EXTRA(financiadoData);
    }
  }, []);

  // useEffect(() => {
  //   if (callData) {
  //     if (
  //       callData?.status &&
  //       callData?.status !== undefined &&
  //       callData?.status !== null
  //     ) {
  //       try {
  //         if (callData?.status?.indexOf("Wrap") > -1) {
  //           setWrapMoment(true);
  //         }
  //       } catch (error) {
  //         console.error("Erro ao qualificar ligação:", error);
  //       }
  //     }
  //   }
  // }, [callData]);

  async function getListaRestricoesNegociacao() {
    const listaRestricoesNegociacao = await GET_DATA(
      getURI("negociacaoParametriResticaoListar"),
      null,
      true
    );

    if (listaRestricoesNegociacao) {
      //setListaRestricoes(listaRestricoesNegociacao);

      const processosdados = localStorage.getItem("processos")
        ? JSON.parse(localStorage.getItem("processos"))
        : null;
      const financiadodados = localStorage.getItem("financiadoData")
        ? JSON.parse(localStorage.getItem("financiadoData"))
        : null;
      if (processosdados === null || financiadodados === null) {
        console.log("não possui dados");
        return;
      }
      const pastas = processosdados?.map((item) => item.pasta);
      const fases = processosdados?.map((item) => item.fase_Atual);

      const fasesIguais = [];

      for (const valorArray of fases) {
        for (const item of listaRestricoesNegociacao) {
          if (item.name === valorArray) {
            fasesIguais.push(valorArray);
          }
        }
      }
      let faseAtual = "";
      let restrigirNegociacao = false;
      if (fasesIguais.length > 0) {
        faseAtual = fasesIguais[0];
        restrigirNegociacao = true;
      }

      const dataFinan = {
        id_financiado: financiadoData?.id_Financiado,
        financiado: financiadoData?.nome,
        fase: faseAtual,
        pastas: pastas,
        restrigirNegociacao: restrigirNegociacao,
      };
      setDadosRestricoesFinanciado(dataFinan);
    }
    return;
  }

  // const handlePastaChange = (pasta) => {
  //   setPasta(pasta);
  // };

  const handleAtualizarCards = () => {
    console.warn("Atualizando cards");
    setAtualizadoEm(new Date());
  };

  useEffect(() => {
    setAtualizadoEm(new Date());
    if (financiadoData) {
      GET_EXTRA(financiadoData);
    }

    if (dadosRestricoesFinanciado !== null) {
      dadosRestricoesFinanciado.restrigirNegociacao = false;
    }
  }, [data]);

  function onChangeCrm(crm) {
    setChosenCrm(crm);
  }

  const onCloseClienteModal = (missingCrm = null) => {
    setMudarClienteModal(false);
    // if (typeof missingCrm === "string") {
    //   setChosenCrm(missingCrm);
    //   setCrmAuthModal(true);
    // }
  };

  const handlePedidoReanalise = async () => {
    setIsLoading(true);
    const payload = {
      id_financiado: dadosRestricoesFinanciado.id_financiado.toString(),
      financiado: dadosRestricoesFinanciado.financiado,
      fase: dadosRestricoesFinanciado.fase,
      pastas: dadosRestricoesFinanciado.pastas,
    };

    await POST_DATA(
      getURI("negociacaoAnalisesRestricaoInserir"),
      payload,
      true
    );
    setIsLoading(false);
    setTemAnaliseResticaoPendente(true);
  };

  const verificaAnaliseRestricao = async () => {
    let isMounted = true;
    setIsLoading(true);
    if (financiadoData) {
      const listaRestricoesPorFinanciado = await GET_DATA(getURI("negociacaoAnalisesRestricaoPorIdFinanciado") + "/" + financiadoData.id_Financiado, null, true);
      if (isMounted) {
        if (listaRestricoesPorFinanciado.length > 0 &&
          listaRestricoesPorFinanciado[0].status === "Aprovado") {
          setTemAnaliseResticaoAprovada(true);
          setTemAnaliseResticaoPendente(false);
        } else if (
          listaRestricoesPorFinanciado.length > 0 &&
          listaRestricoesPorFinanciado[0].status === "Pendente"
        ) {
          setTemAnaliseResticaoPendente(true);
          setTemAnaliseResticaoAprovada(false);
        } else {
          setTemAnaliseResticaoAprovada(false);
          setTemAnaliseResticaoPendente(false);
        }
      }
    } else {
      if (isMounted) {
        setTemAnaliseResticaoAprovada(false);
        setTemAnaliseResticaoPendente(false);
      }
    }

    if (isMounted) {
      setIsLoading(false);
    }

    return () => {
      isMounted = false;
    };
  };

  const postLoginDatacob = async (payload, endpoint = "postDatacobLogin") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const loadNewcon = async () => {
    return new Promise(async (resolve, reject) => {
      try {
        if (!financiadoData) resolve(true);

        const payload = {
          newconCotaId: financiadoData.numero_Contrato
        };

        const cnscCotas = await getCNSCotas(payload, "postnewconcnsccotas");
        let newcon = null;

        if (cnscCotas?.data) {
          newcon = await getNewconDados(`/${cnscCotas.data.idCota}`, "postNewconDadosPrincipais");
        }

        localStorage.setItem("cnscCotas", JSON.stringify(cnscCotas?.data || {}));
        localStorage.setItem("newcon", JSON.stringify(newcon || {}));

        return resolve(true);
      } catch (error) {
        console.error("Erro ao carregar dados Newcon:", error);
        return reject(error);
      }
    });
  }

  return (
    <div>
      <AudioCapture
        connectWebSocketCallback={(callback) => {
          window.connectWebSocketCallback = callback; // Define a callback globalmente
        }}
      />
      <CContainer fluid id="tela_principal_title">
        <CRow>
          <CCol id="telaprincipal">
            <h5>Tela Principal</h5>
          </CCol>
          {status !== "" && status !== null && (
            <CRow>
              <h5>
                <span className="text-danger">Status da Telefonia:</span>{" "}
                {status}
              </h5>
            </CRow>
          )}
          <CCol className="d-flex justify-align-content-end mb-2">
            {dadosRestricoesFinanciado.restrigirNegociacao && (
              <>
                {isLoading ? (
                  <LoadingComponent />
                ) : (
                  <>
                    {temAnaliseResticaoPendente && (
                      <>
                        <span
                          className="text-danger text-right ml-auto"
                          style={{ fontSize: "1.25rem" }}
                        >
                          Atenção: Aguardando Aprovação Jurídica!
                        </span>
                        <CButton
                          color="warning"
                          onClick={{}}
                          className=" ml-2 text-white "
                          disabled
                        >
                          <strong>Aguardando Aprovacao</strong>
                        </CButton>
                      </>
                    )}
                    {temAnaliseResticaoAprovada && (
                      <>
                        <span
                          className="text-danger text-right ml-auto"
                          style={{ fontSize: "1.25rem" }}
                        >
                          Atenção: Aprovação Jurídica Concluída!
                        </span>
                        {/* <CButton
                            color="warning"
                            onClick={{}}
                            className=" ml-2 text-white "
                            disabled
                          >
                            <strong>Aguardando Aprovacao</strong>
                          </CButton> */}
                      </>
                    )}
                    {!temAnaliseResticaoPendente &&
                      !temAnaliseResticaoAprovada && (
                        <>
                          <span
                            className="text-danger text-right ml-auto"
                            style={{ fontSize: "1.25rem" }}
                          >
                            Atenção: Fase Atual não permite negociação!
                          </span>
                          <CButton
                            color="warning"
                            onClick={() => handlePedidoReanalise()}
                            className=" ml-2 text-white "
                          >
                            <strong>Solicitar Aprovação</strong>
                          </CButton>
                        </>
                      )}
                  </>
                )}
              </>
            )}
          </CCol>
          <CCol style={{ textAlign: "right" }}>
            <CIcon name="logo-negative" height={35} />
          </CCol>
        </CRow>
        <CardContratos
          pasta={pasta}
          onHandleAtualizarCards={handleAtualizarCards}
          onLoadProcessos={() => {
            getListaRestricoesNegociacao();
            verificaAnaliseRestricao();
          }}
          onLoadNewcon={loadNewcon}
        />
        <TabFinanceiro visibleTabIds={visibleTabIds} />
        <CRow>
          <CCol md="12">
            <CCard style={{ height: "auto" }}>
              {!checkPermission(
                permissaoTelaPrincipalTelefone.modulo,
                "View",
                permissaoTelaPrincipalTelefone.submodulo
              ) && <>{inforPermissions(permissaoTelaPrincipalTelefone).view}</>}
              {checkPermission(
                permissaoTelaPrincipalTelefone.modulo,
                "View",
                permissaoTelaPrincipalTelefone.submodulo
              ) && <CardTelefone atualizarCard={atualizadoEm} />}
            </CCard>
          </CCol>
          <CCol md="12">
            <CCard style={{ height: "auto" }}>
              {!checkPermission(
                permissaoTelaPrincipalEmail.modulo,
                "View",
                permissaoTelaPrincipalEmail.submodulo
              ) && <>{inforPermissions(permissaoTelaPrincipalEmail).view}</>}
              {checkPermission(
                permissaoTelaPrincipalEmail.modulo,
                "View",
                permissaoTelaPrincipalEmail.submodulo
              ) && <CardEmails atualizarCard={atualizadoEm} />}
            </CCard>
          </CCol>
        </CRow>
        {financiadoData &&
          financiadoData.id_Grupo === 36 &&
          financiadoData.coddatacob === "GVC" && (
            <CRow>
              <CCol>
                <CardHistoricoCresol />
              </CCol>
            </CRow>
          )}

        {checkPermission(
          permissaoHistoricosERegistros.modulo,
          "View",
          permissaoHistoricosERegistros.submodulo
        ) && (
            <CRow>
              <CCol>
                <CardHistorico
                  wrapMoment={wrapMoment}
                  onCloseModal={() => {
                    setWrapMoment(false);
                  }}
                />
              </CCol>
            </CRow>
          )}

      </CContainer>{" "}
      {mudarClienteModal && <MudarClienteModal onClose={onCloseClienteModal} />}
      {changeCrmModal &&
        (user?.datacobs.length > 1 || user?.datacobs.length === 0) && (
          <ChangeCrmModal
            onClose={(chosenCrm) => {
              setChangeCrmModal(false);
              if (chosenCrm) {
                setChosenCrm(chosenCrm);
              }
              setMudarClienteModal(true);
            }}
            onChangeCrm={onChangeCrm}
          />
        )}
      {editUserCrmAuthModal && (
        <EditUserCrmAuthModal
          editUser={user}
          onClose={() => {
            setEditUserCrmAuthModal(false);
          }}
        />
      )}
      {appSettings?.showVisaoBoleto && (
        <TicketChartModal
          onClose={() =>
            updateAppSettings({ ...appSettings, showVisaoBoleto: false })
          }
        />
      )}
      {/* {crmAuthModal && (
        <CrmAuthModal
          onClose={() => {
            setCrmAuthModal(false);
            setMudarClienteModal(true);
          }}
          chosenCrm={chosenCrm}
        />
      )} */}
    </div>
  );
};

export default TelaPrincipal;
