import { useState, useEffect } from "react";
import Select from "react-select";
import {
  CButton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
} from "@coreui/react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import LoadingComponent from "src/reusable/Loading";

const SelecionarDatacobModal = ({ isOpen, onClose, onConfirm }) => {
  const [selectedOption, setSelectedOption] = useState(null);
  const [datacobOptions, setDatacobOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleSelectChange = (selected) => {
    setSelectedOption(selected);
  };

  const handleConfirm = () => {
    onConfirm(selectedOption);
    onClose();
  };

  const updateView = () => {
    setIsLoading(true);
    getDatacobs(null, "getDatacobs")
      .then((data) => {
        if (data) {
          const uniqueDatacob = [...new Set(data.map((item) => item))];
          const optionsDatacob = [
            ...uniqueDatacob.map((x) => ({
              value: x.id,
              label: x.datacobName,
            })),
          ];
          setDatacobOptions(optionsDatacob);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const getDatacobs = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    if (isOpen) {
      updateView();
    }
  }, [isOpen]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Selecione o Datacob</CModalHeader>
      {isLoading ? (
        <div>
          <LoadingComponent />
        </div>
      ) : (
        <CModalBody>
          <div className="flex-grow-1">
            <Select
              className="mr-2"
              size="sm"
              placeholder={"Selecione..."}
              options={datacobOptions}
              value={datacobOptions.find(
                (option) => option.value === selectedOption
              )}
              onChange={handleSelectChange}
            />
          </div>
        </CModalBody>
      )}
      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Cancelar
        </CButton>
        <CButton
          color="primary"
          onClick={handleConfirm}
          disabled={!selectedOption}
        >
          Selecionar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default SelecionarDatacobModal;
