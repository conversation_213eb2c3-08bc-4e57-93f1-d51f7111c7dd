import { useEffect, useState } from "react";

let recognition: SpeechRecognition | null = null;

if ("webkitSpeechRecognition" in window) {
  recognition = new webkitSpeechRecognition();
  recognition.continuous = true;
  recognition.lang = "pt-BR";
}

const useSpeechRecognition = () => {
  const [speech, setSpeech] = useState("" as string);
  const [isListening, setIsListening] = useState(false);

  useEffect(() => {
    if (!recognition) return;

    recognition.onresult = (event: SpeechRecognitionEvent) => {
      recognition.stop();
      setSpeech(event?.results?.[0]?.[0]?.transcript ?? "");
      setIsListening(false);
    };
  }, []);

  const startListening = () => {
    setSpeech("");
    setIsListening(true);
    recognition?.start();
  };

  const stopListening = () => {
    recognition?.stop();
    setIsListening(false);
  };

  return {
    speech,
    isListening,
    startListening,
    stopListening,
    hasRegognitionSupport: !!recognition,
  };
};

export default useSpeechRecognition;
