import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CCol,
  CRow,
  CDataTable,
  CInputCheckbox,
} from "@coreui/react";
import { formatDateTime } from "src/reusable/helpers";

const SelecionarMensagensModal = ({ isOpen, onClose, onSave, dados }) => {
  const [selectedMessages, setSelectedMessages] = useState([]);

  const handleSelectAllCheckbox = (e) => {
    const isChecked = e.target.checked;
    if (isChecked) {
      setSelectedMessages([...dados]);
    } else {
      setSelectedMessages([]);
    }
  };

  const areAllMessagesSelected = () => {
    if (dados) {
      return selectedMessages.length === dados.length;
    } else return false;
  };

  const columns = [
    {
      key: "actions",
      label: (
        <CInputCheckbox
          onChange={handleSelectAllCheckbox}
          checked={areAllMessagesSelected()}
        />
      ),
      _style: { width: "5%", textAlign: "center", paddingLeft: "40px" },
    },
    { key: "received_at", label: "Data e Hora", _style: { width: "15%" } },
    { key: "user", label: "Usuário" },
    { key: "message", label: "Mensagem" },
  ];

  const treatedItems = dados.map((item) => {
    const decodedMessage = JSON.parse(item.message);
    let properMessage;
    try {
      properMessage = decodeURIComponent(decodedMessage);
    } catch (e) {
      properMessage = decodedMessage;
    }
    return {
      ...item,
      message: properMessage,
    };
  });

  const handleCheckbox = (item, target = null) => {
    let number = 0;
    if (target) {
      number = parseInt(target.name, 10);
    } else {
      number = item.id;
    }
    setSelectedMessages((prevSelected) => {
      const existingItem = prevSelected.find((item) => item.id === number);
      if (existingItem) {
        return prevSelected.filter((item) => item.id !== number);
      } else {
        return [
          ...prevSelected,
          {
            ...item,
          },
        ];
      }
    });
  };

  const handleSave = async () => {
    const textMessage = selectedMessages
      .map(
        (msg) =>
          `[${formatDateTime(msg.received_at)}] - [${
            msg.user ?? "Cliente"
          }] - "${msg.message}"`
      )
      .join("\n");
    onSave(textMessage);
    setSelectedMessages("");
    onClose();
  };

  const rowClassName = (item) => {
    if (selectedMessages) {
      return selectedMessages.some((msg) => msg.id === item.id)
        ? "selected-message"
        : "";
    }
  };

  useEffect(() => {
    setSelectedMessages(treatedItems);
  }, [isOpen]);

  return (
    <CModal
      className="custom-modal"
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
      size="xl"
    >
      <CModalHeader closeButton>
        <CModalTitle>Selecionar mensagens</CModalTitle>
      </CModalHeader>
      <CModalBody style={{ maxHeight: "520px", overflowY: "auto" }}>
        <CRow>
          <CCol>
            <CDataTable
              items={treatedItems}
              fields={columns}
              sorter
              scopedSlots={{
                actions: (item) => (
                  <td
                    className={rowClassName(item)}
                    style={{ textAlign: "center", paddingLeft: "40px" }}
                  >
                    {
                      <CInputCheckbox
                        name={item.id}
                        onChange={(e) => handleCheckbox(item, e.target)}
                        checked={selectedMessages.some(
                          (msg) => msg.id === item.id
                        )}
                      />
                    }
                  </td>
                ),
                received_at: (item) => (
                  <td
                    className={rowClassName(item)}
                    onClick={() => handleCheckbox(item)}
                  >
                    {formatDateTime(item.received_at)}
                  </td>
                ),
                user: (item) => (
                  <td
                    className={rowClassName(item)}
                    onClick={() => handleCheckbox(item)}
                  >
                    {item.user}
                  </td>
                ),
                message: (item) => (
                  <td
                    className={rowClassName(item)}
                    onClick={() => handleCheckbox(item)}
                  >
                    {item.message}
                  </td>
                ),
              }}
            />
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={onClose}>
          Cancelar
        </CButton>
        <CButton color="info" onClick={handleSave}>
          Salvar mensagens
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default SelecionarMensagensModal;
