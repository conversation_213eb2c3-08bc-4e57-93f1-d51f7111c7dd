import { useState, useEffect, useRef } from "react";
import { CRow, CCol, CButton, CNavItem, CNavLink, CNav } from "@coreui/react";
import _ from "lodash";

const AbasScrollable = ({ visibleTabs, currentTab, handleTabSelect }) => {
  const tabsRef = useRef(null);
  const [tabWidth, setTabWidth] = useState(0);
  const [tabPercentage, setTabPercentage] = useState(0);

  useEffect(() => {
    if (tabsRef.current) {
      const currentTabsRef = tabsRef.current;

      const handleResize = _.debounce(() => {
        setTabWidth((currentTabsRef.clientWidth / visibleTabs.length) * 2.5);
        setTabPercentage(
          currentTabsRef.clientWidth / currentTabsRef.scrollWidth
        );
      }, 100);

      const resizeObserver = new ResizeObserver(() => {
        handleResize();
      });

      if (currentTabsRef) {
        resizeObserver.observe(currentTabsRef);
      }
      return () => {
        if (currentTabsRef) {
          resizeObserver.unobserve(currentTabsRef);
        }
      };
    }
  }, [tabsRef, visibleTabs.length]);

  const scrollTabs = (direction) => {
    if (tabsRef.current && tabWidth > 0) {
      const scrollAmount = direction === "left" ? -tabWidth : tabWidth;
      tabsRef.current.scrollTo({
        left: tabsRef.current.scrollLeft + scrollAmount,
        behavior: "smooth",
      });
    }
  };

  return (
    <CNav className="custom-nav">
      <CRow
        className="mx-0 flex-grow-1"
        style={{ overflowX: "hidden", whiteSpace: "nowrap" }}
      >
        {tabsRef.current && tabPercentage <= 0.99 && (
          <CCol className="pl-1 pr-0">
            <CButton
              className="btn-aba d-flex justify-content-center"
              onClick={() => scrollTabs("left")}
            >
              {"<"}
            </CButton>
          </CCol>
        )}
        <CCol className="px-1 pt-1" md={tabPercentage <= 0.99 ? "11" : "12"}>
          <div
            ref={tabsRef}
            style={{
              display: "flex",
              transition: "0.3s ease",
              overflowX: "hidden",
            }}
          >
            {visibleTabs &&
              visibleTabs.length > 0 &&
              visibleTabs.map((tab) => (
                <div key={tab.id}>
                  <CNavItem
                    className={currentTab === tab.label ? "" : "nonactive-tab"}
                  >
                    <CNavLink
                      data-tab={tab.label}
                      onClick={() => handleTabSelect(tab)}
                    >
                      <i className={tab.icon} /> {tab.label}
                    </CNavLink>
                  </CNavItem>
                </div>
              ))}
          </div>
        </CCol>
        {tabsRef.current && tabPercentage <= 0.99 && (
          <CCol className="px-0">
            <CButton
              className="btn-aba d-flex justify-content-center"
              onClick={() => scrollTabs("right")}
            >
              {">"}
            </CButton>
          </CCol>
        )}
      </CRow>
    </CNav>
  );
};

export default AbasScrollable;
