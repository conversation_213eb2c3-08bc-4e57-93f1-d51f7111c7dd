import React from 'react';
import CIcon from "@coreui/icons-react";

export const IconWithBadge = ({ name, size, title, onClick, count }) => {
    return (
        <div style={{ position: 'relative', display: 'inline-block', marginLeft: '10px' }}>
            <CIcon name={name} size={size} title={title} onClick={onClick} style={{ fontSize: '24px' }} />
            {count > 0 && (
                <span
                    style={{
                        position: 'absolute',
                        top: '-5px',
                        right: '-5px',
                        backgroundColor: 'red',
                        color: 'white',
                        borderRadius: '50%',
                        padding: '1px 6px',
                        fontSize: '12px',
                        fontWeight: 'bold',
                    }}
                >
                    {count}
                </span>
            )}
        </div>
    );
};