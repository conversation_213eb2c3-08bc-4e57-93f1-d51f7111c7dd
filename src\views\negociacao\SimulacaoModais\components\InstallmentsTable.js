import React, { useState } from "react";
import {
  formatThousands,
  calculateDaysDifference,
  formatCurrency,
  formatDate,
} from "src/reusable/helpers";
import { filterInstallments } from "../functions/InstallmentsTableFunc";
import { CBadge, CTooltip, CButton } from "@coreui/react";

const SELECTED_STATUS = "Ab";

const InstallmentsTable = ({
  selectedParcelas = [],
  checkedAll = false,
  handleSelectAll,
  handleCheckParcela,
}) => {
  const contratos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const renderRow = (item) => {
    const filteredData = filterInstallments(item.parcelas, SELECTED_STATUS);
    let existeNumeroLongo = contratos.some(
      (contrato) =>
        contrato.numero_Contrato.includes(item.numero_Contrato) &&
        contrato.numero_Contrato.length === 25 &&
        item.numero_Contrato !== contrato.numero_Contrato &&
        contrato?.grupo?.toLowerCase()?.includes("safra") &&
        contrato.parcelas.filter((x) => x.status === "A").length > 0
    );
    if (filteredData.length > 0 && !existeNumeroLongo) {
      return (
        <React.Fragment key={item.id_Contrato}>
          <tr>
            <td colSpan={15} style={{ cursor: "pointer" }}>
              {" "}
              Nº Contrato: <strong>{item.numero_Contrato} </strong>{" "}
              {" - Nº Parcelas: "}
              <strong>{filteredData.length} </strong>
            </td>
          </tr>
          {renderInnerRows(filteredData)}
        </React.Fragment>
      );
    } else {
      return <></>;
    }
  };

  const renderInnerRows = (filteredData) => {
    return filteredData.map(
      (
        parcela //se for tirar o filtro vira parcelas.map
      ) => (
        <tr className={`expanded-table `} key={parcela.id_Parcela}>
          <td>
            <input
              type="checkbox"
              onClick={(e) =>
                handleCheckParcela(
                  e,
                  parcela.id_Parcela,
                  parcela.nome_Tipo_Parcela
                )
              }
              checked={selectedParcelas.includes(parcela.id_Parcela)}
            />
          </td>
          <td
            style={{
              borderLeft: "0",
            }}
          >
            {parcela.status === "P" ? (
              <CBadge color={"success"}>Pago</CBadge>
            ) : parcela.status === "D" ? (
              <CBadge style={{ backgroundColor: "#9000ff", color: "white" }}>
                Devolvido
              </CBadge>
            ) : parcela.nr_Acordo ? (
              <CBadge color={"info"}>Acordo</CBadge>
            ) : (
              <CBadge color={parcela.atraso > 0 ? "danger" : "warning"}>
                Aberto
              </CBadge>
            )}
          </td>
          <td>{parcela.nr_Parcela}</td>
          <td>{parcela.nome_Tipo_Parcela}</td>
          <td>
            {parcela.dt_Vencimento ? formatDate(parcela.dt_Vencimento) : "---"}
          </td>
          <td>
            {parcela.vl_Saldo ? formatThousands(parcela.vl_Saldo) : "0,00"}
          </td>
          <td>
            {parcela.vl_Original
              ? formatThousands(parcela.vl_Original)
              : "0,00"}
          </td>
          <td>
            {parcela.vl_Atualizado
              ? formatCurrency(parcela.vl_Atualizado, false)
              : "0,00"}
          </td>
          <td>
            {parcela.vl_Atualizado
              ? formatCurrency(parcela.vl_Desc_Max, false)
              : "0,00"}
          </td>
          <td>{parcela.atraso}</td>
        </tr>
      )
    );
  };
  return (
    <div className="table-container" style={{ maxHeight: "185px" }}>
      <table className="table tabs-table">
        <thead>
          <tr>
            <th>
              <input
                type="checkbox"
                onClick={(e) => handleSelectAll(e)}
                checked={checkedAll}
              />
            </th>
            <th>Status</th>
            <th>Parcela</th>
            <th>Tipo Parcela</th>
            <th>Vencimento</th>
            <th>Saldo</th>
            <th>Original</th>
            <th>Atualizado</th>
            <th>Desc. Máximo</th>
            <th>Atraso</th>
          </tr>
        </thead>
        <tbody>{contratos?.map(renderRow)}</tbody>
      </table>
    </div>
  );
};

export default InstallmentsTable;
