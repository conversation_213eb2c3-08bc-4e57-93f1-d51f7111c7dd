import React, { useEffect, useState } from "react";
import {
  <PERSON>ard,
  CCardBody,
  CLabel,
  CButton,
  CCol,
  <PERSON>ow,
  CSpinner,
  CTooltip,
} from "@coreui/react";
import { POST_DATA, GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { useMyContext } from "src/reusable/DataContext";
import { formatCurrency, formatDateToGlobal } from "src/reusable/helpers";
import CardLoading from "src/reusable/CardLoading";
import { getApi, postApi } from "src/reusable/functions";
import { toast } from "react-toastify";

const PostData = async (payload, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), null, true, true, id);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const CardNegociacaoStatus = ({ parcelas, handleCalculateNeg }) => {
  const { data } = useMyContext();
  const [loading, setLoading] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [list, setList] = useState([]);
  const id = data.id_Agrupamento;
  const searchStatus = async () => {
    setLoading(true);
    PostData(null, "getTUNegociacaoLivreByContract", id).then((response) => {
      setLoading(false);
      if (response?.success === false) return;
      setList(response.data);
    });
  };

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  useEffect(() => {
    searchStatus();
    const intervalId = setInterval(searchStatus, 60000);
    return () => clearInterval(intervalId);
  }, [id]);

  const handleNegotiation = (item) => {
    const json = JSON.parse(item.installmentJson);
    const selecionadas = parcelas.filter((x) => {
      const select = json.find((i) => i.contract === x.nrContrato);
      if (!select) return false;
      const parc = select.installment.map((p) => Number(p));
      return parc.includes(x.nrParcela);
    });

    const parcelasOld = [...parcelas];
    const selecionadasP = parcelasOld.map((item) => {
      item.parcelaSelecionada = selecionadas.some(
        (i) => i.idParcela === item.idParcela
      );
      return item;
    });

    const date = new Date(formatDateToGlobal(item.dueDate) + " 00:00:00");
    handleCalculateNeg(
      Number(item.value.replaceAll(".", "").replaceAll(",", ".")),
      date,
      selecionadasP,
      true
    );
  };

  const handleRetry = async (id) => {
    setLoadingBtn(true);
    const post = await postApi(
      {
        id: id,
        statusAutomation: "Aguardando",
        statusNegotiation: "Pendente",
      },
      "postSaveNegUpdateStatus"
    );
    if (post.success) {
      const exec = await getApi(null, "getSaveNegExecute");

      if (exec) toast.success("Execução em andamento!");
      else toast.error("Erro ao executar ação!");

      // searchStatus();
      //
    } else {
      toast.error("Erro ao atualizar informações!");
    }
    setLoadingBtn(false);
  };

  return (
    <CCard style={{}}>
      <CCardBody style={{ padding: "10px" }}>
        <CRow style={{ fontWeight: "bold" }}>
          <CCol xs="3">
            <CLabel>Negociação</CLabel>
          </CCol>
          <CCol xs="3">
            <CLabel>Valor</CLabel>
          </CCol>
          <CCol xs="4">
            <CLabel>Status</CLabel>
          </CCol>
          {list.length > 0 && !loading && (
            <CCol xs="2">
              <CButton
                color="warning"
                style={{ color: "white", float: "right" }}
                onClick={searchStatus}
              >
                Atualizar
              </CButton>
            </CCol>
          )}
        </CRow>
        {loading && <CardLoading size="lg" />}
        {!loading && (
          <div
            style={{
              overflow: "auto",
              overflowX: "hidden",
              height: "130px",
              marginTop: "10px",
            }}
          >
            {list.length === 0 ? (
              <CRow>
                <CCol>
                  <CLabel>Nenhuma negociação encontrada</CLabel>
                </CCol>
              </CRow>
            ) : (
              list.map((item, index) => {
                return (
                  <CRow key={index}>
                    <CCol xs="3">
                      <button
                        onClick={() => handleNegotiation(item)}
                        style={
                          item.idNeg &&
                          item.statusAprovacao?.indexOf("Aprovado") > -1
                            ? {
                                border: "none",
                                background: "none",
                                cursor: "pointer",
                                color: "blue",
                              }
                            : {
                                border: "none",
                                background: "none",
                                color: "gray",
                              }
                        }
                        disabled={
                          !item.idNeg ||
                          item.statusNegotiation?.indexOf("Aprovado") === -1
                        }
                      >
                        {item.idNeg ? item.idNeg : "---"}
                      </button>
                    </CCol>
                    <CCol xs="3">
                      <CLabel>
                        R$ {formatCurrency(item.vlNegociacao, false)}
                      </CLabel>
                    </CCol>
                    <CCol xs="3">
                      <CLabel>{item.statusAprovacao}</CLabel>
                    </CCol>
                    <CCol xs="3" style={{ textWrap: "nowrap" }}>
                      <CLabel>
                        {item?.observacao &&
                          item?.statusAprovacao === "Rejeitado" && (
                            <CTooltip content={item?.observacao}>
                              <CButton
                                className="mr-1"
                                color="info"
                                style={{ cursor: "initial" }}
                              >
                                <i className="cil-hamburger-menu" />
                              </CButton>
                            </CTooltip>
                          )}
                      </CLabel>
                    </CCol>
                  </CRow>
                );
              })
            )}
          </div>
        )}
      </CCardBody>
    </CCard>
  );
};

export default CardNegociacaoStatus;
