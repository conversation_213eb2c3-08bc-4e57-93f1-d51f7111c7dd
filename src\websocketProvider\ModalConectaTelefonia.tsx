import React, { useState } from 'react';
import { CButton, CModal, CModalBody, CModalFooter, CModalHeader } from "@coreui/react";
import { postAutenticar } from "src/config/telephonyFunctions";
import { useWebsocketTelefoniaContext } from './websocketTelefoniaProvider';
import LoadingComponent from 'src/reusable/Loading';
import { handleSoftphoneOlos } from "src/views/telefonia/SoftphoneBroadcastChannel";

type Props = {
    isOpen: boolean;
    passcode: number;
    agentId: number;
    onClose: () => void;
    isSoftphoneOlosAllowed: boolean;
};

const ModalConectaTelefonia = ({ isOpen, onClose, passcode, agentId, isSoftphoneOlosAllowed }: Props) =>{
    const {passCodeRef} = useWebsocketTelefoniaContext();
    const [loadingAuthCod, setLoadingAuthCod] = useState(false);
    const [disableButton, setDisableButton] = useState(true);

    function generateNewCod() {
        setLoadingAuthCod(true);
        postAutenticar().finally(() => {
            //time 5 seconds
            setTimeout(() => {
                setLoadingAuthCod(false);
                setDisableButton(false);
                if (isSoftphoneOlosAllowed) {
                  handleSoftphoneOlos();
                  onClose();
                }
            }, 5000);
        });
    }

    const reconnect = () => {
        if(agentId && passCodeRef && !isSoftphoneOlosAllowed)
            window.open(`https://rodobens.oloschannel.com.br/softphonewebrtc_unextended/?remote_address=rodobens-ecs01.oloschannel.com.br&passcode=${passCodeRef.current}&agent_id=${agentId}`, '_blank');
    }
    return (
        <CModal show={isOpen} size="lg" onClose={onClose} closeOnBackdrop={false}>
            <CModalHeader closeButton><h3>Atenção Operador !!!</h3></CModalHeader>
            <CModalBody>
                <h5 className='text-center'>Identificamos que você está desconectado do sistema de telefonia!</h5>
                <h5 className='text-center'>O que deseja fazer?</h5>

            </CModalBody>
            <CModalFooter className="justify-content-between">
                <div className="d-flex justify-content-center">Cod Olos: {passCodeRef.current}</div>
                <div className="d-flex justify-content-center">
                    <button className="btn btn-primary d-flex align-items-center" onClick={() => generateNewCod()}>
                        {loadingAuthCod && <span className='mr-2' ><LoadingComponent size='sm' /></span>}
                        <span>
                            Gerar novo cod. OLOS
                        </span>
                    </button>
                    {(!isSoftphoneOlosAllowed) && (
                      <CButton
                          className="btn btn-success ml-2"
                          onClick={reconnect}
                          disabled={!!disableButton}
                          title={"Reconectar OLOS"}
                      >
                          Reconectar OLOS
                      </CButton>
                    )}
                    <button className="btn btn-danger ml-2" onClick={() => onClose()}>Manter desconectado</button>
                </div>
            </CModalFooter>
        </CModal>

    );
}

export default ModalConectaTelefonia;
