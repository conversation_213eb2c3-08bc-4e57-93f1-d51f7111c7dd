import React, { useState, useEffect } from "react";
import {
  CButton,
  CForm,
  CFormGroup,
  CInput,
  CCard,
  CCardBody,
  CCardHeader,
} from "@coreui/react";
import Select from "react-select";
import { postCallCampaign } from "src/config/telephonyFunctions";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LoadingComponent from "src/reusable/Loading";
import { getCampaigns } from "src/config/telephonyFunctions";
import TableSelectItens from "src/reusable/TableSelectItens";
import { formatDateTime } from "src/reusable/helpers";
import { useAuth } from "src/auth/AuthContext";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";

const ParametriCampanhas = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Telefonia",
    submodulo: "Campanhas de Ligação",
  };
  const [groupOptions, setGroupOptions] = useState([]);
  const [selectedGrupo, setSelectedGrupo] = useState(null);

  const userProfile = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : {};

  const handleSelectGrupo = (selectedOption) => {
    setSelectedGrupo(selectedOption);
  };
  const getGroups = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const updateGrupoOptions = () => {
    const payload = {
      ActiveConnection: selectedDatacob.datacobNumber,
    };
    getGroups(payload, "getDatacobGroups")
      .then((data) => {
        if (data) {
          const groupList = data.map((group) => ({
            id: group.id_Grupo,
            name: group.descricao,
          }));

          const allOption = { id: "", name: "Todos" };
          const optionsGroup = [allOption, ...groupList];
          setGroupOptions(optionsGroup);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const [campaignId, setCampaignID] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [tableData, setTableData] = useState(null);
  const [selectedDatacob, setSelectedDatacob] = useState(null);
  const [datacobOptions, setDatacobOptions] = useState([]);

  const handleSelectDatacob = (selectedOption) => {
    setSelectedDatacob(selectedOption);
  };
  const getAllDatacobs = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };
  const updateOptionsAdm = () => {
    setIsLoading(true);
    getAllDatacobs(null, "getDatacobs")
      .then((data) => {
        if (data) {
          const uniqueDatacob = [...new Set(data.map((item) => item))];
          const optionsDatacob = [
            ...uniqueDatacob.map((x) => ({
              datacobNumber: x.datacobNumber,
              datacobName: "Datacob " + x.datacobName,
            })),
          ];
          setDatacobOptions(optionsDatacob);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const fields = [
    { key: "campaignId", label: "ID da Campanha" },
    { key: "groupId", label: "ID do Grupo" },
    { key: "crm", label: "CRM" },
    // { key: "id", label: "ID" },
    {
      key: "createdAt",
      label: "Criada Em",
      formatter: (item) => formatDateTime(item),
    },
    {
      key: "updatedAt",
      label: "Atualizada Em",
      formatter: (item) => formatDateTime(item),
    },
  ];
  function validate() {
    const isValidCampaign = /^[0-9]+$/.test(campaignId);
    if (!isValidCampaign) {
      alert("Por favor, insira o ID da campanha.");
      return false;
    }
    return true;
  }

  const handleAdd = async () => {
    setIsLoading(true);
    const validFields = validate();
    if (validFields) {
      await postCallCampaign(
        campaignId,
        selectedGrupo?.id,
        selectedDatacob.datacobNumber
      );
      toast.success("Campanha adicionada com sucesso!");
    }
    clearInputs();
    setIsLoading(false);
  };

  const clearInputs = () => {
    setCampaignID("");
    // setSelectedItem(null);
  };
  const updateView = () => {
    setIsLoading(true);
    getCampaigns()
      .then((data) => {
        if (data) {
          setTableData(data);
        } else {
          setTableData(null);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    updateView();
    updateOptionsAdm();
  }, []);

  useEffect(() => {
    if (selectedDatacob) {
      updateGrupoOptions();
    }
  }, [selectedDatacob]);

  return (
    <div>
      <h3>Parametrização de Campanhas</h3>
      <p style={{ color: "gray" }}>
        Definir quais campanhas podem fazer ligações.
      </p>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard style={{ width: "60%" }}>
          <CCardBody>
            <CForm>
              <CFormGroup>
                <CInput
                  type="text"
                  placeholder="ID da Campanha"
                  value={campaignId}
                  onChange={(e) =>
                    setCampaignID(e.target.value.replace(/[^0-9.]/g, ""))
                  }
                />
              </CFormGroup>
              <CFormGroup>
                <Select
                  value={selectedDatacob}
                  onChange={handleSelectDatacob}
                  options={datacobOptions}
                  getOptionValue={(option) => option.datacobNumber}
                  getOptionLabel={(option) => option.datacobName}
                  placeholder={"Selecione um CRM"}
                />
              </CFormGroup>
              <CFormGroup>
                <Select
                  placeholder="Selecione o Grupo"
                  value={selectedGrupo}
                  onChange={handleSelectGrupo}
                  options={groupOptions}
                  getOptionValue={(option) => option.id}
                  getOptionLabel={(option) => option.name}
                />
              </CFormGroup>
              {isLoading ? (
                <div>
                  <LoadingComponent />
                </div>
              ) : (
                <CButton
                  color="info"
                  onClick={handleAdd}
                  className="mr-2"
                  title={inforPermissions(permissao).create}
                  disabled={
                    !checkPermission(
                      permissao.modulo,
                      "Create",
                      permissao.submodulo
                    )
                  }
                >
                  Adicionar
                </CButton>
              )}
            </CForm>
          </CCardBody>
        </CCard>
      </div>{" "}
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <CCard style={{ width: "80%" }}>
          <CCardHeader style={{ textAlign: "center" }}>
            <h4>Campanhas Registradas</h4>
          </CCardHeader>
          <CCardBody>
            <TableSelectItens
              data={tableData}
              columns={fields}
              onSelectionChange={(_) => {}}
              defaultSelectedKeys={[]}
              selectable={false}
              heightParam="100%"
            />
          </CCardBody>
        </CCard>
      </div>
    </div>
  );
};

export default ParametriCampanhas;
