import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>ge, CCardBody, CButton } from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";
import LoadingComponent from "src/reusable/Loading";
import TableSelectItens from "src/reusable/TableSelectItens";
import FormCustasModal from "src/views/juridico/partials/FormCustasModal";
import { getApi } from "src/reusable/functions";
import { useMyContext } from "src/reusable/DataContext";

const Custas = ({ pasta, selected }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [modalShow, setModalShow] = useState(false);
  const [dataJuridico, setDataJuridico] = useState(false);
  const { updateCustasProjuris } = useMyContext();
  const [first, setFirst] = useState(true);

  const payload = {
    Pasta: pasta || null,
  };

  const updateView = (_) => {
    if (payload.Pasta !== null) {
      setIsLoading(true);
      setFirst(false);
      getApi(payload, "getcustasprojuris")
        .then((response) => {
          if (response) {
            updateCustasProjuris(response);
            setTableData(response);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  };

  const tableColumns = [
    { key: "pago", label: "Pago", formatter: (value) => renderBadge(value) },
    {
      key: "data_De_Lancamento",
      label: "Dt. Lançamento",
      formatter: (value) => formatDate(value),
    },
    { key: "lancamento", label: "Lançamento" },
    { key: "tipo_De_Lancamento", label: "Tipo" },
    {
      key: "valor_Devido",
      label: "Valor Devido",
      formatter: (value) => formatThousands(value ?? 0),
      totalizer: "sum",
      defaultValue: 0,
    },
    {
      key: "valor_Pago",
      label: "Valor Pago",
      formatter: (value) => formatThousands(value ?? 0),
      totalizer: "sum",
    },
    { key: "", label: "", formatterByObject: (value) => renderBtn(value) },
  ];

  const renderBtn = (value) => {
    return (
      <CButton
        color="info"
        onClick={() => {
          setModalShow(true);
          setDataJuridico(value);
        }}
      >
        Inserir Datacob
      </CButton>
    );
  };

  const renderBadge = (status) => {
    switch (status) {
      case "Sim":
        return (
          <CBadge
            shape="pill"
            color="success"
            className="justify-content-center"
          >
            <i className="cil-check-alt" />
          </CBadge>
        );
      case "Não":
        return (
          <CBadge
            shape="pill"
            color="danger"
            className="justify-content-center"
          >
            <i className="cil-x" />
          </CBadge>
        );
      default:
        return <div></div>;
    }
  };

  useEffect(() => {
    if (selected === true && pasta && first) {
      updateView();
    }
  }, [selected]);

  useEffect(() => {
    setFirst(true);
  }, [pasta]);

  return (
    <>
      <CCardBody>
        {isLoading ? (
          <LoadingComponent
            text={
              "Aguarde! Buscando dados no Projuris! Isso pode demorar um pouco."
            }
          />
        ) : (
          <TableSelectItens
            data={tableData}
            columns={tableColumns}
            onSelectionChange={(_) => {}}
            defaultSelectedKeys={[]}
            selectable={false}
            heightParam="290px"
          />
        )}
      </CCardBody>

      {modalShow && (
        <FormCustasModal
          isOpen={modalShow}
          onClose={() => setModalShow(false)}
          dataJuridico={dataJuridico}
        />
      )}
    </>
  );
};

export default Custas;
