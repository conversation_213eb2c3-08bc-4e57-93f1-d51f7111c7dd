import React, { useState, useEffect } from "react";
import { CDataTable, CCardBody, CButton } from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";
import { getURI } from "src/config/apiConfig";
import { GET_DATA, POST_DATA, GET_DOWNLOAD } from "src/api";
import LoadingComponent from "src/reusable/Loading";
import AlertaModal from "src/views/negociacao/AlertaModal";
import webSocketDocService from "src/config/webSocketDocService";

import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import TableSelectItens from "src/reusable/TableSelectItens";

const Documentos = ({ pasta, selected }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [showAlertaModal, setShowAlertaModal] = useState(false);

  const payload = {
    Pasta: pasta || null,
  };

  const tableColumns = [
    {
      key: "nome_Original",
      label: "Nome Original",
      formatterByObject: (item) => getLinkDocument(item)
    },
    {
      key: "tipo_Documento",
      label: "Tipo Documento",
      style: { whiteSpace: "nowrap" },
    },
    { key: "descricao", label: "Descrição" },
    { key: "data", label: "Data",formatter: (value) => formatDate(value) },
    { key: "tamanho", label: "Tamanho",formatter: (value) => formatThousands(value??0) },
    { key: "status", label: "Status" },
  ];

  const updateView = () => {
    if (payload.Pasta !== null) {
      setIsLoading(true);
      getDocumentos(payload, "getdocumentosprojuris")
        .then((data) => {
          if (data) {
            setTableData(data);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  };

  const getDocumentos = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const downloadFile = async (fileName) => {
    await GET_DOWNLOAD(`Projuris/Documentos/Download/${fileName}`);
    toast.info("Download do documento começará em breve.")
    // const file = await GET_DOWNLOAD(`Projuris/Documentos/Download/${fileName}`);

    // const anchor = document.createElement("a");
    // anchor.download = fileName;
    // anchor.href = "data:application/octet-stream;base64," + file;
    // anchor.target = "_blank";
    // document.body.appendChild(anchor);
    // anchor.click();
  };

  const requestDownload = async (item) => {
    const fileName = {
      folder: item.pasta,
      fileName: item.nome_Original,
      fileDate: item.data,
    };

    postRPA(fileName, "postdownloaddocrpa")
      .then((data) => {
        if (data) {
          if (data.data) {
            downloadFile(data.data);
          } else {
            toast.info("Requisição do documento em andamento.");
            // toast.info(data.message);
          }
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const postRPA = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleDocumento = async (documento) => {
    await requestDownload(documento);
  };

  useEffect(() => {
    if (selected === true && pasta) {
      updateView();
    }
  }, [selected,pasta]);

  const getLinkDocument = (item) => {
    return (<CButton
    className="button-link nowrap-cell"
    onClick={() => handleDocumento(item)}
  >
    {item.nome_Original}
  </CButton>)
  }

  return (
    <CCardBody>
      {isLoading ? (
        <LoadingComponent />
      ) : (
        <>
          {/* <div style={{ width: "fit-content", overflowY: "auto" }}> */}
          <TableSelectItens data={tableData}
          columns={tableColumns}
          onSelectionChange={_ => { }}
          defaultSelectedKeys={[]}
          selectable={false}
          heightParam="290px"
        />

          {/* </div> */}
          <AlertaModal
            isOpen={showAlertaModal}
            onClose={() => setShowAlertaModal(false)}
            dados="Seu processo está em andamento."
          />
        </>
      )}
    </CCardBody>
  );
};

export default Documentos;
