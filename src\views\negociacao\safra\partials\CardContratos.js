import React, { useState, useEffect } from "react";
import { <PERSON>utton, CCard, CCardBody, CCardHeader } from "@coreui/react";
import CardLoading from "src/reusable/CardLoading";
import CardAvisos from "src/reusable/CardAvisos";
import { getURI } from "src/config/apiConfig";
import { POST_DATA } from "src/api";
import CardParcelas from "./CardParcelas";
import TableContratos from "./TableContratos";
import FormNeogciacaoModal from "./FormNeogciacaoModal";

const PostData = async (
  payload,
  endpoint = "cyberSafraconsultarElegibilidade"
) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), payload, true, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const CardContratos = ({ onNegociarClick, buscarAcordo }) => {
  let financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";
  let contratosData = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : "";
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");
  const [msgAviso, setMsgAviso] = useState("");
  const [titleAviso, setTitleAviso] = useState("");

  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");

  const [contratosSelecionados, setContratosSelecionados] = useState(null);
  const [dadosContratos, setDataContratos] = useState(null);
  const [showTableContrato, setShowTableContrato] = useState(false);

  const [modalShow, setModalShow] = useState(false);

  const ConsultarContratosElegiveis = async (contrato) => {
    let ret = false;
    const payload = {
      cliente: financiadoData.cpfCnpj,
      contratos: [
        {
          grupo: "3",
          contrato: contrato,
        },
      ],
    };
    setMsgAvisoLoading(`Consultando contrato ${contrato} Elegíveis`);
    setTitleAvisoLoading("Consultando contratos Elegíveis");
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(payload, "cyberSafraconsultarElegibilidade")
      .then((data) => {
        if (data.success) {
          ret = data.data.tiposAcordo;
        }
      })
      .catch((err) => {
        setTitleAviso(`Erro na chamada das APIS`);
        setMsgAviso(
          `Erro na chamada API de Cyber Consultar Elegibilidade, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`
        );
      })
      .finally(() => {
        setLoading(false);
        setLoadingAction("empty");
        setMsgAvisoLoading("");
        setTitleAvisoLoading("");
      });

    return ret;
  };

  const ConsultarSaldoContratos = async (contrato) => {
    let ret = false;

    contrato.parcelas = contrato.parcelas.filter(
      (parcela) => parcela.status === "A"
    );

    const payload = {
      cliente: financiadoData.cpfCnpj,
      dtReferencia: new Date().toISOString().split("T")[0],
      contratos: [
        {
          grupo: "3",
          contrato: contrato.numero_Contrato,
          parcelas: [
            {
              numParc: contrato.parcelas[0]?.nr_Parcela?.toString(),
              valorParc: contrato.parcelas[0]?.vl_Original,
              dataVenc: contrato.parcelas[0]?.dt_Vencimento?.split("T")[0],
            },
          ],
        },
      ],
    };
    setMsgAvisoLoading(
      `Consultando contrato ${contrato.numero_Contrato} Dados Safra`
    );
    setTitleAvisoLoading("Consultando Dados Safra, aguarde...");
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(payload, "cyberSafraConsultaSaldo")
      .then((data) => {
        if (data.success) {
          const contratos = data.data.contratos;
          ret = contratos.filter(
            (item) => item.contrato === contrato.numero_Contrato
          )[0];
        } else {
          setTitleAviso("Contratos Sem Dados Safra");
          setMsgAviso(
            `O contrato ${contrato.numero_Contrato} não possui dados de cobrança no Safra devido a falha na busca de Saldo ou contrato inelegível!`
          );
        }
      })
      .catch((err) => {
        setTitleAviso(`Erro na chamada das APIS`);
        setMsgAviso(
          `Erro na chamada API de Cyber Consultar Elegibilidade, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`
        );
      })
      .finally(() => {
        setLoading(false);
        setLoadingAction("empty");
        setMsgAvisoLoading("");
        setTitleAvisoLoading("");
      });

    return ret;
  };

  const validaConstratoElegiveis = async () => {
    const contratosElegiveis = [];
    setTitleAviso("");
    setMsgAviso("");
    setShowTableContrato(false);
    setContratosSelecionados(null);
    onNegociarClick(null);
    if (contratosData.length > 0) {
      for (const contrato of contratosData) {
        const ret = await ConsultarContratosElegiveis(contrato.numero_Contrato);
        if (ret) {
          contrato.elegivel = ret;
          contratosElegiveis.push(contrato);
        }
      }

      if (contratosElegiveis.length > 0) {
        for (const contrato of contratosElegiveis) {
          const saldo = await ConsultarSaldoContratos(contrato);
          if (saldo) {
            contrato.dadosSafra = saldo;
          }
        }
        setDataContratos(contratosElegiveis);
        setShowTableContrato(true);
      } else {
        setTitleAviso("Não há contratos Elegíveis");
        setMsgAviso("Não há contratos Elegíveis para acordo");
      }
    } else {
      setTitleAviso("Validação de contratos Ativos");
      setMsgAviso("Não há contratos Ativos");
    }
  };

  useEffect(() => {
    validaConstratoElegiveis();
  }, []);

  const handleContratoSelection = (contrato) => {
    contrato.dadosSafra.vlrHonorario = 0;
    setContratosSelecionados(contrato);
    onNegociarClick(contrato);
    buscarAcordo("");
  };

  const handleClose = () => {
    setModalShow(false);
    validaConstratoElegiveis();
    buscarAcordo("OK");
  };

  return (
    <div>
      <CCard>
        <CCardHeader>
          <h5 className="d-flex justify-content-between">
            <span>Contratos Financiado</span>
            <div>
              {contratosSelecionados != null ? (
                <CButton
                  className={"mr-2"}
                  color="success"
                  onClick={() => setModalShow(true)}
                >
                  Negociar
                </CButton>
              ) : (
                ""
              )}
              <CButton
                color="success"
                disabled={financiadoData.id_Agrupamento == null || loading}
                onClick={() => validaConstratoElegiveis()}
              >
                <i className="cil-reload" />
              </CButton>
            </div>
          </h5>
        </CCardHeader>
        <CCardBody>
          {titleAviso !== "" && msgAviso !== "" ? (
            <CardAvisos Title={titleAviso} Msg={msgAviso} />
          ) : loading && loadingAction === "VarifyParam" ? (
            <CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />
          ) : showTableContrato && dadosContratos !== null ? (
            <TableContratos
              dataTable={dadosContratos}
              contratoIndex={contratosSelecionados}
              onContratoClick={handleContratoSelection}
            />
          ) : (
            ""
          )}
        </CCardBody>
      </CCard>
      <div>
        {contratosSelecionados != null ? (
          <CCard>
            <CCardBody>
              <CardParcelas
                dataParcelas={
                  contratosSelecionados.dadosSafra.parcelasOriginais
                }
              />
            </CCardBody>
          </CCard>
        ) : (
          ""
        )}
      </div>
      {modalShow && (
        <FormNeogciacaoModal
          isOpen={modalShow}
          onClose={handleClose}
          contrato={contratosSelecionados}
        />
      )}
    </div>
  );
};

export default CardContratos;
