import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CButton,
  CForm,
  CFormGroup,
  CLabel,
  CInput,
  CCard,
  CRow,
  CCol,
  CCardBody,
  CCardHeader,
  CInputRadio,
} from "@coreui/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import Select from "react-select";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { formatDate, formatThousands } from "src/reusable/helpers";
import AlertaModal from "../negociacao/AlertaModal";

import CardLoading from "src/reusable/CardLoading";
import LoadingComponent from "src/reusable/Loading";

import { useAuth } from "src/auth/AuthContext";

const ReenvioBoletoModal = ({ isOpen, onClose, dados }) => {
  const { checkPermission, inforPermissions } = useAuth();

  const permissaoNegociacaoBoleto = {
    modulo: "Negociação",
    submodulo: "Boleto",
  };

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : "";

  const clientData = localStorage.getItem("clientData")
    ? JSON.parse(localStorage.getItem("clientData"))
    : "";

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";

  const [email, setEmail] = useState("");
  const [selectedEmail, setSelectedEmail] = useState(null);
  const [emailList, setEmailList] = useState([]);

  const [selectedTipoEnvio, setSelectedTipoEnvio] = useState({
    value: "Email",
    label: "E-mail",
  });
  const [descricao, setDescricao] = useState("");
  const [checkboxValue, setCheckboxValue] = useState(false);

  const [showAlertaModal, setShowAlertaModal] = useState(false);
  const [alertaMessage, setAlertaMessage] = useState("");

  const [selectedNegociador, setSelectedNegociador] = useState(null);
  const [negociadorOptions, setNegociadorOptions] = useState([]);

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingImpressao, setIsLoadingImpressao] = useState(false);
  const [tipoEnvio, setTipoEnvio] = useState("Email");

  const handleTipoEnvioChange = (target) => {
    setSelectedTipoEnvio(target);
    setTipoEnvio(target.value);
  };

  const handleEmailChange = (target) => {
    setSelectedEmail(target);
    setEmail(target.label);
  };

  const handleInputChange = (event) => {
    setEmail(event.target.value);
  };

  const handleDescricaoChange = (event) => {
    setDescricao(event.target.value);
  };

  const handleCheckboxChange = (event) => {
    setCheckboxValue(event.target.checked);
  };

  const impressaoBoleto = async () => {
    setIsLoadingImpressao(true);
    const data = { IdBoleto: dados.IdBoleto };
    const boleto = await GET_DATA("Datacob/DownloadBoleto", data);

    // Defina um título para a nova janela
    const windowTitle = "Visualização de PDF";

    // Crie uma nova janela com título
    const newWindow = window.open(
      "",
      "_blank",
      `width=800,height=600,toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,title=${windowTitle}`
    );

    // Escreva o conteúdo do PDF na nova janela
    newWindow.document.open();
    newWindow.document.write(
      '<embed width="100%" height="100%" src="data:application/pdf;base64,' +
        boleto +
        '" type="application/pdf"/>'
    );
    newWindow.document.close();
    setIsLoadingImpressao(false);
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);
    let success = false;
    const payload = {
      idGrupo: dados.idGrupo,
      idBoleto: dados.IdBoleto,
      email: email,
      nome: financiadoData?.nome,
      crm: financiadoData?.coddatacob,
    };
    try {
      //const result = await boletoPost(dados);
      if (selectedTipoEnvio.value === "Impressao") {
        success = true;
      } else if (selectedTipoEnvio.value === "Email") {
        await postData(payload, "getBoletoEmail")
          .then((data) => {
            success = true;
          })
          .catch((err) => {
            throw err;
          });
      }
      if (success) toast.success("Boleto enviado com sucesso!");
      else toast.warn("Não foi possível reenviar o boleto.");
      onClose();
    } catch (error) {
      console.error("An error occurred during form submission:", error);
      toast.warn(error);
    } finally {
      setIsLoading(false);
      onClose();
    }
  };

  const handleClose = () => {
    onClose();
  };

  const handleNegociadorChange = (target) => {
    setSelectedNegociador(target);
  };

  const handleAlertaClose = () => {
    setShowAlertaModal(false);
  };

  const getUsers = async () => {
    const currentUser = user;
    const payload = { ActiveConnection: user.activeConnection };
    getData(payload, "getDatacobUsers")
      .then((data) => {
        if (data) {
          const options = data.map((x) => {
            return {
              label: x.nome,
              value: x.id_Usuario,
            };
          });
          setNegociadorOptions(options);
          const findUser = data.find((user) => currentUser.name === user.nome);
          if (findUser) {
            const negociador = {
              label: findUser.nome,
              value: findUser.id_Usuario,
            };
            setSelectedNegociador(negociador);
          }
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const getData = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };
  const postData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(
          getURI(endpoint),
          payload,
          true,
          false
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    if (isOpen) {
      getUsers();
      if (clientData) {
        setEmailList(clientData.emails);
      }
    }
  }, [isOpen]);

  const emailOptions = [
    ...emailList.map((x) => ({
      value: x.id_Email,
      label: x.endereco_Email,
    })),
  ];

  const optionsTipoEnvio = [
    { value: "Email", label: "E-mail" },
    // { value: "Impressao", label: "Impressão" },
  ];

  return (
    <CModal size="lg" show={isOpen} onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Reenvio de Boleto</CModalHeader>
      <CModalBody>
        {isLoading ? (
          <CardLoading Title={"Reenviando boleto..."} />
        ) : (
          <CRow>
            <CCol>
              <CCard>
                <CCardHeader className="py-1">Dados de Envio</CCardHeader>
                <CCardBody className="py-1">
                  <CForm>
                    <CFormGroup>
                      <CRow>
                        <CCol>
                          <CLabel>Tipo de Envio</CLabel>
                          <Select
                            value={selectedTipoEnvio}
                            options={optionsTipoEnvio}
                            placeholder="Selecione"
                            onChange={handleTipoEnvioChange}
                            // isDisabled
                          />
                        </CCol>
                      </CRow>
                      <div>
                        {tipoEnvio !== "Email" ? (
                          <>
                            <CLabel>Enviar boleto como</CLabel>
                            <CFormGroup variant="checkbox">
                              <CInputRadio
                                id="pdf"
                                name="checkbox"
                                value="pdf"
                                checked={tipoEnvio === "Impressao"}
                                disabled
                              />
                              <CLabel htmlFor="pdf">PDF</CLabel>
                            </CFormGroup>
                          </>
                        ) : (
                          <>
                            <CLabel>Emails disponíveis</CLabel>
                            <Select
                              value={selectedEmail}
                              options={emailOptions}
                              onChange={handleEmailChange}
                              placeholder="Selecione"
                            />
                            <CLabel>Email destinatário</CLabel>
                            <CInput
                              type="text"
                              value={email}
                              onChange={handleInputChange}
                            />
                          </>
                        )}
                      </div>
                    </CFormGroup>
                  </CForm>
                </CCardBody>
              </CCard>
            </CCol>
            <CCol>
              <CCard>
                <CCardHeader>Valores</CCardHeader>
                <CCardBody>
                  <CRow>
                    <CCol>
                      <CLabel>Valor Atualizado</CLabel> <br />
                      <CLabel>R$ {formatThousands(dados?.vlTotal)}</CLabel>
                    </CCol>
                    <CCol>
                      <CLabel>Taxa Bancária</CLabel> <br />
                      <CLabel>0,00</CLabel>
                    </CCol>
                  </CRow>
                  <CRow>
                    <CCol>
                      <CLabel>Total do Boleto</CLabel> <br />
                      <CLabel>R$ {formatThousands(dados?.vlTotal)}</CLabel>
                    </CCol>
                    <CCol>
                      <CLabel>Vencimento</CLabel> <br />
                      <CLabel>{formatDate(dados?.dataVencimento)}</CLabel>
                    </CCol>
                  </CRow>
                </CCardBody>
              </CCard>
            </CCol>
          </CRow>
        )}
        <CRow>
          <CCol className="d-flex justify-content-end">
            <CButton
              color="warning"
              className="mr-2"
              onClick={() => {
                impressaoBoleto();
              }}
              disabled={isLoading}
            >
              {isLoadingImpressao ? <LoadingComponent /> : "Visualizar"}
            </CButton>
            <CButton
              color="secondary"
              className="mr-2"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancelar
            </CButton>
            <CButton
              type="submit"
              color="primary"
              onClick={handleSubmit}
              disabled={
                selectedTipoEnvio === null ||
                (selectedTipoEnvio.value === "Email" && email === "") ||
                isLoading ||
                !checkPermission(
                  permissaoNegociacaoBoleto.modulo,
                  "Create",
                  permissaoNegociacaoBoleto.submodulo
                )
              }
              title={inforPermissions(permissaoNegociacaoBoleto).create}
            >
              {isLoading ? <LoadingComponent /> : "Ok"}
            </CButton>
          </CCol>
        </CRow>
      </CModalBody>
      <AlertaModal
        isOpen={showAlertaModal}
        onClose={handleAlertaClose}
        dados={alertaMessage}
      />
    </CModal>
  );
};

export default ReenvioBoletoModal;
