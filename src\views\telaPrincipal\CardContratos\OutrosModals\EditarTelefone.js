import { useState, useEffect } from "react";
import Select from "react-select";
import {
  CButton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CFormGroup,
  CForm,
  CRow,
  CInputCheckbox,
  CCol,
  CLabel,
  CInput,
} from "@coreui/react";

const EditarTelefone = ({
  isOpen,
  onClose,
  editData,
  onEdit,
  tipoTelefone,
}) => {
  const [telefone, setTelefone] = useState({
    ddd: "",
    fone: "",
    ramal: "",
    descricao: "",
    contato: "",
    tipoTelefone: 0,
    status: 0,
    isHotNumber: false,
    isWhatsApp: false,
  });

  // const opcoes = JSON.parse(localStorage.getItem("tiposOpcoes"));
  const optionsTelefone = tipoTelefone
    ? [
        ...tipoTelefone.map((item) => {
          return { label: item.descricao, value: item.idTipo };
        }),
      ]
    : [];

  const listaStatus = [
    { value: 0, label: "Inativo" },
    { value: 1, label: "Ativo" },
    { value: 2, label: "Efetivo" },
  ];

  const handleTelefoneChange = (selectedOption) => {
    setTelefone((prevState) => ({
      ...prevState,
      tipoTelefone: selectedOption.value,
    }));
  };

  const handleStatusChange = (selectedOption) => {
    setTelefone((prevState) => ({
      ...prevState,
      status: selectedOption.value,
    }));
  };

  const handleCheckbox = (target) => {
    const { name, checked } = target;
    setTelefone((prevState) => ({
      ...prevState,
      [name]: checked,
    }));
  };

  const handleNumeroChange = (e) => {
    const { name, value } = e.target;
    const pattern = /^[0-9\b]+$/; // Only allow digits (0-9)
    const isValid = pattern.test(value);

    if (isValid || value === "") {
      setTelefone((prevEnd) => ({ ...prevEnd, [name]: value }));
    }
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setTelefone((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  function resetModal() {
    setTelefone({
      ddd: "",
      fone: "",
      ramal: "",
      descricao: "",
      contato: "",
      tipoTelefone: 0,
      status: 0,
      isHotNumber: true,
      isWhatsApp: true,
    });
  }

  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };

  const handleSave = (event) => {
    event.preventDefault();

    const requiredFields = [
      { name: "ddd", displayName: "DDD" },
      { name: "fone", displayName: "Telefone" },
      { name: "tipoTelefone", displayName: "Tipo de Telefone" },
    ];

    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = telefone[field.name];
      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    onEdit(telefone);
    resetModal();
    onClose();
  };

  useEffect(() => {
    if (editData) {
      // setTelefone({ ...editData, tipoTelefone: editData.id_Tipo_Telefone });
      setTelefone({
        ddd: editData.ddd ? editData.ddd.trim() : "",
        fone: editData.fone ? editData.fone.trim() : "",
        ramal: editData.ramal ? editData.ramal : "",
        status: editData.status,
        descricao: editData.descricao ? editData.descricao : "",
        contato: editData.contato ? editData.contato : "",
        tipoTelefone: editData.id_Tipo_Telefone,
        isHotNumber: editData.isHotNumber ? true : false,
        isWhatsApp: editData.isWhatsApp ? true : false,
      });
    }
  }, [editData]);

  return (
    <CModal show={isOpen} size="lg" onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Editar Telefone</CModalHeader>
      <CModalBody>
        <CForm>
          <CFormGroup>
            <CRow>
              <CCol md="2">
                <CLabel>DDD</CLabel>
                <CInput
                  name="ddd"
                  type="text"
                  value={telefone.ddd}
                  onChange={handleNumeroChange}
                  disabled
                />
              </CCol>
              <CCol md="4">
                <CLabel>Telefone</CLabel>
                <CInput
                  type="text"
                  name="fone"
                  value={telefone.fone}
                  onChange={handleInputChange}
                  disabled
                />
              </CCol>
              <CCol md="2">
                <CLabel>Ramal</CLabel>
                <CInput
                  type="text"
                  name="ramal"
                  value={telefone.ramal}
                  onChange={handleNumeroChange}
                  disabled
                />
              </CCol>
              <CCol md="4">
                <CLabel>Contato</CLabel>
                <CInput
                  type="text"
                  name="contato"
                  value={telefone.contato}
                  onChange={handleInputChange}
                  disabled
                />
              </CCol>
            </CRow>
          </CFormGroup>
          <CFormGroup>
            <CRow>
              <CCol md="3">
                <CLabel>Status</CLabel>
                <Select
                  name="status"
                  value={listaStatus.find(
                    (option) => option.value === telefone.status
                  )}
                  onChange={handleStatusChange}
                  options={listaStatus}
                />
              </CCol>
              <CCol md="3">
                <CLabel>Tipo</CLabel>
                <Select
                  name="tipoTelefone"
                  placeholder="Selecione"
                  value={optionsTelefone.find(
                    (option) => option.value === telefone.tipoTelefone
                  )}
                  onChange={handleTelefoneChange}
                  options={optionsTelefone}
                />
              </CCol>
              <CCol md="6">
                <CLabel>Descrição</CLabel>
                <CInput
                  name="descricao"
                  type="text"
                  value={telefone.descricao}
                  onChange={handleInputChange}
                />
              </CCol>
            </CRow>
          </CFormGroup>
          <CFormGroup>
            <CRow>
              <CCol md="3">
                <CLabel>Telefone Hot</CLabel>
                <div style={{ paddingLeft: "40px" }}>
                  <CInputCheckbox
                    className="mx-0"
                    name="isHotNumber"
                    // onChange={(e) => handleCheckbox(e.target)}
                    checked={telefone.isHotNumber}
                    readOnly
                    disabled
                  />
                </div>
              </CCol>
              <CCol md="3">
                <CLabel>Whatsapp</CLabel>
                <div style={{ paddingLeft: "40px" }}>
                  <CInputCheckbox
                    className="mx-0"
                    name="isWhatsApp"
                    onChange={(e) => handleCheckbox(e.target)}
                    checked={telefone.isWhatsApp}
                    // readOnly
                    // disabled
                  />
                </div>
              </CCol>
            </CRow>
          </CFormGroup>
        </CForm>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Cancelar
        </CButton>
        <CButton color="primary" onClick={handleSave}>
          Salvar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default EditarTelefone;
