import CIcon from "@coreui/icons-react";
import {
  CButton,
  CButtonGroup,
  CCard,
  CCardBody,
  CDataTable,
  CRow,
  CTooltip,
} from "@coreui/react";
import { set } from "core-js/core/dict";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { formatCurrency } from "src/reusable/helpers";
import { ApiResponse } from "src/types/common";

import { DatacobSaveNegotiationPending } from "src/types/Negotiation/DatacobSaveNegotiationPending";
import AprovacaoModal from "src/views/aprovacaoAbaixoRegua/partials/aprovacaoModal.tsx";

const ListaPendenciaAprovacao = () => {
  const [data, setData] = useState<Array<DatacobSaveNegotiationPending>>([]);
  const [showAprovacaoModal, setShowAprovacaoModal] = useState(false);
  const [itemSelecionado, setItemSelecionado] =
    useState<DatacobSaveNegotiationPending>(null);
  const [statusAprovacao, setStatusAprovacao] = useState<string>("");

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const getListaPendenciaAprovacao = async () => {
    const payload = {};
    GetData(payload, "getListNegociacaoCalculoLivrePendente")
      .then((x: Array<DatacobSaveNegotiationPending>) => {
        console.log("dados", x);
        if (x) {
          console.log(x);
          let dados = [];
          x.map((item) => {
            // let json = JSON.parse(item.parcelas);
            // item.parcelas = json[0].installment;
            // item.crm = "Rodobens";
            // item.msg = "";
            // item.nrContrato = nrContrato;
            // dados.push(item);
          });

          setData(x);
        } else {
          toast.error("Erro ao listar!");
        }
      })
      .catch((e) => {
        toast.error("Erro ao listar!");
      })
      .finally(() => {});
  };

  const handleClickAprovar = (
    item: DatacobSaveNegotiationPending,
    status: string
  ) => {
    setShowAprovacaoModal(true);
    setItemSelecionado(item);
    setStatusAprovacao(status);
  };

  const handleCloseAprovacaoModal = async () => {
    setShowAprovacaoModal(false);
    setItemSelecionado(null);
    setStatusAprovacao("");
    await getListaPendenciaAprovacao();
  };

  useEffect(() => {
    getListaPendenciaAprovacao();
  }, []);

  const renderActionsButton = (item: DatacobSaveNegotiationPending) => {
    return (
      <td>
        <CButtonGroup>
          <CTooltip content="Aprovar">
            <CButton
              color="success"
              className=""
              onClick={() => handleClickAprovar(item, "Aprovado")}
            >
              <CIcon name="cil-check" size="lg" />
            </CButton>
          </CTooltip>
          <CTooltip content="Rejeitar">
            <CButton
              color="danger"
              className=""
              onClick={() => handleClickAprovar(item, "Rejeitado")}
            >
              <CIcon name="cil-ban" size="lg" />
            </CButton>
          </CTooltip>
        </CButtonGroup>
      </td>
    );
  };

  const columns = [
    {
      key: "idNeg",
      label: "Negociação",
      formatter: (item) => item.idNeg.toString() ?? "---",
    },
    { key: "nrContrato", label: "Contrato" },
    {
      key: "financiado",
      label: "Cliente",
    },
    { key: "parcelas", label: "Parcela" },
    {
      key: "vlNegociacao",
      label: "Valor Negociado",
      formatter: (item) => formatCurrency(item),
    },
    {
      key: "vlMinimoRegua",
      label: "Valor Mínimo Régua",
      formatter: (item) => formatCurrency(item),
    },
    {
      key: "usuarioSolicitacao",
      label: "Solicitante",
    },

    {
      label: "Ações",
      key: "actions",
    },
  ];

  useEffect(() => {
    setData([]);
  }, []);

  return (
    <>
      <CRow>
        <h1>Processsos pendentes de aprovação:</h1>
      </CRow>
      <CRow>
        <CCard>
          <CCardBody>
            <CDataTable
              items={data}
              fields={columns}
              scopedSlots={{
                idNeg: (item) => <td>{item.idNeg ?? "---"}</td>,
                actions: renderActionsButton,
                vlNegociacao: (item) => (
                  <td>{formatCurrency(item.vlNegociacao)}</td>
                ),
                vlMinimoRegua: (item) => (
                  <td>{formatCurrency(item.vlMinimoRegua)}</td>
                ),
              }}
              pagination
              sorter
              columnFilter
              itemsPerPage={10}
            />
          </CCardBody>
        </CCard>
      </CRow>
      <AprovacaoModal
        isOpen={showAprovacaoModal}
        item={itemSelecionado}
        statusAprovacao={statusAprovacao}
        onClose={() => {
          handleCloseAprovacaoModal();
        }}
      />
    </>
  );
};

export default ListaPendenciaAprovacao;
