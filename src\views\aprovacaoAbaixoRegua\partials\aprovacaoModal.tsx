import React, { ReactNode, useState } from "react";
import {
  CButton,
  CCol,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CRow,
  CTextarea,
} from "@coreui/react";
import { DatacobSaveNegotiationPending } from "src/types/Negotiation/DatacobSaveNegotiationPending";
import { getURI } from "src/config/apiConfig";
import { PUT_DATA } from "src/api";

type Props = {
  isOpen: boolean;
  item: DatacobSaveNegotiationPending;
  statusAprovacao: string;
  onClose: () => void;
};

const AprovacaoModal = ({ isOpen, item, statusAprovacao, onClose }: Props) => {
  const [motivo, setMotivo] = useState<string>("");

  const handleClickAprovar = async () => {
    if (
      item?.id === null ||
      item?.id === "" ||
      statusAprovacao === "" ||
      statusAprovacao === null
    ) {
      alert("Não foi possível confirmar a aprovação.");
      return;
    }
    await saveAprovacao();
    onClose();
  };

  const saveAprovacao = async () => {
    const res = await PUT_DATA(
      getURI("updateStatusTuNegociacaoLivre"),
      {
        id: item.id,
        statusAprovacao: statusAprovacao,
        observacao: motivo ?? "",
      },
      true
    );
    if (res?.status === 400) {
      return false;
    }
    if (res?.success === false) {
      return false;
    }
    setMotivo("");
    return true;
  };

  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMotivo(event.target.value);
  };

  return (
    <CModal show={isOpen} onClose={onClose}>
      <CModalHeader>
        {statusAprovacao === "Aprovado" ? (
          <CModalTitle>Aprovar processo pendente</CModalTitle>
        ) : (
          <CModalTitle>Rejeitar processo pendente</CModalTitle>
        )}
      </CModalHeader>
      <CModalBody>
        <CRow>
          <CCol>
            <CLabel>Motivo/Observação</CLabel>
            <CTextarea value={motivo} onChange={handleChange} />
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CButton
          color={statusAprovacao === "Aprovado" ? "success" : "danger"}
          onClick={handleClickAprovar}
        >
          Confirma
        </CButton>{" "}
        <CButton color="secondary" onClick={onClose}>
          Cancelar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default AprovacaoModal;
