import CIcon from "@coreui/icons-react";
import { CTooltip } from "@coreui/react";
import React, { useState, useEffect } from "react";
import { formatCurrency, formatDate } from "src/reusable/helpers";
const customTooltipStyle = {
  "--cui-tooltip-bg": "white",
  "--cui-tooltip-border-color": "#28a745", // Cor de sucesso
  "--cui-tooltip-text-color": "#28a745", // Cor de sucesso
  // Outras variáveis personalizadas aqui, se necessário
};

const TableAcordos = ({ dataTable, contratoIndex, onContratoClick }) => {
  return (
    <div className="table-responsive">
      <table className="table">
        <thead>
          <tr>
            <th>Acordo</th>
            <th>Data Entrada</th>
            <th>Valor da Entrada</th>
            <th>Qtd Parcelas</th>
            <th>Status DataCob</th>
            <th>Boleto DataCob</th>
            <th>Cancelamento</th>
            <th>Status</th>
          </tr>
        </thead>

        <tbody>
          {dataTable.map((item, index) => (
            <tr
              key={index}
              onClick={() => onContratoClick(item)}
              className={
                contratoIndex != null && contratoIndex.id === item.id
                  ? item.status === "Ativo" || item.status === ""
                    ? "bg-success"
                    : item.status === "Pend. Safra"
                    ? "bg-warning"
                    : "bg-danger"
                  : ""
              }
              style={{ cursor: "pointer" }}
            >
              <td>{item.idAcordo}</td>
              <td>{formatDate(item.dtParcelaEntrada)}</td>
              <td>{formatCurrency(item.valorEntrada)}</td>
              <td>{item.parcelas.length - 1}</td>
              <td>{item.statusDataCob}</td>
              <td>{item?.ticket?.nrTicket}</td>
              <td>{item.motivoCancelamento}</td>
              {item.status === "Pend. Safra" ? (
                <td
                  style={{
                    width: "130px",
                    display: "flex",
                    flexDirection: "row",
                  }}
                >
                  <div>{item.status}</div>
                  <div className="ml-2">
                    <CTooltip
                      content="Acordo pendente de aprovação pelo Safra. Aguarde alguns instantes e tente realizar o download do Boleto"
                      placement="right"
                      style={customTooltipStyle}
                    >
                      <CIcon
                        name="cilWarning"
                        className={
                          contratoIndex != null && contratoIndex.id === item.id
                            ? ""
                            : "text-warning"
                        }
                      />
                    </CTooltip>
                  </div>
                </td>
              ) : (
                <td>{item.status}</td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TableAcordos;
