import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CButton,
} from "@coreui/react";

const ConfirmModal = ({ isOpen, onClose, texto }) => {
  const [mensagem, setMensagem] = useState("");

  const handleCancel = () => {
    onClose(false);
  };

  const handleConfirm = () => {
    onClose(true);
  };

  // Remove unwanted characters and replace escape sequences
  const formattedMessage = texto
    .replace(/\r\n/g, "\n") // Replace Windows-style line breaks with newlines
    .replace(/^(?:\[\]|])$/g, "") // Remove leading "[" and trailing "]"
    .replace(/\\n/g, "\n") // Replace escape sequences "\n" with newlines
    .replace(/\\("|')/g, "$1") // Replace escaped double quotes with actual double quotes
    .replace(/\r/g, ""); // Remove carriage return characters

  useEffect(() => {
    setMensagem(formattedMessage);
  }, [isOpen]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} className="custom-modal" >
      <CModalHeader closeButton></CModalHeader>
      <CModalBody>
        <CRow>
          <CCol>
            <div>{mensagem}</div>
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CButton color="danger" onClick={handleCancel}>
          Cancelar
        </CButton>
        <CButton color="info" onClick={handleConfirm}>
          Confirmar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default ConfirmModal;
