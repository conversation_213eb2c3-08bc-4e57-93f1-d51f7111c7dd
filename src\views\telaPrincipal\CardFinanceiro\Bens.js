import React, { useState, useEffect } from "react";
import {
  CDataTable,
  CButton,
  CCardBody,
  CCol,
  CRow,
  CCardFooter,
} from "@coreui/react";
import { formatDate, formatThousands } from "src/reusable/helpers";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import LoadingComponent from "src/reusable/Loading";
import EvolucaoPrecoModal from "./BensModal/EvolucaoPrecoModal";
import DetalhesBensModal from "./BensModal/DetalhesBensModal";
import GarantiaModal from "./BensModal/GarantiaModal";
import TableSelectItens from "src/reusable/TableSelectItens";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import { useAuth } from "src/auth/AuthContext";

import Select from "react-select";

const Bens = ({ selected }) => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissaoTelaPrincipalBensVerEvolucao = {
    modulo: "Tela Principal",
    submodulo: "Bens - Ver Evolução",
  };

  const permissaoTelaPrincipalBensVerDetalhes = {
    modulo: "Tela Principal",
    submodulo: "Bens - Ver Detalhes",
  };

  const permissaoTelaPrincipalBensGarantias = {
    modulo: "Tela Principal",
    submodulo: "Bens - Garantia",
  };

  const financiadoData = JSON.parse(localStorage.getItem("financiadoData"))
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const dadosBens = localStorage.getItem("dadosBens")
    ? JSON.parse(localStorage.getItem("dadosBens"))
    : [];

  const cnscCotas =
    localStorage.getItem("cnscCotas") &&
      localStorage.getItem("cnscCotas") !== "undefined"
      ? JSON.parse(localStorage.getItem("cnscCotas")) ?? []
      : [];

  const [isLoading, setIsLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [filteredData, setFilteredData] = useState("");

  const [selectedMarca, setSelectedMarca] = useState("");
  const [selectedData, setSelectedData] = useState("");
  const [marcaOptions, setMarcaOptions] = useState([]);
  const [dataOptions, setDataOptions] = useState([]);

  const [showEvolucaoPrecoModal, setShowEvolucaoPrecoModal] = useState(false);
  const [dadosEvolucaoPreco, setDadosEvolucaoPreco] = useState(null);
  const [dadosGarantia, setDadosGarantia] = useState(null);
  const [showGarantiaModal, setShowGarantiaModal] = useState(false);

  const handleMarcaChange = (selectedOption) => {
    setSelectedMarca(selectedOption.value);
  };

  const handleDataChange = (selectedOption) => {
    setSelectedData(selectedOption.value);
  };

  const updateView = () => {
    const payload = {
      IdContrato: financiadoData.id_Agrupamento,
    };
    setIsLoading(true);
    getBens(payload, "getbensdatacob")
      .then((data) => {
        if (data) {
          // localStorage.setItem("dadosBens", JSON.stringify(data));
          setTableData(data);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const getBens = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const tableColumns = [
    { key: "tipo", label: "Tipo", formatter: (item) => renderValue(item) },
    {
      key: "numero_Contrato",
      label: "Contrato",
      className: "nowrap-cell",
      formatter: (item) => renderValue(item),
    },
    {
      key: "descricao_Garantia",
      label: "Descrição",
      formatter: (item) => renderValue(item),
    },
    { key: "marca", label: "Marca", formatter: (item) => renderValue(item) },
    { key: "modelo", label: "Modelo", formatter: (item) => renderValue(item) },
    {
      key: "ano_Fabricacao",
      label: "Ano Fab/Mod",
      style: { whiteSpace: "nowrap" },
      formatter: (item) => renderValue(item),
    },
    { key: "cor", label: "Cor", formatter: (item) => renderValue(item) },
    { key: "placa", label: "Placa", formatter: (item) => renderValue(item) },
    {
      key: "renavam",
      label: "Renavam",
      formatter: (item) => renderValue(item),
    },
    {
      key: "dt_Venda",
      label: "Dt. Venda",
      formatter: (item) => (item ? formatDate(item) : "---"),
    },
    {
      key: "vl_Venda",
      label: "Vl. Venda",
      style: { whiteSpace: "nowrap" },
      formatter: (item) => renderValue(item),
    },
    {
      key: "evo_bens",
      label: "Evolução de Bens",
      style: { whiteSpace: "nowrap" },
      formatterByObject: (item) => renderEvolucao(item),
    },
    {
      key: "detalhes",
      label: "Ver Detalhes",
      style: { whiteSpace: "nowrap" },
      formatterByObject: (item) => renderDetalhes(item),
    },
  ];

  function handleButtonEvolucaoBens(item) {
    //Fazer uma busca do idCota pelo contrato do item, cdGrupo é a 1a parte e cdCota a 2a parte
    setDadosEvolucaoPreco(cnscCotas.idCota);
    setShowEvolucaoPrecoModal(true);
  }

  function handleButtonDetalhesBens(item) {
    setDadosGarantia(cnscCotas.idCota);
    setShowGarantiaModal(true);
  }

  useEffect(() => {
    if (dadosBens) {
      const filter = dadosBens.filter((item) => {
        const matchesMarca = !selectedMarca || item.marca === selectedMarca;
        const matchesData =
          !selectedData ||
          item.dt_Venda === selectedData ||
          (selectedData === "All" && item.dt_Venda);

        return matchesMarca && matchesData;
      });
      setFilteredData(filter);

      const uniqueMarcas = [...new Set(dadosBens.map((item) => item.marca))];

      const optionsMarcas = [
        { value: "", label: "Marca" }, // "All" option
        ...uniqueMarcas.map((marca) => ({
          value: marca,
          label: marca,
        })),
      ];
      setMarcaOptions(optionsMarcas);

      if (dadosBens.dt_Venda) {
        const uniqueDatas = [
          ...new Set(dadosBens.map((item) => item.dt_Venda)),
        ];

        // Create number options array
        const optionsData = [
          { value: "", label: "Data" }, // "All" option
          ...uniqueDatas.map((dt_Venda) => ({
            value: dt_Venda,
            label: dt_Venda,
          })),
        ];

        setDataOptions(optionsData);
      }
    }
  }, []);

  const renderValue = (value) => (value ? value : "---");

  const renderDetalhes = (item) => (
    <div>
      <CButton
        color="info"
        onClick={() => handleButtonDetalhesBens(item)}
        style={{ width: "100%" }}
        title={
          cnscCotas.idCota === "0" ||
          cnscCotas.idCota == null ||
          cnscCotas.idCota === undefined
            ? "Não há dados dos detalhes do bem para este financiado."
            : inforPermissions(permissaoTelaPrincipalBensVerDetalhes).view
        }
        disabled={
          cnscCotas.idCota === "0" ||
          cnscCotas.idCota == null ||
          cnscCotas.idCota === undefined ||
          !checkPermission(
            permissaoTelaPrincipalBensVerDetalhes.modulo,
            "View",
            permissaoTelaPrincipalBensVerDetalhes.submodulo
          )
        }
      >
        <i className="cil-magnifying-glass" />
      </CButton>
    </div>
  );

  const renderEvolucao = (item) => (
    <div>
      <CButton
        color="info"
        onClick={() => handleButtonEvolucaoBens(item)}
        style={{ textAlign: "center", whiteSpace: "nowrap" }}
        title={
          cnscCotas.idCota === "0" ||
          cnscCotas.idCota == null ||
          cnscCotas.idCota === undefined
            ? "Não há dados da evolução de bens para este financiado."
            : inforPermissions(permissaoTelaPrincipalBensVerEvolucao).view
        }
        disabled={
          cnscCotas.idCota === "0" ||
          cnscCotas.idCota == null ||
          cnscCotas.idCota === undefined ||
          !checkPermission(
            permissaoTelaPrincipalBensVerEvolucao.modulo,
            "View",
            permissaoTelaPrincipalBensVerEvolucao.submodulo
          )
        }
      >
        Ver evolução <i className="cil-medical-cross" />
      </CButton>
    </div>
  );

  useEffect(() => {
    if (financiadoData) {
      if (selected === true && !localStorage.getItem("dadosBens")) {
        updateView();
      } else {
        if (selected === true && localStorage.getItem("dadosBens")) {
          setTableData(JSON.parse(localStorage.getItem("dadosBens")));
        }
      }
    }
  }, [selected]);

  return (
    <>
      {" "}
      {isLoading ? (
        <div className="mt-2">
          <LoadingComponent />
        </div>
      ) : tableData == null ||
        tableData === undefined ||
        tableData.length === 0 ? (
        <NaoHaDadosTables />
      ) : (
        <div className="my-2">
          <TableSelectItens
            data={tableData}
            columns={tableColumns}
            onSelectionChange={(_) => {}}
            defaultSelectedKeys={[]}
            selectable={false}
            // heightParam="290px"
          />
          <EvolucaoPrecoModal
            isOpen={showEvolucaoPrecoModal}
            onClose={() => setShowEvolucaoPrecoModal(false)}
            idCota={dadosEvolucaoPreco}
          />
          <GarantiaModal
            isOpen={showGarantiaModal}
            onClose={() => setShowGarantiaModal(false)}
            idCota={dadosGarantia}
          />
        </div>
      )}{" "}
    </>
  );
};

export default Bens;
