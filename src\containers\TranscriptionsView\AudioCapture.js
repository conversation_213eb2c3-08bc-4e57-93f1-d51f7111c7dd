import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>r, CRow, CCol, <PERSON>ard, CCardBody, CButton } from "@coreui/react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";

const AudioCapture = ({
  visible,
  onClose,
  requestAudioPermissionsCallback,
  startRecordingCallback,
  stopRecordingCallback,
  connectWebSocketCallback,
  disconnectWebSocketCallback,
}) => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [transcriptions, setTranscriptions] = useState([]);
  const [summary, setSummary] = useState("");
  const websocketRef = useRef(null);
  const [isConnected, setIsConnected] = useState(false);
  const micQueue = useRef([]);
  const speakerQueue = useRef([]);
  const isProcessingQueue = useRef(false);
  const [micStream, setMicStream] = useState(null);
  const [speakerStream, setSpeakerStream] = useState(null);

  const getConfigUrlWebsocket = () => {
    return new Promise(async (resolve, reject) => {
      try {
        await GET_DATA(
          getURI("getConfigByKey"),
          null,
          true,
          true,
          "url_websocket_transcription_call"
        ).then((url) => {
          if (url) {
            resolve(url);
          } else {
            reject("URL do WebSocket não encontrada.");
          }
        });
      } catch (error) {
        console.error("Erro ao buscar URL do WebSocket:", error);
        reject(error);
      }
    });
  };

  const getConfigParamWebsocket = () => {
    return new Promise(async (resolve, reject) => {
      try {
        await GET_DATA(
          getURI("getConfigByKey"),
          null,
          true,
          true,
          "params_websocket_transcription_call"
        ).then((paramResponse) => {
          const param = paramResponse ? JSON.parse(paramResponse) : null;
          if (param) {
            const { selected, types } = param;
            if (selected && types && typeof types === "object" && types[selected]) {
              const apiType = types[selected];
              resolve(apiType);
            } else {
              const error = "Parâmetros inválidos.";
              console.error(error);
              reject(error);
            }
          } else {
            const error = "Resposta ausente ou inválida para os parâmetros.";
            console.error(error);
            reject(error);
          }
        });
      } catch (error) {
        console.error("Erro ao buscar os parâmetros do WebSocket:", error);
        reject(error);
      }
    });
  };

  const processQueue = async (queue, label) => {
    while (queue.current.length > 0 && isProcessingQueue.current) {
      const chunk = queue.current.shift();
      if (
        websocketRef.current &&
        websocketRef.current.readyState === WebSocket.OPEN
      ) {
        const metadata = JSON.stringify({ label });
        websocketRef.current.send(metadata);
        websocketRef.current.send(chunk);
      }
    }
  };

  const connectWebSocket = async () => {
    try {
      await getConfigUrlWebsocket().then(async (urlWebsocket) => {
        await getConfigParamWebsocket().then((paramWebsocket) => {
          const apiType = paramWebsocket;
          const wsURL = urlWebsocket;
          websocketRef.current = new WebSocket(wsURL);
          websocketRef.current.onopen = () => {
            websocketRef.current.send(JSON.stringify({ api_type: apiType }));
            setIsConnected(true);
          };

          websocketRef.current.onmessage = (event) => {
            const data = JSON.parse(event.data);
            if (data.type === "ping") {
              console.info("Heartbeat (ping) received.");
              return;
            }
            const { label, transcription, summary } = data;
            if (summary) {
              setSummary(summary);
            } else {
              setTranscriptions((prev) => [...prev, `${label}: "${transcription}"`]);
            }
          };

          websocketRef.current.onerror = (error) =>
            console.error("WebSocket error:", error);
          websocketRef.current.onclose = (event) => {
            setIsConnected(false);
          };
        });
      });
    } catch (error) {
      console.error("Error connecting to WebSocket:", error);
    }
  };

  const disconnectWebSocketAndStopStreams = () => {
    if (micStream) {
      micStream.getTracks().forEach((track) => track.stop());
      setMicStream(null);
    }
    if (speakerStream) {
      speakerStream.getTracks().forEach((track) => track.stop());
      setSpeakerStream(null);
    }

    if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {
      websocketRef.current.close();
      console.info("WebSocket disconnected.");
    }
    setIsConnected(false);
  };

  const requestAudioPermissions = async () => {
    try {
      const mic = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 48000,
          channelCount: 1,
          latency: 0.01,
          autoGainControl: true,
        },
      });

      const speaker = await navigator.mediaDevices.getDisplayMedia({
        video: { displaySurface: "monitor" },
        audio: {
          echoCancellation: false,
          noiseSuppression: false,
          sampleRate: 48000,
        },
      });
      setMicStream(mic);
      setSpeakerStream(speaker);
    } catch (error) {
      console.error("Erro ao solicitar permissões de áudio:", error);
    }
  };

  const startRecording = async () => {

    if (!isConnected) {
      console.error("WebSocket is not connected.");
      return;
    }

    setTranscriptions([]);
    setSummary("");
    micQueue.current = [];
    speakerQueue.current = [];

    if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {
      websocketRef.current.send(JSON.stringify({ action: "start_new_recording" }));
      console.info("Sent command to start new recording.");
    }

    isProcessingQueue.current = true;
    try {
      const audioContext = new AudioContext();
      const micSource = audioContext.createMediaStreamSource(micStream);
      const speakerSource = audioContext.createMediaStreamSource(speakerStream);

      const micDestination = audioContext.createMediaStreamDestination();
      const speakerDestination = audioContext.createMediaStreamDestination();

      micSource.connect(micDestination);
      speakerSource.connect(speakerDestination);

      const micRecorder = new MediaRecorder(micDestination.stream, {
        mimeType: "audio/webm; codecs=opus",
      });

      const speakerRecorder = new MediaRecorder(speakerDestination.stream, {
        mimeType: "audio/webm; codecs=opus",
      });

      const handleDataAvailable = (queue) => (event) => {
        if (event.data.size > 0) {
          const reader = new FileReader();
          reader.onload = () => queue.current.push(reader.result);
          reader.readAsArrayBuffer(event.data);
        }
      };

      micRecorder.ondataavailable = handleDataAvailable(micQueue);
      speakerRecorder.ondataavailable = handleDataAvailable(speakerQueue);

      const startInterval = (recorder, queue, label) => {
        recorder.start();
        setInterval(() => {
          if (recorder.state === "recording") {
            recorder.stop();
            recorder.start();
            processQueue(queue, label);
          }
        }, 5000);
      };

      startInterval(micRecorder, micQueue, "Operador");
      startInterval(speakerRecorder, speakerQueue, "Cliente");

      setIsRecording(true);
    } catch (error) {
      console.error("Error starting audio recording:", error);
    }
  };

  const stopRecording = () => {
    setIsRecording(false);
    isProcessingQueue.current = false;

    if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {
      websocketRef.current.send(JSON.stringify({ action: "generate_txt" }));
      console.info("Sent command to generate summary. WebSocket remains open.");
    }
  };

  const handleClose = () => {
    if (isRecording) {
      console.info("Parando gravação antes de fechar.");
      stopRecording();
    }
    onClose();
  };

  useEffect(() => {
    return () => {
      if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {
        websocketRef.current.close();
        console.info("WebSocket closed due to component unmount.");
      }
    };
  }, []);

  useEffect(() => {
    if (connectWebSocketCallback) connectWebSocketCallback(connectWebSocket);
    if (disconnectWebSocketCallback) disconnectWebSocketCallback(disconnectWebSocketAndStopStreams);
    if (requestAudioPermissionsCallback) requestAudioPermissionsCallback(requestAudioPermissions);
    if (startRecordingCallback) startRecordingCallback(startRecording);
    if (stopRecordingCallback) stopRecordingCallback(stopRecording);
  }, [connectWebSocketCallback, disconnectWebSocketCallback, requestAudioPermissionsCallback, startRecordingCallback, stopRecordingCallback]);

  return visible ? (
    <CContainer
      fluid
      className="position-fixed d-flex flex-column bg-white shadow rounded"
      style={{
        right: 0,
        bottom: 0,
        top: isMinimized ? "auto" : "50%",
        height: isMinimized ? "50px" : "500px",
        transform: isMinimized ? "none" : "translateY(-50%)",
        maxWidth: "400px",
        zIndex: 1050,
      }}
    >
      <CRow className="align-items-center border-bottom mx-0 py-2">
        <CCol>
          <strong className="text-dark">Transcrição</strong>
        </CCol>
        <CCol xs="auto" className="text-end">
          <CButton size="sm" color="light" onClick={() => setIsMinimized(!isMinimized)}>
            {isMinimized ? "▲" : "▼"}
          </CButton>
          <CButton
            size="sm"
            color="light"
            onClick={handleClose}
            disabled={isRecording}
          >
            ✕
          </CButton>
        </CCol>
      </CRow>
      {!isMinimized && (
        <CRow className="flex-grow-1 overflow-auto">
          <CCol>
            <CCard className="h-100 border-0">
              <CCardBody>
                {transcriptions.length === 0 ? (
                  <p className="text-muted text-center">Aguarde! Iniciando transcrição!</p>
                ) : (
                  <ul>
                    {transcriptions.map((transcription, index) => (
                      <li key={index} className="text-dark">{transcription}</li>
                    ))}
                  </ul>
                )}
                {summary && (
                  <div>
                    <h6 className="fw-bold text-dark mt-4">Resumo:</h6>
                    <p className="text-dark">{summary}</p>
                  </div>
                )}
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
      )}
      {!isMinimized && (
        <>
          <div className="border-top mx-0"></div>
          <CRow className="justify-content-center mx-0 mt-2 mb-2">
            <CCol xs="auto">
              <CButton
                color="primary"
                className="text-white fw-bold px-4 py-2 rounded"
                style={{ backgroundColor: "#4C0BDE" }}
                onClick={stopRecording}
              >
                Resumo
              </CButton>
            </CCol>
          </CRow>
        </>
      )}
    </CContainer>
  ) : null;
};

export default AudioCapture;
