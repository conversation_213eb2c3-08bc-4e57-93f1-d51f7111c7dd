import React from "react";
import { formatCodigoNewcon, formatCurrency, formatDate, formatThousands } from 'src/reusable/helpers';
const tableStyle = {
  maxHeight: "300px", // Defina a altura máxima desejada
  overflowY: "auto",
  width: "100%"  // Adicione uma barra de rolagem vertical quando o conteúdo exceder a altura
};

const tableHeaderStyle = {
  backgroundColor: "#f2f2f2", // Cor de fundo do cabeçalho da tabela
  position: "sticky",
  top: "0", // Mantenha o cabeçalho colado no topo
};

const TableAtrasos = ({ dataTable }) => {

  return (
    <div className="table-responsive-sm" style={tableStyle}>
      <table className='table table-sm'>
        <thead style={tableHeaderStyle}>
          <tr>
            <th>Parcela</th>
            <th>Cd. Movimento</th>
            <th>Histórico</th>
            <th>Vencimento</th>
            <th style={{ textAlign: "left" }}>% Ad<PERSON>ão</th>
            <th style={{ textAlign: "left" }}>% Ideal</th>
            <th style={{ textAlign: "left" }}>Vl. Parcela</th>
            <th style={{ textAlign: "left" }} >Multas</th>
            <th style={{ textAlign: "left" }}>Juros</th>
            <th style={{ textAlign: "left" }}>Total</th>
          </tr>
        </thead>
        <tbody>
          {dataTable.length === 0 ? <tr><td colSpan="10" style={{ textAlign: "center" }}>Nenhum registro encontrado</td></tr> :
            dataTable.map((item, index) => (
              <tr key={index}>
                <td>{item.installmentNumber}</td>
                <td>{item.idCodeFinanMovement ? formatCodigoNewcon(item.idCodeFinanMovement) : "---"}</td>
                <td>{item.nameCodeFinanMovement}</td>
                <td>{item.dueDate ? formatDate(item.dueDate) : '---'}</td>
                <td style={{ textAlign: "left" }}>{item.percIdealAdminstration ? formatThousands(item.percIdealAdminstration) : "0,00"}</td>
                <td style={{ textAlign: "left" }}>{item.percIdealCommonFund ? formatThousands(item.percIdealCommonFund) : "0,00"}</td>
                <td style={{ textAlign: "left" }}>{item.installmentValue ? formatCurrency(item.installmentValue) : "0,00"}</td>
                <td style={{ textAlign: "left" }}>{item.fineValue ? formatCurrency(item.fineValue) : "0,00"}</td>
                <td style={{ textAlign: "left" }}>{item.feesValue ? formatCurrency(item.feesValue) : "0,00"}</td>
                <td style={{ textAlign: "left" }}>{item.delayValue ? formatCurrency(item.delayValue) : "0,00"}</td>
              </tr>
            ))}
        </tbody>
        <tfoot>
          <tr>
            <th colSpan="4" style={{ textAlign: "left" }}>Quantidade de registros: {dataTable.length.toString().padStart(3, "0")}</th>
            <th style={{ textAlign: "left" }}>{formatThousands(dataTable.reduce((acc, cur) => acc + cur.percIdealAdminstration, 0))}</th>
            <th style={{ textAlign: "left" }}>{formatThousands(dataTable.reduce((acc, cur) => acc + cur.percIdealCommonFund, 0))}</th>
            <th style={{ textAlign: "left" }}>{formatCurrency(dataTable.reduce((acc, cur) => acc + cur.installmentValue, 0))}</th>
            <th style={{ textAlign: "left" }}>{formatCurrency(dataTable.reduce((acc, cur) => acc + cur.fineValue, 0))}</th>
            <th style={{ textAlign: "left" }}>{formatCurrency(dataTable.reduce((acc, cur) => acc + cur.feesValue, 0))}</th>
            <th style={{ textAlign: "left" }}>{formatCurrency(dataTable.reduce((acc, cur) => acc + cur.delayValue, 0))}</th>
          </tr>
        </tfoot>
      </table>
    </div>
  );
};

export default TableAtrasos;
