export type DatacobSaveNegotiationPending = {
  id: string | null;
  idArupamento: number;
  parcelas: string[];
  dtNegociacao: string;
  valorPrincipal: number;
  valorCorrecao: number;
  juros: number;
  multa: number;
  comissaoPermanencia: number;
  honorarios: number;
  descontoAutorizado: boolean;
  custas: number;
  notificacao: number;
  valorTarifa: number;
  idNeg: string | null;
  crm: string;
  observacao: string | null;
  statusAprovacao: string;
  idUsuarioAprovacao: string | null;
  nrContrato: string;
  idContrato: string;
  idFinanciado: string;
  vlMinimoRegua: number;
  permiteNegociar: boolean;
  msgRetorno: string | null;
};
