import { CSpinner } from "@coreui/react";

const LoadingComponent = ({ size = "lg", text = null }) => {
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        flexDirection: "column",
      }}
    >
      {size === "lg" ? <CSpinner size="lg" /> : null}
      {size === "sm" ? <CSpinner size="sm" /> : null}
      {text ? <span>{text}</span> : null}
    </div>
  );
};

export default LoadingComponent;
