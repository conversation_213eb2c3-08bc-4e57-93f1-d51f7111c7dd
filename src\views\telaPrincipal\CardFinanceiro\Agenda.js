import React, { useState, useEffect } from "react";
import { CRow, CCol } from "@coreui/react";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import LoadingComponent from "src/reusable/Loading";
import { formatDateTime } from "src/reusable/helpers";
import TableSelectItens from "src/reusable/TableSelectItens";

import AgendaModal from "./AgendaModal/AgendaModal";

const Agenda = ({ selected }) => {
  const [tableData, setTableData] = useState(null);
  const [showAgendaModal, setShowAgendaModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [dadosAgenda, setDadosAgenda] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);
  const [detalhesData, setDetalhesData] = useState(null);
  const [showDetalhesModal, setShowDetalhesModal] = useState(false);
  const [truncatedItems, setTruncatedItems] = useState({});

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const cnscCotas =
    localStorage.getItem("cnscCotas") &&
      localStorage.getItem("cnscCotas") !== "undefined"
      ? JSON.parse(localStorage.getItem("cnscCotas")) ?? []
      : [];

  const agenda = localStorage.getItem("agenda")
    ? JSON.parse(localStorage.getItem("agenda"))
    : [];

  const updateView = () => {
    // const payload = {
    //   idCota: cnscCotas.idCota,
    // };
    setIsLoading(true);
    getAgenda(`/${cnscCotas.idCota}`, "postnewconAgenda")
      // getAgenda(payload, "postnewconAgenda")
      .then((data) => {
        if (data && data.data.length > 0) {
          setTableData(data.data);
          // localStorage.setItem("agenda", JSON.stringify(data.data));
        } else {
          setTableData(null);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const getAgenda = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const fields = [
    {
      key: "idProtocol",
      label: "Protocolo",
      formatterByObject: (item) => renderCell(item, item.idProtocol),
    },
    {
      key: "inclusion",
      label: "Ocorrido em",
      style: { whiteSpace: "nowrap" },
      formatterByObject: (item) => renderTimeDateCell(item, item.inclusion),
    },
    {
      key: "occurrence",
      label: "Descrição",
      formatterByObject: (item) => renderCell(item, item.occurrence),
    },
    {
      key: "obs",
      label: "História",
      formatterByObject: (item) => renderHistoryCell(item, item.obs),
    },
  ];

  const handleClick = async (item) => {
    setSelectedRow(item.protocolNumber);
  };

  const handleRowDoubleClick = (item) => {
    setDetalhesData(item);
    setShowDetalhesModal(true);
  };

  const rowClassName = (item) => {
    return item.protocolNumber === selectedRow ? "pressed" : "";
  };

  const renderCell = (item, itemValue) => {
    return (
      <div onDoubleClick={() => handleRowDoubleClick(item)}>
        {itemValue ?? "---"}
      </div>
    );
  };

  const renderHistoryCell = (item, itemValue) => {
    return (
      <div
        className={
          truncatedItems[item.idProtocol] ? "expand-text" : "truncate-text"
        }
        onClick={() => toggleTextExpansion(item.idProtocol)}
        style={{ width: "600px%" }}
        onDoubleClick={() => handleRowDoubleClick(item)}
      >
        {truncatedItems[item.idProtocol]
          ? itemValue
          : itemValue.length > 200
          ? itemValue.slice(0, 200) + " ..."
          : itemValue}
      </div>
    );
  };

  const renderTimeDateCell = (item, itemValue) => {
    return (
      <tr
        onDoubleClick={() => handleRowDoubleClick(item)}
        style={{ whiteSpace: "nowrap" }}
      >
        {itemValue ? formatDateTime(itemValue) : "---"}
      </tr>
    );
  };

  const toggleTextExpansion = (itemId) => {
    setTruncatedItems((prevState) => ({
      ...prevState,
      [itemId]: !prevState[itemId],
    }));
  };

  useEffect(() => {
    if (selected) {
      updateView();
    }
  }, [selected]);

  return (
    <>
      {" "}
      {isLoading ? (
        <div className="mt-2">
          <LoadingComponent />
        </div>
      ) : (
        <>
          {tableData ? (
            <>
              {" "}
              <CCol className="information-text">
                Dê um duplo clique com o botão esquerdo do mouse para ver os
                detalhes das informações.
              </CCol>
              <TableSelectItens
                data={tableData}
                columns={fields}
                onSelectionChange={(_) => {}}
                defaultSelectedKeys={[]}
                selectable={false}
                rowDataClass={"double-clickable-table"}
                heightParam="290px"
              />
              {showDetalhesModal && (
                <AgendaModal
                  isOpen={showDetalhesModal}
                  onClose={() => setShowDetalhesModal(false)}
                  dados={detalhesData}
                />
              )}
            </>
          ) : (
            <CRow style={{ textAlign: "center", margin: "12px 0" }}>
              <CCol>
                <div>
                  Não foram encontrados dados da agenda para este financiado.
                </div>
              </CCol>
            </CRow>
          )}
        </>
      )}
    </>
  );
};

export default Agenda;
