import React, { useState, useEffect } from "react";
import {
  CCard,
  CCardBody,
  CNav,
  CNavItem,
  CNavLink,
  CTabContent,
  CTabPane,
  CTabs,
  CLabel,
} from "@coreui/react";
import Select from "react-select";
import { Link as Ancora } from "react-scroll";
// import { getURI } from "src/config/apiConfig";
// import { GET_DATA } from "src/api";
// import LoadingComponent from "src/reusable/Loading";

import Processos from "./CardProcessos/Processos";
import Desdobramentos from "./CardProcessos/Desdobramentos";
import Custas from "./CardProcessos/Custas";
import Eventos from "./CardProcessos/Eventos";
import { useMyContext } from "src/reusable/DataContext";
import { getApi } from "src/reusable/functions";

const TabProcessos = ({ onPastaChange, tabSelected = "processos" }) => {
  const { data, updateCustasProjuris, processos, appSettings } = useMyContext();

  // const [processos, setProcessos] = useState(
  //   localStorage.getItem("processos")
  //     ? JSON.parse(localStorage.getItem("processos"))
  //     : null
  // );

  // const processos = localStorage.getItem("processos")
  //   ? JSON.parse(localStorage.getItem("processos"))
  //   : [];

  const [currentTab, setCurrentTab] = useState("Processos");
  const [options, setOptions] = useState([]);
  const [selectedPasta, setSelectedPasta] = useState("");
  const [selectedIdProcesso, setSelectedIdProcesso] = useState(0);

  const [selectedTabIndex, setSelectedTabIndex] = useState("processos");

  const tabs = [
    {
      id: "processos",
      label: "Processos",
      icon: "cil-institution",
      content: (
        <Processos
          pasta={selectedPasta}
          selected={selectedTabIndex === "processos" ? true : false}
          processos={processos}
        />
      ),
    },
    {
      id: "desdobramentos",
      label: "Desdobramentos",
      icon: "cil-fork",
      content: (
        <Desdobramentos
          pasta={selectedPasta}
          idProcesso={selectedIdProcesso}
          selected={selectedTabIndex === "desdobramentos" ? true : false}
        />
      ),
    },
    {
      id: "custas",
      label: "Custas",
      icon: "cil-money",
      content: (
        <Custas
          pasta={selectedIdProcesso.toString()}
          selected={selectedTabIndex === "custas" ? true : false}
        />
      ),
    },
    // {
    //   id: "documentos",
    //   label: "Documentos",
    //   icon: "cil-file",
    //   content: (
    //     <Documentos
    //       pasta={selectedPasta}
    //       selected={selectedTabIndex === "documentos" ? true : false}
    //     />
    //   ),
    // },
    {
      id: "eventos",
      label: "Eventos",
      icon: "cil-calendar",
      content: (
        <Eventos
          pasta={selectedPasta}
          idProcesso={selectedIdProcesso}
          selected={selectedTabIndex === "eventos" ? true : false}
        />
      ),
    },
  ];

  const handlePastaChange = (selectedOption) => {
    setSelectedPasta(selectedOption.value);
    const proc = processos.find((item) => item.pasta === selectedOption.value);
    setSelectedIdProcesso(proc.idProcesso);
    onPastaChange(selectedOption.value);
    handleTabSelect(tabs.find((tab) => tab.id === tabSelected));
  };

  const handleTabSelect = (tab) => {
    setSelectedTabIndex(tab.id);
    setCurrentTab(tab.label);
  };

  const updatePastaOptions = (processos) => {
    if (!processos) return;
    const optionsPastas = processos.map((item) => ({
      value: item.pasta,
      label:
        "Pasta: " + item.pasta + "  |  Processo: " + item.numero_atual_processo,
    }));
    setOptions(optionsPastas);
    // if (
    //   optionsPastas &&
    //   optionsPastas !== null &&
    //   optionsPastas !== undefined &&
    //   optionsPastas.length > 0
    // ) {
    //   handlePastaChange(optionsPastas[0]);
    // } else {
    //   handlePastaChange({ value: "", label: "" });
    // }
  };

  useEffect(() => {
    if (processos && processos.length > 0) {
      updatePastaOptions(processos);
    }
  }, [processos]);

  // useEffect(() => {
  //   const financiadoData = localStorage.getItem("financiadoData")
  //     ? JSON.parse(localStorage.getItem("financiadoData"))
  //     : null;
  //   if (!financiadoData) return;

  //   getCustasProjuris(processos);
  // }, [data]);

  const handlePastaSelect = (value) => {
    setSelectedPasta(value);
    const proc = processos.find((item) => item.pasta === value);
    setSelectedIdProcesso(proc.idProcesso);
    onPastaChange(value);
    handleTabSelect(tabs.find((tab) => tab.id === tabSelected));
  };

  return (
    <CCard id="projuris" style={{ minHeight: "210px", maxHeight: "470px" }}>
      {/* <CCardHeader style={{ display: "flex", alignItems: "center" }}>
        <div style={{ flex: "1" }}>
          <i className="cil-inbox mr-2" />
          Jurídico
        </div>
        <Ancora
          className="btn btn-warning text-white"
          style={{ cursor: "pointer" }}
          to="tela_principal_title"
          smooth={true}
          duration={500}
        >
          <i className="cil-arrow-thick-top mr-2" />
          Votar ao topo da tela
        </Ancora>
      </CCardHeader> */}
      {options && options.length > 0 ? (
        <>
          {selectedPasta ? (
            <>
              <div style={{ padding: "6px 12px" }}>
                <Select
                  options={options}
                  value={options.find(
                    (option) => option.value === selectedPasta
                  )}
                  placeholder="Selecione uma pasta"
                  onChange={handlePastaChange}
                />
              </div>
              {selectedPasta && (
                <CTabs activeTab={currentTab}>
                  <CNav className="custom-nav">
                    {tabs.map((tab) => (
                      <CNavItem
                        key={tab.id}
                        className={
                          currentTab === tab.label ? "" : "nonactive-tab"
                        }
                      >
                        <CNavLink
                          data-tab={tab.label}
                          onClick={() => handleTabSelect(tab)}
                        >
                          <i className={tab.icon} /> {tab.label}
                        </CNavLink>
                      </CNavItem>
                    ))}
                  </CNav>
                  <div style={{ overflowX: "auto" }}>
                    <CTabContent>
                      {tabs.map((tab) => (
                        <CTabPane key={tab.id} data-tab={tab.label}>
                          {tab.content}
                        </CTabPane>
                      ))}
                    </CTabContent>
                  </div>
                </CTabs>
              )}
            </>
          ) : (
            <>
              <div
                className="d-flex"
                style={{
                  minHeight: "150px",
                  flexWrap: "wrap",
                  overflowX: "auto",
                }}
              >
                {options.map((opt, key) => (
                  <CCard
                    className={"bg-dark pointer"}
                    style={{
                      margin: "10px",
                      width: "250px",
                    }}
                    key={key}
                    onClick={() => handlePastaSelect(opt.value)}
                  >
                    <CCardBody>
                      <strong>
                        <CLabel>{opt?.label}</CLabel>
                        <CLabel>
                          Cliente -{" "}
                          {
                            processos.find((p) => p.pasta === opt.value)
                              ?.cliente_Principal
                          }
                        </CLabel>
                      </strong>
                    </CCardBody>
                  </CCard>
                ))}
              </div>
            </>
          )}
        </>
      ) : (
        <CCardBody
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          Não há processos relacionados ao CPF/CNPJ do financiado.
        </CCardBody>
      )}
    </CCard>
  );
};

export default TabProcessos;
