import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CDataTable,
  CModalHeader,
  CModalBody,
  CModalFooter,
} from "@coreui/react";
import {
  formatThousands,
  formatDate,
  formatCodigoNewcon,
} from "src/reusable/helpers";
import LoadingComponent from "src/reusable/Loading";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import TableAtrasos from "./Partial/TableAtrasos";

const AtrasosModal = ({ isOpen, onClose, idCota }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  // const [tableTotal, setTableTotal] = useState([]);

  const fields = [
    { key: "installmentNumber", label: "Parcela" },
    {
      key: "idCodeFinanMovement",
      label: "Cd. Movimento",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "nameCodeFinanMovement", label: "Histórico" },
    { key: "dueDate", label: "Vencimento" },
    {
      key: "percIdealAdminstration",
      label: "% Adesão",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "percIdealCommonFund",
      label: "% Ideal",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "installmentValue",
      label: "Vl. Parcela",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "fineValue", label: "Multas" },
    { key: "feesValue", label: "Juros" },
    { key: "delayValue", label: "Total" },
  ];

  const getAtrasos = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleClose = () => {
    onClose();
  };

  const sumColumn = (data, key) => {
    const total = data.reduce(
      (acc, item) => acc + parseFloat(item[key]) || 0,
      0
    );
    return total.toFixed(4); // Limit to 4 decimal places
  };

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      getAtrasos({ idCota: idCota }, "postnewconatrasos")
        .then((data) => {
          if (data) {
            setTableData(data.data);
            // calcTableTotal(data.data, fields);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="xl"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Atrasos</h5>
      </CModalHeader>
      <CModalBody>
        {" "}
        {isLoading ? (
          <div>
            <LoadingComponent />
          </div>
        ) : (
          <>
            <div>
                <TableAtrasos dataTable={tableData} />
            </div>
          </>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default AtrasosModal;
