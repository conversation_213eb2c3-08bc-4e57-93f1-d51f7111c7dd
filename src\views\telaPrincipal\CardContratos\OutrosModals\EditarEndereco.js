import { useState, useEffect } from "react";
import Select from "react-select";
import {
  CButton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CFormGroup,
  CForm,
  CRow,
  CInputCheckbox,
  CCol,
  CLabel,
  CInput,
  CSwitch,
} from "@coreui/react";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";

const EditarEndereco = ({
  isOpen,
  onClose,
  editEndereco,
  onEditEndereco,
  tipoEndereco,
}) => {
  const [endereco, setEndereco] = useState({
    logradouro: "",
    bairro: "",
    numero: "",
    complemento: "",
    uf: "",
    cidade: "",
    cep: "",
    tipoEndereco: 0,
    carta: false,
    status: 0,
  });
  const [isValid, setIsValid] = useState(true);
  const [, setCEPEncontrado] = useState(false);
  const [isSn, setIsSn] = useState(false);

  const opcoes = JSON.parse(localStorage.getItem("tiposOpcoes"));
  const optionsEndereco = tipoEndereco
    ? [
        ...tipoEndereco.map((item) => {
          return { label: item.descricao, value: item.idTipo };
        }),
      ]
    : [];

  const listaStatus = [
    { value: 0, label: "Inativo" },
    { value: 1, label: "Ativo" },
    { value: 2, label: "Efetivo" },
  ];

  const handleStatusChange = (selectedOption) => {
    setEndereco((prevState) => ({
      ...prevState,
      status: selectedOption.value,
    }));
  };

  const handleSelectChange = (selectedOption) => {
    setEndereco((prevState) => ({
      ...prevState,
      tipoEndereco: selectedOption.value,
    }));
  };

  const handleCheckbox = (isChecked) => {
    setEndereco((prevState) => ({
      ...prevState,
      carta: isChecked,
    }));
  };

  const handleLogradouroChange = (e) => {
    const { value } = e.target;
    const pattern = /^[a-zA-Z0-9\sáàâãéèêíïóôõöúüçñÁÀÂÃÉÈÊÍÏÓÔÕÖÚÜÇÑ.]+$/;
    const isValid = pattern.test(value);

    if (isValid || value === "") {
      setEndereco((prevEnd) => ({ ...prevEnd, logradouro: value }));
    }
  };

  const handleNumeroChange = (e) => {
    const { value } = e.target;
    const pattern = /^[0-9\b]+$/; // Only allow digits (0-9)
    const isValid = pattern.test(value);

    if (isValid || value === "") {
      setEndereco((prevEnd) => ({ ...prevEnd, numero: value }));
    }
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setEndereco((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  function resetModal() {
    setEndereco({
      logradouro: "",
      bairro: "",
      numero: "",
      complemento: "",
      uf: "",
      cidade: "",
      cep: "",
      tipoEndereco: 0,
      carta: true,
      status: 0,
    });
  }

  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };

  const handleEdit = (e) => {
    e.preventDefault();

    const requiredFields = [
      { name: "logradouro", displayName: "Logradouro" },
      { name: "bairro", displayName: "Bairro" },
      { name: "numero", displayName: "Número" },
      { name: "cidade", displayName: "Cidade" },
      { name: "uf", displayName: "UF" },
      { name: "cep", displayName: "CEP" },
    ];

    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = endereco[field.name];
      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    onEditEndereco(endereco);
    resetModal();
    onClose();
  };

  useEffect(() => {
    if (editEndereco) {
      setEndereco({
        logradouro: editEndereco.logradouro,
        bairro: editEndereco.bairro,
        numero: editEndereco.numero,
        complemento: editEndereco.complemento,
        uf: editEndereco.uf,
        cidade: editEndereco.cidade,
        cep: editEndereco.cep,
        tipoEndereco: editEndereco.tipo_Endereco,
        carta: editEndereco.enviarCarta === 1 ? true : false,
        status: editEndereco.tipo_Status,
      });
    }
  }, [editEndereco]);

  const apiGetCEP = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const buscarEndereco = async () => {
    if (endereco?.cep && endereco?.cep.length === 8) {
      try {
        apiGetCEP(endereco?.cep, "getCEPapi")
          .then((data) => {
            if (data) {
              // setCEPEncontrado(true);
              setEndereco((prevState) => ({
                ...prevState,
                uf: data.state ?? "",
                bairro: data.neighborhood ?? "",
                cidade: data.city ?? "",
                logradouro: data.street ?? "",
              }));
              setIsValid(true);
              // if (data.state && data.state !== "") setUfEncontrado(true);
              // else setUfEncontrado(false);
              // if (data.neighborhood && data.neighborhood !== "")
              //   setBairroEncontrado(true);
              // else setBairroEncontrado(false);
              // if (data.city && data.city !== "") setCityEncontrado(true);
              // else setCityEncontrado(false);
              // if (data.street && data.street !== "") setStreetEncontrado(true);
              // else setStreetEncontrado(false);
            } else {
              setIsValid(false);
              setCEPEncontrado(false);
              console.warn("Erro: Falha na busca do CEP");
            }
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {});
      } catch (error) {
        console.error("Erro buscando CEP:", error);
      }
    } else {
      setIsValid(false);
      setCEPEncontrado(false);
    }
  };

  const handleSn = (item) => {
    setIsSn(item.target.checked);
    setEndereco((prevEnd) => ({
      ...prevEnd,
      numero: item.target.checked ? "S/N" : "",
    }));
  };

  return (
    <CModal show={isOpen} size="lg" onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Editar Endereço</CModalHeader>
      <CModalBody>
        <CForm>
          <CFormGroup>
            <CRow>
              <CCol md="3">
                <CLabel>CEP</CLabel>
                <CInput
                  type="text"
                  name="cep"
                  value={endereco.cep}
                  onChange={handleInputChange}
                  onBlur={buscarEndereco}
                />
                {!isValid && <p style={{ color: "red" }}>CEP inválido</p>}
              </CCol>
              <CCol md="6">
                <CLabel>Cidade</CLabel>
                <CInput
                  type="text"
                  name="cidade"
                  value={endereco.cidade}
                  onChange={handleInputChange}
                />
              </CCol>
              <CCol md="3">
                <CLabel>Estado</CLabel>
                <CInput
                  type="text"
                  name="uf"
                  value={endereco.uf}
                  onChange={handleInputChange}
                />
              </CCol>
            </CRow>
          </CFormGroup>
          <CFormGroup>
            <CRow>
              <CCol md="6">
                <CLabel>Logradouro</CLabel>
                <CInput
                  name="logradouro"
                  type="text"
                  value={endereco.logradouro}
                  onChange={(e) => {
                    handleLogradouroChange(e);
                  }}
                />
              </CCol>
              <CCol md="3">
                <CLabel>Bairro</CLabel>
                <CInput
                  type="text"
                  name="bairro"
                  value={endereco.bairro}
                  onChange={handleInputChange}
                />
              </CCol>
              <CCol md="2">
                <CLabel>Número</CLabel>
                <CInput
                  type="text"
                  name="numero"
                  value={endereco.numero}
                  onChange={handleNumeroChange}
                  disabled={isSn}
                />
              </CCol>
              <CCol md="1">
                <CLabel>S/N</CLabel>
                <CSwitch
                  className="mr-1"
                  color="dark"
                  shape="pill"
                  variant="opposite"
                  onChange={handleSn}
                  defaultChecked={false}
                />
              </CCol>
            </CRow>
          </CFormGroup>
          <CFormGroup>
            <CRow>
              <CCol md="6">
                <CLabel>Complemento</CLabel>
                <CInput
                  type="text"
                  name="complemento"
                  value={endereco.complemento}
                  onChange={handleInputChange}
                />
              </CCol>
              <CCol md="4">
                <CLabel>Tipo de Endereço</CLabel>
                <Select
                  value={optionsEndereco.find(
                    (option) => option.value === endereco.tipoEndereco
                  )}
                  placeholder="Selecione"
                  name="tipoEndereco"
                  // value={endereco.tipoEndereco}
                  onChange={handleSelectChange}
                  options={optionsEndereco}
                />
              </CCol>
              <CCol md="2">
                <CLabel>Enviar Carta</CLabel>
                <div>
                  <CInputCheckbox
                    className="mx-0"
                    name="carta"
                    onChange={(e) => handleCheckbox(e.target.checked)}
                    checked={endereco.carta}
                  />
                </div>
              </CCol>
            </CRow>
            <CRow>
              <CCol md="3">
                <CLabel>Status</CLabel>
                <Select
                  name="status"
                  value={listaStatus.find(
                    (option) => option.value === endereco.status
                  )}
                  onChange={handleStatusChange}
                  options={listaStatus}
                />
              </CCol>
            </CRow>
          </CFormGroup>
        </CForm>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Cancelar
        </CButton>
        <CButton color="primary" onClick={handleEdit}>
          Salvar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default EditarEndereco;
