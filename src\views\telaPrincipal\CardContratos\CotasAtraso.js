import React, { useState, useEffect } from "react";
import { CRow, CCardBody } from "@coreui/react";
import { useMyContext } from "src/reusable/DataContext";

const CotasAtraso = () => {
  const { data } = useMyContext();
  const [mensagemAlerta, setMensagemAlerta] = useState([]);

  useEffect(() => {
    const contratosAbertos = localStorage.getItem("contratosAbertos")
      ? JSON.parse(localStorage.getItem("contratosAbertos"))
      : null;
    if (
      contratosAbertos &&
      contratosAbertos !== undefined &&
      contratosAbertos.length > 0
    ) {
      const contratosAberto = contratosAbertos?.map((item) => {
        const mensagem = (
          <div
            key={item.idContrato}
            style={{ color: "red" }}
            className="flat px-0 py-0"
          >
            <PERSON><PERSON> mais <span style={{ fontWeight: "bold" }}>{item.qtd} </span>{" "}
            contratos em aberto no grupo{" "}
            <span style={{ fontWeight: "bold" }}> {item?.grupo}.</span>{" "}
            (Contrato:{" "}
            <span style={{ fontWeight: "bold" }}> {item.nrContrato} </span> )
          </div>
        );
        return mensagem;
      });
      setMensagemAlerta(contratosAberto);
    }
  }, [data]);

  const renderRow = (item, index) => {
    return (
      <CRow key={index} className="mt-2 mx-2">
        <div
          style={{
            border: "red 1px solid",
            padding: "0 1em",
            borderRadius: "5px",
          }}
        >
          {item}
        </div>
      </CRow>
    );
  };

  return (
    <CCardBody>{mensagemAlerta && mensagemAlerta.map(renderRow)}</CCardBody>
  );
};

export default CotasAtraso;
