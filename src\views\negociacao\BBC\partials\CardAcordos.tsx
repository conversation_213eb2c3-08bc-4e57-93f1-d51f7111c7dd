/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect } from "react";
import { <PERSON>utton, CCard, <PERSON>ardBody, CCardHeader } from "@coreui/react";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardAvisos from "src/reusable/CardAvisos";
import CardLoading from "src/reusable/CardLoading";
import TableAcordos from "./TableAcordos.tsx";
import CardParcelasAcordo from "./CardParcelasAcordo.tsx";
// import FormCancelarNegociacaoModal from "./FormCancelarNegociacaoModal.tsx";
// import FormEnviarEmailModal from "./FormEnviarEmailModal.tsx";
import { useBBCContext } from "../pageContext/BBCContext.tsx";
import { ApiResponse } from "src/types/common.ts";

const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(
        getURI(endpoint),
        null,
        true,
        true,
        payload,
        false
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const PostData = async (payload, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI(endpoint),
        payload,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const CardAcordos = () => {
  const [dataRepository, setDataRepository] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");
  const [msgAviso, setMsgAviso] = useState("");
  const [titleAviso, setTitleAviso] = useState("");

  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");

  const [showTableContrato, setShowTableContrato] = useState(false);
  const [contratosSelecionados, setContratosSelecionados] = useState(null);

  const [modalShow, setModalShow] = useState(false);
  // const [modalEnviarEmailShow, setEnviarEmailShow] = useState(false);
  // const [idContratoDataCob, setIdContratoDataCob] = useState(null);
  const BBCContext = useBBCContext();

  const buscarAcordo = async () => {
    setTitleAviso("");
    setMsgAviso("");
    setShowTableContrato(false);
    setContratosSelecionados(null);
    setMsgAvisoLoading(`Buscando Acordos BBC`);
    setTitleAvisoLoading("Consultando Acordos");
    setLoading(true);
    setLoadingAction("VarifyParam");

    await GetData(BBCContext.financiadoData?.id_Agrupamento, "BBCDealList")
      .then((data: ApiResponse<any[]>) => {
        if (data.data !== undefined && data.data !== null && data.data.length > 0) {
          setDataRepository(data.data);
          setShowTableContrato(true);
        } else {
          setTitleAviso("Não há dados");
          setMsgAviso("");
        }
      })
      .catch((err) => {
        setTitleAviso(`Erro na chamada das APIS`);
        setMsgAviso(
          `Erro na chamada API de Neogciação Lista de Acordos, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`
        );
      })
      .finally(() => {
        setLoading(false);
        setLoadingAction("empty");
        setMsgAvisoLoading("");
        setTitleAvisoLoading("");
      });
  };

  useEffect(() => {
    if (BBCContext.comunicacaoState === "OK") {
      buscarAcordo();
    }
  }, [BBCContext.comunicacaoState]);

  useEffect(() => {
    if (!modalShow) buscarAcordo();
  }, [modalShow]);

  const handleContratoSelection = (contrato) => {
    setContratosSelecionados(contrato);
  };
  // const handleClose = () => {
  //   setModalShow(false);
  //   setEnviarEmailShow(false);
  // };

  function base64ToBlob(base64, mime) {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mime });
}

  const DownloadBoletos = async () => {
    if (contratosSelecionados != null) {
      setLoading(true);
      setLoadingAction("VarifyParam");

      await GetData(contratosSelecionados.numeroOperacao, "BBCTicket")
        .then((data: {message: string, success: boolean, data: {arquivo: string, numAcordo: string}}) => {
          if (data.data !== undefined && data.data !== null) {

            const base64String = data.data.arquivo;
            const mimeType = 'application/pdf';
            const blob = base64ToBlob(base64String, mimeType);
            const url = URL.createObjectURL(blob);

            window.open(url, "_blank");
            setTitleAvisoLoading("Dados do Boleto BBC Gerado");
            setMsgAvisoLoading(
              "Caso não abra uma segunda janela com o Boleto, verifique os Bloqueios de Pop-ups do navegador"
            );
          } else {
            setTitleAvisoLoading("Não há boletos em aberto");
            setMsgAvisoLoading(data.message);
          }
        })
        .catch((err) => {
          setTitleAvisoLoading("Não há boletos em aberto");
          setMsgAvisoLoading("");
        })
        .finally(() => {
          limparAvisos();
        });
    }
  };

  const limparAvisos = () => {
    setTimeout(() => {
      setLoading(false);
      setLoadingAction("empty");
      setMsgAvisoLoading("");
      setTitleAvisoLoading("");
    }, 3000);
  };

  // const AtualizarStatusDataCob = async (status) => {
  //   if (contratosSelecionados != null) {
  //     setTitleAvisoLoading("Atualizando Status Acordo");
  //     setMsgAvisoLoading(`Enviando...`);
  //     setLoading(true);
  //     setLoadingAction("VarifyParam");
  //     await PostData(
  //       status,
  //       "negociacaoSafraAtualizarAtualizarStatusDataCob",
  //       contratosSelecionados.id
  //     )
  //       .then((data: ApiResponse<unknown>) => {
  //         if (data.success) {
  //           setTitleAvisoLoading("Status Atualizando do Acordo");
  //           setMsgAvisoLoading("");
  //         } else {
  //           setTitleAvisoLoading("Status Não Atualizando do Acordo");
  //           setMsgAvisoLoading(data.message);
  //         }
  //       })
  //       .catch((err) => {
  //         setTitleAvisoLoading("Erro ao Tentar atualizar Status do Acordo");
  //         setMsgAvisoLoading("");
  //       })
  //       .finally(() => {
  //         limparAvisos();
  //       });
  //   }
  // };

  // const EnviarGvcManager = (id) => {
  //   if (contratosSelecionados != null) {
  //     setLoading(true);
  //     setLoadingAction("VarifyParam");
  //     let action = 0;
  //     if (contratosSelecionados.status === "Cancelado") action = 1;

  //     if (id === null) {
  //       setTitleAvisoLoading("Falha na busca do Id Contrato Tela Única");
  //       setMsgAvisoLoading("");
  //       limparAvisos();
  //       return;
  //     }

  //     setTitleAvisoLoading("Enviando solicitação ao GVC Manager");
  //     setMsgAvisoLoading("Enviando Acordo...");

  //     const dateString = contratosSelecionados.dtParcelaEntrada;
  //     const dateObj = new Date(dateString);

  //     const formattedDate = `${dateObj.getFullYear()}-${String(
  //       dateObj.getMonth() + 1
  //     ).padStart(2, "0")}-${String(dateObj.getDate()).padStart(2, "0")}`;

  //     PostData(
  //       {
  //         type_action: action,
  //         id_agreements: id,
  //         nr_installment: null,
  //         id_agreements_tu: contratosSelecionados.id,
  //         prazoSimulado: contratosSelecionados.qtParcelasAtivas,
  //         idAcordoDataCob: contratosSelecionados.idAcordoDataCob,
  //         valorFinanciadoSimulado:
  //           contratosSelecionados.valorFinanciadoSimulado,
  //         valorHonorario: contratosSelecionados.valorHonorarios,
  //         contratoDataCob: contratosSelecionados.contratoDataCob,
  //         dtParcelaEntrada: formattedDate,
  //         valorEntrada: contratosSelecionados.valorEntrada,
  //         cpfCnpj: BBCContext.financiadoData.cpfCnpj,
  //       },
  //       "gvcmanagerCyberSafraAcoesContrato"
  //     )
  //       .then((data: ApiResponse<unknown>) => {
  //         if (data.success) {
  //           setTitleAvisoLoading("Acordo Enviado ao GVC Manager");
  //           setMsgAvisoLoading("");
  //           AtualizarStatusDataCob(
  //             `Enviado GVC Manager Comando de ${
  //               action === 0 ? "criação" : "cancelamento"
  //             } de Acordo`
  //           );
  //         } else {
  //           AtualizarStatusDataCob("Falha");
  //           limparAvisos();
  //         }
  //       })
  //       .catch((err) => {
  //         setTitleAvisoLoading("Falha ao Enviar Acordo ao GVC Manager");
  //         setMsgAvisoLoading("");
  //         limparAvisos();
  //       });
  //   }
  // };

  // const buscarContratoBBC = () => {
  //   if (contratosSelecionados != null) {
  //     setIdContratoDataCob(null);
  //     setLoading(true);
  //     setLoadingAction("VarifyParam");
  //     GetData(
  //       contratosSelecionados.id,
  //       "BBCDealCheck"
  //     ).then((data: SearchContract | null) => {
  //       if (data !== undefined && data !== null) {
  //         // setIdContratoDataCob(data.id_Contrato);
  //         // EnviarGvcManager(data.id_Contrato);
  //       }
  //     });
  //   }
  // };

  // const HandleModalEmail = () => {
  //   setEnviarEmailShow(true);
  // };

  return (
    <div>
      <CCard>
        <CCardHeader>
          <h5 className="d-flex justify-content-between">
            <span>Acordos BBC</span>
            <div>
              {/* {contratosSelecionados != null ? (
                <CButton
                  className={"mr-2"}
                  color="success"
                  onClick={buscarContratoBBC}
                >
                  Atualizar Contrato
                </CButton>
              ) : (
                ""
              )} */}
              {/* {contratosSelecionados != null &&
              contratosSelecionados.situacaoProposta !== "Cancelado" ? (
                <CButton
                  className={"mr-2"}
                  color="success"
                  onClick={() => setModalShow(true)}
                >
                  Cancelar Acordo
                </CButton>
              ) : (
                ""
              )} */}
              {/* {contratosSelecionados != null &&
              contratosSelecionados.situacaoProposta === "INTEGRADA" ? (
                <CButton
                  className={"mr-2"}
                  color="success"
                  onClick={DownloadBoletos}
                >
                  Download Boleto
                </CButton>
              ) : (
                ""
              )} */}
              {/* {contratosSelecionados != null &&
              contratosSelecionados.situacaoProposta === "INTEGRADA" ? (
                <CButton
                  className={"mr-2"}
                  color="success"
                  onClick={HandleModalEmail}
                >
                  Enviar Email
                </CButton>
              ) : (
                ""
              )} */}
              <CButton
                color="success"
                disabled={
                  BBCContext.financiadoData?.id_Agrupamento == null || loading
                }
                onClick={() => buscarAcordo()}
              >
                <i className="cil-reload" />
              </CButton>
            </div>
          </h5>
        </CCardHeader>
        <CCardBody>
          {titleAviso !== "" ? (
            <CardAvisos Title={titleAviso} Msg={msgAviso} />
          ) : loading && loadingAction === "VarifyParam" ? (
            <CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />
          ) : showTableContrato && dataRepository !== null ? (
            <TableAcordos
              dataTable={dataRepository}
              contratoIndex={contratosSelecionados}
              onContratoClick={handleContratoSelection}
            />
          ) : (
            ""
          )}
        </CCardBody>
      </CCard>
      <div>
        {contratosSelecionados != null ? (
          <CCard>
            <CCardBody>
              <CardParcelasAcordo dataParcelas={contratosSelecionados} />
            </CCardBody>
          </CCard>
        ) : (
          ""
        )}
      </div>

      {/* {modalShow && contratosSelecionados != null && (
        <FormCancelarNegociacaoModal
          idAcordo={contratosSelecionados.idAcordo}
          id={contratosSelecionados.id}
          contratoSelecionado={contratosSelecionados}
          isOpen={modalShow}
          onClose={handleClose}
        />
      )} */}
      {/* {modalEnviarEmailShow && contratosSelecionados != null && (
        <FormEnviarEmailModal
          idAcordo={contratosSelecionados.id}
          isOpen={modalEnviarEmailShow}
          onClose={handleClose}
        />
      )} */}
    </div>
  );
};

export default CardAcordos;
