import React, { useState, useEffect, useRef } from "react";
import { CDropdown, CButton } from "@coreui/react";
import { postAutenticar, deslogarAgente } from "src/config/telephonyFunctions";
import LoadingComponent from "src/reusable/Loading";
import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";
import { MountURI } from "src/api";
import MudarClientePorTelefoneModal from "src/reusable/MudarClientePorTelefoneModal.tsx";
import { handleSoftphoneOlos } from "src/views/telefonia/SoftphoneBroadcastChannel";

const Timer = () => {
  const {
    message
  } = useWebsocketTelefoniaContext()

  const startTime = new Date(message?.beginPause);

  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    let intervalId;
    if (startTime) {
      intervalId = setInterval(() => {
        const currentTime = new Date();
        const timeDifference = Math.floor((currentTime - startTime) / 1000);
        setElapsedTime(timeDifference);
      }, 1000);
    }
    return () => {
      clearInterval(intervalId);
    };
  }, [startTime]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? "0" : ""}${remainingSeconds}`;
  };

  if (elapsedTime > 86400) {
    return "0:00";
  }
  return formatTime(elapsedTime);
};

const TheHeaderDropdownCall = ({
  callButton,
  isDisabled,
  setLoadingAuthCod,
  loadingAuthCod,
  isSoftphoneOlosAllowed,
}) => {
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const {
    tableData,
    tableDataRef,
    status,
    origem,
    emPausa,
    getIsOpen,
    setNewIsOpen,
    message,
    messageRef,
    buscarFinanciadoEConfirmar,
    setDadosLigacao,
    passCodeRef
  } = useWebsocketTelefoniaContext()

  const token = localStorage.getItem("token");

  const [isOpen, setIsOpen] = useState(false);

  const [showModalListaContratosPorTelefone, setShowModalListaContratosPorTelefone] = useState(false);
  const [listContratosPorTelefone, setListContratosPorTelefone] = useState([]);

  function toggleBina() {
    setNewIsOpen(!getIsOpen());
    setIsOpen(!isOpen);
  }

  const selecionarFinanciado = async (tableData) => {
    const callData = tableData
    if (callData.callCrm != null && callData.callIdAgrupamento) {
      if (callData.callId !== null) {
        buscarFinanciadoEConfirmar(callData, "olos");
      }
      if (callData.callIdTactium) {
        buscarFinanciadoEConfirmar(callData, "tactium");
      }
    }
  }
  useEffect(() => {
    const inCall = message
    if (inCall && inCall.status) {
      setIsOpen(true);
    }
  }, []);

  const reconnect = () => {
    if (messageRef.current?.agentId && passCodeRef.current && !isSoftphoneOlosAllowed)
      window.open(`https://rodobens.oloschannel.com.br/softphonewebrtc_unextended/?remote_address=rodobens-ecs01.oloschannel.com.br&passcode=${passCodeRef.current}&agent_id=${messageRef.current?.agentId}`, '_blank');
  }

  function generateNewCod() {
    setLoadingAuthCod(true);
    postAutenticar().finally(() => {
      setLoadingAuthCod(false);
    });
  }

  const BuscarFinanciadoPorTelefone = async () => {
    const datacobs = await getDatacobs();
    const listContratos = [];
    await datacobs.forEach(async (data) => {
      const contratos = await getDadosContratos(data.datacobNumber, tableData.phone);

      if (contratos && !("id_Contrato" in contratos)) {
        listContratos.push(...contratos);
      }
    });
    setListContratosPorTelefone(listContratos);
    setShowModalListaContratosPorTelefone(true);
  }

  const getDatacobs = () => {
    const userDatacobs = user?.datacobs;
    return userDatacobs
  }

  const getDadosContratos = async (data_cob, telefone) => {
    let data = { ActiveConnection: data_cob };

    if (telefone) {
      data = {
        ...data,
        telefone: telefone,
      };
    }

    const url = MountURI("Datacob/BuscaDadosFinanciados", data);

    try {
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.ok) {
        const data = await response.json();
        return data.data;
      } else {
        console.error("Erro:", response?.statusText);
        return []
      }
    } catch (error) {
      console.error("Erro buscando usuários:", error);
      return []
    }
  };

  const broadcastSoftphoneData = (channel, tableData, status, passCodeRef, messageRef) => {
    channel.postMessage({
      type: "update-softphone-data",
      payload: {
        tableData,
        status,
        passCode: passCodeRef.current,
        agentId: messageRef.current?.agentId,
      },
    });
  };

  const hasDeslogadoRef = useRef(false);
  const watchSoftphoneOlos = (messageRef, deslogarAgente, hasDeslogadoRef) => {
    const channel = new BroadcastChannel("softphone_channel");
    const intervalId = setInterval(() => {
      let responded = false;
      channel.postMessage({ type: "check-softphone-open" });

      const timeoutId = setTimeout(() => {
        const softphoneAindaAberto = localStorage.getItem("softphoneOlosActive") === "true";
        if (!responded && !softphoneAindaAberto && !hasDeslogadoRef.current) {
          hasDeslogadoRef.current = true;
          console.info('dropdown deslogue');
          deslogarAgente({ agentId: messageRef.current?.agentId }).finally();
        }
      }, 1000);

      const handleResponse = (event) => {
        if (event.data?.type === "softphone-already-open") {
          console.info('softphone olos ativo');
          responded = true;
          hasDeslogadoRef.current = false;
          clearTimeout(timeoutId);
        }
      };

      channel.addEventListener("message", handleResponse);
      setTimeout(() => channel.removeEventListener("message", handleResponse), 1500);
    }, 1500);

    return () => {
      clearInterval(intervalId);
      channel.close();
    };
  };

  useEffect(() => {
    if (!isSoftphoneOlosAllowed) return;
    const channel = new BroadcastChannel("softphone_channel");
    broadcastSoftphoneData(channel, tableData, status, passCodeRef, messageRef);

    let checkSoftphone = () => { };
    if (status !== "Desconectado" && messageRef.current?.agentId) {
      checkSoftphone = watchSoftphoneOlos(messageRef, deslogarAgente, hasDeslogadoRef);
    }

    return () => {
      checkSoftphone();
      channel.close();
    };
  }, [status, tableData, passCodeRef]);

  return (
    <>
      <MudarClientePorTelefoneModal
        isOpen={showModalListaContratosPorTelefone}
        onClose={() => setShowModalListaContratosPorTelefone(false)}
        data_filter={listContratosPorTelefone}
      />
      <CButton className="c-header-nav-link subtle mr-3" onClick={toggleBina}>
        <i
          className={
            isOpen
              ? "cil-chevron-top header-icon-lg"
              : "cil-chevron-bottom header-icon-lg"
          }
        />
      </CButton>
      <CDropdown inNav className="c-header-nav-item">
        {tableData && isOpen && (
          <div className={`info-panel border rounded`}>
            <table className="table info-table">
              <tbody>
                <tr>
                  <td style={{ textAlign: "end", width: "30%" }}>Nome</td>
                  <td>
                    {">> "}
                    {tableData.name !== ""
                      ? tableData.name
                      : "Não identificado"}
                  </td>
                </tr>
                <tr>
                  <td style={{ textAlign: "end", width: "30%" }}>Telefone</td>
                  <td className="">
                    {">> "}
                    {tableData.phone ? tableData.phone : ""}
                    {tableData.phone && (<button onClick={BuscarFinanciadoPorTelefone} className="btn btn-primary btn-sm ml-2"><i className="cil-search" /></button>)}
                  </td>
                </tr>
                <tr>
                  <td style={{ textAlign: "end", width: "30%" }}>Carteira</td>
                  <td>
                    {">> "}
                    {tableData.wallet ? tableData.wallet : "-"}
                  </td>
                </tr>
                <tr>
                  <td style={{ textAlign: "end", width: "30%" }}>Status</td>
                  <td>
                    {">> "}
                    {status ? status : "Desconhecido"}
                  </td>
                </tr>
                {origem !== "" ? (
                  <>
                    <tr>
                      <td style={{ textAlign: "end", width: "30%" }}>Origem</td>
                      <td>
                        {">> "}
                        {origem}
                      </td>
                    </tr>
                  </>
                ) : null}
                {emPausa && (
                  <>
                    <tr>
                      <td style={{ textAlign: "end", width: "30%" }}>
                        Descrição
                      </td>
                      <td>
                        {">> "}
                        {localStorage.getItem("motivoPausa")
                          ? localStorage.getItem("motivoPausa")
                          : ""}
                      </td>
                    </tr>
                    <tr>
                      <td style={{ textAlign: "end", width: "30%" }}>Tempo</td>
                      <td>
                        {">> "}
                        <Timer />
                      </td>
                    </tr>
                  </>
                )}
                <tr>
                  <td colSpan={2}>
                    <button
                      className="btn btn-primary"
                      style={{
                        width: "100%",
                        margin: "10px 0",
                      }}
                      onClick={() => generateNewCod()}
                    >
                      {!loadingAuthCod ? "Novo Cod OLOS" : <LoadingComponent />}
                    </button>
                  </td>
                </tr>
                {(
                  tableData?.callIdAgrupamento !== null && typeof tableData?.callIdAgrupamento !== 'undefined'
                ) && (
                    <tr>
                      <td colSpan={2}>
                        <button
                          className="btn btn-danger"
                          style={{
                            width: "100%",
                            marginBottom: "10px",
                          }}
                          onClick={() => selecionarFinanciado(tableData)}
                        >
                          Carregar Cliente em Tela
                        </button>
                      </td>
                    </tr>
                  )
                }
                {(!isSoftphoneOlosAllowed) && (
                  <tr>
                    <td colSpan={2}>
                      <button
                        className=" btn btn-info"
                        style={{
                          width: "100%",
                          marginBottom: "10px",
                        }}
                        onClick={() => reconnect()}
                      >
                        Reconectar OLOS
                      </button>
                    </td>
                  </tr>
                )}
                {(isSoftphoneOlosAllowed) && (
                  <tr>
                    <td colSpan={2}>
                      <button
                        className="btn btn-secondary"
                        style={{
                          width: "100%",
                          marginBottom: "10px",
                        }}
                        onClick={() => handleSoftphoneOlos()}
                      >
                        Softphone Olos
                      </button>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </CDropdown>
    </>
  );
};

export default TheHeaderDropdownCall;
