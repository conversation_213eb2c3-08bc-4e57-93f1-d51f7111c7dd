import React, { useEffect, useState } from "react";
import {
  CModalBody,
  CButton,
  CCard,
  CCardBody,
  CCol,
  CInput,
  CLabel,
  CRow,
  CModal,
  CModalHeader,
  CModalFooter,
  CDataTable,
} from "@coreui/react";
import { formatDateTime } from "src/reusable/helpers";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { getApi } from "src/reusable/functions";
import Select from "react-select";
import { useAuth } from "src/auth/AuthContext";
import LoadingComponent from "src/reusable/Loading";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";
import { postManager } from "src/reusable/helpers.js";

const LogIntegracoes = () => {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const [current, setUiPage] = useState(1);
  const [total, setTotalPages] = useState(1);
  const [apiPageSize, setApiPageSize] = useState(1000);

  const [frontendCurrentPage, setFrontendCurrentPage] = useState(1);
  const [itemsPerPage] = useState(15);
  const [displayedData, setDisplayedData] = useState([]);

  const [usersCrm, setUsersCrm] = useState([]);
  const token = localStorage.getItem("token");
  const [loadingUsersCrm, setLoadingUsersCrm] = useState(false);
  const [selectedUserCrm, setSelectedUserCrm] = useState(null);

  const [showViewModal, setShowViewModal] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);

  const history = useHistory();
  const { checkPermission, user } = useAuth();
  const permissao = {
    modulo: "Configurações",
    submodulo: "Logs Integrações",
  };

  const [filters, setFilters] = useState({
    userId: "",
    url: "",
    data_inicial: "",
    data_final: "",
  });

  const urlOptions = [
    { label: "Todos ...", value: "" },
    { label: "Datacob Rodobens", value: "cobranca.rodobens.com.br" },
    { label: "Datacob GVC", value: "cobranca.gvcsolucoes.com.br" },
    { label: "Olos", value: "10.178.26.4" },
    { label: "Web Socket", value: "/Hub/Telephony" },
  ];

  const calculateDisplayedData = (allData, currentPage, itemsPerPage) => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return allData.slice(startIndex, endIndex);
  };

  const calculateTotalFrontendPages = (totalItems, itemsPerPage) => {
    return Math.ceil(totalItems / itemsPerPage);
  };

  useEffect(() => {
    const newDisplayedData = calculateDisplayedData(data, frontendCurrentPage, itemsPerPage);
    setDisplayedData(newDisplayedData);
  }, [data, frontendCurrentPage, itemsPerPage]);

  const getUsersTu = async () => {
    setLoadingUsersCrm(true);
    try {
      let res = await getApi(null, "getAllUsersSimplified");
      if (res?.length > 0) {
        res = await Promise.all(res.map(async (user) => {
          user.name = await postManager(token, user.name, 2);
          return user;
        }));
      }
      setUsersCrm(res ?? []);
      setSelectedUserCrm(null);
    } catch (e) {
      console.error(e);
      setUsersCrm([]);
      setSelectedUserCrm(null);
    }
    setLoadingUsersCrm(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value,
    });
  };

  const handleUrlChange = (selectedOption) => {
    setFilters({
      ...filters,
      url: selectedOption ? selectedOption.value : "",
    });
  };

  const handleSearch = async () => {
    setUiPage(1);
    setFrontendCurrentPage(1);

    if (filters.data_inicial && filters.data_final) {
      await fetchDataFromApi(1);
    }
  };

  const myGetApi = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true, false, "", false);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const fetchDataFromApi = async (page) => {
    setIsLoading(true);
    try {
      const params = {
        userId: selectedUserCrm?.id || '',
        url: filters.url || '',
        init: filters.data_inicial,
        end: filters.data_final,
        page: page,
        pageSize: apiPageSize,
      };

      const result = await myGetApi(params, "getLogIntegracoes");
      const [mCurrent, mTotal] = result['message'].split('/').map(Number);
      setUiPage(mCurrent);
      setTotalPages(mTotal);

      let resultData = Array.isArray(result) ? result : (result?.["data"] ?? []);

      setData(resultData);
    } catch (e) {
      console.error("Error fetching data:", e);
      if (page === 1) {
        setData([]);
        setTotalPages(1);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handlePageChange = async (newPage) => {
    if (newPage >= 1 && newPage <= total) {
      await fetchDataFromApi(newPage);
    }
  };

  const handlePreviousPage = async () => {
    if (current > 1) {
      await handlePageChange(current - 1);
    }
  };

  const handleNextPage = async () => {
    if (current < total) {
      await handlePageChange(current + 1);
    }
  };

  const handleFrontendPreviousPage = () => {
    if (frontendCurrentPage > 1) {
      setFrontendCurrentPage(frontendCurrentPage - 1);
    }
  };

  const handleFrontendNextPage = () => {
    const totalFrontendPages = calculateTotalFrontendPages(data.length, itemsPerPage);
    if (frontendCurrentPage < totalFrontendPages) {
      setFrontendCurrentPage(frontendCurrentPage + 1);
    }
  };

  useEffect(() => {
    if (!checkPermission(permissao.modulo, permissao.submodulo)) {
      history.push("/telaprincipal");
    }
  
    if (!user) {
      history.push("/login");
    }
  
    getUsersTu();
  }, [history]);

  const IconButton = ({ icon, color, titulo, onClick }) => {
    return (
      <CButton
        title={titulo}
        className="mr-1"
        style={{ border: "solid 1px", borderColor: color, color: color, padding: "2px 4px" }}
        onClick={onClick}
      >
        <i className={icon} />
      </CButton>
    );
  };

  const renderAction = (item) => {
    return (
      <div className="d-flex justify-content-center align-content-center">
        <IconButton
          icon={"cil-search"}
          color="info"
          titulo={"Visualizar Log"}
          onClick={() => handleViewMsg(item)}
        />
      </div>
    );
  };

  const handleViewMsg = (item) => {
    setSelectedRow(item);
    setShowViewModal(true);
  }

  const columns = [
    { key: "user", label: "Usuário" },
    { key: "url", label: "URL" },
    { key: "status", label: "Status" },
    { key: "method", label: "Método" },
    {
      key: "date",
      label: "Data",
      formatter: (item) => formatDateTime(item),
    },
    {
      key: "action",
      label: "Visualizar",
      formatterByObject: (item) => renderAction(item),
      filter: false,
    },
  ];

  const totalFrontendPages = calculateTotalFrontendPages(data.length, itemsPerPage);

  return (
    <div style={{ paddingBottom: "60px" }}>
      <h3>Log de Integrações:</h3>
      <CCard>
        <CCardBody>
          <CRow>
            <CCol md={2}>
              <CLabel>Hora Inicial</CLabel>
              <CInput
                type="time"
                name="data_inicial"
                value={filters.data_inicial}
                onChange={handleInputChange}
              />
            </CCol>
            <CCol md={2}>
              <CLabel>Hora Final</CLabel>
              <CInput
                type="time"
                name="data_final"
                value={filters.data_final}
                onChange={handleInputChange}
              />
            </CCol>
            <CCol md={3}>
              <CLabel>Usuário</CLabel> <br />
              <Select
                options={usersCrm}
                value={selectedUserCrm}
                getOptionLabel={(opt) => opt.name}
                getOptionValue={(opt) => opt.id}
                onChange={setSelectedUserCrm}
                placeholder="Selecione"
                isLoading={loadingUsersCrm}
              />
            </CCol>
            <CCol md={3}>
              <CLabel>URL</CLabel> <br />
              <Select
                options={urlOptions}
                value={urlOptions.find(opt => opt.value === filters.url) || null}
                onChange={handleUrlChange}
                placeholder="Selecione"
              />
            </CCol>
            <CCol md={2}>
              <CButton color="primary" onClick={handleSearch} style={{ marginTop: "30px" }}>
                Buscar
              </CButton>
            </CCol>
          </CRow>
        </CCardBody>
      </CCard>

      <CCard>
        <CCardBody>
          {isLoading ? (
            <CModalBody>
              <div className="d-flex justify-content-center">
                <LoadingComponent />
              </div>
            </CModalBody>
          ) : (
            <>
              <div style={{ overflowX: "auto" }}>
                <CDataTable 
                  items={displayedData} 
                  fields={columns} 
                  columnFilter={["user", "url", "status", "method", "date"]}
                  pagination={false}
                  tableFilter={false}
                  itemsPerPageSelect={false}
                  responsive
                  scopedSlots={{
                    user: (item) => (
                      <td style={{ whiteSpace: "nowrap" }}>{item.user ? item.user : "---"}</td>
                    ),
                    url: (item) => (
                      <td style={{ 
                        maxWidth: "300px", 
                        wordBreak: "break-all",
                        fontSize: "0.85rem"
                      }}>
                        {item.url ? item.url : "---"}
                      </td>
                    ),
                    status: (item) => {
                      const status = item.status ? item.status.toString() : "";
                      const isSuccess = status === "200" || status === "201" || status === "204";
                      const isClientError = status.startsWith("4");
                      const isServerError = status.startsWith("5");
                      
                      let bgColor = "#e9ecef"; // cinza padrão
                      let textColor = "#495057"; // texto padrão
                      
                      if (isSuccess) {
                        bgColor = "#d4edda"; // verde
                        textColor = "#155724";
                      } else if (isClientError) {
                        bgColor = "#fff3cd"; // amarelo
                        textColor = "#856404";
                      } else if (isServerError) {
                        bgColor = "#f8d7da"; // vermelho
                        textColor = "#721c24";
                      }
                      
                      return (
                        <td style={{ whiteSpace: "nowrap", textAlign: "center" }}>
                          <span style={{
                            padding: "2px 8px",
                            borderRadius: "4px",
                            fontSize: "0.8rem",
                            backgroundColor: bgColor,
                            color: textColor,
                            fontWeight: "500"
                          }}>
                            {status || "---"}
                          </span>
                        </td>
                      );
                    },
                    method: (item) => (
                      <td style={{ whiteSpace: "nowrap", textAlign: "center" }}>
                        <span style={{
                          padding: "2px 6px",
                          borderRadius: "3px",
                          fontSize: "0.75rem",
                          fontWeight: "bold",
                          backgroundColor: item.method === "GET" ? "#cce5ff" : "#ffe6cc",
                          color: item.method === "GET" ? "#004085" : "#856404"
                        }}>
                          {item.method ? item.method : "---"}
                        </span>
                      </td>
                    ),
                    date: (item) => (
                      <td style={{ whiteSpace: "nowrap", fontSize: "0.85rem" }}>
                        {item.date ? formatDateTime(item.date) : "---"}
                      </td>
                    ),
                    action: (item) => (
                      <td style={{ textAlign: "center" }}>
                        {renderAction(item)}
                      </td>
                    ),
                  }}
                />
              </div>
              {data.length > 0 && (
                <div className="d-flex flex-wrap justify-content-between align-items-center mt-3 p-3" 
                     style={{ backgroundColor: "#f8f9fa", borderRadius: "6px" }}>
                  <div className="d-flex align-items-center mb-2 mb-md-0">
                    <CButton 
                      color="secondary" 
                      size="sm"
                      onClick={handleFrontendPreviousPage}
                      disabled={frontendCurrentPage <= 1}
                      className="mr-2"
                    >
                      <i className="cil-chevron-left"></i> Anterior
                    </CButton>
                    
                    <span className="mx-3 text-nowrap">
                      Página {frontendCurrentPage} de {totalFrontendPages}
                    </span>
                    
                    <CButton 
                      color="secondary" 
                      size="sm"
                      onClick={handleFrontendNextPage}
                      disabled={frontendCurrentPage >= totalFrontendPages}
                      className="ml-2"
                    >
                      Próxima <i className="cil-chevron-right"></i>
                    </CButton>
                  </div>
                  
                  <div className="text-muted small">
                    Mostrando {((frontendCurrentPage - 1) * itemsPerPage) + 1} - {Math.min(frontendCurrentPage * itemsPerPage, data.length)} de {data.length} registros
                  </div>
                </div>
              )}
              {total > 1 && (
                <div className="d-flex justify-content-center align-items-center mt-3 pt-3" 
                     style={{ borderTop: "1px solid #dee2e6" }}>
                  <div className="d-flex align-items-center">
                    <span className="mr-3 text-muted small">Páginas da API:</span>
                    <CButton 
                      color="primary" 
                      size="sm"
                      onClick={handlePreviousPage}
                      disabled={current <= 1 || isLoading}
                      className="mr-2"
                    >
                      <i className="cil-chevron-left"></i> Anterior
                    </CButton>
                    
                    <span className="mx-3 small">
                      Página {current} de {total}
                    </span>
                    
                    <CButton 
                      color="primary" 
                      size="sm"
                      onClick={handleNextPage}
                      disabled={current >= total || isLoading}
                      className="ml-2"
                    >
                      Próxima <i className="cil-chevron-right"></i>
                    </CButton>
                  </div>
                </div>
              )}
            </>
          )}
        </CCardBody>
      </CCard>

      <CModal show={showViewModal} onClose={() => setShowViewModal(false)} closeOnBackdrop={true}>
        <CModalHeader closeButton>Visualizar Logs</CModalHeader>
        <CModalBody>
          {selectedRow ? (
            <>
              <p><strong>Payload:</strong></p>
              <pre>
                {(() => {
                  try {
                    const formattedPayload = JSON.stringify(JSON.parse(selectedRow.payload), null, 2);
                    return formattedPayload;
                  } catch (e) {
                    return selectedRow.payload;
                  }
                })()}
              </pre>
              <hr />
              <p><strong>Response:</strong></p>
              <pre>
                {(() => {
                  try {
                    const formattedResponse = JSON.stringify(JSON.parse(selectedRow.response), null, 2);
                    return formattedResponse;
                  } catch (e) {
                    return selectedRow.response;
                  }
                })()}
              </pre>
            </>
          ) : (
            <p>Nenhum conteúdo disponível.</p>
          )}
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setShowViewModal(false)}>
            Fechar
          </CButton>
        </CModalFooter>
      </CModal>
    </div>
  );
};

export default LogIntegracoes;