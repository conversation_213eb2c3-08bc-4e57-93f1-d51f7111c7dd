import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CLabel,
  CFormGroup,
  CInput,
  CCol,
  CButton,
  CInputGroup,
  CInputGroupAppend,
  CInputGroupText,
} from "@coreui/react";
import { postCredentials } from "src/config/telephonyFunctions";

const OlosFirstLoginModal = ({ isOpen, onClose, onSubmit }) => {
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const [userOlos, setUserOlos] = useState("");
  const [telephonyPass, setTelephonyPass] = useState("");
  const [showPasswordTel, setShowPasswordTel] = useState(false);

  const handleUsernameChange = (event) => {
    setUserOlos(event.target.value);
  };
  const handlePasswordChange = (event) => {
    setTelephonyPass(event.target.value);
  };

  const handleCancel = () => {
    onClose();
  };

  function validate(user, pass) {
    if (user.trim() === "") {
      alert("Por favor preencha o usuário.");
      return false;
    }
    if (pass.trim() === "") {
      alert("Por favor digite uma senha.");
      return false;
    }

    return true;
  }

  // function validate(field) {
  //   const trimmedField = field.trim();
  //   if (trimmedField === "") {
  //     alert("Por favor, cadastre o usuário e senha da OLOS.");
  //     return false;
  //   }
  //   return true;
  // }

  const handleConfirm = async () => {
    const validation = validate(userOlos, telephonyPass)
    if (validation) {
      const payloadTelCredentials = {
        userId: user.id,
        username: userOlos,
        password: telephonyPass,
        telephonyId: user.telephonyId,
      };
      const response = await postCredentials(payloadTelCredentials);
      if(response.success) {
        setUserOlos("")
        setTelephonyPass("")
        onSubmit()
      }
      onClose();
    }
  };

  const toggleShowPasswordTel = () => {
    setShowPasswordTel((prevShowPasswordTel) => !prevShowPasswordTel);
  };

  return (
    <CModal
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
      className="custom-modal"
    >
      {/* <CModalHeader closeButton></CModalHeader> */}
      <CModalBody>
        <CRow>
          <CCol style={{textAlign:"center"}}>
            <CLabel>Identificado primeiro acesso.</CLabel>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CFormGroup>
              <CCol>
                <CLabel htmlFor="firstLoginOlos">
                  Digite seu login da OLOS
                </CLabel>
                <CInput
                  id="firstLoginOlos"
                  name="firstLoginOlos"
                  type="text"
                  placeholder="Usuário"
                  value={userOlos}
                  onChange={handleUsernameChange}
                />
              </CCol>
              <CCol>
                <CLabel htmlFor="firstPassOlos">Senha da Olos</CLabel>
                <CInputGroup>
                  <CInput
                    type={showPasswordTel ? "text" : "password"}
                    id="firstPassOlos"
                    name="firstPassOlos"
                    value={telephonyPass}
                    placeholder="Senha"
                    onChange={handlePasswordChange}
                    autoComplete="new-password"
                  />
                  <CInputGroupAppend>
                    <CInputGroupText onClick={toggleShowPasswordTel}>
                      {showPasswordTel ? (
                        <i className="cil-low-vision" />
                      ) : (
                        <i className="cil-scrubber" />
                      )}
                    </CInputGroupText>
                  </CInputGroupAppend>
                </CInputGroup>
              </CCol>
            </CFormGroup>
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CButton color="info" onClick={handleConfirm}>
          Confirmar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default OlosFirstLoginModal;
