const ErrorScren = () => {
  return (
    <div
      style={{
        display: "flex",
        paddingLeft: "1rem",
        paddingRight: "1rem",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        height: "100vh",
        backgroundColor: "#F3F4F6",
      }}
    >
      <div
        style={{ marginTop: "1rem", maxWidth: "28rem", textAlign: "center" }}
      >
        <div
          style={{
            display: "inline-flex",
            justifyContent: "center",
            alignItems: "center",
            borderRadius: "9999px",
            width: "4rem",
            height: "4rem",
            color: "#ffffff",
            backgroundColor: "rgb(239 146 68)",
          }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            style={{ width: "2rem", height: "2rem" }}
          >
            <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path>
            <path d="M12 9v4"></path>
            <path d="M12 17h.01"></path>
          </svg>
        </div>
        <h1
          style={{
            fontSize: "1.875rem",
            lineHeight: "2.25rem",
            fontWeight: 700,
            letterSpacing: "-0.025em",
            "@media (min-width: 768px)": {
              fontSize: "2.25rem",
              lineHeight: "2.5rem",
            },
          }}
        >
          Atenção
        </h1>
        <p style={{ color: "#6B7280" }}>
          Identificamos um fluxo sistêmico diferente do normal. Por gentiliza,
          clique no botão abaixo para atualizar ou pressione enter.
        </p>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "0.5rem",
            "@media (min-width: 640px)": { flexDirection: "row" },
          }}
        >
          <input
            style={{
              width: "0px",
              height: "0px",
              border: "none",
              cursor: "pointer",
            }}
            className="remove-focus-color"
            autoFocus={true}
            onKeyDown={(e) =>
              e.code === "Enter" ? window.location.reload() : null
            }
            onBlur={(e) => {
              if (e.relatedTarget === null) {
                e.target.focus();
              }
            }}
          />
          <button
            onClick={() => window.location.reload()}
            className="btn-error-udpate"
          >
            Atualizar
          </button>
        </div>
      </div>
    </div>
  );
};

export default ErrorScren;
