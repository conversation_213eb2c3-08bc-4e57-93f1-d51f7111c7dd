import React, { useEffect, useState } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CButton,
  CLabel,
  CInput,
} from "@coreui/react";
import { floatToPerc, formatCurrency, formatDate } from "src/reusable/helpers";
import type {
  Installments,
  InstallmentsApi,
} from "src/types/Negotiation/Calculation";
import BoletoModal from "../BoletoModal";
import { useMyContext } from "src/reusable/DataContext";
import {
  calculaValoresDetelhes,
  calculaValoresParcelas,
} from "../utils/CalculosNegociacao";
import { getApi } from "src/reusable/functions";

type InputValueObject = {
  mainValue: number;
  mainValuePerc: number;
  correctedValue: number;
  fees: number;
  feesPerc: number;
  feesApplied: number;
  fine: number;
  finePerc: number;
  fineApplied: number;
  commission: number;
  commissionPerc: number;
  commissionApplied: number;
  discount: number;
  honor: number;
  honorPerc: number;
  costs: number;
  notification: number;
  fareValue: number;
};

type ReturnValues = {
  mainValue: number;
  correctedValue: number;
  fees: number;
  fine: number;
  commission: number;
  discount: number;
  honor: number;
  costs: number;
  notification: number;
  fareValue: number;
  negotiatedValue: number;
  negotiatedDate: string;
  installmentJson: string;
};

type TicketValues = {
  dtNegociacao: Date;
  formaDesconto: number;
  idAgrupamento: number;
  parcelas: number[];
  vlNegociado: number;
};

export default function CalculoLivreModal({
  isOpen,
  onClose,
  valorNegociado,
  parcelasSelecionadas,
  handleFreeCalculation,
  openBoletoModal,
  setFreeCalcValue,
  selectedDate,
}: {
  isOpen: boolean;
  onClose: () => void;
  valorNegociado: number;
  parcelasSelecionadas: InstallmentsApi[];
  handleFreeCalculation: (value: number) => void;
  openBoletoModal: () => void;
  setFreeCalcValue: (item: ReturnValues) => void;
  selectedDate: Date;
}) {
  const handleClose = () => {
    onClose();
  };

  const context = useMyContext();

  const [negotiatedValue, setNegotiatedValue] = useState<number>(0);
  const [constNegotiatedValue, setConstNegotiatedValue] = useState<number>(0);
  const [updatedValue, setUpdatedValue] = useState<number>(0);
  const [constUpdatedValue, setConstUpdatedValue] = useState<number>(0);
  const [subTotal, setSubTotal] = useState<number>(0);
  // const [constSubTotal, setConstSubTotal] = useState<number>(0);
  const [inputValue, setInputValue] = useState<InputValueObject>();
  // const [constExtraValue, setConstExtraValue] = useState<number>(0);

  const [constFees, setConstFees] = useState<number>(0);
  const [constFine, setConstFine] = useState<number>(0);
  const [constCommission, setConstCommission] = useState<number>(0);
  const [constHonor, setConstHonor] = useState<number>(0);

  const [showBoletoModal, setShowBoletoModal] = useState<boolean>(false);
  const [parametro, setParametro] = useState<any>(null);

  const getParametro = async () => {
    const payload = {
      idclientedatacob: context?.data?.id_Cliente,
      idfasecontrato: context?.data?.id_Fase,
      codorname: context?.data?.coddatacob.toString(),
    };
    const param = await getApi(payload, "getParametroCalculo");
    setParametro(param);
  };

  useEffect(() => {
    const parcelas = parcelasSelecionadas.filter(
      (item) => item.parcelaSelecionada
    );

    const dadosCalculados = calculaValoresDetelhes(parcelas);
    // if (parametro !== null || parametro !== undefined) {
    //   const detalhes = calculaValoresParcelas(
    //     parametro,
    //     parcelas,
    //     0,
    //     selectedDate
    //   );
    // }
    const mainDiscount = dadosCalculados.realNegociacao;
    setUpdatedValue(mainDiscount);
    setConstUpdatedValue(mainDiscount);

    const fees = dadosCalculados.jurosNegociacao;

    const feesPerc = dadosCalculados.percJurosNegociacao;

    const feesApplied = dadosCalculados.percDescJurosNegociacao;

    const fine = dadosCalculados.multaNegociacao;

    const finePerc = dadosCalculados.percMultaNegociacao;

    const fineApplied = 0;

    const commission = dadosCalculados.comissaoNegociacao;

    const commissionPerc = dadosCalculados.percComissaoNegociacao;

    const honor = dadosCalculados.honorariosNegociacao;

    const discount = parcelas[0]?.vlHoNegociado - honor;

    const honorPerc = dadosCalculados.percHonorariosNegociacao;

    setInputValue({
      mainValue: mainDiscount,
      mainValuePerc: 0,
      correctedValue: 0,
      fees: fees,
      feesPerc: feesPerc,
      feesApplied: 0,
      fine: fine,
      finePerc: finePerc,
      fineApplied: 0,
      commission: commission,
      commissionPerc: commissionPerc,
      commissionApplied: 0,
      discount: discount,
      honor: honor,
      honorPerc: honorPerc,
      costs: 0,
      notification: 0,
      fareValue: 0,
    });

    setConstFees(fees);
    setConstFine(fine);
    setConstCommission(commission);

    // setConstExtraValue(honor);
    setConstHonor(honor);

    const mainDiscountWithoutHonor = dadosCalculados.subTotalNegociacao;
    setSubTotal(mainDiscountWithoutHonor);
    // setConstSubTotal(mainDiscountWithoutHonor);

    setNegotiatedValue(valorNegociado);
    setConstNegotiatedValue(valorNegociado);
  }, [parcelasSelecionadas, valorNegociado, parametro]);

  const handleInputChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    perc: boolean = false
  ) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    if (!perc) {
      value = Number(input) / 100;
      if (input.length > 11) return;
    } else {
      value = Number(input) / 1000;
      if (value > 100) value = 100.0;
    }

    // let newObject = {  };
    // newObject[event.target.name] = value;
    setInputValue({ ...inputValue, [event.target.name]: value });
  };

  const handleCalcUpdated = (field: string) => {
    let perc = 0;
    let value = 0;
    switch (field) {
      case "correctedValue":
      case "mainValue":
        perc = -((inputValue.mainValue * 100) / constUpdatedValue - 100);
        value = inputValue.mainValue;
        break;
      case "mainValuePerc":
        perc = inputValue.mainValuePerc;
        value =
          Math.round(
            (constUpdatedValue - (constUpdatedValue * perc) / 100) * 100
          ) / 100;
        break;
    }

    setUpdatedValue(value + inputValue.correctedValue);
    const newInputValue = {
      ...inputValue,
      mainValuePerc: perc,
      mainValue: value,
    };
    calcCascateValues(newInputValue, value + inputValue.correctedValue);
  };

  const calcCascateValues = (
    newInputValue: InputValueObject,
    customValue: number | null = null,
    customHonor: number | null = null
  ) => {
    customValue = customValue || newInputValue?.mainValue;
    const sub = calcSubTotal(newInputValue);
    const honorPerc = Math.round(newInputValue.honorPerc * 100) / 100;
    const honor =
      customHonor || Math.round(((sub * honorPerc) / 100) * 100) / 100;
    setNegotiatedValue(
      sub +
        honor +
        inputValue.costs +
        inputValue.notification +
        inputValue.fareValue
    );
    setInputValue({
      ...newInputValue,
      honor: honor,
    });
    setSubTotal(sub);
  };

  const handleCalcSub = (field: string) => {
    let newInputValue = { ...inputValue };
    switch (field) {
      case "fees":
        const subFeesPerc = -((inputValue.fees * 100) / constFees - 100);
        newInputValue = {
          ...inputValue,
          feesPerc: subFeesPerc,
        };
        break;
      case "fine":
        const subFinePerc = -((inputValue.fine * 100) / constFine - 100);
        newInputValue = {
          ...inputValue,
          finePerc: subFinePerc,
        };

        break;
      case "finePerc":
        //157.04 - (157.04 * 10 / 100)
        const subFine = constFine - (constFine * inputValue.finePerc) / 100;
        newInputValue = {
          ...inputValue,
          fine: subFine,
        };

        break;
      case "commission":
        const subCommissionPerc = -(
          (inputValue.commission * 100) / constCommission -
          100
        );
        newInputValue = {
          ...inputValue,
          commissionPerc: subCommissionPerc,
        };
        break;
    }
    calcCascateValues(newInputValue);
  };

  const handleCalcTotal = (field: string) => {
    let newInputValue = { ...inputValue };
    let honor = null;
    switch (field) {
      case "honor":
        const totalHonorPerc = (inputValue.honor * 100) / subTotal;
        newInputValue = {
          ...inputValue,
          honorPerc: totalHonorPerc,
        };
        honor = inputValue.honor;
        break;
      case "honorPerc":
        const totalHonor =
          constHonor - (constHonor * inputValue.honorPerc) / 100;
        newInputValue = {
          ...inputValue,
          honor: totalHonor,
        };
        break;
    }
    calcCascateValues(newInputValue, null, honor);
  };

  const calcSubTotal = (
    newInputValue: InputValueObject,
    newValue: number | null = null
  ) => {
    let value = newValue || newInputValue?.mainValue;
    return (
      value +
      newInputValue?.correctedValue +
      newInputValue?.fees +
      newInputValue?.fine +
      newInputValue?.commission
    );
  };

  const calcDiscountInput = () => {
    return constHonor - inputValue?.honor;
  };

  // const handleConfirm = () => {
  //   //onClose();
  //   //handleFreeCalculation(negotiatedValue);
  //   //setFreeCalcValue(valuesTicket());
  // };

  const valuesTicket = (): ReturnValues => {
    let arrParc = [];
    parcelasSelecionadas.forEach((item) => {
      if (item.parcelaSelecionada) {
        let itemArr = arrParc.find((x) => x.contract === item.nrContrato);
        if (itemArr !== undefined) {
          itemArr.installment.push(parseInt(item.nrParcela));
        } else {
          arrParc.push({
            contract: item.nrContrato,
            installment: [parseInt(item.nrParcela)],
          });
        }
      }
    });
    return {
      ...inputValue,
      negotiatedValue: constNegotiatedValue,
      negotiatedDate: formatDate(selectedDate.toISOString()),
      installmentJson: JSON.stringify(arrParc),
    };
  };

  const getNrsParcelas = (): number[] => {
    return parcelasSelecionadas
      .filter((item) => item.parcelaSelecionada)
      .map((item) => parseInt(item.nrParcela));
  };

  const mountTicketValues = (): TicketValues => {
    return {
      dtNegociacao: selectedDate,
      formaDesconto: 2,
      idAgrupamento: context?.data?.id_Agrupamento,
      parcelas: parcelasSelecionadas
        .filter((item) => item.parcelaSelecionada)
        .map((item) => item.idParcela),
      vlNegociado: negotiatedValue,
    };
  };

  return (
    <>
      <CModal
        show={isOpen}
        onClose={onClose}
        hidden={showBoletoModal}
        closeOnBackdrop={false}
        size="lg"
      >
        <CModalHeader closeButton>Cálculo de Quitação</CModalHeader>
        <CModalBody>
          <div
            className="d-flex flex-row justify-content-between"
            style={{ gap: "1rem" }}
          >
            <div className="w-100">
              <div className="inline-flex-gap-right mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  Valor Principal
                </CLabel>
                <CInput
                  placeholder="Valor Principal R$"
                  name="mainValue"
                  value={formatCurrency(inputValue?.mainValue, false)}
                  onChange={handleInputChange}
                />
                <CButton
                  color="info"
                  className="ml-2"
                  onClick={() => handleCalcUpdated("mainValue")}
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
              <div className="inline-flex-gap-right mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  Valor Correção
                </CLabel>
                <CInput
                  placeholder="Valor Correção R$"
                  name="correctedValue"
                  value={formatCurrency(inputValue?.correctedValue, false)}
                  onChange={handleInputChange}
                />
                <CButton
                  onClick={() => handleCalcUpdated("correctedValue")}
                  color="info"
                  className="ml-2"
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
            </div>
            <div className="w-100">
              <div className="inline-flex-gap-left mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  %
                </CLabel>
                <CInput
                  placeholder="%"
                  name="mainValuePerc"
                  value={floatToPerc(inputValue?.mainValuePerc, 3)}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange(e, true)
                  }
                />
                <CButton
                  onClick={() => handleCalcUpdated("mainValuePerc")}
                  color="info"
                  className="ml-2"
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
            </div>
          </div>
          <h4 className="mt-3 text-right">
            Valor Atualizado {formatCurrency(updatedValue)}
          </h4>
          <hr />
          <div
            className="d-flex flex-row justify-content-between"
            style={{ gap: "1rem" }}
          >
            <div className="w-100" style={{ marginTop: "37px" }}>
              <div className="inline-flex-gap-right mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  Juros
                </CLabel>
                <CInput
                  placeholder="Juros R$"
                  name="fees"
                  value={formatCurrency(inputValue?.fees, false)}
                  onChange={handleInputChange}
                />
                <CButton
                  onClick={() => handleCalcSub("fees")}
                  color="info"
                  className="ml-2"
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
              <div className="inline-flex-gap-right mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  Multa
                </CLabel>
                <CInput
                  placeholder="Multa R$"
                  name="fine"
                  value={formatCurrency(inputValue?.fine, false)}
                  onChange={handleInputChange}
                />
                <CButton
                  onClick={() => handleCalcSub("fine")}
                  color="info"
                  className="ml-2"
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
              <div className="inline-flex-gap-right mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  Com. de Permanência
                </CLabel>
                <CInput
                  placeholder="Com. de Permanência R$"
                  name="commission"
                  value={formatCurrency(inputValue?.commission, false)}
                  onChange={handleInputChange}
                />
                <CButton
                  onClick={() => handleCalcSub("commission")}
                  color="info"
                  className="ml-2"
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
            </div>
            <div className="w-100">
              <div
                className="inline-flex-gap-left mt-2"
                style={{ marginRight: "20%", justifyContent: "space-around" }}
              >
                {/* <CLabel>% Aplicado</CLabel> */}
                <CLabel>% Desconto</CLabel>
              </div>
              <div className="inline-flex-gap-left mt-2">
                {/* <CLabel color="info" className="ml-2 mb-0">
                  %
                </CLabel>
                <CInput
                  disabled
                  placeholder="% Aplicado"
                  name="feesApplied"
                  value={formatCurrency(inputValue?.feesApplied, false)}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange(e, true)
                  }
                />*/}
                <CLabel color="info" className="ml-2 mb-0">
                  %
                </CLabel>
                <CInput
                  placeholder="% Desconto"
                  name="feesPerc"
                  value={
                    inputValue?.feesApplied === 0
                      ? "0,000"
                      : floatToPerc(inputValue?.feesPerc, 3)
                  }
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange(e, true)
                  }
                  disabled={inputValue?.feesApplied === 0}
                />
                <CButton
                  color="info"
                  className="ml-2"
                  disabled={inputValue?.feesApplied === 0}
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
              <div className="inline-flex-gap-left mt-2">
                {/* <CLabel color="info" className="ml-2 mb-0">
                  %
                </CLabel>
                <CInput
                  disabled
                  placeholder="% Aplicado"
                  name="fineApplied"
                  value={formatCurrency(inputValue?.fineApplied, false)}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange(e, true)
                  }
                /> */}
                <CLabel color="info" className="ml-2 mb-0">
                  %
                </CLabel>
                <CInput
                  placeholder="% Desconto"
                  name="finePerc"
                  value={
                    inputValue?.fineApplied === 0
                      ? "0,000"
                      : floatToPerc(inputValue?.finePerc, 3)
                  }
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange(e, true)
                  }
                  disabled={inputValue?.fineApplied === 0}
                />
                <CButton
                  color="info"
                  className="ml-2"
                  disabled={inputValue?.fineApplied === 0}
                  onClick={() => handleCalcSub("finePerc")}
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
              <div className="inline-flex-gap-left mt-2">
                {/* <CLabel color="info" className="ml-2 mb-0">
                  %
                </CLabel>
                <CInput
                  disabled
                  placeholder="% Aplicado"
                  name="commissionApplied"
                  value={formatCurrency(inputValue?.commissionApplied, false)}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange(e, true)
                  }
                /> */}
                {/* <CButton color="info" className="ml-2">
                <i className="cil-check"></i>
              </CButton> */}
                <CLabel color="info" className="ml-2 mb-0">
                  %
                </CLabel>
                <CInput
                  placeholder="% Desconto"
                  name="commissionPerc"
                  value={
                    inputValue?.commissionApplied === 0
                      ? "0,000"
                      : floatToPerc(inputValue?.commissionPerc, 3)
                  }
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange(e, true)
                  }
                  disabled={inputValue?.commissionApplied === 0}
                />
                <CButton
                  color="info"
                  className="ml-2"
                  disabled={inputValue?.commissionApplied === 0}
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
            </div>
          </div>
          <h4 className="mt-3 text-right">
            Sub-Total {formatCurrency(subTotal)}
          </h4>
          <hr />
          <div
            className="d-flex flex-row justify-content-between"
            style={{ gap: "1rem" }}
          >
            <div className="w-100">
              <div className="inline-flex-gap-right mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  Desconto
                </CLabel>
                <CInput
                  disabled
                  placeholder="Desconto R$"
                  name="discount"
                  value={formatCurrency(
                    inputValue?.discount || calcDiscountInput(),
                    false
                  )}
                  onChange={handleInputChange}
                />
                <CButton color="info" className="ml-2" disabled>
                  <i className="cil-check"></i>
                </CButton>
              </div>
              <div className="inline-flex-gap-right mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  Honorários
                </CLabel>
                <CInput
                  placeholder="Honorários R$"
                  name="honor"
                  value={formatCurrency(inputValue?.honor, false)}
                  onChange={handleInputChange}
                />
                <CButton
                  color="info"
                  className="ml-2"
                  onClick={() => handleCalcTotal("honor")}
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
              <div className="inline-flex-gap-right mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  Custas
                </CLabel>
                <CInput
                  placeholder="Custas R$"
                  name="costs"
                  value={formatCurrency(inputValue?.costs, false)}
                  onChange={handleInputChange}
                />
                <CButton
                  color="info"
                  className="ml-2"
                  onClick={() => handleCalcTotal("costs")}
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
              <div className="inline-flex-gap-right mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  Notificação
                </CLabel>

                <CInput
                  placeholder="Notificação R$"
                  name="notification"
                  value={formatCurrency(inputValue?.notification, false)}
                  onChange={handleInputChange}
                />
                <CButton
                  color="info"
                  className="ml-2"
                  onClick={() => handleCalcTotal("notification")}
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
              <div className="inline-flex-gap-right mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  Vl. Tarifa
                </CLabel>
                <CInput
                  placeholder="Vl. Tarifa R$"
                  name="fareValue"
                  value={formatCurrency(inputValue?.fareValue, false)}
                  onChange={handleInputChange}
                />
                <CButton
                  color="info"
                  className="ml-2"
                  onClick={() => handleCalcTotal("fareValue")}
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
            </div>
            <div className="w-100">
              <div
                className="inline-flex-gap-left mt-2 mb-3"
                style={{ color: "transparent" }}
              >
                <CLabel color="info" className="ml-2 mb-0">
                  Autorizado
                </CLabel>
                <input type="checkbox" hidden placeholder="%" />
              </div>
              <div className="inline-flex-gap-left mt-2">
                <CLabel color="info" className="ml-2 mb-0">
                  %
                </CLabel>
                <CInput
                  placeholder="%"
                  name="honorPerc"
                  value={floatToPerc(inputValue?.honorPerc, 3)}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange(e, true)
                  }
                />
                <CButton
                  color="info"
                  className="ml-2"
                  onClick={() => handleCalcTotal("honorPerc")}
                >
                  <i className="cil-check"></i>
                </CButton>
              </div>
            </div>
          </div>
          <h4 className="mt-3 text-right">
            Total {formatCurrency(negotiatedValue)}
          </h4>
        </CModalBody>
        <CModalFooter>
          {/* <CButton
            color="primary"
            onClick={() => {
              handleClose();
              openBoletoModal();
            }}
          >
            Visualizar Status Boletos Coringas
          </CButton> */}
          <CButton
            color="info"
            onClick={() => {
              //handleConfirm();
              setShowBoletoModal(true);
            }}
          >
            Confirmar
          </CButton>
          <CButton color="secondary" onClick={handleClose}>
            Fechar
          </CButton>
        </CModalFooter>
      </CModal>
      {showBoletoModal && (
        <BoletoModal
          isOpen={showBoletoModal}
          onClose={() => {
            setShowBoletoModal(false);
          }}
          parcelas={getNrsParcelas()}
          dados={mountTicketValues()}
          onSubmit={() => {
            onClose();
            setShowBoletoModal(false);
          }}
          freeCalcValue={valuesTicket()}
          freeCalc={true}
        />
      )}
    </>
  );
}
