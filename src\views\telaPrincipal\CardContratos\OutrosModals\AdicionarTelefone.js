import { useState } from "react";
import Select from "react-select";
import {
  CButton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CFormGroup,
  CForm,
  CRow,
  CInputCheckbox,
  CCol,
  CLabel,
  CInput,
} from "@coreui/react";

import { formatFoneWithoutDdd } from "src/reusable/helpers";

const AdicionarTelefone = ({
  isOpen,
  onClose,
  onSave,
  tipoTelefone,
  inserirTelefone,
}) => {
  const [telefone, setTelefone] = useState({
    ddd: inserirTelefone ? inserirTelefone.slice(0, 2) : "",
    fone: inserirTelefone ? inserirTelefone.slice(2) : "",
    ramal: "",
    descricao: "",
    contato: "",
    tipoTelefone: 1,
    status: 0,
    isHotNumber: false,
    isWhatsApp: false,
  });

  const [fone, setFone] = useState(
    inserirTelefone ? inserirTelefone.slice(2) : ""
  );

  const optionsTelefone = tipoTelefone
    ? [
        ...tipoTelefone.map((item) => {
          return { label: item.descricao, value: item.idTipo };
        }),
      ]
    : [];

  const listaStatus = [
    { value: 0, label: "Inativo" },
    { value: 1, label: "Ativo" },
    { value: 2, label: "Efetivo" },
  ];

  const handleTelefoneChange = (selectedOption) => {
    setTelefone((prevState) => ({
      ...prevState,
      tipoTelefone: selectedOption.value,
    }));
  };

  const handleStatusChange = (selectedOption) => {
    setTelefone((prevState) => ({
      ...prevState,
      status: selectedOption.value,
    }));
  };

  const handleCheckbox = (target) => {
    const { name, checked } = target;
    setTelefone((prevState) => ({
      ...prevState,
      [name]: checked,
    }));
  };

  const handleNumeroChange = (e) => {
    const { name, value } = e.target;

    let formattedValue = formatFoneWithoutDdd(value);

    setTelefone((prevEnd) => ({
      ...prevEnd,
      [name]: formattedValue.replace("-", ""),
    }));
    if (name === "fone") setFone(formattedValue);
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setTelefone((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  function resetModal() {
    setTelefone({
      ddd: "",
      fone: "",
      ramal: "",
      descricao: "",
      contato: "",
      tipoTelefone: 1,
      status: 0,
      isHotNumber: false,
      isWhatsApp: false,
    });
  }

  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };

  const handleSave = (event) => {
    event.preventDefault();

    const requiredFields = [
      { name: "ddd", displayName: "DDD" },
      { name: "fone", displayName: "Telefone" },
      { name: "tipoTelefone", displayName: "Tipo de Telefone" },
    ];

    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = telefone[field.name];

      if (field.name === "fone") {
        if (isFieldEmpty(fieldValue) || fieldValue.length < 8) {
          alert("Telefone fora do padrão, o campo deve conter 8 ou 9 dígitos.");
        }
        return isFieldEmpty(fieldValue) || fieldValue.length < 8;
      }

      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    onSave(telefone);
    resetModal();
    onClose();
  };

  return (
    <CModal show={isOpen} size="lg" onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Adicionar Telefone</CModalHeader>
      <CModalBody>
        <CForm>
          <CFormGroup>
            <CRow>
              <CCol md="2">
                <CLabel>DDD</CLabel>
                <CInput
                  name="ddd"
                  type="text"
                  value={telefone.ddd}
                  onChange={handleNumeroChange}
                  maxLength={2}
                />
              </CCol>
              <CCol md="4">
                <CLabel>Telefone</CLabel>
                <CInput
                  type="text"
                  name="fone"
                  value={fone}
                  onChange={handleNumeroChange}
                  maxLength={10}
                />
              </CCol>
              <CCol md="2">
                <CLabel>Ramal</CLabel>
                <CInput
                  type="text"
                  name="ramal"
                  value={telefone.ramal}
                  onChange={handleNumeroChange}
                />
              </CCol>
              <CCol md="4">
                <CLabel>Contato</CLabel>
                <CInput
                  type="text"
                  name="contato"
                  value={telefone.contato}
                  onChange={handleInputChange}
                />
              </CCol>
            </CRow>
          </CFormGroup>
          <CFormGroup>
            <CRow>
              <CCol md="3">
                <CLabel>Status</CLabel>
                <Select
                  name="status"
                  value={listaStatus.find(
                    (option) => option.value === telefone.status
                  )}
                  onChange={handleStatusChange}
                  options={listaStatus}
                />
              </CCol>
              <CCol md="3">
                <CLabel>Tipo</CLabel>
                <Select
                  name="tipoTelefone"
                  placeholder="Selecione"
                  value={optionsTelefone.find(
                    (option) => option.value === telefone.tipoTelefone
                  )}
                  onChange={handleTelefoneChange}
                  options={optionsTelefone}
                />
              </CCol>
              <CCol md="6">
                <CLabel>Descrição</CLabel>
                <CInput
                  name="descricao"
                  type="text"
                  value={telefone.descricao}
                  onChange={handleInputChange}
                />
              </CCol>
            </CRow>
          </CFormGroup>
          <CFormGroup>
            <CRow>
              <CCol md="3">
                <CLabel>Telefone Hot</CLabel>
                <div style={{ paddingLeft: "40px" }}>
                  <CInputCheckbox
                    className="mx-0"
                    name="isHotNumber"
                    onChange={(e) => handleCheckbox(e.target)}
                    checked={telefone.isHotNumber}
                  />
                </div>
              </CCol>

              <CCol md="3">
                <CLabel>Whatsapp</CLabel>
                <div style={{ paddingLeft: "40px" }}>
                  <CInputCheckbox
                    className="mx-0"
                    name="isWhatsApp"
                    onChange={(e) => handleCheckbox(e.target)}
                    checked={telefone.isWhatsApp}
                  />
                </div>
              </CCol>
            </CRow>
          </CFormGroup>
        </CForm>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Cancelar
        </CButton>
        <CButton color="primary" onClick={handleSave}>
          Salvar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default AdicionarTelefone;
