import React, { useState } from "react";
import {
  CModal<PERSON>ooter,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CButton,
  CFormGroup,
  CLabel,
  CInputGroup,
  CInputGroupAppend,
  CInputGroupText,
  CInput,
} from "@coreui/react";
import CardLoading from "src/reusable/CardLoading";
import { formatCurrency, formatDate } from "src/reusable/helpers";

const ControleUsuariosDetailsModal = ({ isOpen, onClose, ticket = null }) => {
  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="xl">
      <CModalHeader closeButton>
        <CModalTitle></CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div
          className="table-responsive"
          style={{ overflow: "auto", maxHeight: "500px" }}
        >
          <table className="table">
            <thead>
              <tr>
                <th>Nome</th>
                <th>Nr. Contrato</th>
                <th>EMP</th>
                <th>Nr. Boleto</th>
                <th>Nr. Parcela</th>
                <th>Tipo Parcela</th>
                <th>Valor do Caixa</th>
                <th>Honorário</th>
                <th>Vencimento</th>
                <th>Emissão</th>
                <th>Status</th>
                <th>BP</th>
                <th>Observação</th>
                <th>Empreendimento</th>
                <th>Usuário</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{ticket?.nomeCliente}</td>
                <td>{ticket?.numeroContrato}</td>
                <td>{ticket?.emp}</td>
                <td>{ticket?.numeroBoleto}</td>
                <td>{ticket?.numeroParcelaEnvolvida}</td>
                <td>{ticket?.descricaoTipoParcela}</td>
                <td>{formatCurrency(ticket?.valorCaixa)}</td>
                <td>{formatCurrency(ticket?.honorario)}</td>
                <td>{formatDate(ticket?.vencimento)}</td>
                <td>{formatDate(ticket?.dataEmissao)}</td>
                <td>{ticket?.statusBoleto}</td>
                <td>{ticket?.bp}</td>
                <td>{ticket?.descricaoCarteira}</td>
                <td>{ticket?.carteiraEmpreendimento}</td>
                <td>{ticket?.nomeUsuario}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <br />
        <CLabel>Observação</CLabel>
        <textarea className="form-control" rows={4} readOnly>
          {ticket?.observacao}
        </textarea>
      </CModalBody>
      <CModalFooter>
        <CButton color="danger" onClick={onClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default ControleUsuariosDetailsModal;
