import React, { useState, useEffect, useRef } from "react";
import {
  CButton,
  CCard,
  CCol,
  CNav,
  CNavItem,
  CNavLink,
  CRow,
  CTabContent,
  CTabPane,
  CTabs,
} from "@coreui/react";

import ContratosAtivos from "./CardFinanceiro/ContratosAtivos";
import SaldoAcumulado from "./CardFinanceiro/SaldoAcumulado";
import PlanoCobranca from "./CardFinanceiro/PlanoCobranca";
import DetalhesContrato from "./CardFinanceiro/DetalhesContrato";
import Bens from "./CardFinanceiro/Bens";
import EstruturaComercial from "./CardFinanceiro/EstruturaComercial";
import DadosSocio from "./CardFinanceiro/DadosSocios";
import Agenda from "./CardFinanceiro/Agenda";
import DebitoEmContaModal from "./CardFinanceiro/DebitoEmContaModal/DebitoEmContaModal";
import ConsultasBuscas from "./CardFinanceiro/ConsultasBuscas";
import { useMyContext } from "src/reusable/DataContext";
import CardLoading from "src/reusable/CardLoading";
import { useAuth } from "src/auth/AuthContext";
import AbasScrollable from "src/reusable/AbasScrollable";
import ModalPrestadosServicosCall from "../configuracoes/prestadoresServicos/modalPrestadosServicosCall.tsx";

const TabFinanceiro = ({ visibleTabIds }) => {
  const { data } = useMyContext();
  const { checkPermission, inforPermissions } = useAuth();
  const [showModalPrestadosServicosCall, setShowModalPrestadosServicosCall] =
    useState(false);
  const permissaoTelaPrincipalContratosAtivos = {
    modulo: "Tela Principal",
    submodulo: "Contratos Ativos",
  };
  const permissaoTelaPrincipalSaldoAcumulado = {
    modulo: "Tela Principal",
    submodulo: "Saldo Acumulado",
  };

  const permissaoTelaPrincipalBens = {
    modulo: "Tela Principal",
    submodulo: "Bens",
  };

  const permissaoTelaPrincipalEstruturaComercial = {
    modulo: "Tela Principal",
    submodulo: "Estrutura Comercial",
  };
  const permissaoTelaPrincipalDadosSocios = {
    modulo: "Tela Principal",
    submodulo: "Dados dos Sócios",
  };

  const permissaoTelaPrincipalAgenda = {
    modulo: "Tela Principal",
    submodulo: "Agenda",
  };

  const permissaoTelaPrincipalConsultaseBuscas = {
    modulo: "Tela Principal",
    submodulo: "Consultas e Buscas",
  };

  const permissaoTelaPrincipalPlanodeCobranca = {
    modulo: "Tela Principal",
    submodulo: "Plano de Cobrança",
  };

  const permissaoPrestadoresServicos = {
    modulo: "Cadastro de Prestadores de Serviço",
    submodulo: "Cadastro de Prestadores de Serviço",
  };

  const [selectedTabIndex, setSelectedTabIndex] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const tabs = [
    {
      id: 1,
      label: "Contratos Ativos",
      icon: "cil-task",
      content: (
        <ContratosAtivos selected={selectedTabIndex === 1 ? true : false} />
      ),
    },
    {
      id: 2,
      label: "Saldo Acumulado",
      icon: "cil-money",
      content: (
        <SaldoAcumulado selected={selectedTabIndex === 2 ? true : false} />
      ),
    },
    {
      id: 3,
      label: "Plano de Cobrança",
      icon: "cil-dollar",
      content: (
        <PlanoCobranca selected={selectedTabIndex === 3 ? true : false} />
      ),
    },
    {
      id: 4,
      label: "Detalhes de Contrato",
      icon: "cil-magnifying-glass",
      content: (
        <DetalhesContrato selected={selectedTabIndex === 4 ? true : false} />
      ),
    },
    {
      id: 5,
      label: "Bens",
      icon: "cil-car-alt",
      content: <Bens selected={selectedTabIndex === 5 ? true : false} />,
    },
    {
      id: 6,
      label: "Estrutura Comercial",
      icon: "cil-briefcase",
      content: (
        <EstruturaComercial selected={selectedTabIndex === 6 ? true : false} />
      ),
    },
    {
      id: 7,
      label: "Dados dos Sócios",
      icon: "cil-address-book",
      content: <DadosSocio selected={selectedTabIndex === 7 ? true : false} />,
    },
    {
      id: 8,
      label: "Agenda",
      icon: "cil-book",
      content: <Agenda selected={selectedTabIndex === 8 ? true : false} />,
    },
    // {
    //   id: 9,
    //   label: "Consultas e Buscas",
    //   icon: "cil-book",
    //   content: (
    //     <ConsultasBuscas selected={selectedTabIndex === 9 ? true : false} />
    //   ),
    // },
    {
      id: 20,
      label: "Nova aba",
      icon: "cil-bus-alt",
      content: "Sem Permissoes Ativas",
    },
  ];

  const filterTabsPermissions = () => {
    return tabs.filter((tab) => {
      if (tab.label === "Contratos Ativos")
        return checkPermission(
          permissaoTelaPrincipalContratosAtivos.modulo,
          "View",
          permissaoTelaPrincipalContratosAtivos.submodulo
        );
      if (tab.label === "Saldo Acumulado")
        return checkPermission(
          permissaoTelaPrincipalSaldoAcumulado.modulo,
          "View",
          permissaoTelaPrincipalSaldoAcumulado.submodulo
        );
      if (tab.label === "Plano de Cobrança")
        return checkPermission(
          permissaoTelaPrincipalPlanodeCobranca.modulo,
          "View",
          permissaoTelaPrincipalPlanodeCobranca.submodulo
        );
      if (tab.label === "Detalhes de Contrato")
        return checkPermission(
          permissaoTelaPrincipalContratosAtivos.modulo,
          "View",
          permissaoTelaPrincipalContratosAtivos.submodulo
        );
      if (tab.label === "Bens")
        return checkPermission(
          permissaoTelaPrincipalBens.modulo,
          "View",
          permissaoTelaPrincipalBens.submodulo
        );
      if (tab.label === "Estrutura Comercial")
        return checkPermission(
          permissaoTelaPrincipalEstruturaComercial.modulo,
          "View",
          permissaoTelaPrincipalEstruturaComercial.submodulo
        );
      if (tab.label === "Dados dos Sócios")
        return checkPermission(
          permissaoTelaPrincipalDadosSocios.modulo,
          "View",
          permissaoTelaPrincipalDadosSocios.submodulo
        );
      if (tab.label === "Agenda")
        return checkPermission(
          permissaoTelaPrincipalAgenda.modulo,
          "View",
          permissaoTelaPrincipalAgenda.submodulo
        );
      if (tab.label === "Consultas e Buscas")
        return checkPermission(
          permissaoTelaPrincipalConsultaseBuscas.modulo,
          "View",
          permissaoTelaPrincipalConsultaseBuscas.submodulo
        );

      return false;
    });
  };

  const visibleTabs = filterTabsPermissions().filter((tab) =>
    visibleTabIds.includes(tab.id)
  );
  const [currentTab, setCurrentTab] = useState(
    visibleTabs.length > 0 ? visibleTabs[0].label : ""
  );

  const handleTabSelect = (tab) => {
    if (!tab) return;
    setCurrentTab(tab.label);
    setSelectedTabIndex(tab.id);
  };

  const reRender = async () => {
    setIsLoading(true);
    await new Promise((resolve) => setTimeout(resolve, 500));
    setIsLoading(false);
  };

  useEffect(() => {
    if (data) {
      handleTabSelect(
        visibleTabs && visibleTabs.length > 0 ? visibleTabs[0] : null
      );
      reRender();
    }
  }, [data]);

  return (
    <CCard style={{ minHeight: "200px", maxHeight: "420px" }}>
      {isLoading ? (
        <CardLoading />
      ) : (
        <CRow className="">
          <CCol className="">
            <CTabs
              onSelect={handleTabSelect}
              activeTab={visibleTabs.length > 0 ? visibleTabs[0].label : ""}
            >
              <div className="d-flex justify-content-between">
                <AbasScrollable
                  visibleTabs={visibleTabs}
                  currentTab={currentTab}
                  handleTabSelect={handleTabSelect}
                />

                <CButton
                  color="info"
                  size="sm"
                  className="mt-2 mr-2 ml-2"
                  disabled={
                    !checkPermission(
                      permissaoPrestadoresServicos.modulo,
                      "View",
                      null
                    )
                  }
                  onClick={() => setShowModalPrestadosServicosCall(true)}
                >
                  Visualizar Prestadores
                </CButton>
              </div>
              <CTabContent className="px-3 overflow-auto">
                {!visibleTabs ||
                  (visibleTabs.length === 0 && (
                    <CTabPane key={"0"} data-tab={"Sem Permissões"}>
                      Sem Permissões
                    </CTabPane>
                  ))}
                {visibleTabs &&
                  visibleTabs.length > 0 &&
                  visibleTabs.map((tab) => (
                    <CTabPane key={tab.id} data-tab={tab.label}>
                      {tab.content}
                    </CTabPane>
                  ))}
              </CTabContent>
            </CTabs>
          </CCol>
        </CRow>
      )}

      {showModalPrestadosServicosCall && (
        <ModalPrestadosServicosCall
          isOpen={showModalPrestadosServicosCall}
          onClose={() => {
            setShowModalPrestadosServicosCall(false);
          }}
        />
      )}
    </CCard>
  );
};

export default TabFinanceiro;
