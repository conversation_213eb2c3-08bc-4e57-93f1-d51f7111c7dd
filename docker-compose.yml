version: '3.8'
services:
    app-node:
        container_name: tela-unica-app-node
        build:
            context: ./docker/app
            dockerfile: Dockerfile.node
        tty: true
        working_dir: /var/www/
        ports:
            - '3002:3000'
        volumes:
            - ./:/var/www
        restart: always
        networks:
            -  rede-tela-unica
networks:
    rede-tela-unica:
        driver: bridge
