import { useState, useEffect } from "react";
import Select from "react-select";
import {
  CButton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
} from "@coreui/react";

const SelecionarModal = ({ isOpen, options, onClose, onConfirm, tab }) => {
  const [selectedOption, setSelectedOption] = useState(null);

  const handleSelectChange = (selected) => {
    setSelectedOption(selected);
  };

  const handleConfirm = () => {
    let selecao;
    if (tab === 3) {
      selecao = options.find(
        (option) => option.id_Endereco === selectedOption.value
      );
    }
    if (tab === 4) {
      selecao = options.find(
        (option) => option.id_Telefone === selectedOption.value
      );
    }
    if (tab === 5) {
      selecao = options.find(
        (option) => option.id_Email === selectedOption.value
      );
    }

    onConfirm(selecao);
    onClose();
  };

  const listOptions = options.map((x) => {
    let arrayMap;
    if (tab === 3) {
      arrayMap = {
        label: x.logradouro,
        value: x.id_Endereco,
      };
    }
    if (tab === 4) {
      arrayMap = {
        label: x.ddd + " " + x.fone,
        value: x.id_Telefone,
      };
    }
    if (tab === 5) {
      arrayMap = {
        label: x.endereco_Email,
        value: x.id_Email,
      };
    }

    return arrayMap;
  });

  return (
    <CModal show={isOpen} onClose={onClose}>
      <CModalHeader>
        Selecionar {tab === 3 ? "endereço" : (tab === 4 ? "telefone" : "email")}
      </CModalHeader>
      <CModalBody>
        <Select
          options={listOptions}
          value={selectedOption}
          onChange={handleSelectChange}
          placeholder="Selecione"
        />
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Cancelar
        </CButton>
        <CButton color="primary" onClick={handleConfirm}>
          Selecionar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default SelecionarModal;
