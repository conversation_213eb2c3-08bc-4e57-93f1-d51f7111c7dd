import React, { useState, useEffect } from "react";
import { <PERSON>ow, CCol, CLabel, CCardBody, CButton, CBadge, CInputCheckbox } from "@coreui/react";
import SelecionarModal from "./SelecionarModal";
import { POST_DATA } from "src/api";
import { formatDate, formatFone } from "src/reusable/helpers";
import AdicionarTelefone from "./OutrosModals/AdicionarTelefone";
import EditarTelefone from "./OutrosModals/EditarTelefone";
import { getDadosFinanciado, getTiposTelefone } from "src/reusable/functions";
import { useMyContext } from "src/reusable/DataContext";
import { useAuth } from "src/auth/AuthContext";
import { toast } from "react-toastify";

const Telefone = ({ selected, onHandleSave }) => {
  const { checkPermission, inforPermissions } = useAuth();

  const permissao = {
    modulo: "Tela Principal",
    submodulo: "Telefone",
  };

  const { data, appSettings } = useMyContext();

  const [financiadoData, setFinanciadoData] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  const [financiadoTel, setFinanciadoTel] = useState(
    localStorage.getItem("clientData")
      ? JSON.parse(localStorage.getItem("clientData")).telefones
      : null
  );

  useEffect(() => {
    if (financiadoData && selected) {
      updateView();
    }
  }, [financiadoData]);

  const [showModalSelecionar, setShowModalSelecionar] = useState(false);
  const [showModalAdicionar, setShowModalAdicionar] = useState(false);
  const [showModalEditar, setShowModalEditar] = useState(false);
  const [isBlacklist, setIsBlacklist] = useState(false);

  const [tel, setTel] = useState(null);
  const [telefoneList, setTelefoneList] = useState([]);

  const [tipoTelefone, setTipoTelefone] = useState([]);

  const handleCloseAddModal = () => {
    setShowModalSelecionar(false);
    setShowModalAdicionar(false);
  };

  const handleCloseEditModal = () => {
    setShowModalEditar(false);
  };

  const handleCloseModal = () => {
    setShowModalSelecionar(false);
  };

  const handleChange = (selecao) => {
    setTel(selecao);
    setIsBlacklist(selecao.status === -1);
  };

  const handleAdicionar = async (newData) => {
    const postSuccess = await dataPost(newData);
    if (postSuccess.success) {
      const updatedData = await getDadosFinanciado(financiadoData.id_Financiado, financiadoData.numero_Contrato);

      setTelefoneList(updatedData.telefones);
      localStorage.setItem("clientData", JSON.stringify(updatedData));

      const lastIndex = updatedData.telefones.length - 1;
      const lastTel = updatedData.telefones[lastIndex];
      setTel(lastTel);
      setIsBlacklist(lastTel.status === -1);
      if (onHandleSave) onHandleSave();
      toast.success("Dados do financiado alterados com sucesso!");
    } else {
      toast.warning(postSuccess.message);
    }
  };

  const dataPost = async (newData) => {
    const data = {
      cpfCnpj: financiadoData.cpfCnpj,
      idGrupo: financiadoData.id_Grupo,
      nome: financiadoData.nome,
      telefones: [{ ...newData }],
    };
    const result = await POST_DATA("Datacob/DadosCadastrais/Financiado", data);
    return result;
  };

  const updateView = async () => {
    const updatedData = await getDadosFinanciado(financiadoData.id_Financiado, financiadoData.numero_Contrato);
    if (updatedData) {
      setTelefoneList(updatedData.telefones);
      localStorage.setItem("clientData", JSON.stringify(updatedData));
      setFinanciadoTel(updatedData.telefones);
      setTel(updatedData.telefones[0]);
      setIsBlacklist(updatedData.telefones[0] === -1);
    }
  };

  const renderStatus = (value) => {
    switch (value) {
      case 0:
        return <CBadge color="danger"> Inativo </CBadge>;
      case 1:
        return <CBadge color="success"> Ativo </CBadge>;
      case 2:
        return <CBadge color="info"> Efetivo </CBadge>;
      case 3:
        return <CBadge color="primary"> Pesquisado </CBadge>;
      case -1:
        return <CBadge color="dark"> Blacklist </CBadge>;
      default:
        break;
    }
  };

  const tipoReferencia = (value) => {
    switch (value) {
      case 1:
        return <div>Avalista</div>;
      case 2:
        return <div>Contato</div>;
      case 3:
        return <div>Financiado</div>;
      case 4:
        return <div>Terceiro</div>;
      case 5:
        return <div>Socio</div>;
      case 6:
        return <div>Mae</div>;
      case 7:
        return <div>Pai</div>;
      case 8:
        return <div>Filho</div>;
      case 9:
        return <div>Neto</div>;
      case 10:
        return <div>Tio</div>;
      case 11:
        return <div>Avo</div>;
      case 12:
        return <div>Sobrinho</div>;
      case 13:
        return <div>Irmao</div>;
      case 14:
        return <div>Primo</div>;
      case 15:
        return <div>Amigo</div>;
      case 16:
        return <div>Particular</div>;
      case 17:
        return <div>Comercial</div>;
      case 18:
        return <div>Bancaria</div>;
      case 19:
        return <div>Cooperado</div>;
      case 20:
        return <div>Advogado</div>;
      case 21:
        return <div>Esposa</div>;
      case 22:
        return <div>Conjuge</div>;
      default:
        break;
    }
  };

  useEffect(() => {
    if (selected && localStorage.getItem("clientData")) {
      const dadoAtualizado = JSON.parse(localStorage.getItem("clientData")).telefones;
      setFinanciadoTel(dadoAtualizado);

      if (!tel) {
        setTel(dadoAtualizado[0]);
        if (dadoAtualizado.length > 0) {
          setIsBlacklist(dadoAtualizado[0].status === -1);
        }
      } else {
        const existeDado = dadoAtualizado.find(
          (item) => item.id_Telefone === tel.id_Telefone
        );
        if (!existeDado) {
          setTel(dadoAtualizado[0]);
          setIsBlacklist(dadoAtualizado[0].status === -1);
        }
      }
      setTelefoneList(dadoAtualizado);
    } else {
      if (financiadoData && !financiadoTel) {
        updateView();
      }
    }
  }, [selected]);

  const handleTipoTelefone = async () => {
    const res = await getTiposTelefone();
    setTipoTelefone(res);
  };

  useEffect(() => {
    if (data) {
      setFinanciadoData(data);
    }
    const fetchData = async () => {
      try {
        await Promise.all([handleTipoTelefone()]);
      } catch (error) {
        console.log(error);
      }
    };
    fetchData();
  }, [data]);

  useEffect(() => {
    if (appSettings?.telefonia?.inserirTelefone && data) {
      setShowModalAdicionar(true);
    }
  }, [appSettings]);

  return (
    <CCardBody>
      <CRow>
        <CCol>
          <CLabel>DDD</CLabel>
          <div>
            <strong> {tel?.ddd ? tel?.ddd : "---"} </strong>
          </div>
        </CCol>
        <CCol>
          <CLabel>Fone</CLabel>
          <div>
            <strong> {tel?.fone ? formatFone(tel?.fone) : "---"} </strong>
          </div>
        </CCol>
        <CCol>
          <CLabel>Ramal</CLabel>
          <div>
            <strong> {tel?.ramal ? tel?.ramal : "---"} </strong>
          </div>
        </CCol>
        <CCol>
          <CLabel>Contato</CLabel>
          <div>
            <strong> {tel?.contato ? tel?.contato : "---"} </strong>
          </div>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CLabel>Referência</CLabel>
          <div>
            <strong>
              {tipoReferencia(tel?.referencia_Telefone)}
              {/* {tel?.referencia_Telefone ? tel?.referencia_Telefone : "---"}{" "} */}
            </strong>
          </div>
        </CCol>
        <CCol>
          <CLabel>Descrição</CLabel>
          <div>
            <strong> {tel?.descricao ? tel?.descricao : "---"} </strong>
          </div>
        </CCol>
        <CCol>
          <CLabel>Status</CLabel>
          <div>{renderStatus(tel?.status)}</div>
        </CCol>
        <CCol>
          <CLabel>Tipo</CLabel>
          <div>
            <strong>
              {" "}
              {tel?.id_Tipo_Telefone ? tel?.tipo_Telefone : "---"}{" "}
            </strong>
          </div>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CLabel>Operadora</CLabel>
          <div>
            <strong> {tel?.operadora ? tel?.operadora : "---"} </strong>
          </div>
        </CCol>
        <CCol>
          <CLabel>Data de Inclusão</CLabel>
          <div>
            <strong>
              {" "}
              {tel?.dtInclusao ? formatDate(tel?.dtInclusao) : "---"}{" "}
            </strong>
          </div>
        </CCol>
      </CRow>
      <CRow></CRow>
      <CRow>
        <CCol md="2" className="d-flex justify-content-around">
          <div>
            <CLabel>Telefone Hot</CLabel>
          </div>
          <div>
            <CInputCheckbox
              name="isHotNumber"
              defaultChecked={tel?.isHotNumber}
              disabled
            />
          </div>
        </CCol>
        <CCol md="2" className="d-flex justify-content-around">
          <div>
            <CLabel>Facebook</CLabel>
          </div>
          <div>
            <CInputCheckbox
              name="isFaceBook"
              defaultChecked={tel?.isFaceBook}
              disabled
            />
          </div>
        </CCol>
        <CCol md="2" className="d-flex justify-content-around">
          <div>
            <CLabel>Whats App</CLabel>
          </div>
          <div>
            <CInputCheckbox
              name="isWhatsApp"
              defaultChecked={tel?.isWhatsApp}
              disabled
            />
          </div>
        </CCol>
        <CCol
          md="6"
          style={{
            display: "flex",
            alignItems: "flex-end",
            justifyContent: "end",
          }}
        >
          <CButton
            color="secondary"
            onClick={() => setShowModalSelecionar(true)}
            size="sm"
            className="mr-2"
            disabled={!financiadoData}
          >
            Selecionar telefone
          </CButton>
          <CButton
            color="secondary"
            onClick={() => setShowModalEditar(true)}
            size="sm"
            className="mr-2"
            title={
              !financiadoData
                ? "Nenhum financiado Selecionado"
                : inforPermissions(permissao).edit
            }
            disabled={
              !financiadoData ||
              !checkPermission(permissao.modulo, "Edit", permissao.submodulo) ||
              isBlacklist
            }
          >
            Editar
          </CButton>
          <CButton
            color="secondary"
            onClick={() => setShowModalAdicionar(true)}
            size="sm"
            title={
              !financiadoData
                ? "Nenhum financiado Selecionado"
                : inforPermissions(permissao).create
            }
            disabled={
              !financiadoData ||
              !checkPermission(permissao.modulo, "Create", permissao.submodulo)
            }
          >
            Adicionar
          </CButton>
        </CCol>
        {showModalSelecionar && (
          <SelecionarModal
            onClose={handleCloseModal}
            options={telefoneList}
            isOpen={showModalSelecionar}
            onConfirm={handleChange}
            tab={4}
          />
        )}
        {showModalAdicionar && (
          <AdicionarTelefone
            onClose={handleCloseAddModal}
            isOpen={showModalAdicionar}
            onSave={handleAdicionar}
            tipoTelefone={tipoTelefone}
            inserirTelefone={appSettings?.telefonia?.inserirTelefone}
          />
        )}
        {showModalEditar && (
          <EditarTelefone
            onClose={handleCloseEditModal}
            isOpen={showModalEditar}
            editData={tel}
            onEdit={handleAdicionar}
            tipoTelefone={tipoTelefone}
          />
        )}
      </CRow>
    </CCardBody>
  );
};

export default Telefone;
