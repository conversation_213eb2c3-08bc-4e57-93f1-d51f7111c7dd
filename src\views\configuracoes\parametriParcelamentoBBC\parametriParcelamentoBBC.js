import {
  <PERSON>utton,
  <PERSON>ard,
  CCardBody,
  CCol,
  CForm,
  CFormGroup,
  CInput,
  CRow,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { DELETE_DATA, GET_DATA, POST_DATA, PUT_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import ConfirmModal from "src/reusable/ConfirmModal";
import TableSelectItens from "src/reusable/TableSelectItens";
import { formatCurrency } from "src/reusable/helpers";

const ParametriParcelamentoBBC = () => {
  const [data, setData] = useState([]);
  const [valorMin, setValorMin] = useState(0);
  const [valorMax, setValorMax] = useState(0);
  const [qtdParcelas, setQtdParcelas] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [insertMode, setInsertMode] = useState(true);
  const [guid, setGuid] = useState();

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const columns = [
    {
      key: "min",
      label: "Valor Mínimo",
      formatter: (item) => formatCurrency(item),
      defaultSort: "ascending",
    },
    {
      key: "max",
      label: "Valor Máximo",
      formatter: (item) => formatCurrency(item),
    },
    {
      key: "maxInstallments",
      label: "Quantidade de parcelas",
    },
    {
      key: "actions",
      label: "Ações",
      formatterByObject: (item) => renderActionButton(item),
    },
  ];

  const renderActionButton = (item) => (
    <>
      <CButton
        color="info"
        onClick={() => handleConfirmUpdate(item)}
        className="mr-2"
        // title={inforPermissions(permissao).edit}
        // disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
      >
        <i className="cil-reload" />
      </CButton>
      <CButton
        color="danger"
        onClick={() => handleConfirmDelete(item)}
        className="mr-2"
        // title={inforPermissions(permissao).edit}
        // disabled={!checkPermission(permissao.modulo, "Edit", permissao.submodulo)}
      >
        <i className="cil-trash" />
      </CButton>
    </>
  );

  const handleSave = async () => {
    if (valorMin >= valorMax) {
      toast.info(
        "Valor Mínimo não pode ser maior ou igual que o Valor Máximo!"
      );
      return;
    }
    if (qtdParcelas <= 0) {
      toast.info("Quantidade de parcelas não pode ser menor ou igual a 0!");
      return;
    }
    setIsLoading(true);
    if (insertMode) {
      const data = {
        maxInstallments: qtdParcelas,
        max: valorMax,
        min: valorMin,
        userId: user?.id,
      };
      const insertSuccess = await POST_DATA(
        getURI("restBBCInstallmentParam"),
        data,
        true
      );
      if (insertSuccess.success) {
        toast.success("Cadastro realizado com sucesso!");
        await getLista();
        clearInputs();
      } else {
        alert(insertSuccess.message);
      }
    } else {
      const data = {
        maxInstallments: qtdParcelas,
        max: valorMax,
        min: valorMin,
        id: guid,
      };
      const insertSuccess = await PUT_DATA(
        getURI("restBBCInstallmentParam"),
        data,
        true
      );
      if (insertSuccess.success) {
        toast.success("Alteração realizada com sucesso!");
        await getLista();
        clearInputs();
      } else {
        alert(insertSuccess.message);
      }
    }
    setIsLoading(false);
  };

  const handleModalClose = (confirmation) => {
    setShowConfirmModal(false);
    handleDelete(confirmation);
  };

  const handleConfirmDelete = (item) => {
    setSelectedItem(item);
    setShowConfirmModal(true);
  };
  const handleConfirmUpdate = (item) => {
    setGuid(item.id);
    setValorMax(item.max);
    setValorMin(item.min);
    setQtdParcelas(item.maxInstallments);
    setInsertMode(false);
  };

  const handleDelete = async (confirmation) => {
    setIsLoading(true);
    if (confirmation) {
      const deleteSuccess = await DELETE_DATA(
        getURI("restBBCInstallmentParam") + "/" + selectedItem.id,
        null,
        true
      );

      if (deleteSuccess.success) {
        toast.success("Exclusão realizada com sucesso!");
        await getLista();
        clearInputs();
      } else {
        alert(deleteSuccess.message);
      }
    }
    setIsLoading(false);
  };

  const clearInputs = () => {
    setValorMax(0);
    setValorMin(0);
    setQtdParcelas(0);
  };

  async function getLista() {
    setIsLoading(true);
    const lista = await GET_DATA(getURI("restBBCInstallmentParam"), null, true);

    if (lista) {
      setData(lista);
    }
    setIsLoading(false);
    return;
  }

  useEffect(() => {
    const awaitFunc = async () => {
      await getLista();
    };
    awaitFunc();
  }, []);

  const handleValorMin = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");
    const value = Number(input) / 100;
    if (input.length > 11 || isNaN(value)) return;
    setValorMin(value);
  };
  const handleValorMax = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");
    const value = Number(input) / 100;
    if (input.length > 11 || isNaN(value)) return;
    setValorMax(value);
  };

  return (
    <div>
      <h3>Cadastro de faixas de parcelamento BBC:</h3>
      <p style={{ color: "gray" }}></p>
      <div>
        <CCard>
          <CCardBody>
            <CForm>
              <CRow>
                <CCol xs>
                  <CRow>
                    <CCol>
                      <h5 style={{ color: "gray" }}>
                        {insertMode ? "Cadastro" : "Alteração"}:
                      </h5>
                    </CCol>
                  </CRow>
                  <CFormGroup className="row gx-3 gy-2 align-items-center">
                    <CCol xs={8}>
                      <CRow>
                        <CCol>
                          <CInput
                            onChange={handleValorMin}
                            placeholder="Valor Mínimo"
                            value={formatCurrency(valorMin, false)}
                          />
                        </CCol>

                        <CCol>
                          <CInput
                            onChange={handleValorMax}
                            placeholder="Valor Máximo"
                            value={formatCurrency(valorMax, false)}
                          />
                        </CCol>
                        <CCol>
                          <CInput
                            type="number"
                            placeholder="Qtd. de parcelas"
                            value={qtdParcelas}
                            onChange={(event) => {
                              setQtdParcelas(event.target.value);
                            }}
                          />
                        </CCol>
                      </CRow>
                    </CCol>
                    <CCol xs="auto">
                      <CButton
                        color="success"
                        onClick={handleSave}
                        className="mr-2"
                      >
                        {insertMode ? "Incluir" : "Alterar"}
                      </CButton>
                      <CButton
                        color="light"
                        onClick={() => {
                          setInsertMode(true);
                          setGuid(null);
                          clearInputs();
                        }}
                        className="mr-2"
                      >
                        Cancelar
                      </CButton>
                    </CCol>
                  </CFormGroup>
                </CCol>
                <CCol xs>
                  <CRow>
                    <CCol>
                      <h5 style={{ color: "gray" }}>
                        Lista de faixas já inclusas:
                      </h5>
                    </CCol>
                  </CRow>
                  <CRow>
                    {isLoading ? (
                      <CardLoading />
                    ) : (
                      <CCol>
                        <TableSelectItens
                          data={data}
                          columns={columns}
                          onSelectionChange={(_) => {}}
                          defaultSelectedKeys={[]}
                          selectable={false}
                          heightParam="600px"
                        />
                        <ConfirmModal
                          isOpen={showConfirmModal}
                          onClose={handleModalClose}
                          texto={"Tem certeza que deseja excluir esse item?"}
                        />
                      </CCol>
                    )}
                  </CRow>
                </CCol>
              </CRow>
            </CForm>
          </CCardBody>
        </CCard>
      </div>
    </div>
  );
};

export default ParametriParcelamentoBBC;
