export type CalculationDetails = {
  detalhesDescontoMaximo: MaxDiscountDetails;
  detalhesNegociacao: detailsNegotiation;
  detalhesOriginal: detailsOriginal;
};

export type MaxDiscountDetails = {
  atualizado: number;
  baseCalculo: Date;
  comissacaoPerm: number;
  correcao: number;
  custa: number;
  dataPagamenos: Date;
  despesas: number;
  honorarios: number;
  iof: number;
  juros: number;
  multa: number;
  notificacao: number;
  percDescontoJuros: number;
  percDescontoMulta: number;
  real: number;
  subtotal: number;
  tarifa: number;
  total: number;
};

export type detailsNegotiation = {
  atualizado: number;
  baseCalculo: Date;
  comissacaoPerm: number;
  correcao: number;
  custa: number;
  dataPagamenos: Date;
  despesas: number;
  honorarios: number;
  iof: number;
  juros: number;
  multa: number;
  notificacao: number;
  perHonorario: number;
  percDescontoComissaoPerm: number | typeof NaN;
  percDescontoJuros: number | typeof NaN;
  percDescontoMulta: number;
  real: number;
  subtotal: number;
  tarifa: number;
  total: number;
};
export type detailsOriginal = {
  atualizado: number;
  baseCalculo: Date;
  comissacaoPerm: number;
  correcao: number;
  custa: number;
  dataPagamenos: Date;
  despesas: number;
  honorarios: number;
  iof: number;
  juros: number;
  multa: number;
  notificacao: number;
  perHonorario: number;
  real: number;
  subtotal: number;
  tarifa: number;
  total: number;
};

export type Installments = {
  DescontoPrincipal: number;
  DescontoPrincipalMaximo: number;
  atraso: number;
  comissacaoPerm: number;
  comissacaoPermMaxDesconto: number;
  contrato_Aberto: number;
  cpfcnpj: string;
  desconto: number;
  detalhesCalculo: CalculationDetails;
  documento: string;
  dt_Inclusao: Date;
  dt_Negociacao: Date | null;
  dt_Status: Date | null;
  dt_Venc_Boleto: Date | null;
  dt_Vencimento: Date | null;
  fase: string | null;
  grupo: string;
  grupoId: number;
  honorario: number;
  id_Acordo: number;
  id_Contrato: number;
  id_Parcela: number;
  juros: number;
  jurosMaxDesconto: number;
  multa: number;
  multaMaxDesconto: number;
  neg_Descricao: string | null;
  nome_Tipo_Parcela: string | null;
  nr_Acordo: number | null;
  nr_Boleto: number | null;
  nr_Parcela: number;
  nr_Plano: number;
  numero_Contrato: string;
  parcelaSelecionada: boolean;
  perHonorario: number;
  perHonorarioMinimo: number;
  percComissaoPerm: number;
  percDesconto: number;
  percJuros: number;
  percMulta: number;
  qtde_Boleto_Emitido: number;
  status: string;
  tipo_Parcela: number;
  tx_Contrato: number;
  tx_Mora: number;
  tx_Multa: number;
  valorNegociado: number;
  vl_Atualizado: number;
  vl_Boleto: number;
  vl_Custa: number;
  vl_Desc_Max: number;
  vl_Original: number;
  vl_Original_atualizado: number;
  vl_Saldo: number;
  vl_Saldo_Atualizado: number;
  vl_contr: number;
};

export type InstallmentsApi = {
  atraso: number;
  dtVencimento: Date;
  dt_Pgto: Date | null | undefined;
  idParcela: number;
  nome_Tipo_Parcela: string | null;
  nrContrato: string;
  nrParcela: string;
  nrPlano: number;
  parcelaSelecionada: boolean;
  vlAtualizado: number;
  vlAtualizadoDescontoMax: number;
  vlComPermanenciaMaxDesc: number;
  vlComPermanenciaNegociado: number;
  vlComPermanenciaOriginal: number;
  vlDespesasMaxDesc: number;
  vlDespesasNegociado: number;
  vlDespesasOriginal: number;
  vlJurosMaxDesc: number;
  vlJurosNegociado: number;
  vlJurosOriginal: number;
  vlMultaMaxDesc: number;
  vlMultaNegociado: number;
  vlMultaOriginal: number;
  vlNotificacaoMaxDesc: number;
  vlNotificacaoNegociado: number;
  vlNotificacaoOriginal: number;
  vlOriginal: number;
  vlSaldo: number;
  vlTarifaMaxDesc: number;
  vlTarifaNegociado: number;
  vlTarifaOriginal: number;
  vlHoNegociado: number;
  vlHoOriginal: number;
  vlHoMaxDesc: number;
};
