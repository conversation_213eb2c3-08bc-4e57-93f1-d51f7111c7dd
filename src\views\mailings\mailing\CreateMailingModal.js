import React, { useState, useEffect } from "react";
import { CLabel, CInput, CSwitch, CForm, CCol, CRow } from "@coreui/react";
import {
  CButton,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CFormGroup,
} from "@coreui/react";
import { getURI } from "src/config/apiConfig";
import { toast } from "react-toastify";
import Select from "react-select";
import LoadingComponent from "src/reusable/Loading";
import StepsMailing from "./StepsMailing";
import { convertCurrencyToFloat } from "src/reusable/helpers";

const CreateMailingModal = ({ isOpen, editMailing, onClose }) => {
  const token = localStorage.getItem("token");

  const [mailing, setMailing] = useState({
    id: null,
    name: "",
    startDate: null,
    crmId: null,
    groupId: null,
    faseId: null,
    status: null,
    dueDayStart: 0,
    dueDayEnd: 0,
    debtValueStart: null,
    debtValueEnd: null,
    active: true,
    excelFile: null,
  });
  const [gruposOptions, setGruposOptions] = useState([]);
  const [crmsOptions, setCrmsOptions] = useState([]);
  const [crmSelected, setCrmSelectd] = useState(null);
  const [grupoSelected, setGrupoSelected] = useState(null);
  const [selectedFrase, setFraseSelected] = useState(null);
  const [fraseOptions, setFraseOptions] = useState([]);
  const [selectedStatus, setStatusSelected] = useState(null);
  const [statusOptions, setStatusOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCompleted, setCompleted] = useState(false);
  const [labelStatus, setLabelStatus] = useState("Ativo");

  const wait = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

  const handleInputNameChange = async (e) => {
    const { name, value } = e.target;
    setMailing((prevMailing) => ({ ...prevMailing, [name]: value }));

    if (
      name === "dueDayStart" ||
      name === "dueDayEnd" ||
      name === "debtValueStart" ||
      name === "debtValueEnd"
    ) {
      setCompleted(true);
      await wait(500);
      setCompleted(false);
    }
  };

  const handleStatusToggle = () => {
    setMailing((prevMailing) => ({
      ...prevMailing,
      active: !prevMailing.active,
    }));
  };

  const handleCrmsChange = async (selectedOptions) => {
    setCompleted(true);
    setGrupoSelected(null);
    setCrmSelectd(selectedOptions);
    setGruposOptions(selectedOptions.grupos);
    setMailing((prevMailing) => ({
      ...prevMailing,
      crmId: selectedOptions.id,
    }));

    await wait(500);
    setCompleted(false);
  };

  const handleGrupoChange = async (selectedOptions) => {
    setCompleted(true);

    const selectedGrupos = selectedOptions?.map((option) => option.id_Grupo);

    setGrupoSelected(selectedOptions);
    setMailing((prevMailing) => ({
      ...prevMailing,
      groupId: selectedGrupos,
    }));

    await wait(500);
    setCompleted(false);
  };

  const handleFraseChange = async (selectedOptions) => {
    setCompleted(true);
    setFraseSelected(selectedOptions);
    setMailing((prevMailing) => ({
      ...prevMailing,
      faseId: selectedOptions.cod_Fase,
    }));

    await wait(500);
    setCompleted(false);
  };

  const handleStatusChange = (selectedOptions) => {
    setStatusSelected(selectedOptions);
    setMailing((prevMailing) => ({
      ...prevMailing,
      status: selectedOptions.id,
    }));
  };

  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };

  const requiredFields = [
    { name: "name", displayName: "Título" },
    { name: "startDate", displayName: "Data inicial" },
    { name: "crmId", displayName: "CRM" },
    { name: "groupId", displayName: "Grupo CRM" },
    //{ name: "faseId", displayName: "Frase" },
    { name: "status", displayName: "Situação" },
  ];

  const handleSubmit = (e) => {
    e.preventDefault();

    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = mailing[field.name];
      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      toast.warn(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    if (!mailing.mailingUsers || (mailing?.mailingUsers ?? []).length === 0) {
      toast.warn(`Por favor preencha os dados de Usuário.`);
      return;
    }

    if (
      !mailing.mailingContracts ||
      (mailing?.mailingContracts ?? []).length === 0
    ) {
      toast.warn(`Por favor preencha os dados de Contrato.`);
      return;
    }

    const newMailing = {
      name: mailing.name,
      startDate: mailing.startDate,
      crmId: mailing.crmId,
      groupId: mailing.groupId.join(","),
      faseId: mailing.faseId ?? null,
      status: mailing.status,
      active: mailing.active,
      dueDayStart: mailing.dueDayStart,
      dueDayEnd: mailing.dueDayEnd,
      mailingUsers: mailing.mailingUsers,
      mailingContracts: mailing.mailingContracts,
      debtValueStart:
        mailing.debtValueStart !== "" ? mailing.debtValueStart : null,
      debtValueEnd: mailing.debtValueEnd !== "" ? mailing.debtValueEnd : null,
      excelFile: mailing.excelFile,
    };

    handleCreateMailing(newMailing);
  };

  const handleCreateMailing = async (newMailing) => {
    try {
      setIsLoading(true);
      const url = `${getURI()}/mailing`;

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(newMailing),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          toast.success("Mailing criado com sucesso");
          editMailing = status.data;
          setMailing(status.data);
          setIsLoading(false);
          onClose();
        } else {
          toast.warning(status.message);
          setIsLoading(false);
        }
      } else {
        console.error("Erro:", response?.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro cadastrando o Mailing:", error);
      setIsLoading(false);
    }
  };

  const handleUpdateMailing = async (mailing) => {
    try {
      setIsLoading(true);
      const url = `${getURI()}/mailing`;

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...mailing,
          id: mailing.id,
        }),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          setIsLoading(false);
          handleClose();
        } else {
          toast.warning(status.message);
        }
      } else {
        console.error("Erro:", response?.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro editando o Mailing:", error);
      setIsLoading(false);
    }
  };

  const handleEdit = (e) => {
    e.preventDefault();
    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = mailing[field.name];

      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      toast.warn(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    const editedMailing = {
      id: mailing.id,
      name: mailing.name,
      startDate: mailing.startDate,
      crmId: mailing.crmId,
      groupId: mailing.groupId,
      faseId: mailing.faseId ?? null,
      dueDayStart: mailing.dueDayStart,
      dueDayEnd: mailing.dueDayEnd,
      status: mailing.status,
      active: mailing.active,
      debtValueStart: mailing.debtValueStart,
      debtValueEnd: mailing.debtValueEnd,
    };

    handleUpdateMailing(editedMailing);
  };

  const handleClose = () => {
    onClose();
  };

  function resetModal() {
    setMailing({
      id: null,
      name: "",
      startDate: new Date().toISOString().split("T")[0],
      crmId: "",
      groupId: "",
      faseId: "",
      status: "Create",
      dueDayStart: 0,
      dueDayEnd: 0,
      active: true,
      debtValueStart: "",
      debtValueEnd: "",
      excelFile: null,
    });
  }

  useEffect(() => {
    if (isOpen && editMailing?.id) {
      setMailing({
        id: editMailing?.id,
        name: editMailing.name,
        crmId: editMailing.crmId,
        groupId: editMailing.groupId,
        faseId: editMailing.faseId,
        startDate: editMailing.startDate,
        status: editMailing.status,
        dueDayStart: editMailing.dueDayStart ?? 1,
        dueDayEnd: editMailing.dueDayEnd ?? 1,
        debtValueStart: editMailing.debtValueStart,
        debtValueEnd: editMailing.debtValueEnd,
        active: editMailing.active,
        mailingUsers: editMailing.mailingUsers,
        mailingContracts: editMailing.mailingContracts,
        excelFile: null,
      });
    } else resetModal();

    if (editMailing && editMailing["crm"]) {
      setMailing((prevMailing) => ({
        ...prevMailing,
        crm: editMailing["crm"],
      }));

      setCrmsOptions(editMailing["crm"]);
      let crm = editMailing["crm"].find((a) => a.id === editMailing?.crmId);
      setCrmSelectd(crm ?? null);
      setGruposOptions(crm?.grupos ?? []);

      if (editMailing?.groupId && crm?.grupos) {
        if (editMailing?.groupId)
          setGrupoSelected(
            crm?.grupos?.filter((a) => a.id_Grupo === editMailing?.groupId) ??
              null
          );
      }
    }

    if (editMailing && editMailing["phase"]) {
      setFraseOptions(editMailing["phase"]);
      if (editMailing?.faseId)
        setFraseSelected(
          editMailing["phase"]?.filter(
            (a) => a.cod_Fase === editMailing?.faseId
          ) ?? []
        );
    }

    if (editMailing && editMailing["statusList"]) {
      setStatusOptions(editMailing["statusList"]);
      setStatusSelected(
        editMailing["statusList"]?.filter(
          (a) => a.id === (editMailing?.status ?? "Create")
        ) ?? []
      );
    }
  }, [isOpen, editMailing]);

  useEffect(() => {
    setLabelStatus(mailing?.active === true ? "Ativo" : "Inativo");
  }, [mailing?.active]);

  const handleCompleteSteps = (users, contracts) => {
    if (!users || users.length === 0 || !contracts || contracts.length === 0) {
      toast.warn("Não há usuários ou contratos para associar.");
      return;
    }

    const updatedContracts = contracts.map((contract, index) => ({
      ...contract,
      userId: users[index % users.length].userId,
    }));

    setMailing((prevMailing) => ({
      ...prevMailing,
      mailingUsers: users.map((user) => ({
        ...{},
        userId: user.userId,
        active: true,
      })),
      mailingContracts: updatedContracts.map((contract) => ({
        ...{},
        contractId: contract.contractId,
        userId: contract.userId,
        status: "Opened",
        active: true,
      })),
    }));

    setCompleted(true);
  };

  useEffect(() => {
    if (mailing.groupId && mailing.groupId !== "" && gruposOptions.length > 0) {
      // Converter a string de IDs em array de números
      let idsArray = [];
      idsArray = mailing.groupId;
      if (typeof mailing.groupId === "string") {
        idsArray = mailing.groupId
          .split(",")
          .map((id) => parseInt(id.trim()))
          .filter((id) => !isNaN(id));
      }

      // Encontrar os objetos correspondentes nos gruposOptions
      const selectedOptions = gruposOptions.filter((grupo) =>
        idsArray.includes(parseInt(grupo.id_Grupo))
      );

      // Atualizar o estado
      setGrupoSelected(selectedOptions.length > 0 ? selectedOptions : null);
    }
    return () => {};
  }, [mailing]);

  const handleUploadFile = async (event) => {
    const file = event.target.files;
    setMailing((prevMailing) => ({
      ...prevMailing,
      excelFile: file[0],
    }));
  };

  return (
    <>
      <CModal
        show={isOpen}
        onClose={handleClose}
        closeOnBackdrop={false}
        size="lg"
      >
        <CModalHeader closeButton>
          <CModalTitle>
            {mailing?.id ?? false ? "Editar Mailing" : "Adicionar Mailing"}
          </CModalTitle>
        </CModalHeader>
        <CModalBody>
          {isLoading ? (
            <div>
              <LoadingComponent />
            </div>
          ) : (
            <CForm>
              <CFormGroup>
                <CRow>
                  <CCol>
                    <CLabel htmlFor="name">Título</CLabel>
                    <CInput
                      id="name"
                      name="name"
                      placeholder="Informe o Título"
                      autoComplete="off"
                      required
                      value={mailing.name ?? ""}
                      onChange={handleInputNameChange}
                      maxLength="50"
                    />
                  </CCol>
                  <CCol>
                    <CLabel>Data Inicial:</CLabel>
                    <CInput
                      type="date"
                      name="startDate"
                      value={
                        mailing?.startDate
                          ? mailing.startDate.split("T")[0]
                          : new Date().toISOString().split("T")[0]
                      }
                      onChange={handleInputNameChange}
                    />
                  </CCol>
                </CRow>
              </CFormGroup>
              <CFormGroup>
                <CRow>
                  <CCol>
                    <CLabel>Dias atraso Inicial</CLabel>
                    <CInput
                      id="dueDayStart"
                      name="dueDayStart"
                      type="number"
                      min={1}
                      max={365}
                      value={mailing.dueDayStart ?? 1}
                      onChange={handleInputNameChange}
                      disabled={mailing?.id}
                    />
                  </CCol>
                  <CCol>
                    <CLabel>Dias atraso Final</CLabel>
                    <CInput
                      id="dueDayEnd"
                      name="dueDayEnd"
                      type="number"
                      min={1}
                      max={365}
                      value={mailing.dueDayEnd ?? 1}
                      onChange={handleInputNameChange}
                      disabled={mailing?.id}
                    />
                  </CCol>
                </CRow>
              </CFormGroup>
              <CFormGroup>
                <CRow>
                  <CCol>
                    <CLabel>Valor Dívida Inicial</CLabel>
                    <CInput
                      id="debtValueStart"
                      name="debtValueStart"
                      type="number"
                      value={mailing.debtValueStart ?? ""}
                      onChange={handleInputNameChange}
                      disabled={mailing?.id}
                    />
                  </CCol>

                  <CCol>
                    <CLabel>Valor Dívida Final</CLabel>
                    <CInput
                      id="debtValueEnd"
                      name="debtValueEnd"
                      type="number"
                      value={mailing.debtValueEnd ?? ""}
                      onChange={handleInputNameChange}
                      disabled={mailing?.id}
                    />
                  </CCol>
                  <CCol>
                    <CLabel>Gerar por arquivo</CLabel>
                    <CInput
                      type="file"
                      name="excelFile"
                      onChange={handleUploadFile}
                    />
                  </CCol>
                </CRow>
              </CFormGroup>
              <CFormGroup>
                <CRow>
                  <CCol>
                    <CLabel>CRM</CLabel>
                    <Select
                      options={crmsOptions}
                      value={crmSelected ?? null}
                      onChange={handleCrmsChange}
                      getOptionValue={(option) => option.id}
                      getOptionLabel={(option) => option.datacobName}
                      isDisabled={mailing?.id}
                    />
                  </CCol>
                  <CCol>
                    <CLabel>Grupos CRM</CLabel>
                    <Select
                      isMulti
                      options={gruposOptions}
                      value={grupoSelected ?? null}
                      onChange={handleGrupoChange}
                      getOptionValue={(option) => option.id_Grupo}
                      getOptionLabel={(option) => option.descricao}
                      isDisabled={mailing?.id}
                    />
                  </CCol>
                </CRow>
              </CFormGroup>
              <CFormGroup>
                <CRow>
                  <CCol>
                    <CLabel>Selecione a Fase</CLabel>
                    <Select
                      placeholder="Fase do contrato"
                      value={selectedFrase ?? null}
                      onChange={handleFraseChange}
                      options={fraseOptions}
                      getOptionValue={(option) => option.cod_Fase}
                      getOptionLabel={(option) => option.descricao}
                      isDisabled={mailing?.id}
                    />
                  </CCol>
                  <CCol>
                    <CLabel>Situação</CLabel>
                    <Select
                      placeholder="Situação do mailing"
                      value={selectedStatus ?? null}
                      onChange={handleStatusChange}
                      options={statusOptions}
                      getOptionValue={(option) => option.id}
                      getOptionLabel={(option) => option.descricao}
                    />
                  </CCol>
                </CRow>
              </CFormGroup>
              <CFormGroup>
                <CLabel htmlFor="active" style={{ paddingRight: "20px" }}>
                  Status
                </CLabel>
                <CSwitch
                  id="active"
                  name="active"
                  color="success"
                  checked={mailing.active}
                  onChange={handleStatusToggle}
                  shape="pill"
                  size="sm"
                />
                <CLabel style={{ paddingLeft: "20px" }}> {labelStatus} </CLabel>
              </CFormGroup>
            </CForm>
          )}
          {!isCompleted &&
            mailing.groupId &&
            mailing.crmId &&
            (mailing.excelFile !== null ||
              (mailing.dueDayEnd > 0 && mailing.dueDayStart >= 0)) && (
              <StepsMailing
                active={!isCompleted}
                mailing={mailing}
                onComplete={handleCompleteSteps}
              />
            )}
        </CModalBody>
        <CModalFooter>
          {!mailing?.id && isCompleted && (
            <CButton color="info" onClick={handleSubmit} disabled={isLoading}>
              <i className="cil-plus"></i> Adicionar Mailing
            </CButton>
          )}
          {mailing?.id && (
            <>
              <CButton color="info" onClick={handleEdit} disabled={isLoading}>
                Salvar
              </CButton>
            </>
          )}
        </CModalFooter>
      </CModal>
    </>
  );
};

export default CreateMailingModal;
