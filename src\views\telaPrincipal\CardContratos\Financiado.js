import React, { useState, useEffect } from "react";
import { CRow, <PERSON>ol, <PERSON><PERSON>abel, CCardBody } from "@coreui/react";
import { formatDate, formatDocument } from "src/reusable/helpers";
import { useMyContext } from "src/reusable/DataContext";

const Financiado = () => {
  const { data } = useMyContext();

  const [dadosFinanciado, setDadosFinanciados] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  useEffect(() => {
    if (data) {
      setDadosFinanciados(data);
    }
  }, [data]);

  const renderSexo = (key) => {
    switch (key) {
      case "F":
        return <>Feminino</>;
      case "M":
        return <>Masculino</>;
      case "I":
        return <>Outros</>;
      default:
        return <>Outros</>;
    }
  };

  const renderEstadoCivil = (key) => {
    switch (key) {
      case "S":
        return <>Solteiro</>;
      case "C":
        return <>Casado</>;
      default:
        return <>Outros</>;
    }
  };

  const renderPessoaFisica = (key) => {
    switch (key) {
      case "J":
        return <>Jurídica</>;
      case "F":
        return <>Física</>;
      default:
        return <>Outros</>;
    }
  };

  const renderTipo = (key) => {
    switch (key) {
      case 1:
        return <>Financiado</>;
      case 2:
        return <>Avalista</>;
      case 3:
        return <>Terceiro</>;
      case 4:
        return <>Sócio</>;
      case 5:
        return <>Cônjuge</>;
      default:
        return <>Outros</>;
    }
  };

  return (
    <CCardBody>
      <CRow>
        <CCol md="3">
          <CLabel className="my-0">Nome</CLabel>
          <div>
            <strong> {dadosFinanciado ? dadosFinanciado.nome : "---"} </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel className="my-0">Data de nascimento</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado
                ? formatDate(dadosFinanciado.dt_Nascimento)
                : "---"}{" "}
            </strong>
          </div>
        </CCol>
        <CCol md="3">
          <CLabel className="my-0">Data Último Enriquecimento</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado
                ? formatDate(dadosFinanciado.data_Enriquecimento)
                : "---"}{" "}
            </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel className="my-0">Sexo</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado ? renderSexo(dadosFinanciado.sexo) : "---"}{" "}
            </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel className="my-0">Estado Civil</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado
                ? renderEstadoCivil(dadosFinanciado.est_Civil)
                : "---"}{" "}
            </strong>
          </div>
        </CCol>
      </CRow>
      <CRow className="mt-2">
        <CCol md="3">
          <CLabel className="my-0">Grupo</CLabel>
          <div>
            <strong> {dadosFinanciado ? dadosFinanciado?.grupo : "---"} </strong>
          </div>
        </CCol>
        <CCol md="5">
          <CLabel className="my-0">Cliente</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado ? dadosFinanciado.cliente : "---"}{" "}
            </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel className="my-0">Tipo</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado
                ? renderTipo(dadosFinanciado.tipo_Financiado)
                : "---"}{" "}
            </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel className="my-0">Score</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado ? dadosFinanciado.score_Serasa : "---"}{" "}
            </strong>
          </div>
        </CCol>
      </CRow>
      <CRow className="mt-2">
        <CCol md="3">
          <CLabel className="my-0">RG</CLabel>
          <div>
            <strong> {dadosFinanciado ? dadosFinanciado.rg : "---"} </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel className="my-0">Data de Emissão</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado
                ? formatDate(dadosFinanciado.dt_Emiss_Rg)
                : "---"}{" "}
            </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel className="my-0">Emissor</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado ? dadosFinanciado.orgao_Emiss_Rg : "---"}{" "}
            </strong>
          </div>
        </CCol>
        <CCol md="1">
          <CLabel className="my-0">UF</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado ? dadosFinanciado.uf_Emiss_Rg : "---"}{" "}
            </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel className="my-0">CPF/CNPJ</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado
                ? formatDocument(dadosFinanciado.cpfCnpj)
                : "---"}{" "}
            </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel className="my-0">Pessoa</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado
                ? renderPessoaFisica(dadosFinanciado.tipo_Pessoa)
                : "---"}{" "}
            </strong>
          </div>
        </CCol>
      </CRow>
      <CRow className="mt-2">
        <CCol md="3">
          <CLabel className="my-0">Conjuge</CLabel>
          <div>
            <strong>
              {" "}
              {dadosFinanciado ? dadosFinanciado.conjugue : "---"}{" "}
            </strong>
          </div>
        </CCol>
        <CCol md="2">
          <CLabel className="my-0">Mãe</CLabel>
          <div>
            <strong> {dadosFinanciado ? dadosFinanciado.mae : "---"} </strong>
          </div>
        </CCol>
        <CCol>
          <CLabel className="my-0">Pai</CLabel>
          <div>
            <strong> {dadosFinanciado ? dadosFinanciado.pai : "---"} </strong>
          </div>
        </CCol>
        <CCol />
      </CRow>
    </CCardBody>
  );
};

export default Financiado;
