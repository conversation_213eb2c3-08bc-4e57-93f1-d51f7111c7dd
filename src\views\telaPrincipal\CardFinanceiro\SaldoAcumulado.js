import React, { useState, useEffect } from "react";
import { CDataTable, CButton } from "@coreui/react";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";

import { formatDate, formatThousands } from "src/reusable/helpers";
import LoadingComponent from "src/reusable/Loading";

import ValoresPagosModal from "./SaldoAcumuladoModal/ValoresPagosModal";
import SaldoDevedorModal from "./SaldoAcumuladoModal/SaldoDevedorModal";
import AtrasosModal from "./SaldoAcumuladoModal/AtrasosModal";
import ValoresDevolverModal from "./ValoresDevolverModal/ValoresDevolverModal";
import TableSelectItens from "src/reusable/TableSelectItens";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import { useAuth } from "src/auth/AuthContext";

const SaldoAcumulado = ({ selected }) => {
  const { checkPermission,inforPermissions } = useAuth();
  const permissaoTelaPrincipalSaldoAcumuladoValoresPagos = {
    modulo: "Tela Principal",
    submodulo: "Saldo Acumulado - Valores Pagos",
  }

  const permissaoTelaPrincipalSaldoAcumuladoSaldoDevedor = {
    modulo: "Tela Principal",
    submodulo: "Saldo Acumulado - Saldo Devedor",
  }
  const permissaoTelaPrincipalSaldoAcumuladoAtrasos = {
    modulo: "Tela Principal",
    submodulo: "Saldo Acumulado - Atrasos",
  }

  const permissaoTelaPrincipalBens = {
    modulo: "Tela Principal",
    submodulo: "Bens",
  }

  const permissaoTelaPrincipalBensVerEvolucao = {
    modulo: "Tela Principal",
    submodulo: "Bens - Ver Evolução",
  }

  const permissaoTelaPrincipalBensVerDetalhes = {
    modulo: "Tela Principal",
    submodulo: "Bens - Ver Detalhes",
  }

  const permissaoTelaPrincipalBensGarantias = {
    modulo: "Tela Principal",
    submodulo: "Bens - Garantia",
  }

  const [isLoading, setIsLoading] = useState(false);
  const [tableData, setTableData] = useState(null);
  // const [saldoAc, setSaldAc] = useState([]);
  const [valoresPagos, setValoresPagos] = useState(null);
  const [atrasos, setAtrasos] = useState(null);
  const [saldoDevedor, setSaldoDevedor] = useState(null);

  // const [expandedRows, setExpandedRows] = useState([]);
  const [showSaldoDevedorModal, setShowSaldoDevedorModal] = useState(false);
  const [showValoresPagosModal, setShowValoresPagosModal] = useState(false);
  const [showValoresDevolverModal, setShowValoresDevolverModal] =
    useState(false);
  const [showAtrasosModal, setShowAtrasosModal] = useState(false);
  // const [showResumoModal, setShowResumoModal] = useState(false);

  // const [pressedRow, setPressedRow] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const cnscCotas =
    localStorage.getItem("cnscCotas") &&
      localStorage.getItem("cnscCotas") !== "undefined"
      ? JSON.parse(localStorage.getItem("cnscCotas")) ?? []
      : [];

  const newcon = localStorage.getItem("newcon")
    ? JSON.parse(localStorage.getItem("newcon"))
    : null;

  const fields = [
    {
      key: "numberCurrentAssembly",
      label: "Assembl. Atual",
      formatterByObject: (item) => item + " " + formatDate(item.dtAssembleia),
    },
    { key: "amountPaid", label: "Valores pagos" },
    { key: "debtBalance", label: "Saldo devedor" },
    { key: "delays", label: "Atrasos" },
    { key: "parcelDifference", label: "Dif. Parcela" },
    { key: "parcelValue", label: "Vl. da parcela" },
  ];

  const handleRowClick = (nr_assem) => {
    // setPressedRow(nr_assem);
    // setSelectedRow(nr_assem);
  };

  const handleModalValoresPagos = (idCota) => {
    setValoresPagos(idCota);
    setShowValoresPagosModal(true);
  };

  const handleModalSaldoDevedor = (idCota) => {
    setSaldoDevedor(idCota);
    setShowSaldoDevedorModal(true);
  };

  const handleModalAtrasos = (idCota) => {
    setAtrasos(idCota);
    setShowAtrasosModal(true);
  };

  const handleRowDoubleClick = (negociacaoData) => {
    // setDadosNegociacao(negociacaoData);
    // setShowNegociacaoModal(true);
  };

  const rowClassName = (item) => {
    return item.numberCurrentAssembly === selectedRow ? "selected-message" : "";
  };

  const getSaldoAcumulado = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    if (financiadoData) {
      if (selected === true && newcon) {
        const newconTable = [
          {
            numberCurrentAssembly: newcon.noAssembleia,
            amountPaid: newcon.vlAmortizado,
            debtBalance: newcon.vlSdDevedor,
            delays: newcon.vlPcEmAtraso,
            parcelDifference: newcon.vlDiferenca,
            parcelValue: newcon.vlContribuicao,
            dtAssembleia: newcon.dtAssembleia,
          },
        ];

        setTableData(newconTable);
      }
    }
  }, [selected]);

  return (
    <>
      {isLoading ? (
        <div className="mt-2">
          <LoadingComponent />
        </div>
      ) : (
        <>
          {tableData ? (
            <CDataTable
              items={tableData}
              fields={fields}
              scopedSlots={{
                numberCurrentAssembly: (item) => (
                  <td
                    className={rowClassName(item)}
                    onClick={() => handleRowClick(item.numberCurrentAssembly)}
                    onDoubleClick={() => handleRowDoubleClick(item)}
                  >
                    {item.numberCurrentAssembly +
                      " - " +
                      formatDate(item.dtAssembleia)}
                  </td>
                ),
                amountPaid: (item) => (
                  <td
                    className={rowClassName(item)}
                    onClick={() => handleRowClick(item.numberCurrentAssembly)}
                    onDoubleClick={() => handleRowDoubleClick(item)}
                  >
                    {item.amountPaid
                      ? formatThousands(item.amountPaid)
                      : "0,00"}
                    {item.amountPaid && item.amountPaid > 0 ? (
                      <CButton
                        variant="outline"
                        color="info"
                        className="ml-1 px-1 py-0"
                        onClick={() =>handleModalValoresPagos(cnscCotas.idCota)}
                        title={inforPermissions(permissaoTelaPrincipalSaldoAcumuladoValoresPagos).view}
                        disabled={!checkPermission(permissaoTelaPrincipalSaldoAcumuladoValoresPagos.modulo,"View",permissaoTelaPrincipalSaldoAcumuladoValoresPagos.submodulo)}
                      >
                        <i className="cil-magnifying-glass" />
                      </CButton>
                    ) : (
                      ""
                    )}
                  </td>
                ),
                debtBalance: (item) => (
                  <td
                    className={rowClassName(item)}
                    onClick={() => handleRowClick(item.numberCurrentAssembly)}
                    onDoubleClick={() => handleRowDoubleClick(item)}
                  >
                    {item.debtBalance
                      ? formatThousands(item.debtBalance)
                      : "0,00"}
                    {item.debtBalance && item.debtBalance > 0 ? (
                      <CButton
                        variant="outline"
                        color="info"
                        className="ml-1 px-1 py-0"
                        title={inforPermissions(permissaoTelaPrincipalSaldoAcumuladoSaldoDevedor).view}
                        disabled={!checkPermission(permissaoTelaPrincipalSaldoAcumuladoValoresPagos.modulo,"View",permissaoTelaPrincipalSaldoAcumuladoValoresPagos.submodulo)}
                        onClick={() =>handleModalSaldoDevedor(cnscCotas.idCota)}
                      >
                        <i className="cil-magnifying-glass" />
                      </CButton>
                    ) : (
                      ""
                    )}
                  </td>
                ),
                delays: (item) => (
                  <td
                    className={rowClassName(item)}
                    onClick={() => handleRowClick(item.numberCurrentAssembly)}
                    onDoubleClick={() => handleRowDoubleClick(item)}
                  >
                    {item.delays ? formatThousands(item.delays) : "0,00"}
                    {item.delays && item.delays > 0 ? (
                      <CButton
                        variant="outline"
                        color="info"
                        className="ml-1 px-1 py-0"
                        onClick={() => handleModalAtrasos(cnscCotas.idCota)}
                        title={inforPermissions(permissaoTelaPrincipalSaldoAcumuladoAtrasos).view}
                        disabled={!checkPermission(permissaoTelaPrincipalSaldoAcumuladoAtrasos.modulo,"View",permissaoTelaPrincipalSaldoAcumuladoAtrasos.submodulo)}
                      >
                        <i className="cil-magnifying-glass" />
                      </CButton>
                    ) : (
                      ""
                    )}
                  </td>
                ),
                parcelDifference: (item) => (
                  <td
                    className={rowClassName(item)}
                    onClick={() => handleRowClick(item.numberCurrentAssembly)}
                    onDoubleClick={() => handleRowDoubleClick(item)}
                  >
                    {item.parcelDifference
                      ? formatThousands(item.parcelDifference)
                      : "0,00"}
                  </td>
                ),
                parcelValue: (item) => (
                  <td
                    className={rowClassName(item)}
                    onClick={() => handleRowClick(item.numberCurrentAssembly)}
                    onDoubleClick={() => handleRowDoubleClick(item)}
                  >
                    {item.parcelValue
                      ? formatThousands(item.parcelValue)
                      : "0,00"}
                  </td>
                ),
              }}
            />
          ) : (
            <NaoHaDadosTables />
          )}
          <SaldoDevedorModal
            isOpen={showSaldoDevedorModal}
            onClose={() => setShowSaldoDevedorModal(false)}
            idCota={saldoDevedor}
          />
          <ValoresPagosModal
            isOpen={showValoresPagosModal}
            onClose={() => setShowValoresPagosModal(false)}
            idCota={valoresPagos}
          />
          <AtrasosModal
            isOpen={showAtrasosModal}
            onClose={() => setShowAtrasosModal(false)}
            idCota={atrasos}
          />
          <ValoresDevolverModal
            isOpen={showValoresDevolverModal}
            onClose={() => setShowValoresDevolverModal(false)}
            idCota={atrasos}
          />
        </>
      )}
    </>
  );
};

export default SaldoAcumulado;
