import React, { useState } from "react";

import "react-datepicker/dist/react-datepicker.css";

import {
  CButton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalTitle,
  CCol,
  CRow,
  CLabel,
  CModalFooter,
} from "@coreui/react";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import { ApiResponse } from "src/types/common";
import { SearchContract } from "src/types/commonBBC";


const PostData = async (payload, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), payload, true, true, id);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(getURI(endpoint), null, true, true, payload);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const FormCancelarNegociacaoModal = ({ isOpen, onClose, idAcordo, id,contratoSelecionado }) => {
  let financiadoData = localStorage.getItem("financiadoData") ? JSON.parse(localStorage.getItem("financiadoData")) : "";
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");

  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");

  const [payload, setPayload] = useState({
    idAcordo: idAcordo.toString(),
    motivo: "",
    motivoCanc: ""
  });

  const initialErrors = {
    idAcordo: "",
    motivoCanc: ""
  };

  const [errors, setErrors] = useState(initialErrors);

  const RealiarCancelamento = async () => {
    let ret = false;
    setTitleAvisoLoading("Enviando Cancelamento Safra")
    setMsgAvisoLoading(`Enviando...`)
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(payload, "cyberSafraCancelarAcordo")
      .then((data: ApiResponse<unknown>) => {
        if (data !== null)
          if (data.message.includes("cancelado")) {
            setTitleAvisoLoading("Atualizando Motivo Tela Única");
            setMsgAvisoLoading("...");

            PostData(payload.motivoCanc, "negociacaoSafraAcordoAtualizarMotivo", id)
              .then((data: ApiResponse<unknown>) => {
                if (data.success) {
                  setTitleAvisoLoading("Atualizando Status Tela Única");
                  PostData("Cancelado", "negociacaoSafraAcordoAtualizarStatus", id)
                    .then((data) => {
                      buscarContratoTelaUnica();
                    })
                    .finally(() => {
                      setTitleAvisoLoading("Atualizado Status Tela Única");
                    });
                }
              })

          } else {
            setMsgAvisoLoading("Não Cancelado Tente Novamente");
            setTitleAvisoLoading("Status");
            setTimeout(() => {
              setLoading(false);
              setLoadingAction("empty");
              onClose();
            }, 3000);
          }

      }).catch((err) => {
        setTitleAvisoLoading(`Erro na chamada das APIS`);
        setMsgAvisoLoading(`Erro na chamada API de Cyber Simulação de Acordos, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`);
      })

    return ret;
  }

  const handleCancelar = () => {
    setErrors(initialErrors);
    const newErrors = { ...initialErrors };
    handlePayloadChange("idAcordo", idAcordo.toString())
    if (payload.motivoCanc === undefined || payload.motivoCanc === null || payload.motivoCanc === "")
      newErrors.motivoCanc = "Motivo é Obrigatório";

    if (payload.idAcordo === undefined || payload.idAcordo === null || payload.idAcordo === "")
      newErrors.motivoCanc = "Id do Acordo não informado";

    setErrors(newErrors);
    if (!Object.values(newErrors).every(error => error === ""))
      return;

    RealiarCancelamento();
  };

  const handlePayloadChange = (field, value) => {

    setErrors({ ...errors, [field]: "" });
    setPayload((prevPayload) => ({
      ...prevPayload,
      [field]: value
    }));
  };

  const limparAvisos = () => {
    setTimeout(() => {
      setLoading(false);
      setLoadingAction("empty");
      setMsgAvisoLoading("");
      setTitleAvisoLoading("");
      onClose();
    }, 3000);
  }

  const AtualizarStatusDataCob = async (status) => {
    if (contratoSelecionado != null) {
      setTitleAvisoLoading("Atualizando Status Acordo")
      setMsgAvisoLoading(`Enviando...`)
      setLoading(true);
      setLoadingAction("VarifyParam");
      await PostData(status, "negociacaoSafraAtualizarAtualizarStatusDataCob", contratoSelecionado.id)
        .then((data: ApiResponse<unknown>) => {
          if (data.success) {
            setTitleAvisoLoading("Status Atualizando do Acordo")
            setMsgAvisoLoading("")
          } else {
            setTitleAvisoLoading("Status Não Atualizando do Acordo")
            setMsgAvisoLoading(data.message)
          }
        })
        .catch((err) => {
          setTitleAvisoLoading("Erro ao Tentar atualizar Status do Acordo")
          setMsgAvisoLoading("")
        }).finally(() => {
          limparAvisos();
        });

    }
  }

  const buscarContratoTelaUnica = () => {
    if (contratoSelecionado != null) {
      setLoading(true);
      setLoadingAction("VarifyParam");
      GetData(contratoSelecionado.contract_id, "negociacaoSafraContratoBuscarPorId")
        .then((data: null | SearchContract) => {
          if (data !== undefined && data !== null) {
            EnviarGvcManager(data.id_Contrato);
          }
        });
    }
  }

  const EnviarGvcManager = (id_Contrato) => {
    if (contratoSelecionado != null) {
      setLoading(true);
      setLoadingAction("VarifyParam");

      setTitleAvisoLoading("Enviando solicitação ao GVC Manager")
      setMsgAvisoLoading("Enviando Acordo...")

      const dateString = contratoSelecionado.dtParcelaEntrada;
      const dateObj = new Date(dateString);

      const formattedDate = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')}`;

      PostData(
        {
          type_action: 1,
          id_agreements: id_Contrato,
          nr_installment: null,
          id_agreements_tu: contratoSelecionado.id,
          prazoSimulado: contratoSelecionado.qtParcelasAtivas,
          idAcordoDataCob: contratoSelecionado.idAcordoDataCob,
          valorFinanciadoSimulado: contratoSelecionado.valorFinanciadoSimulado,
          valorHonorario: contratoSelecionado.valorHonorarios,
          contratoDataCob: contratoSelecionado.contratoDataCob,
          dtParcelaEntrada: formattedDate,
          valorEntrada: contratoSelecionado.valorEntrada,
          cpfCnpj: financiadoData.cpfCnpj
        }, "gvcmanagerCyberSafraAcoesContrato")
        .then((data: ApiResponse<unknown>) => {
          if (data.success) {
            setTitleAvisoLoading("Acordo Enviado ao GVC Manager")
            setMsgAvisoLoading("")
            AtualizarStatusDataCob(`Enviado GVC Manager Comando de cancelamento de Acordo`);
          } else {
            AtualizarStatusDataCob("Falha")
            limparAvisos();
          }
        })
        .catch((err) => {
          setTitleAvisoLoading("Falha ao Enviar Acordo ao GVC Manager")
          setMsgAvisoLoading("")
          limparAvisos();
        })

    }
  }

  return (
    <CModal
      className="custom-modal"
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
    >
      <CModalHeader closeButton>
        <CModalTitle>Negociação</CModalTitle>
      </CModalHeader>
      <CModalBody>
        {loading && loadingAction === "VarifyParam" ?
          (<CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />) : ""
        }
        {!loading ? <CRow className={"p-2"}>
          <CCol >
            <CRow>
              <CCol>
                <CLabel>Motivo</CLabel> <br />
                <textarea
                  style={{
                    width: "100%",
                    minHeight: "150px",
                    borderRadius: " 5px",
                  }}
                  placeholder=" Insira aqui suas observações."
                  value={payload.motivo}
                  onChange={(e) => { handlePayloadChange('motivoCanc', e.target.value); }}
                  className={errors.motivoCanc ? 'border-danger rounded' : ''}
                />
                {errors.motivoCanc && <div className="text-danger">{errors.motivoCanc}</div>}
              </CCol>
            </CRow>
          </CCol>
        </CRow> : ""}

      </CModalBody>

      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={onClose}>
          Cancelar
        </CButton>
        <CButton
          color="info"
          onClick={handleCancelar}
          disabled={loading}
        >
          Cancelar Acordo
        </CButton>

      </CModalFooter>

    </CModal>);
}

export default FormCancelarNegociacaoModal;
