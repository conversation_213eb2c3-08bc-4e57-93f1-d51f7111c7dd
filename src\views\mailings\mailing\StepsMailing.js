import React, { useState, useEffect, useCallback } from "react";
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CInputCheckbox,
  CInput,
  CLabel,
  CCol,
  CRow,
  CForm,
  CFormGroup,
  CDataTable,
} from "@coreui/react";
import { getURI } from "src/config/apiConfig";
import { GET_DATA, POST_FORMDATA } from "src/api";
import { toast } from "react-toastify";
import TableSelectItens from "src/reusable/TableSelectItens";
import { updateConnection } from "src/config/updateConnection";

const StepsMailing = ({ active, mailing, onComplete }) => {
  const [users, setUsers] = useState([]);
  const [contracts, setContracts] = useState([]);
  const [step, setStep] = useState(1);
  const [dataFetched, setDataFetched] = useState(false);
  const [numberOfContract, setNumberOfContract] = useState(3);

  const token = localStorage.getItem("token");
  const userProfile = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : "";

  const handleNextStep = () => {
    if (step === 1 && users.length === 0) {
      toast.warn(
        "Por favor, selecione pelo menos um usuário antes de continuar."
      );
      return;
    } else if (step === 1) {
      let countContract =
        numberOfContract * (users.filter((a) => a.selected)?.length ?? 1);
      setContracts((prevContracts) => prevContracts.slice(0, countContract));
    }
    setStep(step + 1);
  };

  const handlePreviousStep = () => {
    setStep(step - 1);
  };

  const getUsers = useCallback(async (crm, mailing) => {
    const payload = {
      ActiveConnection: crm.datacobNumber,
      id_GrupoCrm: mailing.groupId,
    };
    try {
      const data = await GET_DATA(getURI("getTelaUnicaUsers"), payload, true);
      if (data) {
        if (mailing?.mailingUsers && mailing?.mailingUsers?.length > 0) {
          setUsers(
            mailing?.mailingUsers.map((x) => ({
              id: x.id,
              mailingId: x.mailingId,
              userId: x.userId,
              name: data?.find((a) => a.id === x.userId)?.name,
              email: data?.find((a) => a.id === x.userId)?.email,
              selected: true,
              active: x.active,
            }))
          );
        } else {
          setUsers(
            data.map((x) => ({
              userId: x.id,
              name: x.name,
              email: x.email,
              selected: false,
            }))
          );
        }
      }
    } catch (err) {
      console.error(err);
    }
  }, []);
  const PostFormData = async (formData, endpoint = "", id = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_FORMDATA(
          getURI(endpoint),
          formData,
          true,
          true,
          id
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const getContracts = useCallback(async (crm, mailing) => {
    try {
      let data = null;
      console.log("mailing", mailing);
      if (mailing?.excelFile) {
        const formData = new FormData();

        formData.append("file", mailing?.excelFile);
        formData.append("crm", crm.datacobNumber);
        formData.append("groupId", mailing.groupId);
        formData.append("faseId", mailing.faseId);
        formData.append("dayDueStart", mailing.dueDayStart);
        formData.append("dayDueEnd", mailing.dueDayEnd);
        formData.append("debtValueStart", mailing.debtValueStart);
        formData.append("debtValueEnd", mailing.debtValueEnd);
        data = (await PostFormData(formData, "getBuscaDadosMailingsFromFile"))
          .data;
      } else {
        const payload = {
          crm: crm.datacobNumber,
          groupId: mailing.groupId,
          faseId: mailing.faseId,
          dayDueStart: mailing.dueDayStart ?? 1,
          dayDueEnd: mailing.dueDayEnd ?? 1,
          debtValueStart: mailing.debtValueStart ?? 0,
          debtValueEnd: mailing.debtValueEnd ?? 0,
        };
        data = await GET_DATA(getURI("getBuscaDadosMailings"), payload, true);
      }
      if (data?.length) {
        if (
          mailing?.mailingContracts &&
          mailing?.mailingContracts?.length > 0
        ) {
          setContracts(
            mailing?.mailingContracts.map((x) => ({
              id: x.id,
              mailingId: x.mailingId,
              userId: x.userId,
              contractId: x.contractId,
              name: data?.find((a) => a.id_Contrato === x.contractId)?.nome,
              details: data?.find((a) => a.id_Contrato === x.contractId)
                ?.numero_Contrato,
              selected: true,
              active: true,
            }))
          );
        } else {
          setContracts(
            data?.map((x) => ({
              contractId: x.id_Contrato,
              name: x.nome,
              details: `${x.numero_Contrato} | ${x.status}`,
              selected: false,
              active: true,
            }))
          );
        }
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  const fetchData = useCallback(async () => {
    try {
      const crmDados = mailing.crm;
      const crm = crmDados.find((a) => a.id === mailing.crmId);

      if (mailing?.mailingUsers?.length > 0)
        setNumberOfContract(
          mailing.mailingContracts.length / mailing?.mailingUsers?.length
        );
      updateConnection({ crm: crm, userProfile }).then(async () => {
        await getUsers(crm, mailing);
        await getContracts(crm, mailing);
      });
    } catch (error) {
      console.error("Erro ao buscar dados:", error);
    }
  }, [mailing, userProfile, getUsers, getContracts]);

  useEffect(() => {
    if (active && !dataFetched) {
      setDataFetched(true);
      fetchData();
    }
  }, [active, dataFetched, fetchData]);

  const handleUpdate = async (route, body) => {
    try {
      const url = `${getURI()}/${route}`;

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...body,
          id: body.id,
        }),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          toast.info(status.message);
        }
      } else {
        console.error("Erro:", status.message);
      }
    } catch (error) {
      console.error("Erro editando o Mailing:", error);
    }
  };

  const handleUserInativar = (item) => {
    item.active = !item.active;
    setUsers((prevUsers) =>
      prevUsers.map((user) =>
        user.userId === item.userId ? { ...user, active: item.active } : user
      )
    );
    handleUpdate("mailingUser", item);
  };

  const handleContractInativar = (item) => {
    item.active = !item.active;
    setContracts((prevContracts) =>
      prevContracts.map((contract) =>
        contract.contractId === item.contractId
          ? { ...contract, active: item.active }
          : contract
      )
    );
    handleUpdate("mailingContract", item);
  };

  const handleUserEdit = (item) => {
    if ((item?.mailingId ?? 0) !== 0) {
      handleUserInativar(item);
    } else {
      setUsers((prevUsers) =>
        prevUsers.map((user) =>
          user.userId === item.userId
            ? { ...user, selected: !user.selected }
            : user
        )
      );
    }
  };
  const handleViewUserSelected = (item) => {
    return (
      <>
        {item?.mailingId && (
          <CButton
            color={item?.active ? "warning" : "info"}
            onClick={() => handleUserEdit(item)}
          >
            <i className="cil-check"></i> {item?.active ? "Inativar" : "Ativar"}
          </CButton>
        )}
        {!item?.mailingId && (
          <input
            id={`user_${item.userId}`}
            name={`user_${item.userId}`}
            type="checkbox"
            checked={item.selected}
            onChange={() => handleUserEdit(item)}
          />
        )}
      </>
    );
  };

  const handleContractEdit = (item) => {
    if ((item?.mailingId ?? 0) !== 0) {
      handleContractInativar(item);
    } else {
      setContracts((prevContracts) =>
        prevContracts.map((contract) =>
          contract.contractId === item.contractId
            ? { ...contract, selected: !contract.selected }
            : contract
        )
      );
    }
  };

  const handleViewContractSelected = (item) => {
    return (
      <>
        {item?.mailingId && (
          <CButton
            color={item?.active ? "warning" : "info"}
            onClick={() => handleContractEdit(item)}
          >
            <i className="cil-check"></i> {item?.active ? "Inativar" : "Ativar"}
          </CButton>
        )}
        {!item?.mailingId && (
          <input
            id={`contract_${item.contractId}`}
            name={`contract_${item.contractId}`}
            type="checkbox"
            checked={item.selected}
            onChange={() => handleContractEdit(item)}
          />
        )}
      </>
    );
  };

  const handleSelectAllUsers = (e) => {
    const isChecked = e.target.checked;
    setUsers(users.map((user) => ({ ...user, selected: isChecked })));
  };

  const areAllUsersSelected = () => users.every((user) => user.selected);

  const handleSelectAllContracts = (e) => {
    const isChecked = e.target.checked;
    setContracts(
      contracts.map((contract) => ({ ...contract, selected: isChecked }))
    );
  };

  const areAllContractsSelected = () =>
    contracts.every((contract) => contract.selected);

  const fields = [
    {
      key: "userId",
      label: "Código",
    },
    {
      key: "name",
      label: "Nome",
    },
    {
      key: "email",
      label: "Email",
    },
    {
      key: "selected",
      label: (
        <CInputCheckbox
          style={{ margin: "0", padding: "10px" }}
          onChange={handleSelectAllUsers}
          checked={areAllUsersSelected()}
        />
      ),
      formatterByObject: (item) => handleViewUserSelected(item),
    },
  ];

  const fieldContracts = [
    {
      key: "contractId",
      label: "Código",
    },
    {
      key: "name",
      label: "Nome",
    },
    {
      key: "details",
      label: "Detalhes",
    },
    {
      key: "selected",
      label: (
        <CInputCheckbox
          style={{ margin: "0", padding: "10px" }}
          onChange={handleSelectAllContracts}
          checked={areAllContractsSelected()}
        />
      ),
      formatterByObject: (item) => handleViewContractSelected(item),
    },
  ];

  const handleInputNameChange = (e) => {
    const { value } = e.target;
    setNumberOfContract(value);
  };

  return (
    <div>
      <CCard>
        <CCardHeader>
          <CForm>
            <CFormGroup>
              <CRow>
                <CCol style={{ paddingTop: "23px" }}>
                  <h4>{step === 1 ? "Usuários" : "Contratos"}</h4>
                </CCol>
                <CCol>
                  <CLabel>Contratos por Usuário</CLabel>
                  <CInput
                    name="numberOfContract"
                    type="number"
                    min={0}
                    max={100}
                    value={numberOfContract}
                    onChange={handleInputNameChange}
                    disabled={step === 2}
                  />
                </CCol>
              </CRow>
            </CFormGroup>
          </CForm>
        </CCardHeader>
        <CCardBody>
          {step === 1 && (
            <div style={{ maxHeight: "300px", overflowY: "auto" }}>
              <CDataTable
                items={users}
                fields={fields}
                hover
                responsive
                heightParam="300px"
                columnFilter
                style={{
                  tableLayout: "fixed",
                  width: "100%",
                }}
                scopedSlots={{
                  selected: (item) => (
                    <td>
                      <CInputCheckbox
                        style={{ margin: "0", padding: "10px" }}
                        onChange={() => handleUserEdit(item)}
                        checked={item.selected}
                      />
                    </td>
                  ),
                }}
              />
            </div>
          )}
          {step === 2 && (
            <TableSelectItens
              data={contracts}
              columns={fieldContracts}
              onSelectionChange={(_) => {}}
              defaultSelectedKeys={[]}
              selectable={false}
              heightParam="300px"
              style={{ tableLayout: "fixed", width: "100%" }}
            />
          )}
        </CCardBody>
      </CCard>

      <div className="d-flex justify-content-between mt-3">
        {step > 1 && (
          <CButton color="secondary" onClick={handlePreviousStep}>
            Voltar
          </CButton>
        )}
        {step === 1 && (
          <CButton color="primary" onClick={handleNextStep}>
            Próximo
          </CButton>
        )}
        {step === 2 && !mailing?.id && (
          <CButton
            color="success"
            onClick={() =>
              onComplete(
                users.filter((a) => a.selected),
                contracts.filter((a) => a.selected)
              )
            }
          >
            Finalizar
          </CButton>
        )}
      </div>
    </div>
  );
};

export default StepsMailing;
