import React, { useState, useMemo, useEffect } from "react";
import { capitalizeAndSpace } from "./functions";
import { formatThousands } from "./helpers";

function TableSelectItens({
  data,
  columns,
  onSelectionChange,
  defaultSelectedKeys,
  selectable = true,
  heightParam = "300px",
  widthParam = "100%",
  rowDataClass = "",
  onDoubleClick,
  header = true,
}) {
  const tableStyle = {
    maxHeight: heightParam, // Defina a altura máxima desejada
    overflowY: "auto",
    width: widthParam, // Adicione uma barra de rolagem vertical quando o conteúdo exceder a altura
  };

  const tableHeaderStyle = {
    backgroundColor: "#f2f2f2", // Cor de fundo do cabeçalho da tabela
    position: "sticky",
    top: "-2px", // Mantenha o cabeçalho colado no topo
  };

  const thContainerStyle = {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  };

  const iconStyle = {
    marginLeft: "auto", // Empurra o ícone para a direita
  };

  const defaultSortColumn = columns.find((column) => column.defaultSort);
  const [selectedItems, setSelectedItems] = useState([]);
  const [sortConfig, setSortConfig] = useState({
    key: defaultSortColumn ? defaultSortColumn.key : columns[0].key,
    direction: defaultSortColumn ? defaultSortColumn.defaultSort : "ascending",
  });
  const [filters, setFilters] = useState({});

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      const newSelectedItems = data.map((item) => item);
      setSelectedItems(newSelectedItems);
      onSelectionChange(newSelectedItems);
    } else {
      setSelectedItems([]);
      onSelectionChange([]);
    }
  };

  const handleSelectItem = (id) => {
    const newSelectedItems = selectedItems.includes(id)
      ? selectedItems.filter((itemId) => itemId !== id)
      : [...selectedItems, id];

    setSelectedItems(newSelectedItems);
    onSelectionChange(newSelectedItems);
  };

  const handleSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  const sortedData = useMemo(() => {
    if (!data) return [];
    let sortableItems = [...data];
    if (sortConfig.key !== null) {
      sortableItems.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === "ascending" ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === "ascending" ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableItems;
  }, [data, sortConfig]);

  // Placeholder para filtros que devem ser implementados conforme os requisitos
  //const filteredData = sortedData; // Substituir pela lógica de filtragem real

  const handleFilterChange = (key, value) => {
    setFilters((prevFilters) => {
      const updatedFilters = { ...prevFilters };
      if (value === "") {
        delete updatedFilters[key]; // Remove o filtro se o valor for vazio
      } else {
        updatedFilters[key] = value; // Atualiza ou adiciona o filtro
      }
      return updatedFilters;
    });
  };

  // Obter valores distintos para a coluna
  const getDistinctColumnValues = (key) => {
    const unique = new Set(sortedData.map((item) => item[key]));
    return Array.from(unique);
  };

  // Filtra os dados com base nos filtros atuais
  const filteredData = useMemo(() => {
    return sortedData?.filter((item) =>
      Object.entries(filters).every(
        ([key, value]) => item[key].toString() === value
      )
    );
  }, [sortedData, filters]);

  useEffect(() => {
    // Atualiza os itens selecionados quando os filtros mudam
    const updatedSelectedItems = selectedItems.filter((item) =>
      filteredData.includes(item)
    );
    setSelectedItems(updatedSelectedItems);
    onSelectionChange(updatedSelectedItems); // Notifica o componente pai
  }, [filteredData]);

  useEffect(() => {
    // Define os itens pré-selecionados ao montar o componente
    if (defaultSelectedKeys) {
      setSelectedItems(defaultSelectedKeys);
      onSelectionChange(defaultSelectedKeys);
    }
  }, [defaultSelectedKeys]);

  const renderCell = (index, item, column) => {
    // Aplica a função de formatação se ela existir, caso contrário, retorna o valor ou o valor padrão
    let value = item[column.key] || column.defaultValue;
    if (column.formatter) value = column.formatter(item[column.key]);

    if (column.formatterByObject) value = column.formatterByObject(item);

    const className = column.className ? column.className : "";

    const style = column.cellStyleCondicional
      ? column.cellStyleCondicional(item)
      : {};
    if (column.cellStyleCondicional && className !== "")
      return (
        <td key={index} style={style} className={className}>
          {value}
        </td>
      );

    if (column.cellStyleCondicional)
      return (
        <td key={index} style={style} className={className}>
          {value}
        </td>
      );

    if (className !== "")
      return (
        <td key={index} className={className}>
          {value}
        </td>
      );

    return <td key={index}>{value}</td>;
  };

  const hasFilter = columns.some((column) => column.filter);
  const hasRown = columns.length > 0;

  // Calcula os totais com base na propriedade totalizer
  const totals = useMemo(() => {
    const totals = {};
    columns.forEach((column) => {
      if (
        column.totalizer &&
        filteredData !== null &&
        filteredData !== undefined
      ) {
        const values = filteredData.map(
          (item) => parseFloat(item[column.key]) || 0
        );
        switch (column.totalizer) {
          case "sum":
            totals[column.key] = formatThousands(
              values.reduce((acc, val) => acc + val, 0) ?? 0
            );
            break;
          case "average":
            totals[column.key] = formatThousands(
              values.reduce((acc, val) => acc + val, 0) / values.length ?? 0
            );
            break;
          case "count":
            totals[column.key] = values.length ?? 0;
            break;
          case "text":
            totals[column.key] = column.totalizerText ?? "";
            break;
          default:
            break;
        }
      }
    });
    return totals;
  }, [filteredData, columns]);

  const hasTotalizer = columns.some((column) => column.totalizer);

  return (
    <div className="table-responsive" style={tableStyle}>
      <table className="table">
        {header && (
          <thead style={tableHeaderStyle}>
            <tr>
              {selectable && (
                <th>
                  <input
                    type="checkbox"
                    onChange={handleSelectAll}
                    checked={selectedItems.length === filteredData.length}
                  />
                </th>
              )}
              {columns.map((column) => (
                <th
                  key={column.key}
                  onClick={() => handleSort(column.key)}
                  style={column.style}
                >
                  <div style={thContainerStyle}>
                    {column.label || capitalizeAndSpace(column.key)}
                    {sortConfig.key === column.key && (
                      <i
                        style={iconStyle}
                        className={`${
                          sortConfig.direction === "ascending"
                            ? "cil-arrow-top"
                            : "cil-arrow-bottom"
                        }`}
                      ></i>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
        )}
        <tbody>
          {/* Renderiza a linha de filtros se pelo menos uma coluna tem filtro habilitado */}
          {hasFilter && (
            <tr>
              {selectable && <td></td>}
              {/* Espaço reservado para a coluna de seleção */}
              {columns.map((column) => (
                <td key={`filter-${column.key}`}>
                  {column.filter && (
                    <select
                      onChange={(e) =>
                        handleFilterChange(column.key, e.target.value)
                      }
                      value={filters[column.key] || ""}
                    >
                      <option value="">Todos</option>
                      {getDistinctColumnValues(column.key).map(
                        (value, index) => (
                          <option key={index} value={value}>
                            {value}
                          </option>
                        )
                      )}
                    </select>
                  )}
                </td>
              ))}
            </tr>
          )}
          {/* Linhas de dados */}
          {filteredData.map((item, index) => (
            <tr
              className={`${rowDataClass} ${
                onDoubleClick ? "detalhes-contrato-tr" : ""
              }`}
              style={{ cursor: onDoubleClick ? "pointer" : "default" }}
              key={index}
              onDoubleClick={onDoubleClick ? () => onDoubleClick(item) : null}
            >
              {selectable && (
                <td>
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(item)}
                    onChange={() => handleSelectItem(item)}
                  />
                </td>
              )}
              {columns.map((column, index) => renderCell(index, item, column))}
            </tr>
          ))}
        </tbody>
        {hasTotalizer && (
          <tfoot>
            <tr>
              {selectable && <td></td>}
              {/* Espaço reservado para a coluna de seleção */}
              {columns.map((column, index) => (
                <td key={index}>
                  {column.totalizer ? totals[column.key] : ""}
                </td>
              ))}
            </tr>
          </tfoot>
        )}
      </table>
    </div>
  );
}

export default TableSelectItens;
