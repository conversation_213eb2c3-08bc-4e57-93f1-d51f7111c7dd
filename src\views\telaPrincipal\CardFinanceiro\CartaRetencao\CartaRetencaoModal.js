import React, { useState } from "react";
import {
  CButton,
  CInput,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
} from "@coreui/react";


const CartaRetencaoModal = ({ isOpen, onClose, datacobData }) => {

  const handleClose = () => {
    onClose();
  };

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={true}
      size="xl"
      className="custom-modal"
      centered
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Criação de cartas - Consórcio Rodobens:</h5>
      </CModalHeader>
      <CModalBody>
        {/* <div className="d-flex" style={{ gap: "5rem" }}>
            <div className="form-check">
                <input
                className="form-check-input"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault1"
                //   onChange={(e) => setTipo(1)}
                />
                <label className="form-check-label" htmlFor="flexRadioDefault1">
                Termo padrão
                </label>
            </div>
            <div className="form-check">
                <input
                className="form-check-input"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault2"
                //   onChange={(e) => setTipo(2)}
                />
                <label className="form-check-label" htmlFor="flexRadioDefault2">
                Parcelamento consórcio
                </label>
            </div>
        </div> */}

        <div className="d-flex mt-0 nowrap-cell">
            <div className="d-flex flex-row">
                <label className="pt-1">Nome do cliente:</label>
                <CInput className="mr-4 ml-2" placeholder="Nome do cliente" />
            </div>
            <div className="d-flex flex-row">
                <label className="pt-1">Grupo e Cota:</label>
                <CInput className="mr-4 ml-2" placeholder="Grupo e Cota" />
            </div>
            <div className="d-flex flex-row">
                <label className="pt-1">Administradora de Consórcio:</label>
                <CInput className="mr-4 ml-2" placeholder="Administradora de Consórcio" />
            </div>
        </div>
        
        <label className="pt-1 mt-3">Selecione o tipo de Acordo desejado:</label>
        <div className="row nowrap-cell mx-2" style={{ gap: "5rem" }}>
            <div className="col-md-3 form-check">
                <input
                className="form-check-input"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault1"
                //   onChange={(e) => setTipo(1)}
                />
                <label className="form-check-label" htmlFor="flexRadioDefault1">
                    Rateio 100%
                </label>
            </div>
            <div className="col-md-3 form-check">
                <input
                className="form-check-input"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault2"
                //   onChange={(e) => setTipo(2)}
                />
                <label className="form-check-label" htmlFor="flexRadioDefault2">
                    Prorrogação
                </label>
            </div>
            <div className="col-md-3 form-check">
                <input
                className="form-check-input"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault3"
                //   onChange={(e) => setTipo(2)}
                />
                <label className="form-check-label" htmlFor="flexRadioDefault3">
                    Prorrogação - Rateio 50%
                </label>
            </div>
        </div>
        <div className="row mt-2 nowrap-cell mx-2" style={{ gap: "5rem" }}>
            <div className="col-md-3 form-check">
                <input
                className="form-check-input"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault4"
                //   onChange={(e) => setTipo(2)}
                />
                <label className="form-check-label" htmlFor="flexRadioDefault4">
                    Prorrogação - Troca de Bem
                </label>
            </div>
            <div className="col-md-3 form-check">
                <input
                className="form-check-input"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault5"
                //   onChange={(e) => setTipo(2)}
                />
                <label className="form-check-label" htmlFor="flexRadioDefault5">
                    Rateio - 50%
                </label>
            </div>
            <div className="col-md-3 form-check">
                <input
                className="form-check-input"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault6"
                //   onChange={(e) => setTipo(2)}
                />
                <label className="form-check-label" htmlFor="flexRadioDefault6">
                    Troca de Bem
                </label>
            </div>
        </div>

        <div className="mt-4 nowrap-cell">
            <div className="row pr-4 mb-3">
                <div className="col-md-4">
                    <label className="pt-1">Parcela Prorrogada:</label>
                    <CInput className="mr-2 ml-2" />
                </div>
                <div className="col-md-4">
                    <label className="pt-1">Parcela Rateio:</label>
                    <CInput className="mr-2 ml-2" />
                </div>
                <div className="col-md-4">
                    <label className="pt-1">Parcelas a Ratear:</label>
                    <CInput className="mr-2 ml-2" />
                </div>
            </div>
            <div className="row pr-4 mt-3 mb-3">
                <div className="col-md-3">
                    <label className="pt-1">Bem atual:</label>
                    <CInput className="mr-2 ml-2" />
                </div>
                <div className="col-md-3">
                    <label className="pt-1">Valor Bem Atual:</label>
                    <CInput className="mr-2 ml-2" />
                </div>
                <div className="col-md-3">
                    <label className="pt-1">Novo Bem:</label>
                    <CInput className="mr-2 ml-2" />
                </div>
                <div className="col-md-3">
                    <label className="pt-1">Valor Novo Bem:</label>
                    <CInput className="mr-2 ml-2" />
                </div>
            </div>
            <div className="row pr-4 mt-3 mb-3">
                <div className="col-md-3">
                    <label className="pt-1">Valor Parcela:</label>
                    <CInput className="mr-2 ml-2" />
                </div>
                <div className="col-md-3">
                    <label className="pt-1">Data do Processo:</label>
                    <CInput className="mr-2 ml-2" />
                </div>
            </div>
        </div>


      </CModalBody>
      <CModalFooter>
        <CButton color="danger" className="mr-2" onClick={handleClose}>
          Sair
        </CButton>
        <CButton color="warning" className="mr-2" onClick={handleClose}>
          Visualizar Prévia da Carta
        </CButton>
        <CButton color="success" className="mr-2" onClick={handleClose}>
          Gravar Carta
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default CartaRetencaoModal;

