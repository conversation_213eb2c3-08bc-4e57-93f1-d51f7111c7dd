import SpeechRecognitionText from "src/hooks/useSpeechRecognitionHook.ts";
import React from "react";

const SpeechRecognition = () => {
  const {
    hasRegognitionSupport,
    isListening,
    startListening,
    stopListening,
    speech,
  } = SpeechRecognitionText();

  return (
    <div>
      {hasRegognitionSupport && (
        <div className="mt-4 flex flex-col items-center justify-center gap-4">
          {!isListening && (
            <button onClick={startListening}>Começar a gravar</button>
          )}

          {isListening && (
            <button onClick={stopListening}>Parar de gravar</button>
          )}
          <div className="text-center">
            <p className="text-sm">{speech}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default SpeechRecognition;
