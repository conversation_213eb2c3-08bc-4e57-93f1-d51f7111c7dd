import React, { CSSProperties } from "react";
import { formatCurrency, formatDate } from "src/reusable/helpers";
const tableStyle: CSSProperties = {
  maxHeight: "300px", // Defina a altura máxima desejada
  overflowY: "auto",
  width: "100%", // Adicione uma barra de rolagem vertical quando o conteúdo exceder a altura
};

const tableHeaderStyle: CSSProperties = {
  backgroundColor: "#f2f2f2", // Cor de fundo do cabeçalho da tabela
  position: "sticky",
  top: "0", // Mantenha o cabeçalho colado no topo
};

const TableParcelasSimulacao = ({ dataTable }) => {
  const parcelasRestantes = dataTable;

  return (
    <div className="table-responsive-sm" style={tableStyle}>
      <table className="table table-sm">
        <thead style={tableHeaderStyle}>
          <tr>
            <th>Parcela</th>
            <th>Vencimento</th>
            <th>Valor Pa<PERSON></th>
            <th><PERSON><PERSON></th>
            <th>Valor Tari<PERSON></th>
          </tr>
        </thead>
        <tbody>
          {parcelasRestantes.map((item, index) => (
            <tr key={index}>
              <td>{item.nrParcela}</td>
              <td>{formatDate(item.vencimento)}</td>
              <td>{formatCurrency(item.valorParcela)}</td>
              <td>{formatCurrency(item.valorDespesa)}</td>
              <td>{formatCurrency(item.valorTarifa)}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TableParcelasSimulacao;
