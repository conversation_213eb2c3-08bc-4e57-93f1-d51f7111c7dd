import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CRow,
  CCol,
  CLabel,
  CInput,
  CSpinner,
} from "@coreui/react";
import {
  formatCurrency,
  formatDateGlobaltoSimplified,
} from "src/reusable/helpers";
import Select from "react-select";
import ReactDatePicker from "react-datepicker";
import { ptBR } from "date-fns/locale";
import { format } from "date-fns";
import { GET_DATA, POST_DATA } from "src/api";
import { toast } from "react-toastify";
import { getURI } from "src/config/apiConfig";
import { formatarTelefone } from "src/reusable/functions";

const minEntradaPerc = 15;
const contratoSelect = [
  "DB",
  "CONTRATO VENDA",
  "CONFISSÃO DE DIVIDA",
  "CONFISSÃO DE CUSTA",
  "CONFISSÃO DE TAXA",
  "CONFISSÃO DE DB",
];
const today = new Date();

const calcularPGTO = (pv, i, n) => {
  const i_decimal = i;
  let pgto = 0;
  if (i_decimal > 0) {
    let numerador = pv * i_decimal * Math.pow(1 + i_decimal, n);
    let denominador = Math.pow(1 + i_decimal, n) - 1;
    pgto = numerador / denominador;
  } else if (i_decimal === 0) {
    pgto = pv / n;
  }
  return pgto;
};

const calcularJuros = (b23) => {
  return Math.pow(1 + b23 / 100, 1 / 12) - 1;
};

const CalcTipoDoisRNI = ({ isOpen, onClose }) => {
  const [minEntrada, setMinEntrada] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const [optionsContract, setOptionsContract] = useState([]);
  const [selectedContract, setSelectedContract] = useState(null);

  const [optionsSelectContracts, setOptionsSelectContracts] = useState([]);
  const [selectedCalculatedContract, setSelectedCalculatedContract] =
    useState(null);
  const [idOcorrenciaSistema, setIdOcorrenciaSistema] = useState(null);

  const [objValue, setObjValue] = useState({
    contrato: null,
    dataVencimento: new Date(),
    entrada: 0,
    vincenda: 0,
    qtdParcelas: 0,
    dataEntrada: new Date(),
    parcelaInicial: "",
    parcelaFinal: "",
    totalAtualizado: 0,
    desconto: 0,
    honorario: 0,
    valorNegociado: 0,
    totalHo: 0,
  });

  const [selectedRetornoTelefone, setSelectedRetornoTelefone] = useState({
    label: "Selecione",
    value: "",
  });
  const [optionsTelefoneRetorno, setOptionsTelefoneRetorno] = useState([
    {
      label: "Selecione",
      value: "",
    },
  ]);

  async function getConfigPercetualValorEntrada() {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "percetual_valor_entrada_calculadora_rni"
    );
    try {
      return JSON.parse(response);
    } catch (err) {
      return 15;
    }
  }

  async function getTotalAtualizado() {
    if (selectedCalculatedContract == null) {
      console.log("contrato nao selecionado");
      return 0;
    }

    let contratos = contratosAtivos.find(
      (contrato) =>
        contrato.numero_Contrato === selectedCalculatedContract.value
    );
    let parcelas = [];

    const payload = {
      idAgrupamento: contratos.id_Contrato,
      dtNegociacao: formatDateGlobaltoSimplified(new Date()),
      vlNegociado: 0,
      parcelas: [],
      formaDesconto: 0,
    };
    try {
      const response = await POST_DATA(
        getURI("postCalcularNegociacao"),
        payload,
        true,
        true,
        ""
      );

      if (
        response.data &&
        response.data.negociacaoDto &&
        response.data.negociacaoDto.length > 0
      ) {
        parcelas = response.data.negociacaoDto[0].parcelas.filter(
          (item) => item.parcelaSelecionada == true
        );
      }
      if (response.success === false) {
        console.warn("error", response.message);
        parcelas = GetParcelas(contratos);
      }
    } catch (error) {
      console.warn("error", error);
      parcelas = GetParcelas(contratos);
    }

    const somaAtualizados = await parcelas.reduce(
      (accumulator, currentItem) => {
        if (
          currentItem.vlOriginal != null &&
          currentItem.vlOriginal != undefined
        ) {
          return accumulator + currentItem.vlOriginal;
        } else if (
          currentItem.vl_Saldo_Atualizado != null &&
          currentItem.vl_Saldo_Atualizado != undefined
        ) {
          return accumulator + currentItem.vl_Saldo_Atualizado;
        }
        return accumulator + currentItem.vl_Atualizado;
      },
      0
    );

    if (somaAtualizados >= 0) {
      setObjValue({ ...objValue, totalAtualizado: somaAtualizados });
    }
    calculaMinEntrada(somaAtualizados);
  }

  function GetParcelas(contratos) {
    let parcelas = contratos.parcelas.filter((item) => item.status == "A");
    parcelas = parcelas.filter(
      (item) => item.nome_Tipo_Parcela !== "SALDO_VINCENDO"
    );

    parcelas = parcelas.map((pItem) => {
      if (pItem.atraso === 0) {
        pItem.vl_Atualizado = pItem.vl_Original;
        pItem.vl_Desc_Max = pItem.vl_Original;
      }
      return pItem;
    });
    return parcelas;
  }

  async function calculaMinEntrada(valortotal) {
    setIsLoading(true);
    let minEntPerc = await getConfigPercetualValorEntrada();
    let minEntCal = parseFloat((valortotal * (minEntPerc / 100)).toFixed(2));
    setMinEntrada(parseFloat(minEntCal));
    setIsLoading(false);
  }

  const validateFields = () => {
    if (objValue?.parcelaInicial === "") {
      alert("Preencha a Parcela Inicial ");
      return false;
    }
    if (objValue?.parcelaFinal === "") {
      alert("Preencha a Parcela Final ");
      return false;
    }

    if (objValue?.entrada < minEntrada) {
      alert(
        "O Valor da Entrada, não pode ser menos que o Valor Mínimo de Entrada:"
      );
      return false;
    }
    return true;
  };
  async function getIdOcorrencia() {
    const tiposOcorrencia = await GET_DATA("Datacob/Ocorrencias");
    let itemOcorrencia = tiposOcorrencia.find(
      (item) => item["cod_Ocorr_Sistema"] === "011"
    );
    if (itemOcorrencia) {
      setIdOcorrenciaSistema(itemOcorrencia.id_Ocorrencia_Sistema);
    }
  }

  function montarDescricaoHistorico() {
    let parcelaInicial = format(objValue?.parcelaInicial, "MM/yyyy", {
      locale: ptBR,
    });
    let parcelaFinal = format(objValue?.parcelaFinal, "MM/yyyy", {
      locale: ptBR,
    });
    let dataEntrada = format(objValue?.dataEntrada, "dd/MM/yyyy", {
      locale: ptBR,
    });
    let dataVencimento = format(objValue?.dataVencimento, "dd/MM/yyyy", {
      locale: ptBR,
    });
    return (
      `CONTRATO SELECIONADO ${selectedCalculatedContract.value}; CONTRATO: ${
        objValue?.contrato
      }; PARCELAS NEGOCIADAS: ${parcelaInicial} a ${parcelaFinal}; VALOR PRINCIPAL: ${formatCurrency(
        objValue?.totalAtualizado
      )}; DESCONTO PRINCIPAL %: ${objValue?.desconto}; ` +
      `%HO:${objValue?.honorario};VALOR NEGOCIADO: ${formatCurrency(
        valorNegociado
      )}; TOTAL + HO: ${valorTotalHo}; ` +
      `VALOR ENTRADA: ${formatCurrency(objValue?.entrada)}; ` +
      `TAXA DE JUROS %: ${
        objValue?.taxaJuros
      }; DATA ENTRADA : ${dataEntrada}; QUANTIDADE DE PARCELAS: ${
        objValue?.qtdParcelas
      }; VALOR DE PARCELA: ${formatCurrency(
        valorParcela ?? 0
      )}; PRIMEIRO VENCIMENTO: ${dataVencimento}`
    );
  }

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  async function historicoAdicionar() {
    if (!validateFields()) return false;

    if (selectedRetornoTelefone.value === "") {
      toast.error("Selecione um telefone");
      return false;
    }
    let telefone = selectedRetornoTelefone.value;

    const data = {
      login: user?.username,
      id_Contrato: financiadoData?.id_Contrato,
      id_Ocorrencia_Sistema: idOcorrenciaSistema,
      observacao: montarDescricaoHistorico(),
      complemento: "",
      telefones: [formatarTelefone(telefone)],
      callType: null,
      telefoneParaRetorno: formatarTelefone(telefone),
    };
    const ocorrencia = await POST_DATA("Datacob/historicoAdicionar", data, false, true);
    if (ocorrencia.success) {
      toast.info("Ocorrência adicionada com sucesso");
      window.location.reload();
    }
    return ocorrencia;
  }

  const [valorParcela, setValorParcela] = useState(0);
  const [valorNegociado, setValorNegociado] = useState(0);
  const [valorTotalHo, setValorTotalHo] = useState(0);

  const handleClose = async (x) => {
    if (x) {
      if ((await historicoAdicionar()) == false) {
        return;
      }
    }
    onClose();
  };

  useEffect(() => {
    setOptionsSelectContracts(
      contratosAtivos.map((x) => {
        return { label: x.numero_Contrato, value: x.numero_Contrato };
      })
    );

    setOptionsContract(
      contratoSelect.map((x) => {
        return { label: x, value: x };
      })
    );
    getIdOcorrencia();
  }, [isOpen]);

  useEffect(() => {
    if (
      selectedCalculatedContract !== null &&
      selectedCalculatedContract !== undefined
    ) {
      getTotalAtualizado();
    }
  }, [selectedCalculatedContract]);

  useEffect(() => {
    if (
      selectedCalculatedContract == null ||
      selectedCalculatedContract == undefined
    )
      setSelectedCalculatedContract(optionsSelectContracts[0]);
  }, [optionsSelectContracts]);

  const handleChangeSelectedCalculatedContract = (item) => {
    setSelectedCalculatedContract(item);
  };

  useEffect(() => {
    setObjValue({ ...objValue, entrada: minEntrada });
  }, [minEntrada]);

  const handleInputChange = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    value = Number(input) / 100;
    if (input.length > 11) return;

    setObjValue({ ...objValue, [event.target.name]: value });
  };

  const handleInputIntChange = (event) => {
    const input = Number(event.target.value.replace(/\D/g, ""));

    setObjValue({ ...objValue, [event.target.name]: input });
  };

  const handleInputPercChange = (event) => {
    const input = Number(event.target.value.replace(/\D/g, ""));
    let value = input;

    if (input > 100) value = 100;
    if (input < 0) value = 0;
    if (isNaN(value) || !isFinite(value)) value = 0;

    setObjValue({ ...objValue, [event.target.name]: value });
  };

  const handleDateChange = (date) => {
    setObjValue({ ...objValue, dataVencimento: date });
  };

  const handleDateEntryChange = (date) => {
    setObjValue({ ...objValue, dataEntrada: date });
  };

  const handleDateInitialInstallmentChange = (date) => {
    setObjValue({ ...objValue, parcelaInicial: date });
  };
  const handleDateFinalInstallmentChange = (date) => {
    setObjValue({ ...objValue, parcelaFinal: date });
  };

  useEffect(() => {
    const val2 = parseFloat(
      (
        objValue?.totalAtualizado -
        objValue?.totalAtualizado * (objValue?.desconto / 100)
      ).toFixed(2)
    );
    if (isNaN(val2) || !isFinite(val2)) setValorNegociado(0);
    else setValorNegociado(val2);

    const val3 = parseFloat(
      (val2 + val2 * (objValue?.honorario / 100)).toFixed(2)
    );

    if (isNaN(val3) || !isFinite(val3)) setValorTotalHo(0);
    else setValorTotalHo(val3);

    const val = calcularPGTO(
      val3 - objValue?.entrada,
      calcularJuros(objValue?.taxaJuros),
      objValue?.qtdParcelas
    );
    if (isNaN(val) || !isFinite(val)) setValorParcela(0);
    else setValorParcela(val);
  }, [objValue]);

  const statusTelefoneName = (item) => {
    return item.status === 1
      ? " Ativo "
      : item.status === 2
      ? " Efetivo "
      : item.status === 3
      ? " Pesquisado "
      : item.status === -1
      ? " Blacklist "
      : " Inativo ";
  };
  useEffect(() => {
    const telJson = JSON.parse(localStorage.getItem("clientData"))?.telefones;
    let telefoneEfetivo = [];
    telefoneEfetivo = telJson.filter((x) => x.status === 2);
    if (telefoneEfetivo.length === 0) {
      telefoneEfetivo = telJson.filter((x) => x.status === 1);
    }
    let telefone = "";
    if (telefoneEfetivo.length > 0)
      telefone = telefoneEfetivo[0].ddd + telefoneEfetivo[0].fone;

    if (telJson !== null && telJson !== undefined) {
      const tel = telJson;
      const telOp = tel.map((item) => {
        let label = statusTelefoneName(item) + " - " + item.ddd + item.fone;
        if (item.descricao !== "" && item.descricao != null)
          label += " - " + item.descricao;

        return {
          label: label,
          value: item.ddd + item.fone,
        };
      });
      const telRetOp = [...optionsTelefoneRetorno, ...telOp];

      setOptionsTelefoneRetorno(telRetOp);
      if (telefone !== "") {
        let telRet = telRetOp.find((x) => x.value === telefone);
        if (telRet) {
          setSelectedRetornoTelefone(telRet);
        }
      }
    }
  }, [isOpen]);

  const handleTelefoneRetornoChange = (event) => {
    setSelectedRetornoTelefone(event);
  };

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="lg"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Cálculo - Parcelamento Desconto Principal:</h5>
      </CModalHeader>
      <CModalBody>
        <CRow>
          <CCol md="4">
            <CLabel>Contrato Selecionado:</CLabel>
          </CCol>
          <CCol md="8">
            <Select
              value={selectedCalculatedContract}
              options={optionsSelectContracts}
              onChange={handleChangeSelectedCalculatedContract}
              placeholder={"Selecione o contrato"}
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Valor atualizado (Datacob):</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              value={formatCurrency(objValue?.totalAtualizado, false)}
              disabled
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Valor Mínimo de Entrada:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput value={formatCurrency(minEntrada, false)} disabled />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Contrato:</CLabel>
          </CCol>
          <CCol md="8">
            <Select
              value={selectedContract}
              options={optionsContract}
              onChange={(e) => {
                setSelectedContract(e);
                setObjValue({ ...objValue, contrato: e.value });
              }}
              placeholder={"Selecione"}
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Telefone</CLabel>
          </CCol>
          <CCol md="8">
            <Select
              value={selectedRetornoTelefone}
              onChange={handleTelefoneRetornoChange}
              options={optionsTelefoneRetorno}
              placeholder={"Selecione"}
            ></Select>
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Parcelas Negociadas:</CLabel>
          </CCol>
          <CCol md="8">
            <CRow>
              <CCol md="5">
                {/* <CInput
                  onChange={handleInputIntChange}
                  value={objValue?.parcelaInicial}
                  name="parcelaInicial"
                /> */}
                <ReactDatePicker
                  selected={objValue?.parcelaInicial}
                  onChange={handleDateInitialInstallmentChange}
                  className="form-control"
                  dateFormat="MM/yyyy"
                  showMonthYearPicker
                  locale={ptBR}
                  name="parcelaInicial"
                  onKeyDown={(e) => e.preventDefault()}
                />
              </CCol>{" "}
              <CCol md="1" className={"p-1"}>
                <span>a</span>
              </CCol>
              <CCol md="5">
                {/* <CInput
                  onChange={handleInputIntChange}
                  value={objValue?.parcelaFinal}
                  name="parcelaFinal"
                /> */}
                <ReactDatePicker
                  selected={objValue?.parcelaFinal}
                  onChange={handleDateFinalInstallmentChange}
                  className="form-control"
                  dateFormat="MM/yyyy"
                  showMonthYearPicker
                  locale={ptBR}
                  name="parcelaFinal"
                  onKeyDown={(e) => e.preventDefault()}
                />
              </CCol>
            </CRow>
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Valor Principal:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              value={formatCurrency(objValue?.totalAtualizado, false)}
              onChange={handleInputChange}
              name="totalAtualizado"
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Desconto Principal:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              onChange={handleInputPercChange}
              value={objValue?.desconto}
              name="desconto"
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>%HO:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              onChange={handleInputPercChange}
              value={objValue?.honorario}
              name="honorario"
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Valor Negociado:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput value={formatCurrency(valorNegociado, false)} disabled />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Total + HO:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput value={formatCurrency(valorTotalHo, false)} disabled />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Valor de Entrada:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              value={formatCurrency(objValue?.entrada, false)}
              onChange={handleInputChange}
              name="entrada"
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Taxa de Juros:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              value={objValue?.taxaJuros}
              onChange={handleInputPercChange}
              name="taxaJuros"
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Data de Entrada:</CLabel>
          </CCol>
          <CCol md="8">
            <ReactDatePicker
              selected={objValue?.dataEntrada}
              onChange={handleDateEntryChange}
              className="form-control"
              minDate={today}
              dateFormat="dd/MM/yyyy"
              onKeyDown={(e) => e.preventDefault()}
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Qtd de Parcelas:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              onChange={handleInputIntChange}
              value={objValue?.qtdParcelas}
              name="qtdParcelas"
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>valor das parcelas:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput value={formatCurrency(valorParcela, false)} disabled />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Primeiro Vencimento:</CLabel>
          </CCol>
          <CCol md="8">
            <ReactDatePicker
              selected={objValue?.dataVencimento}
              onChange={handleDateChange}
              className="form-control"
              minDate={today}
              dateFormat="dd/MM/yyyy"
              onKeyDown={(e) => e.preventDefault()}
            />
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter className={"justify-content-center"}>
        <CButton
          color="success"
          className="mr-2"
          onClick={() => handleClose(true)}
          disabled={objValue?.totalAtualizado <= 0}
        >
          {isLoading ? (
            <>
              <CSpinner size="sm" className="mr-1" />
              Gravar e Gerar Ocorrência
            </>
          ) : (
            "Gravar e Gerar Ocorrência"
          )}
        </CButton>
        <CButton
          color="danger"
          className="mr-2"
          onClick={() => handleClose(false)}
        >
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default CalcTipoDoisRNI;
