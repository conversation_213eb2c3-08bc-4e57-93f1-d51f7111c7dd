import React, { useEffect, useState } from "react";
import { getApiInline } from "src/reusable/functions";
import { CCard, CCardBody, CRow } from "@coreui/react";
import CardLoading from "src/reusable/CardLoading";

const NewconIframe = () => {
  const [validadacaoTela, setValidadacaoTela] = useState(true);
  const [textValidacao, setTextValidacao] = useState("");
  const [loading, setLoading] = useState(true);
  const [url, setUrl] = useState(null);

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const validacoeTela = async () => {
    setValidadacaoTela(true);
    setTextValidacao("");
    setLoading(true);
    let keep = false;

    try {
      const config = await getApiInline(
        "grupos_rodobens_iframe",
        "getConfigUnicaByKey"
      );
      if (!config || config === undefined) {
        setTextValidacao(`Não há parâmetro para o(s) grupo(s) do usuário`);
        setValidadacaoTela(false);
      } else {
        const grupos = JSON.parse(config);
        for (const item of user?.groups) {
          if (grupos.indexOf(item.id) > -1) {
            keep = true;
            break;
          }
        }
        if (!keep) {
          setTextValidacao(`Não há parâmetro para o(s) grupo(s) do usuário`);
          setValidadacaoTela(false);
        }
      }
    } catch (error) {
      setTextValidacao("Erro ao validar grupo");
      setValidadacaoTela(false);
    }

    if (keep) {
      const config2 = await getApiInline(
        "rodobens_newcon_iframe",
        "getConfigUnicaByKey"
      );
      if (!config2 || config2 === undefined) {
        setTextValidacao(`Não há conexão com o Newcon`);
        setValidadacaoTela(false);
      } else {
        setUrl(config2);
      }
    }

    setLoading(false);
  };

  useEffect(() => {
    validacoeTela();
  }, []);

  return (
    <>
      {(!validadacaoTela || loading) && (
        <CRow>
          <CCard style={{ width: "100%" }}>
            <CCardBody>
              <div className="text-center" style={{ width: "100%" }}>
                <label>{loading ? <CardLoading /> : textValidacao}</label>
              </div>
            </CCardBody>
          </CCard>
        </CRow>
      )}
      {validadacaoTela && !loading && (
        <div>
          <CRow className="mx-3 mb-2">
            <h1>NewCon</h1>
          </CRow>
          <CRow className="d-flex mb-2 mx-3"></CRow>
          <iframe
            src={url}
            title="Newcon Iframe"
            width="100%"
            style={{ height: "80vh" }}
          ></iframe>
        </div>
      )}
    </>
  );
};

export default NewconIframe;
