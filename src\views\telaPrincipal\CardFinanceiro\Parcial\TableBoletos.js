import { <PERSON><PERSON>ge, <PERSON>utton, CDataTable, CInputCheckbox } from "@coreui/react";
import React from "react";
import {
  formatDate,
  formatDocument,
  formatThousands,
} from "src/reusable/helpers";

const TableBoletos = ({
  tableBoletos,
  handleDetalhesBoleto,
  handleVisualizarBoleto,
  handleBaixarBoleto,
  downBol,
  itemPerPage = null,
}) => {
  const boletosFields = [
    { key: "detalhes" },
    { key: "visualizar" },
    { key: "status" },
    {
      key: "dt_Pago",
      label: "Data Pagamento",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "vl_Pago",
      label: "Valor Pago",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "recibo_Emitido",
      label: "Recibo Emitido",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "recibo_Boleto_Obrigatorio",
      label: "Recibo Boleto Obrigatório",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "nr_Boleto", label: "Nº Boleto", _style: { whiteSpace: "nowrap" } },
    { key: "nosso_Nr", label: "Nosso Nº", _style: { whiteSpace: "nowrap" } },
    {
      key: "vl_Boleto",
      label: "Valor Boleto",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "vl_Tx_Banc",
      label: "Valor Taxa Banco",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "dt_Proc", label: "Dt Processo", _style: { whiteSpace: "nowrap" } },
    {
      key: "dt_Venc",
      label: "Dt Vencimento",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dt_Emiss",
      label: "Dt Emissão",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dt_Docto",
      label: "Dt Documento",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "documento", label: "Documento", _style: { whiteSpace: "nowrap" } },
    { key: "moeda", label: "S Moeda", _style: { whiteSpace: "nowrap" } },
    { key: "qtde", label: "Quantidade" },
    {
      key: "qtde_Valor",
      label: "Quantidade Valor",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "tipo_Envio", _style: { whiteSpace: "nowrap" } },
    {
      key: "descricao_Motivo_Cancel",
      label: "Descrição Motivo Cancelamento",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "email_Bol", label: "Email" },
    { key: "telfax", label: "Fax" },
    {
      key: "situacao_registro",
      label: "Situação de Registro",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dt_Agenda_Envio",
      label: "Dt Situação Registro",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "userBoleto",
      label: "Nome Operador Gerador",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "userNegociacao",
      label: "Nome do Negociador",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "mensagem",
      label: "Descrição",
      _style: { whiteSpace: "nowrap" },
    },
  ];

  const renderStatus = (status) => {
    switch (status) {
      case "P":
        return <CBadge color="success">Pago</CBadge>;
      case "A":
        return <CBadge color="info">Aberto</CBadge>;
      case "C":
        return <CBadge color="danger">Cancelado</CBadge>;
      default:
        break;
    }
  };

  return (
    <CDataTable
      items={tableBoletos}
      fields={boletosFields}
      pagination={itemPerPage !== null}
      itemsPerPage={itemPerPage}
      hover
      responsive
      scopedSlots={{
        detalhes: (item) => (
          <td>
            <CButton
              className="button-link py-0 px-0"
              onClick={() => handleDetalhesBoleto(item)}
            >
              Detalhes
            </CButton>
          </td>
        ),
        visualizar: (item) => (
          <td className="d-flex">
            <CButton
              className="button-link py-0 px-0 mr-2"
              onClick={() => handleVisualizarBoleto(item)}
            >
              Visualizar
            </CButton>
            <CButton
              disabled={downBol}
              color="info"
              className={"py-0 px-2"}
              onClick={() => handleBaixarBoleto(item)}
            >
              <i className="cil-arrow-thick-to-bottom"></i>
            </CButton>
          </td>
        ),
        recibo_Emitido: (item) => (
          <td
            style={{
              paddingLeft: "40px",
              textAlign: "center",
            }}
          >
            <CInputCheckbox
              id={item.id_Boleto}
              defaultChecked={item.recibo_Emitido}
              disabled
            />
          </td>
        ),
        recibo_Boleto_Obrigatorio: (item) => (
          <td
            style={{
              paddingLeft: "40px",
              textAlign: "center",
            }}
          >
            <CInputCheckbox
              id={item.id_Boleto}
              defaultChecked={item.recibo_Boleto_Obrigatorio}
              disabled
            />
          </td>
        ),
        nr_Boleto: (item) => <td className="nowrap-cell">{item.nr_Boleto}</td>,
        nosso_Nr: (item) => <td className="nowrap-cell">{item.nosso_Nr}</td>,
        descricao_Motivo_Cancel: (item) => (
          <td className="nowrap-cell">{item.descricao_Motivo_Cancel}</td>
        ),
        dt_Venc: (item) =>
          item.dt_Venc ? <td>{formatDate(item.dt_Venc)}</td> : <td>---</td>,
        dt_Pago: (item) =>
          item.dt_Pago ? <td>{formatDate(item.dt_Pago)}</td> : <td>---</td>,
        dt_Inco: (item) =>
          item.dt_Inco ? <td>{formatDate(item.dt_Inco)}</td> : <td>---</td>,
        dt_Emiss: (item) =>
          item.dt_Emiss ? <td>{formatDate(item.dt_Emiss)}</td> : <td>---</td>,
        dt_Proc: (item) =>
          item.dt_Proc ? <td>{formatDate(item.dt_Proc)}</td> : <td>---</td>,
        dt_Docto: (item) =>
          item.dt_Docto ? <td>{formatDate(item.dt_Docto)}</td> : <td>---</td>,
        dt_Agenda_Envio: (item) =>
          item.dt_Agenda_Envio ? (
            <td>{formatDate(item.dt_Agenda_Envio)}</td>
          ) : (
            <td>---</td>
          ),
        documento: (item) =>
          item.documento ? (
            <td className="nowrap-cell">{formatDocument(item.documento)}</td>
          ) : (
            <td>---</td>
          ),
        vl_Boleto: (item) =>
          item.vl_Boleto ? (
            <td>{formatThousands(item.vl_Boleto)}</td>
          ) : (
            <td>---</td>
          ),
        vl_Pago: (item) =>
          item.vl_Pago ? (
            <td>{formatThousands(item.vl_Pago)}</td>
          ) : (
            <td>---</td>
          ),
        status: (item) => <td> {renderStatus(item.status)} </td>,
        boleto_Emitido: (item) =>
          item.boleto_Emitido ? (
            <td style={{ textAlign: "center" }}>
              <i className="cil-check-circle" />
            </td>
          ) : (
            <td>X</td>
          ),
        situacao_registro: (item) =>
          item.situacao_registro ? (
            <td>{item.situacao_registro}</td>
          ) : (
            <td>Não registrado</td>
          ),
        userBoleto: (item) => <td>{item.userBoleto ?? "---"}</td>,
        userNegociacao: (item) => <td>{item.userNegociacao ?? "---"}</td>,
        mensagem: (item) => (
          <td
            style={{
              cursor: "pointer",
              minWidth: "300px",
              whiteSpace: "normal",
              overflow: "visible",
              textOverflow: "unset",
            }}
          >
            {item.mensagem ?? "---"}
          </td>
        ),
      }}
    />
  );
};

export default TableBoletos;
