// selectedStatus = Ab => Aberto; D => Devolvido
export const filterInstallments = (parcelas, selectedStatus) => {
  const sortData = (parcela) => {
    const sortedData = [...parcela];
    //sortedData.sort((a, b) => a.nr_Parcela - b.nr_Parcela);
    sortedData.sort(compararParcelas);

    return sortedData;
  };
  const parcelaOrganizada = sortData(parcelas);

  const filteredData = parcelaOrganizada.filter((item) => {
    const matchesStatus =
      !selectedStatus ||
      item.status === selectedStatus ||
      (selectedStatus === "Ac" && temAcordo(item)) ||
      (selectedStatus === "Ab" && item.status === "A" && !temAcordo(item));

    return matchesStatus;
  });
  return filteredData;
};

export function compararParcelas(a, b) {
  // Primeiro, compare pelo status ascendente
  const statusA = a.status;
  const statusB = b.status;

  if (statusA < statusB) {
    return -1;
  }
  if (statusA > statusB) {
    return 1;
  }

  // Se os status forem iguais, compare pelo nr_Acordo descendente
  const nrAcordoA = b.nr_Acordo - a.nr_Acordo;

  if (nrAcordoA !== 0) {
    return nrAcordoA;
  }

  // Se os números de acordo forem iguais, compare pelo atraso descendente
  return b.atraso - a.atraso;
}

export const temAcordo = (item) => {
  if (item.status === "A") {
    if (item.nr_Acordo > 0) {
      return true;
    } else {
      return false;
    }
  }
};
