import {
  CButton,
  CCol,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CRow,
} from "@coreui/react";
import React from "react";
import { formatFone } from "src/reusable/helpers";

const DadosContatoModal = ({ isOpen, onClose, clientData }) => {
  const handleClose = () => {
    onClose();
  };

  const telefones = clientData?.telefones;
  const enderecos = clientData?.enderecos;
  const emails = clientData?.emails;

  const getBiggerArray = () => {
    if (
      telefones?.length >= emails?.length &&
      telefones?.length >= enderecos?.length
    )
      return telefones;
    if (
      emails?.length >= telefones?.length &&
      emails?.length >= enderecos?.length
    )
      return emails;
    if (
      enderecos?.length >= telefones?.length &&
      enderecos?.length >= emails?.length
    )
      return enderecos;
    return [];
  };

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="lg">
      <CModalHeader>Dados de Contato</CModalHeader>
      <CModalBody>
        <CRow
          className={"m-3"}
          style={{ maxHeight: "232px", overflow: "auto" }}
        >
          <table className="table  table-hover">
            <thead>
              <th>Telefones</th>
              <th>Endereços</th>
              <th>E-Mails</th>
            </thead>
            <tbody>
              {getBiggerArray().map((item, key) => (
                <tr>
                  <td>
                    {formatFone(
                      (telefones[key]?.ddd ?? "") + (telefones[key]?.fone ?? "")
                    )}
                  </td>
                  <td>
                    {enderecos[key]
                      ? enderecos[key]?.logradouro +
                        " - " +
                        enderecos[key]?.cidade +
                        " - " +
                        enderecos[key]?.uf
                      : ""}
                  </td>
                  <td>{emails[key]?.endereco_Email ?? ""}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CRow>
          <CCol className="d-flex justify-content-end">
            <CButton
              color="secondary"
              className="mr-2"
              onClick={() => handleClose()}
            >
              Fechar
            </CButton>
          </CCol>
        </CRow>
      </CModalFooter>
    </CModal>
  );
};

export default DadosContatoModal;
