/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import ReactDatePicker from "react-datepicker";
import ptBR from "date-fns/locale/pt-BR";

import "react-datepicker/dist/react-datepicker.css";

import {
  CButton,
  CInput,
  CModal,
  CModalBody,
  CModalHeader,
  CModalTitle,
  CCol,
  CRow,
  CLabel,
  CModalFooter,
} from "@coreui/react";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardCalcular from "./CardCalcular.tsx";
import CardLoading from "src/reusable/CardLoading";
import FormularioSimulacao from "./FormularioSimulacao.tsx";
import { ApiResponse } from "src/types/common.ts";
import {
  BBCSimulacao,
  useBBCContext,
  simulatePayload,
  simulateInstallmentPayload,
} from "../pageContext/BBCContext.tsx";
// import TableParcelasSimulacao from "./TableParcelasSimulacao.tsx";
// import { BBCElegibilidade } from "src/types/commonBBC.ts";
import TableSelectItens from "src/reusable/TableSelectItens.js";
import {
  convertCurrencyToFloat,
  formatCurrency,
  roundFloat,
} from "src/reusable/helpers.js";
import { useAuth } from "src/auth/AuthContext.js";
import { toast } from "react-toastify";
// import { ChangeEvent } from "react";

const PostData = async (payload, endpoint = "BBCSimulation") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), payload, true, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

type discountList = {
  discountAtt: number;
  discountQtc: number;
};

const FormNegociacaoModal = ({ isOpen, onClose, contrato }) => {
  const { permissions } = useAuth();
  const BBCContext = useBBCContext();
  const Elegibilidade = BBCContext.contratoNegociar.elegivel;
  const installmentParams = BBCContext.installmentParams;
  const discountParams = BBCContext.discountParams;
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");

  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");

  const [dataVencimento, setDataVencimento] = useState<Date>(new Date());
  const [valorNegociacao, setValorNegociacao] = useState<number>(0);
  const [valorTotal, setValorTotal] = useState<number>(0);
  const [valorMinimoNegociacao, setValorMinimoNegociacao] = useState<number>(0);
  // const [valorBaseNegociacao, setValorBaseNegociacao] = useState<number>(0);
  const [valorBase, setValorBase] = useState<number>(0);
  const [valorBaseParcelasVencidas, setValorBaseParcelasVencidas] =
    useState<number>(0);
  const [parcelasNegociacao, setParcelasNegociacao] = useState([]);

  const [porcentagemHO, setPorcentagemHO] = useState(10);
  const [valorHO, setValorHO] = useState(0);
  const [valorMulta, setValorMulta] = useState(0);
  const [valorMultaPagar, setValorMultaPagar] = useState(0);
  const [valorMora, setValorMora] = useState(0);
  const [valorMoraPagar, setValorMoraPagar] = useState(0);

  const [porcentagemDescontoMulta, setPorcentagemDescontoMulta] = useState(0);
  const [porcentagemDescontoMora, setPorcentagemDescontoMora] = useState(0);

  const [porcentagemDescontoPrincipal, setPorcentagemDescontoPrincipal] =
    useState<number>(0);

  const [parcelamentoMax, setParcelamentoMax] = useState<number>(0);

  const [percPrincipal, setPercPrincipal] = useState<discountList[]>([]);
  const [percPrincipalAtt, setPercPrincipalAtt] = useState<number>(0);
  const [percPrincipalQtc, setPercPrincipalQtc] = useState<number>(0);
  const [maxDiscount, setMaxDiscount] = useState<number>(0);

  // const [contratoOriginal, setContratoOriginal] = useState(null);
  const [contratoSimulado, setContratoSimulado] = useState(null);
  const [parcelamento, setParcelamento] = useState(1);

  const [errors, setErrors] = useState({
    maxParcelas: "",
    valorNegociacao: "",
    parcelamento: "",
  });

  function isObjectEmpty(obj) {
    return Object.values(obj).every(
      (value) => value === null || value === undefined || value === ""
    );
  }

  const [payload, setPayload] = useState<simulatePayload>();

  const verifInstallment = (installment: number) => {
    const param = installmentParams.find(
      (item) => item.min <= valorTotal && item.max >= valorTotal
    );
    let maxInst = 6;
    if (param) {
      maxInst = param.maxInstallments;
    }
    setParcelamentoMax(maxInst);
    if (installment <= maxInst) {
      return true;
    }
    toast.info(`Quantidade de parcelas não pode ser maior que ${maxInst}!`);
    return false;
  };

  const verifDiscount = (item: any) => {
    const params: discountList[] = [];
    for (const parcela of item) {
      const param = discountParams.find(
        (x) => x.dayFrom <= parcela.atraso && x.dayTo >= parcela.atraso
      );
      let discountAtt = 0;
      let discountQtc = 0;
      if (param) {
        discountAtt = param.updatePerc;
        discountQtc = param.acquittancePerc;
        params.push({ discountAtt, discountQtc });
      }
    }
    setPercPrincipal(params);
  };

  const RealizarSimulacao = async () => {
    let ret = false;
    setTitleAvisoLoading("Enviando Simulação BBC");
    setMsgAvisoLoading(`Enviando dados para Simulação...`);
    setLoading(true);
    setLoadingAction("VarifyParam");

    if (!verifInstallment(parcelamento)) {
      setLoading(false);
      setLoadingAction("empty");
      setMsgAvisoLoading("");
      setTitleAvisoLoading("");
      return;
    }

    await PostData(payload)
      .then(
        (
          data: ApiResponse<{ data: object; message: string; success: boolean }>
        ) => {
          if (data.success && data.message === "") {
            let tempContratoNegociar = { ...BBCContext.contratoNegociar };
            tempContratoNegociar.dadosBBC.simulacao =
              data.data as unknown as BBCSimulacao;

            BBCContext.setContratoNegociar(tempContratoNegociar);
            // contratoNegociado.dadosBBC.simulacao = data.data;
            setContratoSimulado(BBCContext.contratoNegociar);
          } else {
            setTitleAvisoLoading("Falha Simulação BBC");
            setMsgAvisoLoading(`Simulação não gerada ${data.message}`);
          }
        }
      )
      .catch((err) => {
        setTitleAvisoLoading(`Erro na chamada das APIS`);
        setMsgAvisoLoading(
          `Erro na chamada API BBC Simulação de Acordos, Entre em contato com o Administraodor de Sistemas, realize uma nova busca e tente novamente.`
        );
      })
      .finally(() => {
        setTimeout(() => {
          setLoading(false);
          setLoadingAction("empty");
          setMsgAvisoLoading("");
          setTitleAvisoLoading("");
        }, 8000);
      });

    return ret;
  };

  const handleSimular = () => {
    RealizarSimulacao();
  };

  const filtraParcelasParaNegociacao = () => {
    setParcelasNegociacao(contrato.dadosBBC.parcelas);

    // Comentando para desabilitar o filtro pela elegibilidade

    // const elegibilidade: BBCElegibilidade = contrato.elegivel;
    // const parcelas = contrato.dadosBBC.parcelas;

    // const permitidas = parcelas.filter((item) => {

    //   return (
    //     item.atraso >= elegibilidade.minAtraso &&
    //     item.atraso <= elegibilidade.maxAtraso
    //   );
    // });

    // setParcelasNegociacao(permitidas);
  };

  useEffect(() => {
    filtraParcelasParaNegociacao();
  }, []);

  // const formatValorNegociar = () => {
  //   setValorNegociacao(
  //     formatCurrency(
  //       typeof valorNegociacao === "string"
  //         ? convertCurrencyToFloat(valorNegociacao)
  //         : valorNegociacao,
  //       false
  //     )
  //   );
  // };
  const handleValorNegociarChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");
    const value = Number(input) / 100;
    if (input.length > 11 || isNaN(value)) return;
    setValorNegociacao(value);
  };

  useEffect(() => {
    // Verifica se o valor da negociação está abaixo do minimo
    if (
      convertCurrencyToFloat(formatCurrency(valorNegociacao, false)) <
      convertCurrencyToFloat(formatCurrency(valorMinimoNegociacao, false))
    ) {
      setErrors({ ...errors, valorNegociacao: "valor abaixo do minimo" });

      return;
    } else {
      setErrors({ ...errors, valorNegociacao: "" });
    }
    if (parcelamento === 0) {
      setErrors({
        ...errors,
        parcelamento: "selecione a quantidade de parcelas",
      });

      return;
    } else {
      setErrors({ ...errors, parcelamento: "" });
    }

    const valorHO_Float = convertCurrencyToFloat(
      formatCurrency(valorHO, false)
    );

    const valorTotal_Float = convertCurrencyToFloat(
      formatCurrency(valorTotal, false)
    );
    const date = new Date(dataVencimento);
    const year = date.getFullYear();

    const formattedMonth = (date.getMonth() + 1).toString().padStart(2, "0");
    const formattedDay = date.getDate().toString().padStart(2, "0");
    const formatHour = date.getHours().toString().padStart(2, "0");
    const formatMinutes = date.getMinutes().toString().padStart(2, "0");
    const formatSeconds = date.getSeconds().toString().padStart(2, "0");
    const formatMiliseconds = date.getMilliseconds();

    // Calculo para encontrar valor de tarifa por parcela e valor da tarifa da ultima parcela
    const vlTarifaDiv = roundFloat(valorHO_Float / parcelamento);
    const vlTarifaDivTotal = roundFloat(vlTarifaDiv * parcelamento);
    const vlTarifaDivDiff = roundFloat(valorHO_Float - vlTarifaDivTotal);
    const vlTarifaDivLast = roundFloat(vlTarifaDiv + vlTarifaDivDiff);

    const fluxo = [] as simulateInstallmentPayload[];
    for (let i = 1; i <= parcelamento; i++) {
      if (i === parcelamento) {
        fluxo.push({
          nrParcela: i,
          valorDespesa: 0,
          valorTarifa: vlTarifaDivLast,
        });
      } else {
        fluxo.push({
          nrParcela: i,
          valorDespesa: 0,
          valorTarifa: vlTarifaDiv,
        });
      }
    }

    setPayload({
      idContrato: contrato.id_Contrato,
      contract: contrato.numero_Contrato,
      product: BBCContext.produtoSelecionado.product,
      taxa: BBCContext.produtoSelecionado.taxa,
      parcelas: fluxo,
      parcelasContrato: BBCContext.contratoNegociar.parcelasSelecionadas?.map(
        (e) => Number.parseInt(e.numParc)
      ),
      dtVenc: `${year}-${formattedMonth}-${formattedDay}T${formatHour}:${formatMinutes}:${formatSeconds}.${formatMiliseconds}Z`,
      valor: valorTotal_Float,
      valorDespesaReembolsavel: 0,
      valorTarifaHonorario: valorHO_Float,
    });
  }, [dataVencimento, valorTotal, parcelamento]);

  const porcentagemDoValor = (valor: number, porcentagem: number) => {
    let percentValue = (convertCurrencyToFloat(valor) * porcentagem) / 100;
    return percentValue;
  };

  const handleCheckParcelas = (item) => {
    // if (item.length > Elegibilidade.parcelaFinal) return;
    setErrors({ ...errors, maxParcelas: "" });

    let tempValorBase = 0;
    let tempValorBaseParcelasVencidas = 0;
    let tempValorMulta = 0;
    let tempValorMora = 0;

    // Validando o maximo de parcelas selecionadas
    // if(item.length > elegibilidade.parcelaFinal){
    //   setErrors({...errors, maxParcelas: `Você ultrapassou o numero maximo de parcelas permitidas ${elegibilidade.parcelaFinal}`})
    //   return;
    // }

    item.forEach((element) => {
      tempValorMulta += convertCurrencyToFloat(element.multa);
      tempValorMora += convertCurrencyToFloat(element.mora);
      tempValorBase += convertCurrencyToFloat(element.valor);

      if (element.atraso > 0) {
        tempValorBaseParcelasVencidas += convertCurrencyToFloat(element.valor);
      }
    });
    verifDiscount(item);

    setValorMulta(formatCurrency(tempValorMulta));
    setValorMora(formatCurrency(tempValorMora));
    setValorBase(tempValorBase);
    setValorBaseParcelasVencidas(tempValorBaseParcelasVencidas);

    BBCContext.setContratoNegociar({
      ...BBCContext.contratoNegociar,
      parcelasSelecionadas: item,
    });
  };

  // Atualizando o valor
  useEffect(() => {
    const valorBaseComDesconto =
      valorBase - (valorBase * porcentagemDescontoPrincipal) / 100;
    const valorBaseParcelasVencidasComDesconto =
      valorBaseParcelasVencidas -
      (valorBaseParcelasVencidas * porcentagemDescontoPrincipal) / 100;
    //Calculando valor de H.O (Para o minimo)

    const multa = convertCurrencyToFloat(valorMulta);
    const mora = convertCurrencyToFloat(valorMora);
    const descontoMulta = porcentagemDoValor(
      formatCurrency(multa, false),
      porcentagemDescontoMulta
    );
    const descontoMora = porcentagemDoValor(
      formatCurrency(mora, false),
      porcentagemDescontoMora
    );
    // const tempValorBaseNegociacao =
    //   valorBaseComDesconto +
    //   valorHO +
    //   (multa - descontoMulta) +
    //   (mora - descontoMora);

    setValorMultaPagar(formatCurrency(multa - descontoMulta));
    setValorMoraPagar(formatCurrency(mora - descontoMora));
    // Setando um valor sugerido, para a negociação Valor de Base + Porcentagem de H.O + Multa + Mora
    setValorMinimoNegociacao(
      valorBaseComDesconto + (multa - descontoMulta) + (mora - descontoMora)
    );
    const valorNeg =
      valorBaseComDesconto + (multa - descontoMulta) + (mora - descontoMora);
    setValorNegociacao(valorNeg);

    const valorHO = (valorNeg * porcentagemHO) / 100;
    setValorHO(formatCurrency(valorHO));

    setValorTotal(valorNeg + valorHO);
    // setValorBaseNegociacao(tempValorBaseNegociacao);
    // setValorNegociacao(tempValorBaseNegociacao);
  }, [
    valorBase,
    porcentagemHO,
    porcentagemDescontoMulta,
    porcentagemDescontoPrincipal,
    porcentagemDescontoMora,
  ]);

  useEffect(() => {
    const vHO = (valorNegociacao * porcentagemHO) / 100;
    setValorTotal(valorNegociacao + vHO);
    setValorHO(formatCurrency(vHO));
  }, [valorNegociacao]);

  useEffect(() => {
    verifInstallment(0);
  }, [valorTotal]);

  useEffect(() => {
    let discAtt = 0;
    let discQtc = 0;
    let discDivAtt = 0;
    let discDivQtc = 0;
    if (percPrincipal.length > 0) {
      for (const item of percPrincipal) {
        discAtt += item.discountAtt;
        discQtc += item.discountQtc;
      }
      discDivAtt = discAtt / percPrincipal.length;
      discDivQtc = discQtc / percPrincipal.length;
    }
    setPercPrincipalQtc(discDivQtc);
    setPercPrincipalAtt(discDivAtt);
    const dis = parcelamento === 1 ? percPrincipalQtc : percPrincipalAtt;
    setMaxDiscount(dis);
    if (porcentagemDescontoPrincipal > dis) {
      setPorcentagemDescontoPrincipal(dis);
      return;
    }
  }, [percPrincipal, parcelamento]);

  const columns = [
    { key: "numParc", label: "Parcela", defaultSortColumn: true },
    { key: "dtVcto", label: "Data Vencimento" },
    { key: "atraso", label: "Atraso" },
    { key: "valor", label: "Valor" },
    { key: "pmt", label: "PMT" },
    { key: "mora", label: "Mora" },
    { key: "multa", label: "Multa" },
    { key: "iof", label: "IOF" },
    { key: "saldoCurva", label: "Saldo Curva" },
  ];

  const handleParcelamentoChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = parseInt(
      event.currentTarget.value === "" ? "0" : event.currentTarget.value
    );
    if (isNaN(value) || value < 0) return;

    if (verifInstallment(value)) {
      setParcelamento(value);
    }
  };

  const handlePercPrincipalChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const percPrincipalComJuros =
      parcelamento === 1 ? percPrincipalQtc : percPrincipalAtt;
    if (Number(event.currentTarget.value) > percPrincipalComJuros) {
      setPorcentagemDescontoPrincipal(percPrincipalComJuros);
      return;
    }
    setPorcentagemDescontoPrincipal(Number(event.currentTarget.value));
  };

  return (
    <CModal
      className="custom-modal"
      size="xl"
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
    >
      <CModalHeader closeButton>
        <CModalTitle>Negociação</CModalTitle>
      </CModalHeader>
      <CModalBody>
        {loading && loadingAction === "VarifyParam" ? (
          <CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />
        ) : (
          ""
        )}
        {contratoSimulado != null && !loading ? (
          <FormularioSimulacao
            onClickNovaSimulacao={() => {
              setContratoSimulado(null);
              BBCContext.resetaSimulacao();
            }}
            onFinshAcordo={onClose}
            contrato={contratoSimulado}
          />
        ) : (
          ""
        )}
        {contratoSimulado == null && !loading ? (
          <CRow>
            <CCol xs="8">
              {/* Informações "Fixas" */}
              <CRow className="d-none">
                <CCol>
                  <CLabel>idContrato</CLabel>
                  <CInput readOnly type="text" value={contrato.id_Contrato} />
                </CCol>

                <CCol>
                  <CLabel>contract</CLabel>
                  <CInput
                    readOnly
                    type="text"
                    value={contrato.numero_Contrato}
                  />
                </CCol>

                <CCol>
                  <CLabel>product</CLabel>
                  <CInput
                    readOnly
                    type="text"
                    value={BBCContext.produtoSelecionado["product"]}
                  />
                </CCol>

                <CCol>
                  <CLabel>taxa</CLabel>
                  <CInput
                    readOnly
                    type="text"
                    value={BBCContext.produtoSelecionado["taxa"]}
                  />
                </CCol>
              </CRow>
              {/* Informações "Fixas" */}

              <CRow className={"mt-2"}>
                <CCol md="4">
                  <CLabel>Valor Multa</CLabel>
                  <CInput readOnly type="text" value={valorMulta} />
                </CCol>

                <CCol md="2">
                  <CLabel>% Desc Multa</CLabel>
                  <CInput
                    type="number"
                    min={0}
                    max={Elegibilidade.percMulta}
                    value={porcentagemDescontoMulta}
                    onChange={(event) => {
                      if (event.currentTarget.value > Elegibilidade.percMulta) {
                        setPorcentagemDescontoMulta(Elegibilidade.percMulta);
                        return;
                      }
                      setPorcentagemDescontoMulta(event.currentTarget.value);
                    }}
                  />
                </CCol>

                <CCol md="4">
                  <CLabel>Multa a Ser Paga</CLabel>
                  <CInput readOnly type="text" value={valorMultaPagar} />
                </CCol>
              </CRow>
              <CRow className={"mt-2"}>
                <CCol md="4">
                  <CLabel>Valor Mora</CLabel>
                  <CInput readOnly type="text" value={valorMora} />
                </CCol>

                <CCol md="2">
                  <CLabel>% Desc Mora</CLabel>
                  <CInput
                    type="number"
                    min={0}
                    max={Elegibilidade.percMora}
                    value={porcentagemDescontoMora}
                    onChange={(event) => {
                      if (event.currentTarget.value > Elegibilidade.percMora) {
                        setPorcentagemDescontoMora(Elegibilidade.percMora);
                        return;
                      }
                      setPorcentagemDescontoMora(event.currentTarget.value);
                    }}
                  />
                </CCol>

                <CCol md="4">
                  <CLabel>Mora a Ser Paga</CLabel>
                  <CInput readOnly type="text" value={valorMoraPagar} />
                </CCol>
              </CRow>
              {/* <div style={{ padding: "20px" }}>
                <b>Valor Base:</b> {formatCurrency(valorBase)} (Somando o valor
                de todas as parcelas + Mora)
                <br />
                <b>Valor Base Com Desconto:</b>{" "}
                {formatCurrency(
                  valorBase - (valorBase * porcentagemDescontoPrincipal) / 100
                )}
                <br />
                <b>Valor Base Parcelas Vencidas:</b>{" "}
                {formatCurrency(valorBaseParcelasVencidas)} (Somando somente
                vencidas + Mora)
                <br />
                <b>Valor Base Parcelas Vencidas Com Desconto:</b>{" "}
                {formatCurrency(
                  valorBaseParcelasVencidas -
                    (valorBaseParcelasVencidas * porcentagemDescontoPrincipal) /
                      100
                )}
                <br />
                <b>Valor Base Negociação:</b> {valorBaseNegociacao} (Valor Base
                (com desconto) + (% H.O sobre parcelas vencidas) + Multa (com
                desconto))
                <br />
              </div> */}

              <CRow className={"mt-2"}>
                <CCol md="3">
                  <CLabel>Valor Minimo Negociação</CLabel>
                  <CInput
                    type="text"
                    value={formatCurrency(valorMinimoNegociacao)}
                    readOnly
                  />
                  {/* {errors.dtPagamentoInicial && <div className="text-danger">{errors.dtPagamentoInicial}</div>} */}
                </CCol>

                <CCol md="3">
                  <CLabel>% Desconto Principal</CLabel>
                  <CInput
                    type="number"
                    value={porcentagemDescontoPrincipal}
                    onChange={handlePercPrincipalChange}
                    min={0}
                    max={maxDiscount}
                  />
                  {/* {errors.dtPagamentoInicial && <div className="text-danger">{errors.dtPagamentoInicial}</div>} */}
                </CCol>
              </CRow>

              <CRow className={"mt-4"}>
                <CCol md="3">
                  <CLabel>Data Vencimento</CLabel>
                  <ReactDatePicker
                    selected={dataVencimento}
                    onChange={(date) => {
                      setDataVencimento(date);
                    }}
                    dateFormat="dd/MM/yyyy"
                    // placeholder="dd/MM/yyyy"
                    placeholderText="dd/mm/yyyy"
                    minDate={new Date()}
                    maxDate={
                      new Date(
                        new Date().getTime() +
                          15 * // Dias que deve vir da API
                            24 *
                            60 *
                            60 *
                            1000
                      )
                    }
                    locale={ptBR}
                    className="form-control input"
                    // className={`form-control input ${errors.dtPagamentoInicial ? 'border-danger rounded' : ''}`}
                  />
                  {/* {errors.dtPagamentoInicial && <div className="text-danger">{errors.dtPagamentoInicial}</div>} */}
                </CCol>

                <CCol md="3">
                  <CLabel>Valor Negociação</CLabel>
                  <CInput
                    type="text"
                    value={formatCurrency(valorNegociacao, false)}
                    onChange={handleValorNegociarChange}
                    onBlur={handleValorNegociarChange}
                    className={`form-control input ${
                      errors.valorNegociacao ? "border-danger rounded" : ""
                    }`}
                  />
                  {errors.valorNegociacao && (
                    <div className="text-danger">{errors.valorNegociacao}</div>
                  )}
                </CCol>
                <CCol md="2">
                  <CLabel>Parcelamento</CLabel>
                  <CInput
                    value={parcelamento}
                    onChange={handleParcelamentoChange}
                  />
                  {errors.parcelamento && (
                    <div className="text-danger">{errors.parcelamento}</div>
                  )}
                </CCol>
              </CRow>

              <CRow className={"mt-2"}>
                <CCol md="2">
                  <CLabel>% H.O</CLabel>
                  <CInput
                    type="number"
                    max={permissions?.isAdmin === true ? 100 : 10}
                    min={permissions?.isAdmin === true ? 0 : 10}
                    readOnly={!permissions?.isAdmin}
                    value={porcentagemHO}
                    onChange={(event) => {
                      setPorcentagemHO(event.currentTarget.value);
                    }}
                  />
                </CCol>

                <CCol md="4">
                  <CLabel>Valor H.O</CLabel>
                  <CInput readOnly type="text" value={valorHO} />
                </CCol>
                <CCol md="4">
                  <CLabel>Valor Total</CLabel>
                  <CInput
                    type="text"
                    value={formatCurrency(valorTotal)}
                    className={`form-control input`}
                    disabled
                  />
                </CCol>
              </CRow>

              <CRow className={"mt-4"}>
                <CCol md="12">
                  {/* <CLabel>Parcelas permitidas para negociação</CLabel> */}
                  {/* <TableParcelasSimulacao dataTable={contrato.dadosBBC.parcelas}/> */}
                  {errors.maxParcelas && (
                    <div className="text-danger">{errors.maxParcelas}</div>
                  )}
                  <TableSelectItens
                    onDoubleClick={() => {}}
                    data={parcelasNegociacao}
                    columns={columns}
                    onSelectionChange={handleCheckParcelas}
                    defaultSelectedKeys={contrato.dadosBBC.parcelas}
                    selectable={true}
                    heightParam="300px"
                  />
                </CCol>
              </CRow>
            </CCol>
            <CCol xs="4">
              <CardCalcular />

              <div style={{ padding: "0px 20px" }}>
                <CRow>
                  <CLabel>
                    Desconto máximo de atualizacão no principal:{" "}
                    {percPrincipalAtt}%
                  </CLabel>
                </CRow>
                <CRow>
                  <CLabel>
                    Desconto máximo de quitação no principal: {percPrincipalQtc}
                    %
                  </CLabel>
                </CRow>
                <CRow>
                  <CLabel>
                    Desconto máximo na multa: {Elegibilidade.percMulta}%
                  </CLabel>
                </CRow>
                <CRow>
                  <CLabel>
                    Desconto máximo na mora: {Elegibilidade.percMora}%
                  </CLabel>
                </CRow>
                <CRow>
                  <CLabel>Parcelamento máximo: {parcelamentoMax}</CLabel>
                </CRow>
              </div>
              {/* <CardCalcular contratoNegociar={contratoNegociado} /> */}
            </CCol>
          </CRow>
        ) : (
          ""
        )}
      </CModalBody>
      {contratoSimulado != null ? (
        ""
      ) : (
        <CModalFooter>
          <CButton
            color="secondary"
            className="mr-2"
            onClick={onClose}
            disabled={loading}
          >
            Cancelar
          </CButton>
          {isObjectEmpty(errors) &&
            dataVencimento !== null &&
            BBCContext.contratoNegociar.parcelasSelecionadas?.length > 0 && (
              <CButton color="info" onClick={handleSimular} disabled={loading}>
                Simular Negociação
              </CButton>
            )}
        </CModalFooter>
      )}
    </CModal>
  );
};

export default FormNegociacaoModal;
