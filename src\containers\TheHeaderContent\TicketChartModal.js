import React, { useEffect, useState } from "react";
import {
  C<PERSON>odal<PERSON>ooter,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CRow,
  CCol,
  CCardBody,
  CButton,
  CInput,
  CLabel,
  CSelect,
  CBadge,
} from "@coreui/react";
import "react-toastify/dist/ReactToastify.css";
import CardLoading from "src/reusable/CardLoading";
import TicketChart from "./TicketChart.tsx";
import { getApi, getApiInline, postApi } from "src/reusable/functions.js";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { GET_DATA, POST_FILE_DATA, POST_FORMDATA } from "src/api.js";
import { getURI } from "src/config/apiConfig.js";
import Select from "react-select";
import TicketDetails from "./TicketDetails.js";
import { toast } from "react-toastify";
import LoadingComponent from "src/reusable/Loading.js";

const bgColor = [
  "#ff6384",
  "#41B883",
  "#ffcd56",
  "#36a2eb",
  "#E46651",
  "#4bc0c0",
  "#00D8FF",
  "#DD1B16",
  "#c9cbcf",
];

const today = new Date();

const PostFormData = async (formData, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_FORMDATA(
        getURI(endpoint),
        formData,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const TicketChartModal = ({ onClose }) => {
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState(false);
  const [tickets, setTickets] = useState([]);
  const [selectDateBegin, setSelectDateBegin] = useState(today);
  const [selectDateEnd, setSelectDateEnd] = useState(today);
  const [selectedClient, setSelectedClient] = useState(null);
  const [selectCrm, setSelectCrm] = useState({
    datacobName: user?.activeConnection,
    datacobNumber: user?.activeConnection,
  });

  const [showDetails, setShowDetails] = useState(false);
  const [selectedTickets, setSelectedTickets] = useState(null);

  const [usersCrm, setUsersCrm] = useState([]);
  const [loadingUsersCrm, setLoadingUsersCrm] = useState(false);
  const [selectedUserCrm, setSelectedUserCrm] = useState(null);

  const [loadingExport, setLoadingExport] = useState(false);

  const getTicketsData = async () => {
    setSearch(true);
    setLoading(true);
    setSelectedClient(null);
    try {
      const result = await GET_DATA(
        getURI("getTicketsUser") +
          `${selectedUserCrm !== null ? "/" + selectedUserCrm.id_Usuario : ""}`,
        {
          crm: selectCrm?.datacobNumber,
          inicio: selectDateBegin.toISOString().substring(0, 10),
          fim: selectDateEnd.toISOString().substring(0, 10),
        },
        true
      );
      if (result.length > 0) {
        const res = [];
        const todos = {
          nomeCliente: "Todos",
          boletos: [],
          dataset: [],
          labels: ["Cancelados", "Pagos", "Abertos"],
        };
        for (let i in result) {
          const ticket = result[i];

          todos.boletos = [...todos.boletos, ...ticket.boletos];

          const item = {
            nomeCliente: "",
            boletos: [],
            dataset: [],
            labels: ["Cancelados", "Pagos", "Abertos"],
          };
          item.nomeCliente = ticket.nomeCliente;
          const cancelados = ticket.boletos.filter(
            (x) => x.statusBoleto === "C"
          ).length;
          const pagos = ticket.boletos.filter(
            (x) => x.statusBoleto === "P"
          ).length;
          const abertos = ticket.boletos.filter(
            (x) => x.statusBoleto === "A"
          ).length;
          item.dataset.push({
            backgroundColor: bgColor,
            data: [cancelados, pagos, abertos],
          });
          item.boletos = ticket.boletos;
          res.push(item);
        }

        const canceladosTodos = todos.boletos.filter(
          (x) => x.statusBoleto === "C"
        ).length;
        const pagosTodos = todos.boletos.filter(
          (x) => x.statusBoleto === "P"
        ).length;
        const abertosTodos = todos.boletos.filter(
          (x) => x.statusBoleto === "A"
        ).length;

        todos.dataset.push({
          backgroundColor: bgColor,
          data: [canceladosTodos, pagosTodos, abertosTodos],
        });
        setTickets([todos, ...res]);
      } else {
        setTickets([]);
      }
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const getUsersTu = async () => {
    setLoadingUsersCrm(true);
    try {
      const res = await getApi(
        {
          activeConnection: selectCrm?.datacobNumber,
        },
        "getDatacobUsers"
      );
      setUsersCrm(res === null ? [] : res);
    } catch (e) {
      setUsersCrm([]);
      console.error(e);
    }
    setLoadingUsersCrm(false);
  };

  useEffect(() => {
    if (user?.isAdmin === true) {
      getUsersTu();
    }
  }, [selectCrm]);

  const handleShowDetails = (selTickets, status = "") => {
    if (status !== "") {
      const filter = selTickets.boletos.filter(
        (x) => x.statusBoleto === status
      );
      setSelectedTickets(filter);
    } else {
      setSelectedTickets(selTickets?.boletos);
    }
    setShowDetails(true);
  };

  const handleCloseDetails = () => {
    setSelectedTickets(null);
    setShowDetails(false);
  };

  const handleSelectCrm = (e) => {
    setSelectCrm(e);
  };

  const handleExportPdf = async (e) => {
    setLoadingExport(true);
    try {
      const url = getURI("postTicketsUserPdf");
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
        body: JSON.stringify(selectedTickets),
      });

      // Cria uma URL para o Blob
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "boletos.pdf";
        document.body.appendChild(link); // Necessário para o Firefox
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url); // Limpa o objeto URL
      } else {
        toast.warning("Nenhum resultado encontrado!");
      }
    } catch (error) {
      console.log(error);
    }
    setLoadingExport(false);
  };

  const handleExportExcel = async (e) => {
    setLoadingExport(true);
    try {
      const url = getURI("postTicketsUserExcel");
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
        body: JSON.stringify(selectedTickets),
      });

      // Cria uma URL para o Blob
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "boletos.xlsx";
        document.body.appendChild(link); // Necessário para o Firefox
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url); // Limpa o objeto URL
      } else {
        toast.warning("Nenhum resultado encontrado!");
      }
    } catch (error) {
      console.log(error);
    }
    setLoadingExport(false);
  };

  return (
    <CModal
      show={true}
      onClose={onClose}
      className={"custom-modal-2"}
      closeOnBackdrop={false}
      size="xl"
    >
      <CModalHeader>
        <CModalTitle>Visão de Boletos</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CRow>
          {!showDetails && (
            <CCol>
              <CCardBody className="py-1">
                <CRow>
                  <CCol md={2}>
                    <CLabel>Data Inicio</CLabel> <br />
                    <ReactDatePicker
                      selected={selectDateBegin}
                      onChange={(e) => setSelectDateBegin(e)}
                      className="form-control w-100"
                      dateFormat="dd/MM/yyyy"
                      onKeyDown={(e) => e.preventDefault()}
                    />
                  </CCol>
                  <CCol md={2}>
                    <CLabel>Data Fim</CLabel> <br />
                    <ReactDatePicker
                      selected={selectDateEnd}
                      onChange={(e) => setSelectDateEnd(e)}
                      className="form-control"
                      dateFormat="dd/MM/yyyy"
                      onKeyDown={(e) => e.preventDefault()}
                    />
                  </CCol>
                  {user?.datacobs?.length > 1 && (
                    <CCol md={2} style={{ color: "black" }}>
                      <CLabel>CRM</CLabel> <br />
                      <Select
                        options={user.datacobs}
                        onChange={handleSelectCrm}
                        getOptionLabel={(opt) => opt.datacobNumber}
                        getOptionValue={(opt) => opt.datacobNumber}
                        placeholder={"Selecione"}
                        value={selectCrm}
                        height="500px"
                      />
                    </CCol>
                  )}
                  {user?.isAdmin === true && (
                    <CCol md={3} style={{ color: "black" }}>
                      {loadingUsersCrm ? (
                        <div style={{ color: "black" }}>
                          <CardLoading />
                        </div>
                      ) : (
                        <>
                          <CLabel>Usuário</CLabel> <br />
                          <Select
                            options={usersCrm}
                            value={selectedUserCrm}
                            getOptionLabel={(opt) => opt.nome}
                            getOptionValue={(opt) => opt.id_Usuario}
                            onChange={(e) => setSelectedUserCrm(e)}
                            placeholder={"Selecione"}
                          />
                        </>
                      )}
                    </CCol>
                  )}
                  <CCol md={2}>
                    <CButton
                      color="primary"
                      style={{ marginTop: "31px" }}
                      onClick={getTicketsData}
                    >
                      Buscar
                    </CButton>
                  </CCol>
                </CRow>
                <br />
                {search && (
                  <>
                    {loading ? (
                      <div style={{ color: "black" }}>
                        <CardLoading />
                      </div>
                    ) : (
                      <>
                        {tickets.length > 0 && (
                          <>
                            <div style={{ color: "black" }}>
                              <Select
                                options={tickets}
                                onChange={(e) => setSelectedClient(e)}
                                getOptionLabel={(option) => option.nomeCliente}
                                getOptionValue={(opt) => opt.nomeCliente}
                                placeholder={"Selecione o cliente..."}
                              />
                            </div>
                            <br />
                            {selectedClient && (
                              <>
                                <CRow>
                                  <CCol md={8}>
                                    <TicketChart
                                      Datasets={selectedClient.dataset}
                                      Labels={selectedClient.labels}
                                      displayTitle={true}
                                      legendPosition="bottom"
                                      title={selectedClient.nomeCliente}
                                    />
                                  </CCol>
                                  <CCol md={4} className={"d-flex flex-column"}>
                                    <CButton
                                      color="danger"
                                      className="mr-2 mt-2"
                                      onClick={() =>
                                        handleShowDetails(selectedClient, "C")
                                      }
                                    >
                                      {selectedClient.dataset[0].data[0]}{" "}
                                      Cancelados
                                    </CButton>
                                    <CButton
                                      color="success"
                                      className="mr-2 mt-2"
                                      onClick={() =>
                                        handleShowDetails(selectedClient, "P")
                                      }
                                    >
                                      {selectedClient.dataset[0].data[1]} Pagos
                                    </CButton>
                                    <CButton
                                      color="warning"
                                      className="mr-2 mt-2"
                                      onClick={() =>
                                        handleShowDetails(selectedClient, "A")
                                      }
                                    >
                                      {selectedClient.dataset[0].data[2]}{" "}
                                      Abertos
                                    </CButton>
                                    <CButton
                                      color="primary"
                                      className="mr-2 mt-2"
                                      onClick={() =>
                                        handleShowDetails(selectedClient)
                                      }
                                    >
                                      {selectedClient.boletos.length} Total
                                    </CButton>
                                  </CCol>
                                </CRow>
                              </>
                            )}
                          </>
                        )}
                        {(tickets === null || tickets.length === 0) && (
                          <div className="text-center">
                            <h3>Nenhum boleto encontrado</h3>
                          </div>
                        )}
                      </>
                    )}
                  </>
                )}
              </CCardBody>
            </CCol>
          )}
          {showDetails && selectedTickets !== null && (
            <TicketDetails
              tickets={selectedTickets}
              onClose={handleCloseDetails}
            />
          )}
        </CRow>
      </CModalBody>
      <CModalFooter className={"justify-content-center"}>
        {showDetails && selectedTickets !== null && (
          <CRow>
            {loadingExport ? (
              <div style={{ color: "black" }}>
                <LoadingComponent />
              </div>
            ) : (
              <>
                <CButton color="secondary" onClick={handleCloseDetails}>
                  Voltar
                </CButton>
                <CButton
                  color="info"
                  className={"ml-2"}
                  onClick={handleExportPdf}
                >
                  Exportar PDF
                </CButton>
                <CButton
                  color="primary"
                  className={"ml-2"}
                  onClick={handleExportExcel}
                >
                  Exportar Excel
                </CButton>
              </>
            )}
          </CRow>
        )}
        {!showDetails && selectedTickets === null && (
          <CButton color="secondary" onClick={onClose}>
            Fechar
          </CButton>
        )}
      </CModalFooter>
    </CModal>
  );
};

export default TicketChartModal;
