import {
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CButton,
  CForm,
  CFormGroup,
  CLabel,
  CInput,
  CImg,
} from "@coreui/react";
import { useState, useRef } from "react";

const EditProfileModal = ({ onClose }) => {
  const [userProfile] = useState(
    JSON.parse(localStorage.getItem("user")).name
  );

  const [name, setName] = useState(userProfile?.firstName);
  // const [lastName, setLastName] = useState(userProfile?.lastName);
  const [profileImage, setProfileImage] = useState(userProfile?.profileImage);
  const defaultImage = "/avatars/default.jpg"

  const fileInputRef = useRef(null);

  const handleNameChange = (event) => {
    setName(event.target.value);
  };

  // const handleSurnameChange = (event) => {
  //   setLastName(event.target.value);
  // };

  const handleImageChange = (event) => {
    // No momento não faz nenhuma checagem do tamanho do arquivo.
    const file = event.target.files[0];
    const reader = new FileReader();

    reader.onload = () => {
      const imageData = reader.result;
      setProfileImage(imageData);
    };

    if (file) {
      reader.readAsDataURL(file);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current.click();
  };

  const handleSave = () => {
    const updatedUserProfile = {
      // firstName: firstName,
      firstName: (name.trim() !== '') ? name : userProfile.name,
      // lastName:  (lastName.trim() !== '') ? lastName : userProfile.lastName,
      // lastName: lastName,
      profileImage: profileImage,
    };

    // Update the localStorage with the updated user data
    const currentUserProfile = JSON.parse(localStorage.getItem("userProfile"));
    const updatedUser = {
      ...currentUserProfile,
      ...updatedUserProfile,
    };
    localStorage.setItem("userProfile", JSON.stringify(updatedUser));

    window.location.reload();
    onClose(); 
  };

  return (
    <CModal show={true} onClose={onClose} size="sm">
      <CModalHeader style={{ color: "darkcyan" }} closeButton>
        {" "}
        <strong>Editar Perfil</strong>
      </CModalHeader>
      <CModalBody>
        <div className="d-flex align-items-center justify-content-center mb-4">
          <div className="mr-3">
            {/* Display user avatar */}
            {/* <img src="public/avatars/1.jpg" alt="User Avatar" className="avatar" /> */}
            <CImg
              src={profileImage ? profileImage : defaultImage}
              // className="c-avatar-img"
              alt={"User Profile Image"}
              className="rounded-circle me-2"
              width="70"
              height="70"
            />
          </div>
          <div>
            <CLabel style={{ color: "black" }}>
              {" "}
              <strong>Edite sua foto: </strong>
            </CLabel>
            <p style={{ color: "gray", fontSize: "small" }}>
              Envie arquivos de até 5 MB
            </p>
            <input
              type="file"
              accept="image/*"
              style={{ display: "none" }}
              ref={fileInputRef}
              onChange={handleImageChange}
            />
            <CButton color="info" onClick={handleUploadClick}>
              <i className="cil-camera" /> Upload
            </CButton>
          </div>
        </div>
        <CForm>
          <CFormGroup>
            <CLabel htmlFor="name" style={{ color: "black" }}>
              <strong>Nome:</strong>
            </CLabel>
            <CInput
              type="text"
              id="name"
              placeholder={
                userProfile.name
                // userProfile?.firstName ? userProfile?.firstName : "Nome"
              }
              onChange={handleNameChange}
            />
          </CFormGroup>
        </CForm>
        {/* <CForm>
          <CFormGroup>
            <CLabel htmlFor="lastName" style={{ color: "black" }}>
              <strong> Sobrenome:</strong>
            </CLabel>
            <CInput
              type="text"
              id="lastName"
              placeholder={
                userProfile?.lastName ? userProfile?.lastName : "Sobrenome"
              }
              onChange={handleSurnameChange}
            />
          </CFormGroup>
        </CForm> */}
        <div className="d-flex justify-content-end">
          <CButton className="mt-2" size="sm" color="info" onClick={handleSave}>
            <i className="cil-user" /> Salvar perfil
          </CButton>
        </div>
      </CModalBody>
    </CModal>
  );
};

export default EditProfileModal;
