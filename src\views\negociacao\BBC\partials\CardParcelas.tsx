import React from "react";
/* {
    numParc: '001',
    dtVcto: '29/05/2024',
    valor: '3.777,23',
    pmt: '3.777,23',
    mora: '196,74',
    multa: '75,54',
    iof: '0,00',
    saldo<PERSON>urva: '4.049,51'
} */
const CardParcelas = ({dataParcelas}) => {
  
  return (
    <div className="table-responsive">
      <table className='table'>
        <thead>
          <tr>
              <th>Parcela</th>
              <th>Data Vencimento</th>
              <th>Valor</th>
              <th>PMT</th>
              <th>Mora</th>
              <th>Multa</th>
              <th>IOF</th>
              <th>Saldo Curva</th>
          </tr>
        </thead>
        <tbody>
          {dataParcelas.map((item, index) => {
            return (
              <tr key={index}>
                <td>{item.numParc}</td>
                <td>{item.dtVcto}</td>
                <td>{item.valor}</td>
                <td>{item.pmt}</td>
                <td>{item.mora}</td>
                <td>{item.multa}</td>
                <td>{item.iof}</td>
                <td>{item.saldoCurva}</td>
              </tr>
            )
          })}
        </tbody>
      </table>
    </div>
  );
};

export default CardParcelas;
