import React, { createContext, useContext, useState } from "react";

const MyContext = createContext();

export const ContextProvider = ({ children }) => {
  const [data, setData] = useState(null);
  const [contratos, setContratos] = useState(null);
  const [custas, setCustas] = useState(null);
  const [custasProjuris, setCustasProjuris] = useState(null);
  const [appSettings, setAppSettings] = useState(null);
  const [processos, setProcessos] = useState(null);
  const [boletos, setBoletos] = useState(null);

  const updateData = (newData) => {
    resetData();
    setData(newData);
  };

  const resetData = () => {
    updateAppSettings({
      juridico: {
        showProcessos: false,
      },
      showVisaoBoleto: false,
    });
    updateCustas(null);
    updateCustasProjuris(null);
    updateProcessos(null);
  };

  const updateContratos = (newContratos) => {
    setContratos(newContratos);
  };

  const updateBoletos = (newBoletos) => {
    localStorage.setItem("boletos", JSON.stringify(newBoletos));
    setBoletos(newBoletos);
  };

  const updateCustas = (newCustas) => {
    localStorage.setItem("custas", JSON.stringify(newCustas));
    setCustas(newCustas);
  };

  const updateCustasProjuris = (newCustasProjuris) => {
    localStorage.setItem("custasProjuris", JSON.stringify(newCustasProjuris));
    setCustasProjuris(newCustasProjuris);
  };

  const updateAppSettings = (newAppSettings) => {
    localStorage.setItem("appSettings", JSON.stringify(newAppSettings));
    setAppSettings(newAppSettings);
  };

  const updateProcessos = (newProcessos) => {
    localStorage.setItem("processos", JSON.stringify(newProcessos));
    setProcessos(newProcessos);
  };

  return (
    <MyContext.Provider
      value={{
        data,
        updateData,
        custas,
        updateCustas,
        custasProjuris,
        updateCustasProjuris,
        contratos,
        updateContratos,
        boletos,
        updateBoletos,
        appSettings,
        updateAppSettings,
        processos,
        updateProcessos,
      }}
    >
      {children}
    </MyContext.Provider>
  );
};

export const useMyContext = () => {
  const context = useContext(MyContext);
  if (!context) {
    throw new Error("useMyContext must be used within a ContextProvider");
  }
  return context;
};
