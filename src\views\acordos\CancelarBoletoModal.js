import React, { useState } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CButton,
  CFormGroup,
  CLabel,
  CInput,
  CInputGroup,
} from "@coreui/react";
import { DELETE_DATA } from "src/api";
import Select from "react-select";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useAuth } from 'src/auth/AuthContext';

const CancelarBoletoModal = ({ isOpen, onClose, onSubmit, boleto }) => {
  const { checkPermission, inforPermissions } = useAuth();

  const permissaoNegociacaoBoleto = {
    modulo: "Negociação",
    submodulo: "Boleto",
  }

  const [selectedMotivo, setSelectedMotivo] = useState("");
  const [observation, setObservation] = useState("");

  const options = [
    { label: "Cancelado pela API", value: "Cancelado pela API" },
    { label: "Cancelado pela recepção", value: "Cancelado pela recepção" },
    { label: "Cancelamento automático", value: "Cancelamento automático" },
    { label: "Cancelamento via Kitado", value: "Cancelamento via Kitado" },
    { label: "Cancelar Boleto Vencido", value: "Cancelar Boleto Vencido" },
    { label: "Descrição", value: "Descrição" },
    { label: "Erro Registrar Boleto", value: "Erro Registrar Boleto" },
    { label: "Outros Motivos", value: "Outros Motivos" },
  ];

  const handleMotivoChange = (selectedOption) => {
    setSelectedMotivo(selectedOption);
  };

  const handleObservationChange = (event) => {
    setObservation(event.target.value);
  };

  const handleCancelar = async () => {
    const data = { id: boleto, motivo: selectedMotivo.value };
    const result = await DELETE_DATA(`Datacob/CancelarBoletoId`, data);
    // const data = {
    //   banco: boleto.banco,
    //   nrBoleto: boleto.nr_Boleto,
    //   motivo: selectedMotivo.value,
    // };
    // const result = await DELETE_DATA(`Datacob/CancelarBoleto`, data);
    if (result?.success) {
      toast.success(result.data);
      onSubmit(true)
    } else {
      const messageArray = JSON.parse(result?.message);
      const message = messageArray[0];
      if (message?.startsWith("O")) {
        toast.warning("O boleto já está cancelado!");
      } else if (message?.startsWith("Este")) {
        toast.info(
          "O Boleto selecionado já possui pagamento registrado! Por gentileza, entrar em contato com a gerência para cancelamento deste boleto.  "
        );
      } else {
        toast.info(result?.message);
      }
    }
    handleClose();
  };

  const handleClose = () => {
    setSelectedMotivo("");
    setObservation("");
    onClose();
  };

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Cancelar boleto?</CModalHeader>
      <CModalBody>
        <CRow>
          <CCol>
            <CFormGroup className="d-flex align-items-end nowrap">
              <CLabel className="mr-4">Motivo: </CLabel>{" "}
              <div className="flex-grow-1">
                <Select
                  options={options}
                  value={selectedMotivo}
                  onChange={handleMotivoChange}
                  placeholder={"Selecione"}
                />
              </div>
            </CFormGroup>
            <CFormGroup className="d-flex align-items-end">
              <CLabel className="mr-2">Descrição: </CLabel>
              <CInputGroup>
                <CInput
                  type="text"
                  value={observation}
                  onChange={(e) => {
                    handleObservationChange(e);
                  }}
                />
              </CInputGroup>
            </CFormGroup>
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={handleClose}>
          Fechar
        </CButton>
        <CButton color="danger" onClick={handleCancelar}
          title={inforPermissions(permissaoNegociacaoBoleto).delete}
          disabled={!checkPermission(permissaoNegociacaoBoleto.modulo, "Delete", permissaoNegociacaoBoleto.submodulo)}
        >
          Cancelar boleto
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default CancelarBoletoModal;
