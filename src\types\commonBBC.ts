export type TypeSelect = {
    value: string,
    label: string
}

export type SearchContract = {
    id_Contrato: number
}

export type ContratoBBC = {
    contrato:number
}

export type BBCConsultaSaldo = {
    atraso: number,
    numParc: string,
    dtVcto: string,
    iof: string,
    mora: string,
    multa: string,
    pmt: string,
    saldoCurva: string,
    valor: string,
}

export type BBCElegibilidade = {
    descIOF: boolean,
    maxAtraso: number,
    minAtraso: number,
    parcelaFinal: number,
    parcelaInicial: number,
    percDesp: number,
    percMora: number,
    percMulta: number,
    percPrincipalComJuros: number,
    percTLA: number,
}

export type BBCFinanciadoCriar = {
    id: string
}
export type BBCAcordo = {
    situacaoProposta: string,
    atividade: number,
    numeroAcordo: string,
    numeroOperacao: string,
    idAcordo: string
  }
