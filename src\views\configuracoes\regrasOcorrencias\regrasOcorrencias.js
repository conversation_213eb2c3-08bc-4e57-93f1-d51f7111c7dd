import React, { useState, useEffect } from "react";
import {
  CButton,
  CForm,
  CFormGroup,
  CInput,
  CCard,
  CCardBody,
  CLabel,
  CCol,
  CInputRadio,
  CRow,
  CInputCheckbox,
  CInvalidFeedback,
} from "@coreui/react";
import { GET_DATA, POST_DATA, PUT_DATA, DELETE_DATA } from "src/api";
import ConfirmModal from "src/reusable/ConfirmModal";
import TableSelectItens from "src/reusable/TableSelectItens";
import { formatDateTime, isEmailValid } from "src/reusable/helpers";
import CardLoading from "src/reusable/CardLoading";
import { checkPermission } from "src/reusable/CheckPermission";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useAuth } from "src/auth/AuthContext";
import Select from "react-select";
import LoadingComponent from "src/reusable/Loading";

const STD_FIELD_VALUE = {
  contract: false,
  name: false,
  document: false,
  activeEmail: false,
  activePhone: false,
  balance: false,
  originalValue: false,
  updatedValue: false,
  openInstallments: false,
};

const RegrasOcorrencias = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Regras Ocorrências",
    submodulo: null,
  };

  const [data, setData] = useState([]);
  const optionCrm = [
    { value: 0, label: "GVC" },
    { value: 1, label: "Rodobens" },
  ];
  const [selectedCrm, setSelectedCrm] = useState(null);
  const [optionsOcc, setOptionsOcc] = useState([]);
  const [selectedOcc, setSelectedOcc] = useState(null);
  const [sendEmail, setSendEmail] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [fields, setFields] = useState(STD_FIELD_VALUE);
  const [customFields, setCustomFields] = useState([""]);
  const [emails, setEmails] = useState([""]);
  const [invalidEmail, setInvalidEmail] = useState(false);
  const [element, setElement] = useState(null);
  const [isLoadingSave, setIsLoadingSave] = useState(false);

  const [filterCodOcc, setFilterCodOcc] = useState(null);
  const [filterSendEmail, setFilterSendEmail] = useState(null);
  const [filterCrm, setFilterCrm] = useState(null);

  async function getValues(occ = null, crm = null) {
    setIsLoading(true);
    const regras = await GET_DATA(`OccurrenceRules`);
    if (regras !== null && regras !== undefined) {
      setData(regras);
      if (occ !== null && crm !== null) {
        const crmString = crm === 0 ? "GVC" : "Rodobens";
        const el = regras.find(
          (x) => parseInt(x.codOcc) === parseInt(occ) && x.crm === crmString
        );
        if (el !== undefined && el !== null) {
          showToEdit(el, regras);
        }
      }
    } else {
      toast.error("Falha ao buscar regras ocorrências");
      setData([]);
    }
    setIsLoading(false);
    return;
  }

  const getOcc = async (occ) => {
    setIsLoading(true);
    let ret = [];
    const tiposOcorrencia = await GET_DATA(`Datacob/Ocorrencias/${occ?.value}`);
    if (tiposOcorrencia !== null && tiposOcorrencia !== undefined) {
      const options = [
        ...tiposOcorrencia.map((item) => {
          return {
            label: item.cod_Ocorr_Sistema + " - " + item.descricao,
            value: item.id_Ocorrencia_Sistema,
            cod_Ocorr_Sistema: item.cod_Ocorr_Sistema,
          };
        }),
      ];
      ret = options;
      setOptionsOcc(options);
      setSelectedOcc(null);
      cleanInputs();
    } else {
      toast.error("Falha ao buscar ocorrências");
      setOptionsOcc([]);
      setSelectedCrm(null);
      setSelectedOcc(null);
      cleanInputs();
    }
    setIsLoading(false);
    return ret;
  };

  const handleAdd = async () => {
    const data = {
      codOcc: selectedOcc.cod_Ocorr_Sistema,
      send: sendEmail,
      contract: fields.contract,
      name: fields.name,
      document: fields.document,
      activeEmail: fields.activeEmail,
      activePhone: fields.activePhone,
      balance: fields.balance,
      originalValue: fields.originalValue,
      updatedValue: fields.updatedValue,
      openInstallments: fields.openInstallments,
      crm: selectedCrm?.value,
      occurrenceRulesEmails: emails
        .map((item) => {
          if (item !== "") return { email: item };
          return null;
        })
        .filter((item) => item !== null),
      occurrenceRulesCustemFields: customFields
        .map((item, index) => {
          if (item !== "") return { field: item, order: index + 1 };
          return null;
        })
        .filter((item) => item !== null),
    };
    const postSuccess = await POST_DATA("OccurrenceRules", data);
    if (postSuccess?.success) {
      toast.success("Regra de ocorrência criada com sucesso.");
      await getValues(selectedOcc.cod_Ocorr_Sistema, selectedCrm?.value);
    } else {
      toast.error("Erro ao criar regra de ocorrência");
    }
  };

  const handleEdit = async () => {
    const data = {
      id: element.id,
      send: sendEmail,
      contract: fields.contract,
      name: fields.name,
      document: fields.document,
      activeEmail: fields.activeEmail,
      activePhone: fields.activePhone,
      balance: fields.balance,
      originalValue: fields.originalValue,
      updatedValue: fields.updatedValue,
      openInstallments: fields.openInstallments,
      occurrenceRulesEmails: emails
        .map((item) => {
          if (item !== "") return { email: item };
          return null;
        })
        .filter((item) => item !== null),
      occurrenceRulesCustemFields: customFields
        .map((item, index) => {
          if (item !== "") return { field: item, order: index + 1 };
          return null;
        })
        .filter((item) => item !== null),
    };
    const updateSuccess = await PUT_DATA("OccurrenceRules", data);
    if (updateSuccess.success) {
      toast.success("Regra de ocorrência alterada com sucesso.");
      await getValues();
    } else {
      toast.error("Erro ao alterar regra de ocorrência");
    }
  };

  const handleSave = async () => {
    if (invalidEmail) {
      toast.error("E-mail inválido");
      return;
    }
    if (emails?.filter((item) => item !== "").length < 1 && sendEmail) {
      toast.error("É necessário pelo menos um e-mail");
      return;
    }
    if (selectedOcc === null) {
      toast.error("É necessário selecionar uma ocorrência");
      return;
    }
    setIsLoadingSave(true);
    if (element !== null) {
      await handleEdit();
    } else {
      await handleAdd();
    }
    setIsLoadingSave(false);
  };

  useEffect(() => {
    getValues();
  }, []);

  const handleChangeOcc = (event, crm = null, newData = null) => {
    setSelectedOcc(event);
    const arr = newData !== null ? newData : data;
    const element = arr.find(
      (el) =>
        el?.codOcc === event?.cod_Ocorr_Sistema &&
        el?.crm === (typeof crm === "string" ? crm : selectedCrm?.label)
    );
    if (element !== undefined) {
      setSendEmail(element?.send ?? false);
      const newFields = { ...STD_FIELD_VALUE };
      newFields.contract = element?.contract ?? false;
      newFields.name = element?.name ?? false;
      newFields.document = element?.document ?? false;
      newFields.activeEmail = element?.activeEmail ?? false;
      newFields.activePhone = element?.activePhone ?? false;
      newFields.balance = element?.balance ?? false;
      newFields.originalValue = element?.originalValue ?? false;
      newFields.updatedValue = element?.updatedValue ?? false;
      newFields.openInstallments = element?.openInstallments ?? false;
      setFields(newFields);
      setElement(element);
      const existEmail = element?.occurrenceRulesEmails
        ?.map((item) => item?.email)
        ?.filter((item) => item !== null);
      setEmails(existEmail.length > 0 ? existEmail : [""]);
      const existCustomField = element?.occurrenceRulesCustemFields
        ?.map((item) => item?.field)
        ?.filter((item) => item !== null);
      setCustomFields(existCustomField.length > 0 ? existCustomField : [""]);
    } else {
      cleanInputs();
    }
    setInvalidEmail(false);
  };

  const cleanInputs = () => {
    setSendEmail(false);
    setFields(STD_FIELD_VALUE);
    setElement(null);
    setEmails([""]);
    setCustomFields([""]);
  };

  const handleFieldsChange = (field, value) => {
    setFields({ ...fields, [field]: value });
  };

  const handleAddCustomField = () => {
    setCustomFields([...customFields, ""]);
  };

  const handleRemoveCustomField = (index) => {
    const newCustomFields = [...customFields];
    newCustomFields.splice(index, 1);
    setCustomFields(newCustomFields);
  };

  const handleCustomFieldsChange = (index, value) => {
    const newCustomFields = [...customFields];
    newCustomFields[index] = value;
    setCustomFields(newCustomFields);
  };

  const handleAddEmail = () => {
    setEmails([...emails, ""]);
  };

  const handleRemoveEmail = (index) => {
    const newEmails = [...emails];
    newEmails.splice(index, 1);
    setEmails(newEmails);
  };

  const handleEmailChange = (index, value) => {
    const newEmails = [...emails];
    newEmails[index] = value;
    setEmails(newEmails);
    for (const item of newEmails) {
      if (!isEmailValid(item) && item !== "") {
        setInvalidEmail(true);
        return;
      }
      setInvalidEmail(false);
    }
  };

  const showToEdit = async (item, newData = null) => {
    const optOcc = await getOcc({ value: item.crm === "GVC" ? 0 : 1 });
    setSelectedCrm(optionCrm.find((x) => x.label === item.crm));
    const occ = optOcc.find((x) => x.cod_Ocorr_Sistema === item.codOcc);
    if (occ === undefined) {
      toast.error("Ocorrência não encontrada");
      return;
    }
    handleChangeOcc(occ, item.crm, newData);
  };

  const handleDelete = async (item) => {
    const deleteSuccess = await DELETE_DATA("OccurrenceRules/" + item?.id);
    if (deleteSuccess?.success) {
      toast.success("Regra de ocorrência removida com sucesso.");
      await getValues();
      setOptionsOcc([]);
      setSelectedCrm(null);
      setSelectedOcc(null);
      cleanInputs();
    } else {
      toast.error("Erro ao remover regra de ocorrência");
    }
  };

  return (
    <div>
      <h3>Parametrização de Ocorrências - Regras</h3>
      <CCard>
        <CCardBody>
          <CForm>
            <div className="form-group row">
              <CLabel className="col-sm-2 col-form-label">
                Selecione o CRM:
              </CLabel>
              <div className="col-sm-5">
                <Select
                  options={optionCrm}
                  placeholder="Selecione um CRM"
                  onChange={(e) => {
                    getOcc(e);
                    setSelectedCrm(e);
                  }}
                  value={selectedCrm}
                />
              </div>
            </div>
            <div className="form-group row">
              <CLabel className="col-sm-2 col-form-label">
                Selecione a ocorrência:
              </CLabel>
              <div className="col-sm-5">
                <Select
                  options={optionsOcc}
                  placeholder="Selecione a ocorrência"
                  isDisabled={isLoading || selectedCrm === null}
                  onChange={handleChangeOcc}
                  value={selectedOcc}
                />
              </div>
            </div>
            <div className="form-group row">
              <CCol md="2">
                <CLabel>Enviará e-mail:</CLabel>
              </CCol>
              <CCol md="9">
                <CFormGroup variant="custom-radio" inline>
                  <CInputRadio
                    custom
                    id="sendEmailYes"
                    name="sendEmail"
                    onChange={() => setSendEmail(true)}
                    checked={sendEmail}
                  />
                  <CLabel variant="custom-checkbox" htmlFor="sendEmailYes">
                    Sim
                  </CLabel>
                </CFormGroup>
                <CFormGroup variant="custom-radio" inline>
                  <CInputRadio
                    custom
                    id="sendEmailNo"
                    name="sendEmail"
                    onChange={() => setSendEmail(false)}
                    checked={!sendEmail}
                  />
                  <CLabel variant="custom-checkbox" htmlFor="sendEmailNo">
                    Não
                  </CLabel>
                </CFormGroup>
              </CCol>
            </div>
            <CRow className={"mt-3 mb-3"}>
              <CCol className={"mr-3"} style={{ backgroundColor: "lightgray" }}>
                <CLabel className={"mt-2 font-weight-bold"}>
                  Selecione os campos que serão exibidos:
                </CLabel>
                <div
                  className="mb-2 p-2"
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    backgroundColor: "white",
                  }}
                >
                  <CFormGroup variant="custom-checkbox" inline>
                    <CInputCheckbox
                      custom
                      id="cb-contrato"
                      checked={fields.contract}
                      onChange={(e) =>
                        handleFieldsChange("contract", e.target.checked)
                      }
                    />
                    <CLabel variant="custom-checkbox" htmlFor="cb-contrato">
                      Contrato
                    </CLabel>
                  </CFormGroup>
                  <CFormGroup variant="custom-checkbox" inline>
                    <CInputCheckbox
                      custom
                      id="cb-cpfcnpj"
                      checked={fields.document}
                      onChange={(e) =>
                        handleFieldsChange("document", e.target.checked)
                      }
                    />
                    <CLabel variant="custom-checkbox" htmlFor="cb-cpfcnpj">
                      CPF/CNPJ
                    </CLabel>
                  </CFormGroup>
                  <CFormGroup variant="custom-checkbox" inline>
                    <CInputCheckbox
                      custom
                      id="cb-cliente"
                      checked={fields.name}
                      onChange={(e) =>
                        handleFieldsChange("name", e.target.checked)
                      }
                    />
                    <CLabel variant="custom-checkbox" htmlFor="cb-cliente">
                      Nome do cliente
                    </CLabel>
                  </CFormGroup>
                  <CFormGroup variant="custom-checkbox" inline>
                    <CInputCheckbox
                      custom
                      id="cb-email-principal"
                      checked={fields.activeEmail}
                      onChange={(e) =>
                        handleFieldsChange("activeEmail", e.target.checked)
                      }
                    />
                    <CLabel
                      variant="custom-checkbox"
                      htmlFor="cb-email-principal"
                    >
                      E-mail Principal (Ativo)
                    </CLabel>
                  </CFormGroup>
                  <CFormGroup variant="custom-checkbox" inline>
                    <CInputCheckbox
                      custom
                      id="cb-telefone"
                      checked={fields.activePhone}
                      onChange={(e) =>
                        handleFieldsChange("activePhone", e.target.checked)
                      }
                    />
                    <CLabel variant="custom-checkbox" htmlFor="cb-telefone">
                      Telefone (Ativo)
                    </CLabel>
                  </CFormGroup>
                  <CFormGroup variant="custom-checkbox" inline>
                    <CInputCheckbox
                      custom
                      id="cb-saldo"
                      checked={fields.balance}
                      onChange={(e) =>
                        handleFieldsChange("balance", e.target.checked)
                      }
                    />
                    <CLabel variant="custom-checkbox" htmlFor="cb-saldo">
                      Saldo
                    </CLabel>
                  </CFormGroup>
                  <CFormGroup variant="custom-checkbox" inline>
                    <CInputCheckbox
                      custom
                      id="cb-valor-original"
                      checked={fields.originalValue}
                      onChange={(e) =>
                        handleFieldsChange("originalValue", e.target.checked)
                      }
                    />
                    <CLabel
                      variant="custom-checkbox"
                      htmlFor="cb-valor-original"
                    >
                      Valor Original
                    </CLabel>
                  </CFormGroup>
                  <CFormGroup variant="custom-checkbox" inline>
                    <CInputCheckbox
                      custom
                      id="cb-valor-atualizado"
                      checked={fields.updatedValue}
                      onChange={(e) =>
                        handleFieldsChange("updatedValue", e.target.checked)
                      }
                    />
                    <CLabel
                      variant="custom-checkbox"
                      htmlFor="cb-valor-atualizado"
                    >
                      Valor atualizado
                    </CLabel>
                  </CFormGroup>
                  <CFormGroup variant="custom-checkbox" inline>
                    <CInputCheckbox
                      custom
                      id="cb-parcelas-aberto"
                      checked={fields.openInstallments}
                      onChange={(e) =>
                        handleFieldsChange("openInstallments", e.target.checked)
                      }
                    />
                    <CLabel
                      variant="custom-checkbox"
                      htmlFor="cb-parcelas-aberto"
                    >
                      Parcelas em aberto
                    </CLabel>
                  </CFormGroup>
                </div>
              </CCol>
              <CCol className={"mr-3"} style={{ backgroundColor: "lightgray" }}>
                <CLabel className={"mt-2 font-weight-bold"}>
                  Digite os campos que deseja a mais (prenchimento manual)
                </CLabel>
                <div
                  className="mb-2 p-2"
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    backgroundColor: "white",
                  }}
                >
                  {customFields.map((item, index) => (
                    <>
                      <div className="mb-2 d-flex justify-content-between">
                        <CInput
                          className={" mr-2"}
                          value={item}
                          key={index}
                          onChange={(e) =>
                            handleCustomFieldsChange(index, e.target.value)
                          }
                        />
                        {index + 1 === customFields.length && index < 29 && (
                          <>
                            <CButton
                              color="primary"
                              onClick={handleAddCustomField}
                              className={"mr-2"}
                            >
                              <i className="cil-plus" />
                            </CButton>
                          </>
                        )}{" "}
                        {customFields.length > 1 && (
                          <CButton
                            color="danger"
                            onClick={() => handleRemoveCustomField(index)}
                          >
                            <i className="cil-minus" />
                          </CButton>
                        )}
                      </div>
                    </>
                  ))}
                </div>
              </CCol>
              <CCol style={{ backgroundColor: "lightgray" }}>
                <CLabel className={"mt-2 font-weight-bold"}>
                  Informe os e-mails de destino (destinatários)
                </CLabel>
                <div
                  className="mb-2 p-2"
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    backgroundColor: "white",
                  }}
                >
                  {emails.map((item, index) => (
                    <>
                      <div className="mb-1 mt-1 d-flex justify-content-between">
                        <CInput
                          className={" mr-2"}
                          value={item}
                          key={index}
                          onChange={(e) =>
                            handleEmailChange(index, e.target.value)
                          }
                          placeholder={`E-mail ${index + 1}`}
                          invalid={!isEmailValid(item) && item !== ""}
                        />
                        {index + 1 === emails.length && index < 6 && (
                          <>
                            <CButton
                              color="primary"
                              onClick={handleAddEmail}
                              className={"mr-2"}
                            >
                              <i className="cil-plus" />
                            </CButton>
                          </>
                        )}{" "}
                        {emails.length > 1 && (
                          <CButton
                            color="danger"
                            onClick={() => handleRemoveEmail(index)}
                          >
                            <i className="cil-minus" />
                          </CButton>
                        )}
                      </div>
                      {!isEmailValid(item) && item !== "" && (
                        <small className="font-weight-bold text-danger">
                          E-mail inválido
                        </small>
                      )}
                    </>
                  ))}
                </div>
              </CCol>
            </CRow>
            <CButton
              color="info"
              onClick={handleSave}
              className="mr-2"
              title={inforPermissions(permissao).edit}
              disabled={
                !checkPermission(
                  permissao.modulo,
                  "Edit",
                  permissao.submodulo
                ) || isLoadingSave
              }
            >
              {isLoadingSave ? <LoadingComponent /> : "Salvar"}
            </CButton>
          </CForm>
        </CCardBody>
      </CCard>
      <CCard>
        <CCardBody>
          <CRow></CRow>
          <table className="table table-bordered">
            <thead>
              <tr>
                <th>Cod</th>
                <th>Enviar Email</th>
                <th>CRM</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <Select
                    options={[{ label: "Selecione", value: null }].concat(
                      data.map((e) => {
                        return {
                          label: e.codOcc,
                          value: e.codOcc,
                        };
                      })
                    )}
                    placeholder={"Selecione"}
                    onChange={(e) => setFilterCodOcc(e.value)}
                  />
                </td>
                <td>
                  <Select
                    options={[{ label: "Selecione", value: null }].concat([
                      {
                        label: "Sim",
                        value: true,
                      },
                      {
                        label: "Não",
                        value: false,
                      },
                    ])}
                    placeholder={"Selecione"}
                    onChange={(e) => setFilterSendEmail(e.value)}
                  />
                </td>
                <td>
                  <Select
                    options={[{ label: "Selecione", value: null }].concat(
                      optionCrm
                    )}
                    placeholder={"Selecione"}
                    onChange={(e) =>
                      setFilterCrm(e.value === null ? null : e.label)
                    }
                  />
                </td>
              </tr>
              {data
                .sort((a, b) => {
                  if (parseInt(a.codOcc) < parseInt(b.codOcc)) return -1;
                  if (parseInt(a.codOcc) > parseInt(b.codOcc)) return 1;
                  return 0;
                })
                .filter((x) => {
                  let filCrm = true;
                  let filCodOcc = true;
                  let filSendEmail = true;
                  if (filterCrm !== null && filterCrm !== "")
                    filCrm = x.crm === filterCrm;
                  if (filterCodOcc !== null && filterCodOcc !== "")
                    filCodOcc =
                      x.codOcc
                        .toLowerCase()
                        .indexOf(filterCodOcc.toLowerCase()) > -1;
                  if (filterSendEmail !== null && filterSendEmail !== "")
                    filSendEmail = x.send === filterSendEmail;
                  return filCrm && filCodOcc && filSendEmail;
                })
                .map((item, key) => (
                  <tr key={key}>
                    <td>{item.codOcc}</td>
                    <td>{item.send ? "Sim" : "Não"}</td>
                    <td>{item.crm}</td>
                    <td>
                      <CButton
                        color="info"
                        onClick={() => showToEdit(item)}
                        title={inforPermissions(permissao).edit}
                        className={"mr-2"}
                      >
                        <i className="cil-pencil" />
                      </CButton>
                      <CButton
                        color="danger"
                        onClick={() => handleDelete(item)}
                        title={inforPermissions(permissao).delete}
                      >
                        <i className="cil-trash" />
                      </CButton>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </CCardBody>
      </CCard>
    </div>
  );
};

export default RegrasOcorrencias;
