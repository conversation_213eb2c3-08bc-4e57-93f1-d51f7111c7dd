import React, { useState } from "react";

import "react-datepicker/dist/react-datepicker.css";

import {
  CRow,
  <PERSON>ard<PERSON><PERSON>le,
  CCardBody,
  <PERSON>ard,
  CCardFooter,
  CButton,
  CCol,
} from "@coreui/react";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import DadosDaSimulacaoSimulacao from "./DadosDaSimulacaoSimulacao";
import { isEmailValid } from "src/reusable/helpers";

const PostData = async (
  payload,
  endpoint = "cyberSafraSimularSaldo",
  id = ""
) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI(endpoint),
        payload,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const FormularioSimulacao = ({
  onClickNovaSimulacao,
  onFinshAcordo,
  contrato,
}) => {
  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;
  const parcelasAbertas = contratosAtivos?.flatMap((item) =>
    item.parcelas.filter(
      (pItem) =>
        pItem.status === "A" && !pItem.nr_Acordo /* && pItem.atraso > 0  ||
        (pItem.status === "A" && !pItem.nr_Acordo && pItem.atraso === 0 && pItem.nome_Tipo_Parcela === "DIF_PARCELAS")*/
    )
  );
  let financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";
  let user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : "";
  const [loading, setLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState("empty");

  const [msgAvisoLoading, setMsgAvisoLoading] = useState("");
  const [titleAvisoLoading, setTitleAvisoLoading] = useState("");
  const [emailValido, setEmailValido] = useState(true);
  const [primeiraParcela, setPrimeiraParcela] = useState(null);
  const [primeiraParcelaContratoLongo, setPrimeiraParcelaContratoLongo] =
    useState(null);

  // Estado para armazenar o email do
  const [email, setEmail] = useState("");

  // Função para lidar com a atualização do email do
  const handleEmailChange = (email) => {
    setEmailValido(true);
    setEmail(email);
  };

  const payload = {
    vlrEntrada: 1458.75,
    vlrParcBalao: 0,
    diasCarencia: 0,
    periodicidade: "MONTHLY",
    tipoContato: "B",
    cpfCnpj: "09875012670",
    qtdParcelas: 6,
    vlrNegociado: 68028.96,
    vlrHonorarios: 132.61,
    cdTipoAcordo: "RN01",
    contratos: [],
    parcelasAgr: [],
    cdAscob: "0069",
    email: "",
  };

  const payloadContratos = {
    grupo: "3",
    contrato: "1620030162004390000000001",
    parcelas: [],
  };

  React.useEffect(() => {
    if (contratosAtivos != null) {
      const parcelas = JSON.parse(JSON.stringify(parcelasAbertas));
      const ordenarParcelas = parcelas.sort((a, b) => {
        // Verifica se a.nr_Parcela ou b.nr_Parcela é igual a 0
        if (a.nr_Parcela === 0) return 1; // Move 'a' para o final se a.nr_Parcela é 0
        if (b.nr_Parcela === 0) return -1; // Move 'b' para o final se b.nr_Parcela é 0

        // Caso contrário, ordena normalmente
        return a.nr_Parcela - b.nr_Parcela;
      });
      const ordenarContrato = ordenarParcelas.sort(
        (a, b) => a.numero_Contrato - b.numero_Contrato
      );
      // const parcela = ordenarContrato[0];
      const parcelaLongo = ordenarContrato.filter(
        (p) => p.numero_Contrato?.length === 25 && p.status === "A"
      );
      const parcelaContrato = ordenarContrato.filter(
        (item) => item.numero_Contrato === contrato.numero_Contrato
      )[0];
      setPrimeiraParcela(parcelaContrato);
      setPrimeiraParcelaContratoLongo(parcelaLongo[0] ?? null);
    }
  }, []);

  const GravarDados = async (financiado) => {
    setTitleAvisoLoading("Gravando Dados Tela Única");
    setMsgAvisoLoading(`Enviando dados...`);
    setLoading(true);
    setLoadingAction("VarifyParam");

    try {
      financiado.dadosSafra.id_Contrato =
        financiado.dadosFinanciado.id_Contrato.toString();
      financiado.dadosSafra.id_Grupo = financiado.id_Grupo;
      const payload = {
        financedData: financiado.dadosFinanciado,
        contractData: {
          financed_id: financiado.dadosSafra.financed_id,
          id_Contrato: financiado.dadosSafra.id_Contrato,
          id_Grupo: financiado.dadosSafra.id_Grupo,
          grupo: financiado?.dadosSafra?.grupo,
          contrato: financiado.dadosSafra.contrato,
          vlrPrincipal: financiado.dadosSafra.vlrPrincipal,
          vlrDevedor: financiado.dadosSafra.vlrDevedor,
          vlrPrincDesc: financiado.dadosSafra.vlrPrincDesc,
          vlrJurosCDI: financiado.dadosSafra.vlrJurosCDI,
          vlrJuros: financiado.dadosSafra.vlrJuros,
          vlrMulta: financiado.dadosSafra.vlrMulta,
          vlrTarifa: financiado.dadosSafra.vlrTarifa,
          vlrCustas: financiado.dadosSafra.vlrCustas,
          vlrAlvara: financiado.dadosSafra.vlrAlvara,
          vlrJurTxContr: financiado.dadosSafra.vlrJurTx,
          vlrDevedorEncargos: financiado.dadosSafra.vlrDevedorEncargos,
          parcelasOriginais: financiado.dadosSafra.parcelasOriginais,
        },
        agreementData: {
          contract_id: financiado.dadosSafra.simulacao.complementar.contract_id,
          idAcordo: financiado.dadosSafra.acordo.idAcordo,
          idContrato: financiado.dadosSafra.simulacao.complementar.idContrato,
          prazoSimulado:
            financiado.dadosSafra.simulacao.complementar.prazoSimulado,
          valorFinanciadoSimulado:
            financiado.dadosSafra.simulacao.complementar
              .valorFinanciadoSimulado,
          coeficienteParcela:
            financiado.dadosSafra.simulacao.complementar.coeficienteParcela,
          taxaContratoSimulado:
            financiado.dadosSafra.simulacao.complementar.taxaContratoSimulado,
          idUsuario: financiado.dadosSafra.simulacao.complementar.idUsuario,
          qtParcelasAtivas:
            financiado.dadosSafra.simulacao.complementar.qtParcelasAtivas,
          encargos: financiado.dadosSafra.simulacao.complementar.encargos,
          tarifas: financiado.dadosSafra.simulacao.complementar.tarifas,
          iof: financiado.dadosSafra.simulacao.complementar.iof,
          iofComplementar:
            financiado.dadosSafra.simulacao.complementar.iofComplementar,
          dtParcelaEntrada:
            financiado.dadosSafra.simulacao.complementar.dtParcelaEntrada,
          dtPagamentoEntrada:
            financiado.dadosSafra.simulacao.complementar.dtPagamentoEntrada,
          parcelaEntrada:
            financiado.dadosSafra.simulacao.complementar.parcelaEntrada,
          valorEntrada:
            financiado.dadosSafra.simulacao.complementar.valorEntrada,
          valorMultaEntrada:
            financiado.dadosSafra.simulacao.complementar.valorMultaEntrada,
          valorJurosEntrada:
            financiado.dadosSafra.simulacao.complementar.valorJurosEntrada,
          valorHonorarios: financiado.dadosSafra.vlrHonorario,
          contratoDataCob: financiado.dadosSafra.contrato,
          cpfCnpj: financiado.dadosSafra.simulacao.complementar.cpfCnpj,
          situacao: financiado.dadosSafra.simulacao.complementar.situacao,
          status: financiado.dadosSafra.simulacao.complementar.status,
          parcelas: financiado.dadosSafra.simulacao.parcelas,
        },
      };
      const data = await PostData(payload, "negociacaoSafraProcessoCriar");
      if (data.success) {
        setTitleAvisoLoading("Dados Gravado com Sucesso");
        setMsgAvisoLoading("");
        await GerarBoletoCoringa(financiado, data.data.id);
      } else {
        setTitleAvisoLoading("Processo de gravação não realizado");
        setMsgAvisoLoading(
          "Solicite ver os Logs para Admnistrador de Sistemas, pois o contrato do Safra pode ter Sido Gerado e esse precisar ser cancelado."
        );
      }
    } catch (err) {
      setTitleAvisoLoading(`Erro na chamada das APIS`);
      setMsgAvisoLoading(
        `Erro na chamada API, Solicite ver os Logs para Admnistrador de Sistemas, pois o contrato do Safra pode ter Sido Gerado e esse precisar ser cancelado.`
      );
    }
    limparAvisos();
  };
  const GravarFinanciado = async (financiado) => {
    let ret = false;
    setTitleAvisoLoading("Gravando Financiado Tela Única");
    setMsgAvisoLoading(`Enviando dados...`);
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(financiado.dadosFinanciado, "negociacaoSafraFinanciadoCriar")
      .then((data) => {
        if (data.success) {
          financiado.dadosSafra.financed_id = data.data.id;
          financiado.dadosSafra.id_Contrato =
            financiado.dadosFinanciado.id_Contrato.toString();
          financiado.dadosSafra.id_Grupo = financiado.id_Grupo;
          setTitleAvisoLoading("Financiado Gravado com Sucesso");
          GravarContrato(financiado);
        } else {
          setTitleAvisoLoading("Financiado não gravado");
          setMsgAvisoLoading(
            "Solicite ver os Logs para Admnistrador de Sistemas, pois o contrato do Safra pode ter Sido Gerado e esse precisar ser cancelado."
          );
          limparAvisos();
        }
      })
      .catch((err) => {
        setTitleAvisoLoading(`Erro na chamada das APIS`);
        setMsgAvisoLoading(
          `Erro na chamada API Financiado, Solicite ver os Logs para Admnistrador de Sistemas, pois o contrato do Safra pode ter Sido Gerado e esse precisar ser cancelado.`
        );
        limparAvisos();
      });

    return ret;
  };

  const GravarContrato = async (contratoSaldo) => {
    let ret = false;
    setTitleAvisoLoading("Gravando Contrato Tela Única");
    setMsgAvisoLoading(`...`);
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(
      {
        financed_id: contratoSaldo.dadosSafra.financed_id,
        id_Contrato: contratoSaldo.dadosSafra.id_Contrato,
        id_Grupo: contratoSaldo.dadosSafra.id_Grupo,
        grupo: contratoSaldo?.dadosSafra?.grupo,
        contrato: contratoSaldo.dadosSafra.contrato,
        vlrPrincipal: contratoSaldo.dadosSafra.vlrPrincipal,
        vlrDevedor: contratoSaldo.dadosSafra.vlrDevedor,
        vlrPrincDesc: contratoSaldo.dadosSafra.vlrPrincDesc,
        vlrJurosCDI: contratoSaldo.dadosSafra.vlrJurosCDI,
        vlrJuros: contratoSaldo.dadosSafra.vlrJuros,
        vlrMulta: contratoSaldo.dadosSafra.vlrMulta,
        vlrTarifa: contratoSaldo.dadosSafra.vlrTarifa,
        vlrCustas: contratoSaldo.dadosSafra.vlrCustas,
        vlrAlvara: contratoSaldo.dadosSafra.vlrAlvara,
        vlrJurTxContr: contratoSaldo.dadosSafra.vlrJurTx,
        vlrDevedorEncargos: contratoSaldo.dadosSafra.vlrDevedorEncargos,
        parcelasOriginais: contratoSaldo.dadosSafra.parcelasOriginais,
      },
      "negociacaoSafraContratoCriar"
    )
      .then((data) => {
        if (data.success) {
          setTitleAvisoLoading("Contrato Gravado com Sucesso");
          contratoSaldo.dadosSafra.simulacao.complementar.contract_id =
            data.data.id;
          GravarAcordo(contratoSaldo);
        } else {
          setTitleAvisoLoading("Erro ao Gravar Contrato Tela Única");
          setMsgAvisoLoading(
            "Contrato não gravado, Solicite ver os Logs para Admnistrador de Sistemas, pois o contrato do Safra pode ter Sido Gerado e esse precisar ser cancelado"
          );
          limparAvisos();
        }
      })
      .catch((err) => {
        setTitleAvisoLoading(`Erro na chamada das APIS`);
        setMsgAvisoLoading(
          `Erro na chamada API do Contrato, Solicite ver os Logs para Admnistrador de Sistemas, pois o contrato do Safra pode ter Sido Gerado e esse precisar ser cancelado.`
        );
        limparAvisos();
      });

    return ret;
  };

  const GravarAcordo = async (acordoData) => {
    let ret = false;
    setTitleAvisoLoading("Gravando Acordo Tela Única");
    setMsgAvisoLoading(`Enviando dados...`);
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(
      {
        contract_id: acordoData.dadosSafra.simulacao.complementar.contract_id,
        idAcordo: acordoData.dadosSafra.acordo.idAcordo,
        idContrato: acordoData.dadosSafra.simulacao.complementar.idContrato,
        prazoSimulado:
          acordoData.dadosSafra.simulacao.complementar.prazoSimulado,
        valorFinanciadoSimulado:
          acordoData.dadosSafra.simulacao.complementar.valorFinanciadoSimulado,
        coeficienteParcela:
          acordoData.dadosSafra.simulacao.complementar.coeficienteParcela,
        taxaContratoSimulado:
          acordoData.dadosSafra.simulacao.complementar.taxaContratoSimulado,
        idUsuario: acordoData.dadosSafra.simulacao.complementar.idUsuario,
        qtParcelasAtivas:
          acordoData.dadosSafra.simulacao.complementar.qtParcelasAtivas,
        encargos: acordoData.dadosSafra.simulacao.complementar.encargos,
        tarifas: acordoData.dadosSafra.simulacao.complementar.tarifas,
        iof: acordoData.dadosSafra.simulacao.complementar.iof,
        iofComplementar:
          acordoData.dadosSafra.simulacao.complementar.iofComplementar,
        dtParcelaEntrada:
          acordoData.dadosSafra.simulacao.complementar.dtParcelaEntrada,
        dtPagamentoEntrada:
          acordoData.dadosSafra.simulacao.complementar.dtPagamentoEntrada,
        parcelaEntrada:
          acordoData.dadosSafra.simulacao.complementar.parcelaEntrada,
        valorEntrada: acordoData.dadosSafra.simulacao.complementar.valorEntrada,
        valorMultaEntrada:
          acordoData.dadosSafra.simulacao.complementar.valorMultaEntrada,
        valorJurosEntrada:
          acordoData.dadosSafra.simulacao.complementar.valorJurosEntrada,
        valorHonorarios: acordoData.dadosSafra.vlrHonorario,
        contratoDataCob: acordoData.dadosSafra.contrato,
        cpfCnpj: acordoData.dadosSafra.simulacao.complementar.cpfCnpj,
        situacao: acordoData.dadosSafra.simulacao.complementar.situacao,
        status: acordoData.dadosSafra.simulacao.complementar.status,
        parcelas: acordoData.dadosSafra.simulacao.parcelas,
      },
      "negociacaoSafraAcordoCriar"
    )
      .then((data) => {
        if (data.success) {
          setTitleAvisoLoading("Acordo Gravado com Sucesso");
          // EnviarGvcManager(acordoData, data.data.id);
          GerarBoletoCoringa(acordoData, data.data.id);
        } else {
          setTitleAvisoLoading(`Erro ao Gravar Acordo`);
          setTitleAvisoLoading(
            "Acordo não gravado, Solicite ver os Logs para Admnistrador de Sistemas, pois o contrato do Safra pode ter Sido Gerado e esse precisar ser cancelado."
          );
          limparAvisos();
        }
      })
      .catch((err) => {
        setTitleAvisoLoading(`Erro na chamada das APIS`);
        setMsgAvisoLoading(
          `Erro na chamada API do Acordo, Solicite ver os Logs para Admnistrador de Sistemas, pois o contrato do Safra pode ter Sido Gerado e esse precisar ser cancelado.`
        );
        limparAvisos();
      });

    return ret;
  };

  const GerarAcordo = async () => {
    let ret = false;
    setTitleAvisoLoading("Enviando Simulação para gerar o Acordo Safra");
    setMsgAvisoLoading(`Enviando dados para o Acordo...`);
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(payload, "cyberSafraCadastrarAcordo")
      .then((data) => {
        if (data && data.data && data.data.idAcordo != null) {
          contrato.dadosSafra.acordo = data.data;
          // GravarFinanciado(contrato);
          GravarDados(contrato);
        } else {
          setTitleAvisoLoading("Acordo Safra não gerado");
          setMsgAvisoLoading(`Msg Safra: ${data.data.message}`);
          setTimeout(() => {
            setLoading(false);
            setLoadingAction("empty");
            setMsgAvisoLoading("");
            setTitleAvisoLoading("");
          }, 3000);
        }
      })
      .catch((err) => {
        setTitleAvisoLoading(`Erro na chamada das APIS`);
        setMsgAvisoLoading(
          `Erro na chamada API de Cyber Gravar o Acordo, Entre em contato com o Administrador de Sistemas, realize uma nova busca e tente novamente.`
        );
        setTimeout(() => {
          setLoading(false);
          setLoadingAction("empty");
          setMsgAvisoLoading("");
          setTitleAvisoLoading("");
        }, 3000);
      });

    return ret;
  };

  const limparAvisos = () => {
    setTimeout(() => {
      setLoading(false);
      setLoadingAction("empty");
      setMsgAvisoLoading("");
      setTitleAvisoLoading("");
      onFinshAcordo();
    }, 3000);
  };

  const AtualizarStatusDataCob = async (id_contrato, status) => {
    setTitleAvisoLoading("Atualizando Status Acordo");
    setMsgAvisoLoading(`Enviando...`);
    setLoading(true);
    setLoadingAction("VarifyParam");
    await PostData(
      status,
      "negociacaoSafraAtualizarAtualizarStatusDataCob",
      id_contrato
    )
      .then((data) => {
        if (data.success) {
          setTitleAvisoLoading("Status Atualizando do Acordo");
          setMsgAvisoLoading("");
        } else {
          setTitleAvisoLoading("Status Não Atualizando do Acordo");
          setMsgAvisoLoading(data.message);
        }
      })
      .catch((err) => {
        setTitleAvisoLoading("Erro ao Tentar atualizar Status do Acordo");
        setMsgAvisoLoading("");
      })
      .finally(() => {
        limparAvisos();
      });
  };

  const EnviarGvcManager = async (acordoData, id_acordo_telaunica) => {
    setLoading(true);
    setLoadingAction("VarifyParam");
    setTitleAvisoLoading("Enviando solicitação ao GVC Manager");
    setMsgAvisoLoading("Enviando Acordo...");
    const dateString =
      acordoData.dadosSafra.simulacao.complementar.dtParcelaEntrada;
    const dateObj = new Date(dateString);

    const formattedDate = `${dateObj.getFullYear()}-${String(
      dateObj.getMonth() + 1
    ).padStart(2, "0")}-${String(dateObj.getDate()).padStart(2, "0")}`;

    await PostData(
      {
        type_action: 0,
        id_agreements: acordoData.dadosSafra.id_Contrato,
        nr_installment: null,
        id_agreements_tu: id_acordo_telaunica,
        prazoSimulado:
          acordoData.dadosSafra.simulacao.complementar.qtParcelasAtivas,
        idAcordoDataCob: 0,
        valorFinanciadoSimulado:
          acordoData.dadosSafra.simulacao.complementar.valorFinanciadoSimulado,
        valorHonorario: acordoData.dadosSafra.vlrHonorario,
        contratoDataCob: acordoData.dadosSafra.contrato,
        dtParcelaEntrada: formattedDate,
        valorEntrada: acordoData.dadosSafra.simulacao.complementar.valorEntrada,
        cpfCnpj: financiadoData.cpfCnpj,
      },
      "gvcmanagerCyberSafraAcoesContrato"
    )
      .then((data) => {
        if (data.success) {
          setTitleAvisoLoading("Acordo Enviado ao GVC Manager");
          setMsgAvisoLoading("");
          AtualizarStatusDataCob(
            id_acordo_telaunica,
            `Enviado GVC Manager Comando de criação de Acordo`
          );
        } else {
          AtualizarStatusDataCob(id_acordo_telaunica, "Falha");
        }
      })
      .catch((err) => {
        setTitleAvisoLoading("Falha ao Enviar Acordo ao GVC Manager");
        setMsgAvisoLoading("");
        limparAvisos();
      });
  };

  const GerarBoletoCoringa = async (acordoData, id_acordo_telaunica) => {
    setLoading(true);
    setLoadingAction("VarifyParam");
    setTitleAvisoLoading("Gerando Boleto");
    setMsgAvisoLoading("Integrando CRM...");
    const parcela = acordoData.dadosSafra.simulacao?.parcelas[0];
    const dateObj = new Date(parcela.dtVencimento);

    const formattedDate = `${dateObj.getFullYear()}-${String(
      dateObj.getMonth() + 1
    ).padStart(2, "0")}-${String(dateObj.getDate()).padStart(2, "0")}`;

    await PostData(
      {
        agreementId: id_acordo_telaunica,
        negotiationData: {
          idAgrupamento: financiadoData.id_Agrupamento,
          parcelas: [primeiraParcelaContratoLongo?.id_Parcela],
          dtNegociacao: formattedDate,
          valorPrincipal: parcela.vlrParcela,
          valorCorrecao: 0,
          juros: parcela.vlrJuros,
          multa: parcela.vlrImposto,
          comissaoPermanencia: 0,
          // honorarios: parcela.vlrParcela * 0.1,
          honorarios: 0,
          descontoAutorizado: true,
          custas: 0,
          notificacao: 0,
          valorTarifa: 0,
        },
      },
      "negociacaoSafraIntegracao"
    )
      .then((data) => {
        if (data?.success) {
          setTitleAvisoLoading("Integração Realizada com Sucesso");
          setMsgAvisoLoading("");
        } else {
          AtualizarStatusDataCob(id_acordo_telaunica, "Falha na integração");
          setMsgAvisoLoading("");
          limparAvisos();
        }
      })
      .catch((err) => {
        setTitleAvisoLoading("Falha ao Gerar Boleto Coringa");
        setMsgAvisoLoading("");
        limparAvisos();
      });
  };

  const handleGerarAcordo = () => {
    payload.vlrEntrada =
      contrato.dadosSafra.simulacao.complementar.valorEntrada;
    payload.cpfCnpj = contrato.dadosSafra.simulacao.complementar.cpfCnpj;
    payload.qtdParcelas = contrato.dadosSafra.simulacao.parcelas.length - 1;
    payload.vlrNegociado = parseFloat(
      (
        contrato.dadosSafra.vlrPrincipal +
        contrato.dadosSafra.vlrMulta +
        contrato.dadosSafra.vlrJuros +
        contrato.dadosSafra.vlrPrincDesc +
        contrato.dadosSafra.vlrCustas +
        contrato.dadosSafra.vlrHonorario
      ).toFixed(2)
    );
    payload.vlrHonorarios = contrato.dadosSafra.vlrHonorario;
    payload.cdTipoAcordo = contrato.dadosSafra.tipoAcordo.cdTipo;
    payload.parcelasAgr = contrato.dadosSafra.simulacao.parcelas;
    payload.email = email;
    setEmailValido(true);
    if (!isEmailValid(payload.email)) {
      setEmailValido(false);
      return;
    }

    const parcelas = contrato.dadosSafra.parcelasOriginais
      .map((parcela, index) => {
        if (primeiraParcela.nr_Parcela <= parcela.nrParcela) {
          return {
            grupo: "3",
            contrato: contrato.dadosSafra.contrato,
            nrParcela: parcela.nrParcela,
          };
        }
      })
      .filter((parcela) => parcela !== undefined);

    payloadContratos.contrato = contrato.dadosSafra.contrato;
    payloadContratos.parcelas = parcelas;
    payload.contratos = [payloadContratos];
    GerarAcordo();
  };
  return (
    <CCard style={{ border: "none" }}>
      <CCardBody>
        {loading && loadingAction === "VarifyParam" ? (
          <CardLoading Title={titleAvisoLoading} Msg={msgAvisoLoading} />
        ) : (
          ""
        )}
        <CRow>
          <CCol>
            <CCard style={{ border: "none" }}>
              <CCardTitle style={{ fontSize: "1.2rem" }}>
                <strong>Dados do Acordo</strong>
              </CCardTitle>
              <CCardBody>
                <DadosDaSimulacaoSimulacao
                  simulacao={contrato}
                  onEmailChange={handleEmailChange}
                  emailValido={emailValido}
                />
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
      </CCardBody>
      <CCardFooter style={{ display: "flex", justifyContent: "flex-end" }}>
        <CButton
          color="secondary"
          className="mr-2"
          onClick={onClickNovaSimulacao}
          disabled={loading}
        >
          Nova Simulação
        </CButton>
        <CButton color="info" onClick={handleGerarAcordo} disabled={loading}>
          {" "}
          Gravar Acordo
        </CButton>
      </CCardFooter>
    </CCard>
  );
};

export default FormularioSimulacao;
