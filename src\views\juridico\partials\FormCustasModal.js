import React, { useState, useEffect } from "react";
import ReactDatePicker from "react-datepicker";
import ptBR from "date-fns/locale/pt-BR";

import "react-datepicker/dist/react-datepicker.css";

import {
  CButton,
  CInput,
  CModal,
  CModalBody,
  CModalHeader,
  CModalTitle,
  CCol,
  CRow,
  CLabel,
} from "@coreui/react";
import Select from "react-select";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import LoadingComponent from "src/reusable/Loading";
import { convertCurrencyToFloat, formatCurrency } from "src/reusable/helpers";
import { postApi } from "src/reusable/functions";

const FormCustasModal = ({
  isOpen,
  onClose,
  edit = false,
  deleteCusta = false,
  dataEdit = null,
  dataJuridico = null,
}) => {
  const [loading, setLoading] = useState(false);
  const [resultOK, setResultOK] = useState("");
  const [payload, setPayload] = useState({
    idAgrupamento: 0,
    idContrato: 0,
    nrContrato: "",
    nrComprov: "",
    dtComprov: "",
    dtDespesa: "",
    vlDespesa: 0,
    despesaSistema: "2",
    motivo: "",
    codCorreio: "",
    tipo: "",
    numero: "",
    reembolso: false,
    tipoCobranca: 0,
    motivoDev: 0,
    TipoAcao: 1,
  });

  const [payloadEdit, setPayloadEdit] = useState({
    idCustas: 0,
    idAgrupamento: 0,
    idContrato: 0,
    nrContrato: "",
    nrComprov: "",
    dtComprov: "",
    dtDespesa: "",
    vlDespesa: 0,
    despesaSistema: "2",
    motivo: "",
    codCorreio: "",
    tipo: "",
    numero: "",
    reembolso: false,
    tipoCobranca: 0,
    motivoDev: 0,
    TipoAcao: 3,
  });
  const [selectContrato, setSelectContrato] = useState(0);
  const [selectedTipoComprovante, setSelectedTipoComprovante] = useState(0);
  const [selectedDataComprovante, setSelectedDataComprovante] = useState(null);
  const [selectedDataDespesa, setSelectedDataDespesa] = useState(null);
  const [numeroComprovante, setNumeroComprovante] = useState("");
  const [valor, setValor] = useState("");
  const [motivo, setMotivo] = useState("");
  const [acaoModal, setAcaoModal] = useState("Adicionar");

  const initialErrors = {
    apiResponse: "",
    idAgrupamento: "",
    idContrato: "",
    nrContrato: "",
    nrComprov: "",
    dtComprov: "",
    dtDespesa: "",
    vlDespesa: "",
    motivo: "",
    codCorreio: "",
    numero: "",
    tipoCobranca: "",
    motivoDev: "",
  };

  const [errors, setErrors] = useState(initialErrors);

  const [optionsTipoComprovante, setoptionsTipoComprovante] = useState([
    { label: "NF", value: "NF" },
    { label: "CUPOM", value: "CUPOM" },
    { label: "RECIB", value: "RECIB" },
  ]);

  const handlePayloadChange = (field, value) => {
    setErrors({ ...errors, [field]: "" });
    setPayload((prevPayload) => ({
      ...prevPayload,
      [field]: value,
    }));
    setPayloadEdit((prevPayload) => ({
      ...prevPayload,
      [field]: value,
    }));
  };

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";

  const formatDateForPayload = (dateString) => {
    try {
      const date = new Date(dateString);
      /* 2023-08-11T11:32:52.596Z */
      const formattedDate = date.toISOString(); // Formata para o formato ISO (yyyy-mm-ddThh:mm:ss.sssZ)
      return formattedDate;
    } catch {
      return null;
    }
  };

  async function onSave() {
    const payloadData = { ...payload };
    const payloadEditData = { ...payloadEdit };

    payloadData.dtComprov = formatDateForPayload(payloadData.dtComprov);
    payloadData.dtDespesa = formatDateForPayload(payloadData.dtDespesa);

    payloadEditData.dtComprov = formatDateForPayload(payloadEditData.dtComprov);
    payloadEditData.dtDespesa = formatDateForPayload(payloadEditData.dtDespesa);
    //payloadData.vlDespesa = convertCurrencyToFloat(payloadData.vlDespesa);
    payloadData.nrContrato = payloadData.nrContrato.toString();
    payloadData.nrComprov = payloadData.nrComprov.toString();

    payloadEditData.nrContrato = payloadEditData.nrContrato.toString();
    payloadEditData.nrComprov = payloadEditData.nrComprov.toString();

    setLoading(true);

    if (edit) {
      try {
        const retEdit = await postApi(
          {
            idContrato: payloadData.idContrato,
            custas: {
              idCustas: payloadEditData.idCustas,
              tipoComprovante: payloadEditData.tipo,
              nrComprovante: payloadEditData.nrComprov,
              dataDespesa: payloadEditData.dtDespesa,
              valorDespesa: payloadEditData.vlDespesa,
              valorSaldo: payloadEditData.vlDespesa,
              cobradoCliente: false,
              motivo: payloadEditData.motivo,
              cobrarFinanciadoFinanceira: payloadEditData.tipoCobranca,
              dataComprovante: payloadEditData.dtComprov,
              codigoRastreioCorreio: payloadEditData.codCorreio,
            },
          },
          "postCustasAtualizar"
        );

        if (retEdit.status === 400) throw new Error(retEdit.errors);

        setResultOK(retEdit.message);
        onClose();
      } catch (err) {
        const newErrors = { ...initialErrors };
        newErrors.apiResponse = err.message;
        setErrors(newErrors);
      }
      setLoading(false);
      return;
    }

    if (deleteCusta) {
      try {
        const retDev = await postApi(
          {
            idContrato: payloadData.idContrato,
            custas: {
              idCustas: payloadEditData.idCustas,
              idMotivoDevolucao: 0,
            },
          },
          "postCustasDevolver"
        );

        if (retDev.status === 400) throw new Error(retDev.errors);

        setResultOK(retDev.message);
        onClose();
      } catch (err) {
        const newErrors = { ...initialErrors };
        newErrors.apiResponse = err.message;
        setErrors(newErrors);
      }
      setLoading(false);
      return;
    }

    console.warn(payloadData, payloadEditData);

    try {
      const retSave = await postApi(
        {
          idContrato: payloadData.idContrato,
          custas: {
            idParcela: null,
            codDespesaSistema: "2",
            codDespesaCliente: "02",
            tipoComprovante: payloadData.tipo,
            nrComprovante: payloadData.nrComprov,
            dataDespesa: payloadData.dtDespesa,
            valorDespesa: payloadData.vlDespesa,
            valorSaldo: payloadData.vlDespesa,
            cobradoCliente: false,
            motivo: payloadData.motivo,
            cobrarFinanciadoFinanceira: payloadData.tipoCobranca,
            dataComprovante: payloadData.dtComprov,
            codigoRastreioCorreio: payloadData.codCorreio,
            idCustasExterna: null,
          },
        },
        "postCustasCadastrar"
      );

      if (retSave.status === 400) throw new Error(retSave.errors);

      setResultOK(retSave.message);
      onClose();
    } catch (err) {
      const newErrors = { ...initialErrors };
      newErrors.apiResponse = err.message;
      setErrors(newErrors);
    }

    setLoading(false);
    // setCustas(deleteCusta ? payloadEditData : payloadData)
    //   .then((data) => {
    //     if (data.status == 400) throw new Error(data.errors);

    //     setResultOK(data.message);
    //     setTimeout(() => {
    //       onClose();
    //     }, 3000);
    //   })
    //   .catch((err) => {
    //     const newErrors = { ...initialErrors };
    //     newErrors.apiResponse = err.message;
    //     setErrors(newErrors);
    //   })
    //   .finally(() => {
    //     setLoading(false);
    //   });
  }

  const handleSave = async () => {
    const payloadData = { ...payload };
    payloadData.dtComprov = formatDateForPayload(payloadData.dtComprov);
    payloadData.dtDespesa = formatDateForPayload(payloadData.dtDespesa);
    /* payloadData.vlDespesa = convertCurrencyToFloat(payloadData.vlDespesa); */

    const newErrors = { ...initialErrors };
    if (payloadData.nrContrato === "")
      newErrors.nrContrato = "O campo 'Contrato' não pode estar vazio.";

    if (payloadData.tipo === "")
      newErrors.tipo = "O campo 'Tipo de comprovante' não pode estar vazio.";

    if (payloadData.idContrato === 0) {
      newErrors.idContrato =
        "A indentificação do Contrato não encontrada contacte o suporte.";
    }

    if (payloadData.idAgrupamento === 0) {
      newErrors.idAgrupamento =
        "A indentificação do Agrupamento do contrato não encontrado contacte o suporte.";
    }

    if (payloadData.nrComprov == "") {
      newErrors.nrComprov =
        "O campo 'Número do Comprovante' não pode estar vazio.";
    }

    if (!payloadData.dtComprov) {
      newErrors.dtComprov = "O campo 'Data do Comprovante' é inválido.";
    }

    if (!payloadData.dtDespesa) {
      newErrors.dtDespesa = "O campo 'Data da Despesa' é inválido.";
    }

    if (!payloadData.vlDespesa) {
      newErrors.vlDespesa = "O campo 'Valor' é obrigatório.";
    }

    setErrors(newErrors);
    if (!Object.values(newErrors).every((error) => error === "")) return;
    onSave();
  };

  const contractList = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const optionsContrato = contractList
    ? [
        // { label: "Selecione", value: 0 },
        ...contractList.map((item) => {
          return {
            label: item.numero_Contrato,
            value: item.numero_Contrato.toString(),
          };
        }),
      ]
    : [];

  useEffect(() => {
    const types = [
      { label: "NF", value: "NF" },
      { label: "CUPOM", value: "CUPOM" },
      { label: "RECIB", value: "RECIB" },
    ];
    handlePayloadChange("idAgrupamento", financiadoData.id_Agrupamento);
    handlePayloadChange("idContrato", financiadoData.id_Contrato);
    setoptionsTipoComprovante(types);
    if (edit || deleteCusta) {
      setAcaoModal(edit ? "Editar" : "Excluir");
      HandleSelectContrato({
        label: dataEdit.numero_Contrato,
        value: dataEdit.numero_Contrato.toString(),
      });
      handleTipoComprovante({
        label: dataEdit.tipo_Comprov,
        value: dataEdit.tipo_Comprov,
      });
      handleNumeroComprovante(dataEdit.nr_Comprov);

      const dateParts = dataEdit.dt_Despesa.split("/");
      const convertedDate = new Date(
        dateParts[2],
        dateParts[1] - 1,
        dateParts[0]
      );
      if (!isNaN(convertedDate)) handleDataComprovante(convertedDate);

      const datePartsDespesa = dataEdit.dt_Inc.split("/");
      const convertedDateDespesa = new Date(
        datePartsDespesa[2],
        datePartsDespesa[1] - 1,
        datePartsDespesa[0]
      );
      if (!isNaN(convertedDateDespesa)) handleDataDespesa(convertedDateDespesa);

      const sanitizedValue = dataEdit.vl_Despesa.replace(/[^\d]/g, "");
      const formattedValue = formatarParaMoeda(sanitizedValue);
      const formattedValueF = formattedValue
        .replace(/\./g, "")
        .replace(",", ".");

      handleValor(dataEdit.vl_Despesa);
      handleMotivo(dataEdit.motivo);

      const payloadEditData = {
        idCustas: dataEdit.id_Custas,
        idAgrupamento: financiadoData.id_Agrupamento,
        idContrato: 0,
        nrContrato: dataEdit.numero_Contrato,
        nrComprov: dataEdit.nr_Comprov,
        dtComprov: convertedDate,
        dtDespesa: convertedDateDespesa,
        vlDespesa: parseFloat(formattedValueF),
        despesaSistema: "2",
        motivo: dataEdit.motivo,
        codCorreio: "",
        tipo: dataEdit?.tipo_Comprov,
        numero: "",
        reembolso: false,
        tipoCobranca: 0,
        motivoDev: 0,
        TipoAcao: edit ? 2 : 3,
      };

      setPayloadEdit(payloadEditData);
    }
  }, []);

  const HandleSelectContrato = (e) => {
    setSelectContrato(e);

    const contratoFind = contractList.find(
      (item) => item.numero_Contrato === e.value
    );
    handlePayloadChange("nrContrato", e.value);
    handlePayloadChange("idContrato", contratoFind.id_Contrato);
  };

  const handleTipoComprovante = (e) => {
    setSelectedTipoComprovante(e);
    handlePayloadChange("tipo", e.value);
  };

  const handleDataComprovante = (e) => {
    setSelectedDataComprovante(e);
    handlePayloadChange("dtComprov", e);
  };

  const handleNumeroComprovante = (e) => {
    setNumeroComprovante(e);
    handlePayloadChange("nrComprov", e);
  };

  const handleDataDespesa = (e) => {
    setSelectedDataDespesa(e);
    handlePayloadChange("dtDespesa", e);
  };

  const handleValor = (value) => {
    // Remove caracteres que não são dígitos
    const sanitizedValue = value.replace(/[^\d]/g, "");
    // Formata o valor enquanto o usuário digita
    const formattedValue = formatarParaMoeda(sanitizedValue);
    const formattedValueF = formattedValue.replace(/\./g, "").replace(",", ".");
    setValor(parseFloat(formattedValueF));
    handlePayloadChange("vlDespesa", parseFloat(formattedValueF));
  };

  const formatarParaMoeda = (valor) => {
    let valorNumerico = valor.replace(/\D/g, "");
    valorNumerico = (parseInt(valorNumerico, 10) / 100).toFixed(2);
    valorNumerico = valorNumerico.replace(".", ",");

    // Dividindo a string em parte inteira e decimal
    let partes = valorNumerico.split(",");
    let parteInteira = partes[0];
    let parteDecimal = partes[1];

    // Adicionando os pontos como separadores de milhares
    let parteInteiraFormatada = parteInteira.replace(
      /\B(?=(\d{3})+(?!\d))/g,
      "."
    );

    // Reconstruindo o valor formatado
    valorNumerico = parteInteiraFormatada + "," + parteDecimal;

    return valorNumerico;
  };

  const handleMotivo = (e) => {
    setMotivo(e);
    handlePayloadChange("motivo", e);
  };

  useEffect(() => {
    if (dataJuridico) {
      setValor(dataJuridico.valor_Devido);
      handlePayloadChange("vlDespesa", dataJuridico.valor_Devido);
      setMotivo(dataJuridico.lancamento);
      handlePayloadChange("motivo", dataJuridico.lancamento);
      if (optionsContrato.length === 1) {
        setSelectContrato(optionsContrato[0]);
        handlePayloadChange("nrContrato", optionsContrato[0].value);
      }
    }
  }, [dataJuridico]);

  return (
    <CModal
      className="custom-modal"
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
    >
      <CModalHeader closeButton>
        <CModalTitle>{acaoModal} Custa</CModalTitle>
      </CModalHeader>
      <CModalBody>
        {loading ? <LoadingComponent /> : ""}
        {resultOK ? <div style={{ fontSize: "22px" }}>{resultOK}</div> : ""}
        <div
          style={{ display: !loading && resultOK === "" ? "block" : "none" }}
        >
          <CRow>
            <CCol>
              {errors.apiResponse && (
                <div className="text-danger">{errors.apiResponse}</div>
              )}
              {errors.idContrato && (
                <div className="text-danger">{errors.idContrato}</div>
              )}
              {errors.idAgrupamento && (
                <div className="text-danger">{errors.idAgrupamento}</div>
              )}
              <CLabel>Contrato</CLabel>
              <Select
                value={selectContrato}
                options={optionsContrato}
                onChange={HandleSelectContrato}
                placeholder={"Selecione"}
                className={errors.nrContrato ? "border-danger rounded" : ""}
                isDisabled={edit || deleteCusta}
              />
              {errors.nrContrato && (
                <div className="text-danger">{errors.nrContrato}</div>
              )}
            </CCol>
          </CRow>

          <CRow className="my-3">
            <CCol>
              <CLabel>Tipo de Comprovante/Despesa</CLabel>
              <Select
                value={selectedTipoComprovante}
                options={optionsTipoComprovante}
                onChange={handleTipoComprovante}
                placeholder={"Selecione"}
                className={errors.tipo ? "border-danger rounded" : ""}
                isDisabled={deleteCusta}
              />
              {errors.tipo && <div className="text-danger">{errors.tipo}</div>}
            </CCol>
          </CRow>

          <CRow className="my-3">
            <CCol>
              <CLabel>Data Comprovante</CLabel>
              <ReactDatePicker
                selected={selectedDataComprovante}
                onChange={(date) => handleDataComprovante(date)}
                dateFormat="dd/MM/yyyy"
                placeholder="dd/MM/yyyy"
                locale={ptBR}
                className={`form-control input ${
                  errors.dtComprov ? "border-danger rounded" : ""
                }`}
                disabled={deleteCusta}
              />
              {errors.dtComprov && (
                <div className="text-danger">{errors.dtComprov}</div>
              )}
            </CCol>
            <CCol>
              <CLabel>Número Comprovante</CLabel>
              <CInput
                type="text"
                onChange={(e) => handleNumeroComprovante(e.target.value)}
                placeholder="Número Comprovante"
                className={errors.nrComprov ? "border-danger rounded" : ""}
                value={numeroComprovante}
                disabled={deleteCusta}
              />
              {errors.nrComprov && (
                <div className="text-danger">{errors.nrComprov}</div>
              )}
            </CCol>
          </CRow>

          <CRow className="my-3">
            <CCol>
              <CLabel>Data Despesa</CLabel>
              <ReactDatePicker
                selected={selectedDataDespesa}
                onChange={(date) => handleDataDespesa(date)}
                placeholder="dd/MM/yyyy"
                dateFormat="dd/MM/yyyy"
                className={`form-control input ${
                  errors.dtDespesa ? "border-danger rounded" : ""
                }`}
                locale={ptBR}
                disabled={deleteCusta}
              />
              {errors.dtDespesa && (
                <div className="text-danger">{errors.dtDespesa}</div>
              )}
            </CCol>
            <CCol>
              <CLabel>Valor</CLabel>
              <CInput
                type="text"
                onChange={(e) => {
                  handleValor(e.target.value);
                }}
                placeholder="Valor"
                className={errors.vlDespesa ? "border-danger rounded" : ""}
                value={formatCurrency(valor, false)}
                disabled={deleteCusta}
              />
              {errors.vlDespesa && (
                <div className="text-danger">{errors.vlDespesa}</div>
              )}
            </CCol>
          </CRow>

          <CRow className="my-3">
            <CCol>
              <CLabel>Motivo</CLabel> <br />
              <textarea
                style={{
                  width: "100%",
                  minHeight: "150px",
                  borderRadius: " 5px",
                }}
                placeholder=" Insira aqui suas observações."
                value={motivo}
                onChange={(e) => {
                  handleMotivo(e.target.value);
                }}
                className={errors.motivo ? "border-danger rounded" : ""}
                disabled={deleteCusta}
              />
              {errors.motivo && (
                <div className="text-danger">{errors.motivo}</div>
              )}
            </CCol>
          </CRow>
          {(deleteCusta || edit) && (
            <CRow className="my-3 px-3">
              <div style={{ fontSize: "20px", fontWeight: "bold" }}>
                Confirma a {acaoModal} da custa?
              </div>
            </CRow>
          )}
          <CRow className="d-flex justify-content-end px-3">
            <CButton color="danger" className="mr-2" onClick={onClose}>
              Cancelar
            </CButton>
            <CButton color="info" onClick={handleSave}>
              {acaoModal} Custa
            </CButton>
          </CRow>
        </div>
      </CModalBody>
    </CModal>
  );
};

export default FormCustasModal;
