import React, { ReactNode, useState } from "react";
import {
  CButton,
  CCol,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CRow,
  CTextarea,
} from "@coreui/react";
import { getURI } from "src/config/apiConfig";
import { PUT_DATA } from "src/api";
import { ItemFilaAprovacaoType } from "../../types/ItemFilaAprovacaoType";
import LoadingComponent from "src/reusable/Loading";

type Props = {
  isOpen: boolean;
  item: ItemFilaAprovacaoType;
  statusAprovacao: string;
  onClose: (updateData: boolean) => void;
};

const AprovacaoModal = ({ isOpen, item, statusAprovacao, onClose }: Props) => {
  const [motivo, setMotivo] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const handleClickAprovar = async () => {
    if (
      item?.id === null ||
      item?.id === "" ||
      statusAprovacao === "" ||
      statusAprovacao === null
    ) {
      alert("Não foi possível confirmar a aprovação.");
      return;
    }
    setIsLoading(true);
    await saveAprovacao();
    setIsLoading(false);
    onClose(true);
  };

  const saveAprovacao = async () => {
    const res = await PUT_DATA(
      getURI("filaAprovacaoCartasETermosUpdateStatus"),
      {
        id: item.id,
        status: statusAprovacao,
      },
      true
    );
    if (res?.status === 400) {
      return false;
    }
    if (res?.success === false) {
      return false;
    }
    setMotivo("");
    return true;
  };

  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMotivo(event.target.value);
  };

  return (
    <CModal show={isOpen} onClose={() => onClose(false)}>
      <CModalHeader>
        {statusAprovacao === "Aprovado" ? (
          <CModalTitle>Aprovar processo pendente</CModalTitle>
        ) : (
          <CModalTitle>Rejeitar processo pendente</CModalTitle>
        )}
      </CModalHeader>
      <CModalFooter>
        {isLoading ? (
          <div className="text-center w-100">
            <LoadingComponent />
          </div>
        ) : (
          <>
            <CButton
              color={statusAprovacao === "Aprovado" ? "success" : "danger"}
              onClick={() => handleClickAprovar()}
            >
              Confirma
            </CButton>
            <CButton color="secondary" onClick={() => onClose(false)}>
              Cancelar
            </CButton>
          </>
        )}
      </CModalFooter>
    </CModal>
  );
};

export default AprovacaoModal;
