<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <httpProtocol>
      <customHeaders>
        <!-- Impede ataques de Clickjacking, bloqueando o uso de iframes -->
        <add name="X-Frame-Options" value="DENY" />
        <!-- Alternativa moderna ao X-Frame-Options, impede que o site seja carregado em iframes -->
        <add name="Content-Security-Policy" value="frame-ancestors 'none';" />
        <!-- HSTS: forca o navegador a sempre usar HTTPS e impede ataques de downgrade -->
        <add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains; preload" />
        <!-- Evita ataques de MIME sniffing -->
        <add name="X-Content-Type-Options" value="nosniff" />
        <!-- Protege contra ataques de Cross-Site Scripting (XSS) -->
        <add name="X-XSS-Protection" value="1; mode=block" />
        <!-- Controla o envio do cabecalho Referer para proteger dados sensiveis -->
        <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
        <!-- Bloqueia funcionalidades nao utilizadas como geolocalizacao, microfone e camera -->
        <add name="Permissions-Policy" value="geolocation=(), camera=()" />
        <!-- Protege contra ataques de compartilhamento entre diferentes origens -->
        <add name="Cross-Origin-Embedder-Policy" value="unsafe-none" />
        <add name="Cross-Origin-Resource-Policy" value="cross-origin" />
        <add name="Cross-Origin-Opener-Policy" value="same-origin-allow-popups" />
      </customHeaders>
    </httpProtocol>
    <security>
      <requestFiltering>
        <requestLimits maxUrl="30000000" maxQueryString="30000000" maxAllowedContentLength="104857600" />
      </requestFiltering>
    </security>
    <rewrite>
      <rules>
        <rule name="Redirect to HTTPS" stopProcessing="true">
          <match url="(.*)" />
          <conditions>
            <add input="{HTTPS}" pattern="off" ignoreCase="true" />
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent" />
        </rule>
      </rules>
      <outboundRules>
        <!-- Remove o cabecalho "Server" das respostas HTTP -->
        <rule name="Remove Server Header">
          <match serverVariable="RESPONSE_Server" pattern=".+" />
          <action type="Rewrite" value="" />
        </rule>
        <rule name="Remove X-Powered-By">
          <match serverVariable="RESPONSE_X-Powered-By" pattern=".+" />
          <action type="Rewrite" value="" />
        </rule>
      </outboundRules>
    </rewrite>
  </system.webServer>
</configuration>
 