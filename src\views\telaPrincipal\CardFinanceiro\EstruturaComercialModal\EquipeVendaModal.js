import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CCol,
  CRow,
  CCard,
  CCardHeader,
} from "@coreui/react";
import { formatDate } from "src/reusable/helpers";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import TableSelectItens from "src/reusable/TableSelectItens";

const EquipeVendaModal = ({ isOpen, onClose, dados }) => {
  const [tableData, setTableData] = useState(null);
  const [headerData, setHeaderData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const tableColumns = [
    { key: "cod", label: "Código" },
    { key: "salesTeam", label: "Equipe de Venda" },
    { key: "email", label: "Email" },
    {
      key: "inclusion",
      label: "Data Inclusão",
      formatter: (item) => formatDate(item),
    },
  ];

  const handleClose = () => {
    onClose();
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(
          getURI(endpoint),
          null,
          true,
          true,
          payload
        );
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const updateView = async () => {
    setIsLoading(true);

    GetData(`/${dados}`, "getNewConEstruturaEquipe")
      .then((data) => {
        if (data) {
          setHeaderData([
            {
              label: "Equipe de Venda",
              value: data.salesTeam,
            },
            {
              label: "E-mail",
              value: data.email,
            },
            {
              label: "Data de Inclusão",
              value: data.inclusion ? formatDate(data.inclusion) : "---",
            },
          ]);
          setTableData(data.history);
        } else {
          setHeaderData(null);
          setTableData(null);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    if (isOpen) {
      updateView();
    }
  }, [isOpen]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal"
      size="lg"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Equipe de Vendas</h5>
      </CModalHeader>
      {isLoading ? (
        <div className="mt-2">
          <CardLoading />
        </div>
      ) : headerData == null ||
        headerData === undefined ||
        headerData.length === 0 ? (
        <NaoHaDadosTables />
      ) : (
        <CModalBody>
          {headerData && (
            <>
              <table className="table table-hover calculo">
                <tbody>
                  {headerData.map((row) => (
                    <CRow key={row.label}>
                      <CCol md="6" style={{ textAlign: "end" }}>
                        <strong>{row.label}:</strong>
                      </CCol>
                      <CCol md="6">{row.value ? row.value : "---"}</CCol>
                    </CRow>
                  ))}
                </tbody>
              </table>
              <CCard>
                <CCardHeader>Histórico</CCardHeader>
                {tableData && tableData.length > 0 ? (
                  <TableSelectItens
                    data={tableData}
                    columns={tableColumns}
                    onSelectionChange={(_) => {}}
                    defaultSelectedKeys={[]}
                    selectable={false}
                    heightParam="290px"
                  />
                ) : (
                  <CRow style={{ textAlign: "center", margin: "12px 0" }}>
                    <CCol>
                      <div>
                        Não foram encontrados dados de histórico para essa
                        equipe de venda.
                      </div>
                    </CCol>
                  </CRow>
                )}
              </CCard>
            </>
          )}
        </CModalBody>
      )}
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default EquipeVendaModal;
