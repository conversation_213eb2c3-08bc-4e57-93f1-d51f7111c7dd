import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CLabel,
  CFormGroup,
  CInput,
  CCol,
  CButton,
} from "@coreui/react";

const RamalModal = ({ isOpen, onClose, onSubmit }) => {
  const [ramalNumber, setRamalNumber] = useState("");

  const handleInputChange = (event) => {
    setRamalNumber(event.target.value);
  };
  const handleCancel = () => {
    onClose();
  };
  const handleConfirm = () => {
    onSubmit(ramalNumber);
    setRamalNumber("")
    onClose()
  };

  return (
    <CModal
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
      className="custom-modal"
    >
      <CModalHeader closeButton></CModalHeader>
      <CModalBody>
        <CRow>
          <CCol>
            <CFormGroup>
              <CLabel>Insira seu ramal do Tactium</CLabel>
              <CInput
                name="ramalTactium"
                type="text"
                placeholder="Ramal"
                value={ramalNumber}
                onChange={(e) =>
                  setRamalNumber(e.target.value.replace(/[^0-9.]/g, ""))
                }
              />
            </CFormGroup>
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        {/* <CButton color="secondary" onClick={handleCancel}>
          Cancelar
        </CButton> */}
        <CButton color="info" onClick={handleConfirm}>
          Confirmar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default RamalModal;
