import { CBadge, CButton, CInputCheckbox } from "@coreui/react";
import { POST_DATA } from "src/api";
import { removeDDDfromPhone } from "src/reusable/helpers";

const ordenacaoStatusBadge = (item) => {
  return item.status === 1
    ? 2
    : item.status === 2
    ? 1
    : item.status === 3
    ? 3
    : item.status === -1
    ? 5
    : 4;
};

const ordenacaoTelefones = (items) => {
  const clientPhonesSortData = items.map((item) => {
    item.ordenacao = ordenacaoStatusBadge(item);
    return item;
  });
  const sortPhones = clientPhonesSortData.sort(function (a, b) {
    // Primeiro, ordene pela propriedade ordenacao em ordem crescente
    if (a.ordenacao < b.ordenacao) return -1;
    if (a.ordenacao > b.ordenacao) return 1;

    // Se a ordenacao for igual, ordene pela propriedade dtInclusao em ordem decrescente
    var dataA = new Date(b.dtInclusao);
    var dataB = new Date(a.dtInclusao);

    if (dataA < dataB) return -1;
    if (dataA > dataB) return 1;

    return 0;
  });
  return sortPhones;
};

const renderActionStatus = (item, handleChangeStatus) => {
  return (
    <div style={{ display: "flex" }}>
      <IconButton
        icon={"cil-warning"}
        color={"blue"}
        titulo={"Efetivar"}
        onClick={() => handleChangeStatus(item, 2)}
        disabled={item.status === -1}
      />
      <IconButton
        icon={"cil-warning"}
        color={"red"}
        titulo={"Inativar"}
        onClick={() => handleChangeStatus(item, 0)}
        disabled={item.status === -1}
      />
      <IconButton
        icon={"cil-search"}
        color={"dark"}
        titulo={"Pesquisado"}
        onClick={() => handleChangeStatus(item, 3)}
        disabled={item.status === -1}
      />
      <IconButton
        icon={"cil-check-circle"}
        color={"green"}
        titulo={"Ativar"}
        onClick={() => handleChangeStatus(item, 1)}
        disabled={item.status === -1}
      />
    </div>
  );
};

const renderStatusBadge = (item) => {
  return item.status === 1 ? (
    <CBadge color={"success"}> Ativo </CBadge>
  ) : item.status === 2 ? (
    <CBadge color={"info"}> Efetivo </CBadge>
  ) : item.status === 3 ? (
    <CBadge color={"primary"}> Pesquisado </CBadge>
  ) : item.status === -1 ? (
    <CBadge color={"dark"}> Blacklist </CBadge>
  ) : (
    <CBadge color={"danger"}> Inativo </CBadge>
  );
};

const renderTelefone = (item) => {
  return (
    <label for={"checkboxTel" + item.id_Telefone}>{item.ddd + item.fone}</label>
  );
};

const renderRadio = (item, handleTelefonesChange, selectedPhone = null) => {
  let selected = false;
  if (selectedPhone !== null && selectedPhone.length > 0) {
    if (typeof selectedPhone[0] === "string") {
      const fone = removeDDDfromPhone(selectedPhone[0].trim());
      selected = fone === item.fone.trim();
    }
  }
  return (
    <input
      type="radio"
      name="radioTel"
      id={"radioTel" + item.id_Telefone}
      value={item.ddd + item.fone}
      onChange={handleTelefonesChange}
      checked={selected}
    />
  );
};

const renderCheckbox = (item, handleTelefonesChange, selectedPhone = null) => {
  let selected = false;
  try {
    if (selectedPhone !== null && selectedPhone.length > 0) {
      const index = selectedPhone.find((x) => {
        return x.replace(/\D/g, "") === item.ddd.trim() + item.fone.trim();
      });
      if (index) {
        selected = true;
      }
    }
  } catch (err) {
    selected = false;
    console.error(err);
  }
  return (
    <input
      type="checkbox"
      name="checkboxTel"
      id={"checkboxTel" + item.id_Telefone}
      value={item.ddd + item.fone}
      onChange={handleTelefonesChange}
      checked={selected}
    />
  );
};

const telefonePost = async (data) => {
  const result = await POST_DATA("Datacob/DadosCadastrais/Financiado", data);
  return result;
};

const IconButton = ({ icon, color, titulo, onClick, disabled = false }) => {
  return (
    <CButton
      title={titulo}
      className="mr-1"
      style={{
        border: "solid 1px",
        borderColor: color,
        color: color,
        padding: "2px 4px",
      }}
      onClick={onClick}
      disabled={disabled}
    >
      <i className={icon} />
    </CButton>
  );
};

export {
  renderActionStatus,
  ordenacaoStatusBadge,
  ordenacaoTelefones,
  renderTelefone,
  renderStatusBadge,
  telefonePost,
  IconButton,
  renderRadio,
  renderCheckbox,
};
