import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CRow,
  CCol,
  CButton,
  CModalTitle,
  CCardBody,
  CForm,
  CFormGroup,
  CInput,
  CLabel,
} from "@coreui/react";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { User } from "src/types/localStorage";

const CrmAuthModal = ({
  onClose,
  chosenCrm,
}: {
  chosenCrm: string;
  onClose: () => void;
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [login, setLogin] = useState("");
  const [password, setPassword] = useState("");

  const user = JSON.parse(localStorage.getItem("user") || "{}") as User;

  useEffect(() => {
    if (error !== "") {
      toast.info(error);
      setError("");
    }
  }, [error, onClose]);

  async function handleConfirm(): Promise<void> {
    if (login === "" || password === "") {
      setError("Por favor, preencha todos os campos");
      return;
    }
    setLoading(true);
    try {
      const response = await POST_DATA(
        getURI("postUserCrm"),
        {
          login: login,
          password: password,
          crm: chosenCrm,
          userId: user?.id,
        },
        true
      );
      if (response.success === true) {
        toast.info("Credenciais salvas com sucesso");
        user.crmCred = [...user.crmCred, response.data];
        localStorage.setItem("user", JSON.stringify(user));
        onClose();
      } else {
        toast.error("Erro ao salvar credenciais");
      }
    } catch (error) {
      toast.error("Erro ao salvar credenciais");
    }
    setLoading(false);
  }

  return (
    <CModal
      show={true}
      onClose={onClose}
      closeOnBackdrop={false}
      className="custom-modal"
    >
      <CModalHeader>
        <CModalTitle>
          Por gentileza, informe seu usuário e senha do Datacob
        </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CRow>
          <CCol>
            <CCardBody className="py-1">
              {loading ? (
                <CardLoading Title={""} Msg={""} />
              ) : (
                <>
                  <CForm>
                    <CFormGroup>
                      <CLabel>login</CLabel>
                      <CInput
                        onChange={(e) => setLogin(e.currentTarget.value)}
                      />
                      <CLabel>Senha</CLabel>
                      <CInput
                        type="password"
                        onChange={(e) => setPassword(e.currentTarget.value)}
                      />
                    </CFormGroup>
                  </CForm>
                </>
              )}
            </CCardBody>
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CButton color="info" onClick={handleConfirm}>
          Confirmar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default CrmAuthModal;
