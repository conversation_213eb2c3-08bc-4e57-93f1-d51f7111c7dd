import React, { useState, useEffect } from "react";
import {
  CButton,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CRow,
  CCol,
} from "@coreui/react";
import LoadingComponent from "src/reusable/Loading";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import "react-toastify/dist/ReactToastify.css";
import { formatDateTime, formatDocument } from "src/reusable/helpers";
import { formatarTelefone } from "src/reusable/functions";
import { useMyContext } from "src/reusable/DataContext";
import { toast } from "react-toastify";
import { useHistory } from "react-router-dom";

const GetData = async (payload, endpoint = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(getURI(endpoint), payload, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const HistCallModal = ({ isOpen, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [ligacoes, setLigacoes] = useState([]);
  const { updateData, updateCustas, updateCustasProjuris } = useMyContext();
  const history = useHistory();

  async function getLigacoes() {
    setIsLoading(true);
    const response = await GET_DATA(
      getURI("getAgentCall"),
      { id: userProfile.id },
      true
    );
    if (response.length) setLigacoes(response);
    setIsLoading(false);
  }

  useEffect(() => {
    if (isOpen) getLigacoes();
  }, [isOpen]);

  async function loadFinanced(id, doc) {
    setIsLoading(true);
    const response = await GET_DATA(
      getURI("getCrm"),
      { idContrato: id, documento: doc },
      true
    );
    if (response !== null) {
      await buscarFinanciado(response, id);
    } else {
      setIsLoading(false);
      toast.error("Erro: Dados não encontrados");
    }
  }

  const getContratosAtivos = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const buscarFinanciado = async (crm, idAgrupamento) => {
    const payload = {
      ActiveConnection: crm,
      agrupamentoId: idAgrupamento,
    };
    // const payload = { ActiveConnection: "GVC", agrupamentoId: 35199 };
    await updateConnection(crm);
    await GetData(payload, "getDadosContratoLigacao")
      .then((resultado) => {
        if (resultado && resultado.length > 0) {
          //updateFinanciado(resultado[0]);
          localStorage.setItem("financiadoData", JSON.stringify(resultado[0])); //Atualizando o financiadoData no localStorage
          updateData(resultado[0]); //Atualizando o financiadoData na telaPrincipal
          updateCustas(null);
          updateCustasProjuris(null);
          //setDadosFinanciados(resultado[0]); //Atualizando o financiadoData nesse arquivo
          //confirmarFinanciado(); //Confirmando a ligação com a API
          history.push("/telaprincipal");
        } else {
          //Se apareceu o contrato e teve permissão de clicar, nunca deveria falhar aqui
          toast.error("Erro: Dados não encontrados");
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
        onClose();
      });
  };

  const userProfile = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : "";

  const updateConnection = async (idcrm) => {
    const payload = {
      activeConnection: idcrm,
      userId: userProfile.id,
    };
    await postActiveConnection(payload, "postUserConnection")
      .then((data) => {
        if (data) {
          const newActiveConnection = {
            ...userProfile,
            activeConnection: idcrm,
          };
          localStorage.setItem("user", JSON.stringify(newActiveConnection));
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const postActiveConnection = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="lg">
      <CModalHeader closeButton>
        <CModalTitle>Histórico Ligações</CModalTitle>
      </CModalHeader>
      <CModalBody className="pb-0 mb-2" style={{ minHeight: "340px" }}>
        {isLoading ? (
          <CRow style={{ justifyContent: "center" }}>
            <LoadingComponent />
          </CRow>
        ) : (
          <CRow>
            <CCol>
              <table className="table">
                <thead>
                  <tr>
                    <th>Número</th>
                    <th>Documento</th>
                    <th>Financiado</th>
                    <th>Tipo</th>
                    <th>Horário</th>
                  </tr>
                </thead>
                <tbody>
                  {ligacoes.length > 0 ? (
                    ligacoes.map((ligacao, index) => (
                      <>
                        <tr>
                          <td>{formatarTelefone(ligacao.phone)}</td>
                          <td>
                            {ligacao.document && ligacao.financedName !== ""
                              ? formatDocument(ligacao.document)
                              : "Não identificado"}
                          </td>
                          <td>
                            <CLabel
                              style={
                                ligacao.idGrouping !== null
                                  ? { cursor: "pointer", color: "#321fdb" }
                                  : {}
                              }
                              onClick={() => {
                                if (ligacao.idGrouping !== null)
                                  loadFinanced(
                                    ligacao.idGrouping,
                                    ligacao.document
                                  );
                                else return false;
                              }}
                            >
                              {ligacao.financedName &&
                              ligacao.financedName !== ""
                                ? ligacao.financedName
                                : "Não identificado"}
                            </CLabel>
                          </td>
                          <td>{ligacao.type}</td>
                          <td>{formatDateTime(ligacao.beginCall)}</td>
                        </tr>
                      </>
                    ))
                  ) : (
                    <>
                      <tr>
                        <td colSpan="4">Nenhuma ligação encontrada</td>
                      </tr>
                    </>
                  )}
                  ;
                </tbody>
                {/* {ligacoes.length > 0
                  ? ligacoes.map((ligacao, index) => (
                      <>
                        <div
                          className="d-flex shadow-sm p-3 mb-1 bg-white rounded"
                          style={{
                            justifyContent: "center",
                            gap: "10%",
                          }}
                        >
                          <CLabel>{formatarTelefone(ligacao.phone)}</CLabel>
                          <CLabel>
                            {ligacao.document && ligacao.financedName !== ""
                              ? formatDocument(ligacao.document)
                              : "Não identificado"}
                          </CLabel>
                          <CLabel
                            style={
                              ligacao.idGrouping !== null
                                ? { cursor: "pointer", color: "#321fdb" }
                                : {}
                            }
                            onClick={() => {
                              if (ligacao.idGrouping !== null)
                                loadFinanced(
                                  ligacao.idGrouping,
                                  ligacao.document
                                );
                              else return false;
                            }}
                          >
                            {ligacao.financedName && ligacao.financedName !== ""
                              ? ligacao.financedName
                              : "Não identificado"}
                          </CLabel>
                          <CLabel>{formatDateTime(ligacao.beginCall)}</CLabel>
                        </div>
                      </>
                    ))
                  : null} */}
              </table>
            </CCol>
          </CRow>
        )}
      </CModalBody>
      <CModalFooter className="my-0 py-1">
        <CButton color="secondary" onClick={onClose}>
          Fechar
        </CButton>
        {/* <CButton
          color="info"
          onClick={handleConfirmarPausa}
          disabled={isLoadingConfirmar}
        >
          {isLoadingConfirmar ? <LoadingComponent /> : "Ok"}
        </CButton> */}
      </CModalFooter>
    </CModal>
  );
};

export default HistCallModal;
